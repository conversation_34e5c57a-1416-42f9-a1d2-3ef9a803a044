import { userAPI } from '../API/userAPI';
import { PORTAL_ADMINISTRATION } from '../mockAPI/consts/userRoles';
import axios from 'axios';

/**
 * Serviço para gerenciamento de autenticação e autorização
 */
class AuthService {
  /**
   * Verifica se o usuário atual tem permissão de administrador (ID 2)
   * @returns {Promise<boolean>} True se tem permissão de admin, false caso contrário
   */
  async verificarPermissaoAdmin() {
    try {
      console.log('AuthService: Verificando permissão de administrador...');

      // Obter detalhes do usuário do backend
      const userDetails = await this.getUserDetailsFromBackend();

      if (!userDetails || !userDetails[0] || !userDetails[0].userPermissions) {
        console.warn('AuthService: Dados de usuário inválidos ou sem permissões');
        return false;
      }

      const permissoes = userDetails[0].userPermissions;
      console.log('AuthService: Permissões encontradas:', permissoes);

      // Verificar especificamente a permissão ID 2 (PORTAL_ADMINISTRATION)
      const temPermissaoAdmin = permissoes.some(
        (permission) =>
          String(permission.id) === "2" ||
          permission.id === 2 ||
          permission.name === PORTAL_ADMINISTRATION ||
          permission.name === "903010"
      );
      console.log('AuthService: Usuário tem permissão de administrador?', temPermissaoAdmin);
      return temPermissaoAdmin;
    } catch (error) {
      console.error('AuthService: Erro ao verificar permissão de administrador:', error);
      return false;
    }
  }

  /**
   * Obtém detalhes do usuário diretamente do backend
   * @returns {Promise<Array>} Array com detalhes do usuário
   */
  async getUserDetailsFromBackend() {
    try {
      console.log('AuthService: Obtendo detalhes do usuário do backend...');

      // Tentar obter detalhes usando a API existente
      const detalhes = await userAPI.GetUserDetails();

      if (detalhes && !detalhes.error) {
        console.log('AuthService: Detalhes do usuário obtidos com sucesso via API');
        return detalhes;
      }

      // Se falhar, tentar diretamente com Axios
      console.log('AuthService: Tentando obter detalhes do usuário diretamente via Axios...');
      const token = this.getAuthToken();
      const baseUrl = process.env.REACT_APP_WEBAPI_URL || 'https://localhost:44354';

      // Tentar diferentes caminhos para o endpoint
      const possibleEndpoints = [
        `${baseUrl}/api/user/GetUserDetails`,
        `${baseUrl}/api/User/GetUserDetails`,
        `${baseUrl}/api/user/GetCurrentUser`,
        `${baseUrl}/api/User/GetCurrentUser`
      ];

      let response = null;
      let success = false;

      for (const endpoint of possibleEndpoints) {
        try {
          console.log(`AuthService: Tentando endpoint: ${endpoint}`);
          response = await axios.get(endpoint, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });

          if (response.status === 200 && response.data) {
            console.log(`AuthService: Detalhes obtidos com sucesso via ${endpoint}`);
            success = true;
            break;
          }
        } catch (err) {
          console.log(`AuthService: Falha ao tentar ${endpoint}: ${err.message}`);
        }
      }

      if (success && response) {
        return response.data;
      }
      throw new Error('Falha ao obter detalhes do usuário em todos os endpoints possíveis');
    } catch (error) {
      console.error('AuthService: Erro ao obter detalhes do usuário:', error);
      throw error;
    }
  }

  /**
   * Verifica se o usuário está autenticado
   * @returns {Promise<boolean>} True se autenticado, false caso contrário
   */
  async verificarAutenticacao() {
    try {
      console.log('AuthService: Verificando autenticação...');

      // Primeiro verificar se há token
      const token = this.getAuthToken();
      if (!token) {
        console.log('AuthService: Nenhum token encontrado');
        return false;
      }

      // Verificar autenticação no backend
      const authInfo = await userAPI.fetchAuthInfo();

      if (authInfo && authInfo.isAuthenticated) {
        console.log('AuthService: Usuário está autenticado');
        return true;
      }

      console.log('AuthService: Usuário não está autenticado');
      return false;
    } catch (error) {
      console.error('AuthService: Erro ao verificar autenticação:', error);
      return false;
    }
  }

  /**
   * Obtém o token de autenticação
   * @returns {string|null} Token de autenticação ou null se não existir
   */
  getAuthToken() {
    const azureToken = localStorage.getItem('azureToken');
    const session = localStorage.getItem('session');
    const accessToken = localStorage.getItem('accessToken');

    if (azureToken) return azureToken;

    if (session) {
      try {
        const parsed = JSON.parse(session);
        return parsed;
      } catch (e) {
        return session;
      }
    }

    if (accessToken) {
      try {
        const parsed = JSON.parse(accessToken);
        return parsed;
      } catch (e) {
        return accessToken;
      }
    }

    return null;
  }

  /**
   * Verifica se o usuário pode acessar o dashboard
   * @returns {Promise<boolean>} True se pode acessar, false caso contrário
   */
  async podeAcessarDashboard() {
    const autenticado = await this.verificarAutenticacao();
    if (!autenticado) {
      console.log('AuthService: Usuário não autenticado, acesso negado ao dashboard');
      return false;
    }

    const temPermissaoAdmin = await this.verificarPermissaoAdmin();
    if (!temPermissaoAdmin) {
      console.log('AuthService: Usuário sem permissão admin, acesso negado ao dashboard');
      return false;
    }

    console.log('AuthService: Usuário autenticado e com permissão admin, acesso permitido ao dashboard');
    return true;
  }

  /**
   * Verifica o status completo de autenticação e permissões do usuário
   * @returns {Promise<Object>} Objeto com status de autenticação e permissões
   */
  async verificarStatusCompleto() {
    const autenticado = await this.verificarAutenticacao();
    let permissoes = [];
    let temPermissaoAdmin = false;

    if (autenticado) {
      try {
        const detalhes = await this.getUserDetailsFromBackend();
        if (detalhes && detalhes[0] && detalhes[0].userPermissions) {
          permissoes = detalhes[0].userPermissions;

          temPermissaoAdmin = permissoes.some(
            (permission) =>
              String(permission.id) === "2" ||
              permission.id === 2 ||
              permission.name === PORTAL_ADMINISTRATION ||
              permission.name === "903010"
          );
        }
      } catch (error) {
        console.error('AuthService: Erro ao obter permissões:', error);
      }
    }

    return {
      autenticado,
      permissoes,
      temPermissaoAdmin,
      podeAcessarDashboard: autenticado && temPermissaoAdmin
    };
  }
}

// Singleton
const authService = new AuthService();
export default authService;
