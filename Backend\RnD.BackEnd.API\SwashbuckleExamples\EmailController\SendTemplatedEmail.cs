﻿using System.Collections.Generic;
using RnD.BackEnd.API.Model.Email;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.EmailController
{
    /// <summary>
    /// Send Templated Email request example
    /// </summary>
    public class SendTemplatedEmail : IExamplesProvider<SpecificTemplateDto>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to Send Templated Email.
        /// </returns>
        public SpecificTemplateDto GetExamples()
        {
            return new SpecificTemplateDto
            {
                TemplateData = new CodeAcademyTemplateDto
                {
                    Subject = "Code Academy Email Service",
                    Message = "This is a test e-mail",
                    Session = "SendGrid email service",
                    Date = "19-07-2022"
                },
                Recipients = new List<string> { "<EMAIL>" },
                TemplateId = "d-8d3eac75edc84f6e87cdfe48a3e850b5"
            };
        }
    }
}
