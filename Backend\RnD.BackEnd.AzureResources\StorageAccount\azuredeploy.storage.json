{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"tenantId": {"type": "string", "defaultValue": "[subscription().tenantId]", "metadata": {"description": "Specifies the Azure Active Directory tenant ID that should be used for authenticating requests to the key vault. Get it by using Get-AzSubscription cmdlet."}}, "location": {"defaultValue": "[resourceGroup().location]", "type": "string", "metadata": {"description": "Provide the location for resources."}}, "storage-Name": {"type": "string", "minLength": 1}, "storage-Type": {"type": "string", "defaultValue": "Standard_LRS", "allowedValues": ["Standard_LRS", "Standard_ZRS", "Standard_GRS", "Standard_RAGRS", "Premium_LRS"]}, "storage-container-Name": {"type": "string", "minLength": 1}}, "variables": {"storageName": "[concat(parameters('storage-Name'), uniqueString(resourceGroup().id))]"}, "resources": [{"name": "[variables('storageName')]", "type": "Microsoft.Storage/storageAccounts", "location": "[parameters('location')]", "apiVersion": "2019-04-01", "sku": {"name": "[parameters('storage-Type')]"}, "dependsOn": [], "tags": {"displayName": "storage-account"}, "kind": "BlobStorage", "properties": {"networkAcls": {"bypass": "AzureServices", "virtualNetworkRules": [], "ipRules": [], "defaultAction": "Allow"}, "supportsHttpsTrafficOnly": true, "encryption": {"services": {"file": {"enabled": true}, "blob": {"enabled": true}}, "keySource": "Microsoft.Storage"}, "accessTier": "Cool"}}, {"type": "Microsoft.Storage/storageAccounts/blobServices", "apiVersion": "2019-04-01", "name": "[concat(variables('storageName'), '/default')]", "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts', variables('storageName'))]"], "sku": {"name": "Standard_LRS"}, "properties": {"cors": {"corsRules": []}, "deleteRetentionPolicy": {"enabled": false}}, "tags": {"displayName": "storage-blobService"}}, {"type": "Microsoft.Storage/storageAccounts/blobServices/containers", "apiVersion": "2019-04-01", "name": "[concat(variables('storageName'), '/default/', parameters('storage-container-Name'))]", "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts/blobServices', variables('storageName'), 'default')]", "[resourceId('Microsoft.Storage/storageAccounts', variables('storageName'))]"], "properties": {"publicAccess": "None"}, "tags": {"displayName": "storage-container"}}], "outputs": {"BlobStorage-ServiceName": {"value": "[variables('storageName')]", "type": "string"}, "BlobStorage-Connection": {"type": "string", "value": "[concat('DefaultEndpointsProtocol=https;AccountName=', variables('storageName'), ';AccountKey=', listKeys(resourceId('Microsoft.Storage/storageAccounts', variables('storageName')), '2019-04-01').keys[0].value)]"}, "BlobStorage-AccountKey": {"type": "string", "value": "[listKeys(resourceId('Microsoft.Storage/storageAccounts', variables('storageName')), '2019-04-01').keys[0].value]"}}}