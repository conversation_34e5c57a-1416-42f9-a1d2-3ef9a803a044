﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RnD.BackEnd.API.Models
{
    public class Subscription
    {
        [Key]
        public int ID { get; set; }

        [Required]
        [MaxLength(255)]
        public string SubscriptionID { get; set; }

        public virtual ICollection<ConsumoSubscricao> Consumos { get; set; }
    }
}
