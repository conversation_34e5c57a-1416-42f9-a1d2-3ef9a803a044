﻿using System.Collections.Generic;
using RnD.BackEnd.API.Model.Weather;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController
{
    /// <summary>
    /// Multiple Weather forecast create request example
    /// </summary>
    public class ListWeather : IMultipleExamplesProvider<List<CreateWeatherDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to create multiple Weather forecast.
        /// </returns>
        public IEnumerable<SwaggerExample<List<CreateWeatherDto>>> GetExamples()
        {
            yield return SwaggerExample.Create("Poland", new List<CreateWeatherDto>
            {
                new CreateWeatherDto
                {
                    Location = "Warsaw",
                    Summary = "Snowing",
                    Temperature = -10
                },
                new CreateWeatherDto
                {
                    Location = "Krakow",
                    Summary = "Raining",
                    Temperature = 1
                }
            });

            yield return SwaggerExample.Create("Portugal", new List<CreateWeatherDto>
            {
                new CreateWeatherDto
                {
                    Location = "Lisbon",
                    Summary = null,
                    Temperature = 10
                },
                new CreateWeatherDto
                {
                    Location = "Porto",
                    Summary = null,
                    Temperature = 11
                }
            });
        }
    }
}