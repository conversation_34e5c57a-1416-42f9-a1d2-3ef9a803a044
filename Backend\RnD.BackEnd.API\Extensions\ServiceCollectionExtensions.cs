﻿using System.Net;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Polly;
using RnD.BackEnd.API.Services;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Entities.Vehicle;
using RnD.BackEnd.Domain.Entities.Weather;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Managers;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.UnitsOfWork;
using RnD.BackEnd.Domain.Interfaces.Services;
using RnD.BackEnd.Domain.Settings;
using RnD.BackEnd.Email.Interfaces;
using RnD.BackEnd.Email.Managers;
using RnD.BackEnd.Infrastructure.Interfaces;
using RnD.BackEnd.Infrastructure.Managers;
using RnD.BackEnd.Infrastructure.Repositories.Cosmos;
using RnD.BackEnd.Infrastructure.Repositories.Database;
using RnD.BackEnd.Infrastructure.UnitsOfWork;
using RnD.BackEnd.Service.Services;
using Microsoft.AspNetCore.Authorization;

namespace RnD.BackEnd.API.Extensions
{
    /// <summary>
    /// Service collection extensions class
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Configures the infrastructure layer.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <param name="appSettings">The application settings.</param>
        /// <returns>
        /// The service collection.
        /// </returns>
        /// <exception cref="System.Exception">No appsettings section has been found
        /// or
        /// No valid settings.</exception>
        public static IServiceCollection ConfigureInfrastructure(this IServiceCollection services, AppSettings appSettings)
        {
            // Register Database unit of work
            services.AddTransient<IDatabaseUnitOfWork, DatabaseUnitOfWork>();

            // Register SQL Repositories
            services.AddScoped<IWeatherRepository, WeatherRepository>();

            // Register CosmosDb manager
            // Microsoft recommends a singleton client instance to be used throughout the application
            // https://docs.microsoft.com/en-us/dotnet/api/microsoft.azure.cosmos.cosmosclient?view=azure-dotnet#definition
            // "CosmosClient is thread-safe. Its recommended to maintain a single instance of CosmosClient per lifetime of the application which enables efficient connection management and performance"
            services.AddSingleton<ICosmosDbManager, CosmosDbManager>();

            //Register Cosmos Repositories
            services.AddScoped<IDynamicEntityRepository, DynamicEntityRepository>();
            services.AddScoped<IVehiclesRepository, VehiclesRepository>();

            // Register Cache manager
            services.AddSingleton<ICacheManager, MemoryCacheManager>();

            // Register API connector manager
            services.AddSingleton<IApiConnectorManager, ApiConnectorManager>();

            // Configure email manager
            EmailSettings emailSettings = appSettings.EmailSettings;
            services.AddTransient<IEmailManager>(eml => new SendGridManager(emailSettings.ApiKey, emailSettings.AllowSendingEmails, emailSettings.SendAlwaysToAddresses));

            return services;
        }

        /// <summary>
        /// Configures the business services.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <returns>
        /// The service collection.
        /// </returns>
        public static IServiceCollection ConfigureBusinessServices(this IServiceCollection services)
        {
            // Configure business services
            services.AddTransient<IEmailService, EmailService>();
            services.AddTransient<IWeatherService, WeatherService>();
            services.AddTransient<IVehiclesService, VehicleService>();
            services.AddTransient<IDynamicEntityService, DynamicEntityService>();
            
            // Adicionar o serviço de user
            services.AddScoped<IUserService, UserService>();

            // Configure business services that use API Connector manager
            services.AddSingleton<IWorkflowService, WorkflowService>();
            services.AddSingleton<IDataEntryService, DataEntryService>();

            services.AddHttpContextAccessor();
            services.TryAddSingleton<IActionContextAccessor, ActionContextAccessor>();

            // cache in memory
            services.AddMemoryCache();

            // caching response for middlewares
            services.AddResponseCaching();

            return services;
        }

        /// <summary>
        /// Configures the policies.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <returns>
        /// The service collection.
        /// </returns>
        public static IServiceCollection ConfigurePolicies(this IServiceCollection services)
        {
            services.AddSingleton<IAsyncPolicy<ServiceOutput<Vehicle>>>(x => Policy
                .HandleResult<ServiceOutput<Vehicle>>(x => x.Code == HttpStatusCode.Conflict)
                .RetryAsync(3));

            services.AddSingleton<IAsyncPolicy<ServiceOutput<Weather>>>(x => Policy
                .HandleResult<ServiceOutput<Weather>>(x => x.Code == HttpStatusCode.Conflict)
                .RetryAsync(3));
                
            // Adicionar políticas de autorização para Azure AD
            services.AddAuthorization(options =>
            {
                // Adicionar política para Azure AD
                options.AddPolicy("AzureAD", policy =>
                {
                    policy.RequireAuthenticatedUser();
                    policy.AuthenticationSchemes.Add("Bearer");
                });
            });

            return services;
        }
    }
}
