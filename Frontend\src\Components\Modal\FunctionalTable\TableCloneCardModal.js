import { useState } from 'react';
import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';
import Modal from '../ModalTemplate';

const TableCloneCardModal = (props) => {
  const { onClose, open, selectedProcesses } = props;
  const [isSubmitting] = useState(false);

  return (
    <Modal
      onClose={onClose}
      open={open}
      title="Duplicar Processos"
      onCancel={onClose}
      isSubmitting={isSubmitting}
      content={
        <Box>
          <Typography
            color="textPrimary"
            variant="h1"
          >
            {`Pretende duplicar o${(selectedProcesses.length > 1 ? 's' : '')} processo${(selectedProcesses.length > 1 ? 's' : '')} selecionado${(selectedProcesses.length > 1 ? 's' : '')}?`}
          </Typography>
        </Box>
      }
    />
  );
};

TableCloneCardModal.propTypes = {
  selectedProcesses: PropTypes.array,
  onClose: PropTypes.func,
  open: PropTypes.bool.isRequired,
};

export default TableCloneCardModal;
