﻿using System.Collections.Generic;
using System.Net;
using System.Runtime.Serialization;
using RnD.BackEnd.API.Model.Exception;

namespace RnD.BackEnd.API.Model.Generic
{
    /// <summary>
    /// API output class
    /// </summary>
    /// <typeparam name="T"></typeparam>
    [DataContract]
    public class ApiOutput<T>
    {
        /// <summary>
        /// Gets or sets the code.
        /// </summary>
        /// <value>
        /// The code.
        /// </value>
        [DataMember]
        public HttpStatusCode Code { get; set; } = HttpStatusCode.OK;

        /// <summary>
        /// Gets or sets the description.
        /// </summary>
        /// <value>
        /// The description.
        /// </value>
        [DataMember]
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the value.
        /// </summary>
        /// <value>
        /// The value.
        /// </value>
        [DataMember]
        public T Value { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this <see cref="ApiOutput{T}"/> is error.
        /// </summary>
        /// <value>
        ///   <c>true</c> if error; otherwise, <c>false</c>.
        /// </value>
        [DataMember]
        public bool Error { get; set; }

        /// <summary>
        /// Gets or sets the exception messages.
        /// </summary>
        /// <value>
        /// The exception messages.
        /// </value>
        [DataMember]
        public ModelException ExceptionMessages { get; set; }

        /// <summary>
        /// Gets or sets the properties.
        /// </summary>
        /// <value>
        /// The properties.
        /// </value>
        [DataMember]
        public Dictionary<string, object> Properties { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ApiOutput{T}"/> class.
        /// </summary>
        public ApiOutput()
        {
            ExceptionMessages = new ModelException();
        }
    }
}
