{"name": "rnd_frontend_template", "version": "0.1.0", "private": true, "dependencies": {"@azure/msal-browser": "^2.39.0", "@azure/msal-react": "^1.5.13", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.10.15", "@mui/material": "^5.16.14", "@mui/x-data-grid": "^5.17.26", "@mui/x-date-pickers": "^5.0.20", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.3", "chart.js": "^3.9.1", "clsx": "^1.2.1", "date-fns": "^2.30.0", "formik": "^2.4.6", "html-react-parser": "^3.0.7", "i18next": "^22.5.1", "lodash.merge": "^4.6.2", "material-ui-popup-state": "^4.1.0", "moment": "^2.30.1", "msal": "^1.4.17", "prismjs": "^1.29.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^4.3.1", "react-collapsible": "^2.10.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-hot-toast": "^2.5.2", "react-i18next": "^12.3.1", "react-perfect-scrollbar": "^1.5.8", "react-router": "^6.4.3", "react-router-dom": "^6.4.3", "react-router-prompt": "^0.3.0", "react-scripts": "^4.0.1", "react-simple-code-editor": "^0.13.1", "sass": "^1.56.1", "stylis-plugin-rtl": "^2.1.1", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "scripts": {"start": "set NODE_OPTIONS=--openssl-legacy-provider && concurrently \"react-scripts start\" \"cd ../2.DummyAPI && npm run start-dev\"", "build": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts build || exit 0", "test": "react-scripts test", "eject": "react-scripts eject"}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"concurrently": "^7.6.0", "eslint": "^7.11.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0"}, "resolutions": {"react-error-overlay": "6.0.9"}}