﻿using System;

namespace RnD.BackEnd.Domain.Entities.DataEntry
{
    public class DataEntryEntity
    {
        public string Id { get; set; }
        public string ModelId { get; set; }
        public string ModelName { get; set; }
        public string FunctionalName { get; set; }
        public string TechnicalName { get; set; }
        public int DataLength { get; set; }
        public DateTime Created { get; set; }
        public string CreatedBy { get; set; }
        public string CreatedByName { get; set; }
        public DateTime Modified { get; set; }
        public string ModifiedBy { get; set; }
        public string ModifiedByName { get; set; }
    }
}
