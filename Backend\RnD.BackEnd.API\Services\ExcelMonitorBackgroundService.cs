using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using System.Linq;
using RnD.BackEnd.API.Models;
using RnD.BackEnd.API.Data;
using Microsoft.EntityFrameworkCore;

namespace RnD.BackEnd.API.Services
{
    public class ExcelMonitorBackgroundService : BackgroundService
    {
        private readonly ILogger<ExcelMonitorBackgroundService> _logger;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly IConfiguration _configuration;
        private DateTime _lastCheckTime = DateTime.MinValue;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(5); //Verify a cada 5 min

        public ExcelMonitorBackgroundService(
            ILogger<ExcelMonitorBackgroundService> logger,
            IServiceScopeFactory scopeFactory,
            IConfiguration configuration)
        {
            _logger = logger;
            _scopeFactory = scopeFactory;
            _configuration = configuration;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Iniciando serviço de monitoramento de arquivos Excel");
            
            // Criar um scope para registar o log de inicialização
            using (var scope = _scopeFactory.CreateScope())
            {
                var logService = scope.ServiceProvider.GetRequiredService<LogProcessamentoService>();
                await logService.RegistrarLog(0, "Serviço de monitoramento de arquivos Excel iniciado", TipoLog.Info);
            }

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CheckForNewExcelFiles();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro ao verificar novos arquivos Excel");
                    
                    // Criar um scope para registar o log de erro
                    using (var scope = _scopeFactory.CreateScope())
                    {
                        var logService = scope.ServiceProvider.GetRequiredService<LogProcessamentoService>();
                        await logService.RegistrarLog(0, $"Erro ao verificar novos arquivos Excel: {ex.Message}", TipoLog.Erro);
                    }
                }

                // Aguarda o intervalo de verificação
                await Task.Delay(_checkInterval, stoppingToken);
            }
        }

        private async Task CheckForNewExcelFiles()
        {
            _logger.LogInformation("Verificando novos arquivos Excel no Azure Storage");

            string connectionString = _configuration["AzureStorage:ConnectionString"];
            string containerName = _configuration["AzureStorage:ContainerName"];

            var blobContainerClient = new BlobContainerClient(connectionString, containerName);
            await blobContainerClient.CreateIfNotExistsAsync();

            // Busca todos os blobs modificados após a última verificação
            var blobs = blobContainerClient.GetBlobs()
                .Where(b => b.Properties.LastModified > _lastCheckTime)
                .OrderByDescending(b => b.Properties.LastModified)
                .ToList();

            if (blobs.Any())
            {
                _logger.LogInformation($"Encontrados {blobs.Count} novos arquivos Excel");

                using (var scope = _scopeFactory.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                    var logService = scope.ServiceProvider.GetRequiredService<LogProcessamentoService>();

                    foreach (var blob in blobs)
                    {
                        bool arquivoJaExiste = await dbContext.ArquivosProcessados.AnyAsync(a => a.Nome == blob.Name);

                        if (arquivoJaExiste)
                        {
                            await logService.RegistrarLog(0, $"Arquivo {blob.Name} já foi processado anteriormente. Ignorando.", TipoLog.Aviso);
                            continue;
                        }

                        var excelService = scope.ServiceProvider.GetRequiredService<ExcelAzureReaderService>();
                        await excelService.ImportarConsumosFromAzureExcelAsync();
                    }
                }
            }
            else
            {
                _logger.LogInformation("Nenhum novo arquivo Excel encontrado");
            }
        }
    }
}