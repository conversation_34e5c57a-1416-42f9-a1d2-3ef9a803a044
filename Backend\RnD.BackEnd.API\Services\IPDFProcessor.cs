using RnD.BackEnd.API.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RnD.BackEnd.API.Services
{
    public interface IPDFProcessor
    {
        /// <summary>
        /// Determina se este processador pode processar o tipo de PDF fornecido
        /// </summary>
        /// <param name="textoCompleto">Texto extraído do PDF</param>
        /// <returns>True se pode processar, False caso contrário</returns>
        bool PodeProcessar(string textoCompleto);

        /// <summary>
        /// Processa o PDF e extrai os dados de consumo
        /// </summary>
        /// <param name="textoCompleto">Texto extraído do PDF</param>
        /// <param name="fileId">ID do arquivo para logging</param>
        /// <returns>Lista de consumos extraídos</returns>
        Task<List<ConsumoPDFModel>> ProcessarPDF(string textoCompleto, int fileId);

        /// <summary>
        /// Retorna o tipo/identificador do processador
        /// </summary>
        string TipoProcessador { get; }
    }
}