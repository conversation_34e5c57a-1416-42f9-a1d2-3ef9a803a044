﻿param(
   [string][parameter(Mandatory = $true)] $resourceGroupName,
   [string][parameter(Mandatory = $true)] $appInsightsName,
   [string][parameter(Mandatory = $true)] $vaultName
)

function Set-Secret()
{
    [CmdletBinding()]
    param
    (
        [string][parameter(Mandatory = $true)]$secretName,
        [string][parameter(Mandatory = $true)]$value,
        [string][parameter(Mandatory = $true)]$vaultName
    )
    BEGIN
    {
        Write-Host -ForegroundColor Green "Updating KeyVault: $secretName ...STARTING..."

        $secretValue = ConvertTo-SecureString $value -AsPlainText -Force

        Set-AzKeyVaultSecret -VaultName $vaultName -Name $secretName -SecretValue $secretValue
        Write-Host "Setting ApplicationInsights-ApiKey written to $secretName"

        Write-Host -ForegroundColor Green "Updating KeyVault: $secretName ...FINISHED!"
    }
}

try {
    $output = New-AzApplicationInsightsApiKey -ResourceGroupName $resourceGroupName -Name $appInsightsName -Permissions ReadTelemetry -Description "Read Telemetry"

    Set-Secret -secretName "ApplicationInsights-ApiKey" -value $output.ApiKey -vaultName $vaultName
}
catch {
    Write-Host $_.Exception.Message
}

