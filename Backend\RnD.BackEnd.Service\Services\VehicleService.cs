﻿using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.JsonPatch;
using Polly;
using RnD.BackEnd.Domain.BusinessMessages;
using RnD.BackEnd.Domain.Constants;
using RnD.BackEnd.Domain.DataModels;
using RnD.BackEnd.Domain.Entities.Exception;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Entities.Vehicle;
using RnD.BackEnd.Domain.Extensions;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos;
using RnD.BackEnd.Domain.Interfaces.Services;
using RnD.BackEnd.Domain.Validators.Vehicle;

namespace RnD.BackEnd.Service.Services
{
    /// <summary>
    /// Vehicles service class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Services.IVehiclesService" />
    public class VehicleService : IVehiclesService
    {
        private readonly IVehiclesRepository _repository;
        private readonly IAsyncPolicy<ServiceOutput<Vehicle>> _cosmosEtagRetryPolicy;

        /// <summary>
        /// Initializes a new instance of the <see cref="VehicleService" /> class.
        /// </summary>
        /// <param name="repository">The repository.</param>
        /// <param name="cosmosEtagRetryPolicy">The cosmos etag retry policy.</param>
        public VehicleService(IVehiclesRepository repository, IAsyncPolicy<ServiceOutput<Vehicle>> cosmosEtagRetryPolicy)
        {
            _repository = repository;
            _cosmosEtagRetryPolicy = cosmosEtagRetryPolicy;
        }

        /// <summary>
        /// Adds the vehicle.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The vehicle added.
        /// </returns>
        public async Task<ServiceOutput<Vehicle>> AddAsync(Vehicle request)
        {
            ServiceOutput<Vehicle> output = new ServiceOutput<Vehicle>();

            ModelException validationErrors = VehicleValidations.IsCreateRequestValid(request);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                ResponseModel<Vehicle> response = await _repository.AddAsync(request);

                if (response.Code != HttpStatusCode.Created)
                {
                    output.CustomErrorCode(response.Code, VehicleMessages.Error_Create);
                }
                else
                {
                    output.Created(response.Value);
                }
            }

            return output;
        }

        /// <summary>
        /// Adds the specified vehicles.
        /// </summary>
        /// <param name="vehicles">The vehicles.</param>
        /// <returns>
        /// The vehicles added.
        /// </returns>
        public async Task<ServiceOutput<List<Vehicle>>> AddAsync(List<Vehicle> vehicles)
        {
            ServiceOutput<List<Vehicle>> output = new ServiceOutput<List<Vehicle>>();

            ModelException validationErrors;
            foreach (Vehicle vehicle in vehicles)
            {
                validationErrors = VehicleValidations.IsCreateRequestValid(vehicle);

                if (validationErrors.HasMessages)
                {
                    output.BadRequest(validationErrors);
                    return output;
                }
            }

            ResponseModel<List<Vehicle>> response = await _repository.AddAsync(vehicles);

            if (response.Code != HttpStatusCode.OK)
            {
                output.CustomErrorCode(response.Code, VehicleMessages.Error_Create);
                output.ExceptionMessages.Messages.AddRange(response.ErrorMessages);
            }
            else
            {
                output.Value = response.Value;
            }

            return output;
        }

        /// <summary>
        /// Updates the vehicle (replace)
        /// </summary>
        /// <param name="request">The request</param>
        /// <param name="id">The object id</param>
        /// <param name="etag">The object etag</param>
        /// <returns>
        /// The vehicle updated.
        /// </returns>
        public async Task<ServiceOutput<Vehicle>> UpdateAsync(Vehicle request, string id, string etag)
        {
            ServiceOutput<Vehicle> output = new ServiceOutput<Vehicle>();

            ModelException validationErrors = VehicleValidations.IsUpdateRequestValid(request, id);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                ResponseModel<Vehicle> response = await _repository.UpdateAsync(request, etag);

                output = response.Code switch
                {
                    HttpStatusCode.OK => output.Ok(response.Value),
                    HttpStatusCode.PreconditionFailed => output.Conflict(VehicleMessages.ETag_Mismatch),
                    _ => output.CustomErrorCode(response.Code, VehicleMessages.Error_Update)
                };
            }

            return output;
        }

        /// <summary>
        /// Updates the vehicle (discrete properties)
        /// </summary>
        /// <param name="changes">The changes</param>
        /// <param name="id">The id of the vehicle to be updated</param>
        /// <returns>
        /// The vehicle updated.
        /// </returns>
        public async Task<ServiceOutput<Vehicle>> UpdateAsync(JsonPatchDocument<Vehicle> changes, string id)
        {
            ServiceOutput<Vehicle> output = new ServiceOutput<Vehicle>();

            return await _cosmosEtagRetryPolicy.ExecuteAsync(async () =>
            {
                ResponseModel<Vehicle> response = await _repository.GetAsync(id);
                if (response.Code != HttpStatusCode.OK)
                {
                    output.CustomErrorCode(response.Code, VehicleMessages.Error_Get);
                }
                else
                {
                    changes.ApplyTo(response.Value);
                    response = await _repository.UpdateAsync(response.Value, response.Etag);

                    output = response.Code switch
                    {
                        HttpStatusCode.OK => output.Ok(response.Value),
                        HttpStatusCode.PreconditionFailed => output.Conflict(VehicleMessages.ETag_Mismatch),
                        _ => output.CustomErrorCode(response.Code, VehicleMessages.Error_Update)
                    };
                }
                return output;
            });
        }

        /// <summary>
        /// Deletes the specified vehicle by id.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// True if operation succeeded, False otherwise.
        /// </returns>
        public async Task<ServiceOutput<object>> DeleteAsync(string id)
        {
            ServiceOutput<object> output = new ServiceOutput<object>();

            ModelException validationErrors = VehicleValidations.IsIdValid(id);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                ResponseModel<bool> response = await _repository.DeleteAsync(id);

                if (response.Code != HttpStatusCode.NoContent)
                {
                    output.CustomErrorCode(response.Code, VehicleMessages.Error_Delete);
                }
                else
                {
                    output.NoContent();
                }
            }

            return output;
        }

        /// <summary>
        /// Gets the by identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The vehicle.
        /// </returns>
        public async Task<ServiceOutput<Vehicle>> GetByIdAsync(string id)
        {
            ServiceOutput<Vehicle> output = new ServiceOutput<Vehicle>();

            ModelException validationErrors = VehicleValidations.IsIdValid(id);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                ResponseModel<Vehicle> response = await _repository.GetAsync(id);

                if (response.Code == HttpStatusCode.NotFound)
                {
                    output.NotFound(VehicleMessages.Not_Found);
                }
                else if (response.Code == HttpStatusCode.OK)
                {
                    output.Value = response.Value;
                    output.Properties.Add(SystemConstants.ETAG, response.Etag);
                }
                else
                {
                    output.CustomErrorCode(response.Code, string.Format(VehicleMessages.Error_Get, id));
                }
            }

            return output;
        }

        /// <summary>
        /// Get the list of vehicles.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="brandId">The brand identifier.</param>
        /// <param name="modelName">Name of the model.</param>
        /// <param name="fuelType">Type of the fuel.</param>
        /// <param name="version">The version.</param>
        /// <param name="year">The year.</param>
        /// <param name="pageNumber">The page number.</param>
        /// <param name="maxItems">The maximum items.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// The list of vehicles.
        /// </returns>
        public async Task<ServiceOutput<List<Vehicle>>> ListAsync(
            List<string> id,
            List<string> brandId,
            List<string> modelName,
            List<string> fuelType,
            List<string> version,
            List<int> year,
            int? pageNumber,
            int? maxItems,
            string sortField,
            bool sortAscending,
            string continuationToken,
            CancellationToken cancellationToken)
        {
            ServiceOutput<List<Vehicle>> output = new ServiceOutput<List<Vehicle>>();

            ResponseModel<List<Vehicle>> response = await _repository.ListAsync(
                id,
                brandId,
                modelName,
                fuelType,
                version,
                year,
                pageNumber,
                maxItems,
                sortField,
                sortAscending,
                continuationToken,
                cancellationToken);

            if (response.Code != HttpStatusCode.OK)
            {
                output.CustomErrorCode(response.Code, VehicleMessages.Error_List);
            }
            else
            {
                output.Value = response.Value;
                if (response.ContinuationToken != null)
                {
                    output.Properties.Add(SystemConstants.CONTINUATION_TOKEN, response.ContinuationToken);
                }
            }

            return output;
        }

        /// <summary>
        /// Adds or updates the vehicle.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The vehicle added or updated.
        /// </returns>
        public async Task<ServiceOutput<Vehicle>> AddOrUpdateAsync(Vehicle request)
        {
            ServiceOutput<Vehicle> output = new ServiceOutput<Vehicle>();

            ModelException validationErrors = VehicleValidations.IsCreateRequestValid(request);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                ResponseModel<Vehicle> response = await _repository.AddOrUpdateAsync(request);

                if (response.Code != HttpStatusCode.OK)
                {
                    output.CustomErrorCode(response.Code, VehicleMessages.Error_Create);
                }
                else
                {
                    output.Value = response.Value;
                }
            }

            return output;
        }
    }
}
