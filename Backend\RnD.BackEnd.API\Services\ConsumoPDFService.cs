using Azure.Storage.Blobs;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;

using UglyToad.PdfPig;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace RnD.BackEnd.API.Services
{
    public class ConsumoPDFService : IConsumoPDFService
    {
        private readonly ApplicationDbContext _context;
        private readonly BlobServiceClient _blobServiceClient;
        private readonly ILogger<ConsumoPDFService> _logger;
        private readonly LogProcessamentoService _logService;
        private readonly string _containerName;
        private readonly IEnumerable<IPDFProcessor> _pdfProcessors;
        private readonly CrayonTestExtractorService _crayonExtractor;

        public ConsumoPDFService(ApplicationDbContext context, BlobServiceClient blobServiceClient, ILogger<ConsumoPDFService> logger, LogProcessamentoService logService, IConfiguration configuration, IEnumerable<IPDFProcessor> pdfProcessors, CrayonTestExtractorService crayonExtractor)
        {
            _context = context;
            _blobServiceClient = blobServiceClient;
            _logger = logger;
            _logService = logService;
            _containerName = configuration["AzureStoragePDF:ContainerName"] ?? "consumo-pdf";
            _pdfProcessors = pdfProcessors;
            _crayonExtractor = crayonExtractor;
        }



        private IPDFProcessor EncontrarProcessadorApropriado(string textoCompleto)
        {
            foreach (var processor in _pdfProcessors)
            {
                if (processor.PodeProcessar(textoCompleto))
                {
                    _logger.LogInformation($"Processador encontrado: {processor.TipoProcessador}");
                    return processor;
                }
            }

            _logger.LogWarning("Nenhum processador apropriado encontrado para o PDF");
            return null;
        }

        public async Task<List<ConsumoPDFModel>> ProcessarPDF(MemoryStream memoryStream, int fileId)
        {
            try
            {
                string textoCompleto = ExtractTextFromPDF(memoryStream);

                if (string.IsNullOrEmpty(textoCompleto))
                {
                    await _logService.RegistrarLog(fileId, "Não foi possível extrair texto do PDF", TipoLog.Erro);
                    throw new InvalidOperationException("Não foi possível extrair texto do PDF");
                }

                await _logService.RegistrarLog(fileId, "Texto extraído com sucesso do PDF", TipoLog.Info);

                // Determinar o tipo de PDF e processar adequadamente
                var processador = EncontrarProcessadorApropriado(textoCompleto);
                if (processador == null)
                {
                    await _logService.RegistrarLog(fileId, "Tipo de PDF não reconhecido", TipoLog.Erro);
                    throw new InvalidOperationException("Tipo de PDF não reconhecido");
                }

                await _logService.RegistrarLog(fileId, $"PDF identificado como tipo: {processador.GetType().Name}", TipoLog.Info);

                // Usar o processador específico
                return await processador.ProcessarPDF(textoCompleto, fileId);
            }
            catch (Exception ex)
            {
                await _logService.RegistrarLog(fileId, $"Erro ao processar PDF: {ex.Message}", TipoLog.Erro);
                throw;
            }
        }

        private string ExtractTextFromPDF(MemoryStream memoryStream)
        {
            using (var pdfDocument = PdfDocument.Open(memoryStream))
            {
                var text = string.Empty;
                foreach (var page in pdfDocument.GetPages())
                {
                    text += string.Join(" ", page.GetWords().Select(w => w.Text)) + " ";
                }
                return text;
            }
        }

        public async Task<List<ConsumoPDFModel>> ProcessPDFFile(string fileName, bool forceReprocessing = false)
        {
            var consumos = new List<ConsumoPDFModel>();
            int sucessos = 0;
            int erros = 0;

            // Verificar se o arquivo existe no Azure Blob Storage
            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            var blobClient = containerClient.GetBlobClient(fileName);

            if (!await blobClient.ExistsAsync())
            {
                throw new FileNotFoundException($"Arquivo {fileName} não encontrado no Azure Blob Storage");
            }

            // Verificar se o arquivo já foi processado
            var arquivoJaProcessado = await _context.ArquivosProcessados
                .FirstOrDefaultAsync(a => a.Nome == fileName);

            if (arquivoJaProcessado != null && arquivoJaProcessado.Processado && !forceReprocessing)
            {
                _logger.LogInformation($"Arquivo {fileName} já foi processado anteriormente");
                return consumos;
            }

            // Registrar o arquivo como sendo processado
            if (arquivoJaProcessado == null)
            {
                arquivoJaProcessado = new ArquivoProcessado
                {
                    Nome = fileName,
                    BlobUrl = blobClient.Uri.ToString(),
                    DataUpload = DateTime.Now,
                    Processado = false,
                    RegistrosProcessados = 0,
                    RegistrosComErro = 0
                };
                _context.ArquivosProcessados.Add(arquivoJaProcessado);
                await _context.SaveChangesAsync();
            }

            var fileId = arquivoJaProcessado.Id;
            await _logService.RegistrarLog(fileId, $"Iniciando processamento do arquivo {fileName}", TipoLog.Info);

            try
            {
                // Baixar o arquivo do Azure Blob Storage
                using var memoryStream = new MemoryStream();
                await blobClient.DownloadToAsync(memoryStream);
                memoryStream.Position = 0;

                // Processar PDF usando o novo padrão de processadores
                memoryStream.Position = 0;
                consumos = await ProcessarPDF(memoryStream, fileId);

                sucessos = consumos.Count;

                // Salvar os consumos extraídos no banco de dados
                if (consumos.Any())
                {
                    _logger.LogInformation($"Salvando {consumos.Count} consumos no banco de dados");

                    foreach (var consumoPDF in consumos)
                    {
                        // Converter ConsumoPDFModel para Consumo
                        var consumo = new Consumo
                        {
                            ClienteID = consumoPDF.ClienteID,
                            DataInicio = consumoPDF.DataInicio,
                            DataFim = consumoPDF.DataFim,
                            CountryCustomerTotal = consumoPDF.Total, // Usando o valor total como CountryCustomerTotal
                            TipoServico = consumoPDF.Codigo,
                            Regiao = "Portugal", // Valor padrão
                            Moeda = "EUR" // Valor padrão
                        };

                        _logger.LogInformation($"Adicionando consumo: ClienteID={consumo.ClienteID}, DataInicio={consumo.DataInicio:dd/MM/yyyy}, DataFim={consumo.DataFim:dd/MM/yyyy}, Total={consumo.CountryCustomerTotal:N2}, TipoServico={consumo.TipoServico}");

                        // Adicionar o consumo ao contexto
                        _context.Consumo.Add(consumo);

                        // Salvar para obter o ID gerado
                        await _context.SaveChangesAsync();

                        // Criar a relação Consumo_Cliente
                        var consumoCliente = new Consumo_Cliente
                        {
                            ClienteID = consumo.ClienteID,
                            ConsumoID = consumo.ID
                        };

                        _logger.LogInformation($"Adicionando relação Consumo_Cliente: ClienteID={consumoCliente.ClienteID}, ConsumoID={consumoCliente.ConsumoID}");

                        // Adicionar a relação ao contexto
                        _context.Consumo_Cliente.Add(consumoCliente);

                        // Adicionar registro na tabela ConsumoPDF
                        try
                        {
                            var consumoPDFEntity = new ConsumoPDF
                            {
                                ClienteID = consumoPDF.ClienteID,
                                DataInicio = consumoPDF.DataInicio,
                                DataFim = consumoPDF.DataFim,
                                Total = consumoPDF.Total,
                                Iva = consumoPDF.Iva,
                                TotalFatura = consumoPDF.TotalFatura,
                                Codigo = consumoPDF.Codigo,
                                Identificador = consumoPDF.Identificador
                            };

                            _logger.LogInformation($"Adicionando registro na tabela ConsumoPDF: ClienteID={consumoPDFEntity.ClienteID}, Total={consumoPDFEntity.Total:N2}, Identificador={consumoPDFEntity.Identificador}");

                            // Adicionar o consumoPDF ao contexto
                            _context.ConsumoPDF.Add(consumoPDFEntity);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"Erro ao adicionar registro na tabela ConsumoPDF: {ex.Message}");
                            await _logService.RegistrarLog(fileId, $"Erro ao adicionar registro na tabela ConsumoPDF: {ex.Message}", TipoLog.Erro);
                        }
                    }

                    // Salvar todas as alterações
                    await _context.SaveChangesAsync();
                    _logger.LogInformation($"Consumos salvos com sucesso no banco de dados");
                }

                // Atualiza o status do arquivo processado
                arquivoJaProcessado.Processado = true;
                arquivoJaProcessado.RegistrosProcessados = sucessos;
                arquivoJaProcessado.RegistrosComErro = erros;
                await _context.SaveChangesAsync();

                await _logService.RegistrarLog(fileId, $"Processamento concluído. Total: {sucessos} registros processados, {erros} com erro", TipoLog.Info);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Erro ao processar arquivo {fileName}: {ex.Message}");
                await _logService.RegistrarLog(fileId, $"Erro crítico no processamento: {ex.Message}", TipoLog.Erro);

                arquivoJaProcessado.Processado = true;
                arquivoJaProcessado.RegistrosComErro = 1;
                await _context.SaveChangesAsync();

                throw;
            }

            return consumos;
        }

        public async Task<string> UploadPDFFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("Arquivo não fornecido");

            if (!file.ContentType.Equals("application/pdf", StringComparison.OrdinalIgnoreCase))
                throw new ArgumentException("O arquivo deve ser um PDF");

            var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
            await containerClient.CreateIfNotExistsAsync();

            // Gera um nome único para o arquivo
            string fileName = $"{Guid.NewGuid()}-{file.FileName}";
            var blobClient = containerClient.GetBlobClient(fileName);

            using (var stream = file.OpenReadStream())
            {
                await blobClient.UploadAsync(stream, true);
            }

            return fileName;
        }

        /// <summary>
        /// Obtém o texto bruto de todos os PDFs Crayon no container Azure
        /// </summary>
        public async Task<Dictionary<string, string>> GetCrayonPDFsRawText()
        {
            return await _crayonExtractor.GetCrayonPDFsRawText();
        }

        /// <summary>
        /// Obtém os dados extraídos de todos os PDFs Crayon no container Azure
        /// </summary>
        public async Task<Dictionary<string, CrayonPDFExtractionResult>> GetCrayonPDFsExtractedData()
        {
            return await _crayonExtractor.GetCrayonPDFsExtractedData();
        }
    }
}