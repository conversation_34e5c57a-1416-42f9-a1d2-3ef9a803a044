﻿using AutoMapper;
using RnD.BackEnd.API.Model.Email;
using RnD.BackEnd.Domain.Entities.Email;

namespace RnD.BackEnd.API.MappingProfiles
{
    /// <summary>
    /// Email mapping profile
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class EmailProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="EmailProfile"/> class.
        /// </summary>
        public EmailProfile()
        {
            CreateMap(typeof(EmailMessageDto<>), typeof(EmailMessage<>));
            CreateMap(typeof(EmailAttachmentsDto<>), typeof(EmailAttachments<>));

            CreateMap<CodeAcademyTemplateDto, CodeAcademyTemplate>();
            CreateMap<EmailDto, BaseEmail>();
        }
    }
}
