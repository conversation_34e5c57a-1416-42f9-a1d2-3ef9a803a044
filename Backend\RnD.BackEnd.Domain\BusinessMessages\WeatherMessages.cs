﻿namespace RnD.BackEnd.Domain.BusinessMessages
{
    public static class WeatherMessages
    {
        public const string Error_Get = "Error getting weather with id {0}.";
        public const string Error_Delete = "Error deleting weather.";
        public const string Error_Update = "Error updating weather.";
        public const string Error_Create = "Error creating weather.";
        public const string Error_List = "Error getting list of weathers.";
        public const string Not_Found = "Weather not found";
        public const string Id_Mandatory = "Id is mandatory";
        public const string Id_Mismatch = "Id must match";
        public const string Rowversion_Mismatch = "The weather has been modified outside this context";
        public const string Location_Mandatory = "Location is mandatory";
    }
}
