﻿using System;
using Hangfire;
using Hangfire.Console;
using Hangfire.Console.Extensions;
using Hangfire.SqlServer;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.AzureAD.UI;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Managers;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.UnitsOfWork;
using RnD.BackEnd.Domain.Interfaces.Services;
using RnD.BackEnd.Domain.Settings;
using RnD.BackEnd.Email.Interfaces;
using RnD.BackEnd.Email.Managers;
using RnD.BackEnd.Infrastructure.Managers;
using RnD.BackEnd.Infrastructure.Repositories.Database;
using RnD.BackEnd.Infrastructure.UnitsOfWork;
using RnD.BackEnd.Jobs.LicenseValidation;
using RnD.BackEnd.Licensing.Extensions;
using RnD.BackEnd.Service.Services;

namespace RnD.BackEnd.Jobs.Extensions
{
    /// <summary>
    /// Service collection extensions class
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Configures the data access layer.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <param name="appSettings">The application settings.</param>
        /// <returns>
        /// The service collection.
        /// </returns>
        /// <exception cref="System.Exception">No appsettings section has been found
        /// or
        /// No valid settings.</exception>
        public static IServiceCollection ConfigureInfrastructure(this IServiceCollection services, AppSettings appSettings)
        {
            // Register Database unit of work
            services.AddTransient<IDatabaseUnitOfWork, DatabaseUnitOfWork>();

            // Register SQL Repositories
            services.AddScoped<IWeatherRepository, WeatherRepository>();

            // Register Cache manager
            services.AddSingleton<ICacheManager, MemoryCacheManager>();

            // Register API manager
            services.AddSingleton<IApiConnectorManager, ApiConnectorManager>();

            // Configure email manager
            EmailSettings emailSettings = appSettings.EmailSettings;
            services.AddTransient<IEmailManager>(eml => new SendGridManager(emailSettings.ApiKey, emailSettings.AllowSendingEmails, emailSettings.SendAlwaysToAddresses));

            return services;
        }

        /// <summary>
        /// Configures the business services.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <returns></returns>
        /// <exception cref="System.Exception">No appsettings section has been found
        /// or
        /// No valid settings.
        /// or
        /// No ConnectionStrings section has been found</exception>
        public static IServiceCollection ConfigureBusinessServices(this IServiceCollection services)
        {
            // Configure business services
            services.AddTransient<IJobsService, JobsService>();
            services.AddTransient<IWorkflowService, WorkflowService>();
            services.AddTransient<IEmailService, EmailService>();

            return services;
        }

        /// <summary>
        /// Configures the Hangfire Server.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <param name="configuration">The configuration.</param>
        /// <param name="connectionStrings">The connection strings.</param>
        /// <param name="policyName">Name of the policy.</param>
        /// <returns>
        /// The service collection.
        /// </returns>
        public static IServiceCollection ConfigureHangfire(this IServiceCollection services, IConfiguration configuration, ConnectionStrings connectionStrings, string policyName)
        {
            // Add Authentication
            services.AddAuthentication(AzureADDefaults.AuthenticationScheme).AddAzureAD(options => configuration.Bind("AzureAd", options));

            services.Configure<OpenIdConnectOptions>(AzureADDefaults.OpenIdScheme, options =>
            {
                options.Authority = options.Authority + "/v2.0/"; // Microsoft identity platform
                options.TokenValidationParameters.ValidateIssuer = false;
            });

            // Add a new policy for hangfire
            services.AddAuthorization(options =>
            {
                // Policy to be applied to hangfire endpoint
                options.AddPolicy(policyName, builder =>
                {
                    builder
                        .AddAuthenticationSchemes(AzureADDefaults.AuthenticationScheme)
                        .RequireAuthenticatedUser();
                });
            });

            // Add Hangfire services.
            services.AddHangfire(cfg => cfg
                .UseSimpleAssemblyNameTypeSerializer()
                .UseRecommendedSerializerSettings()
                .UseSqlServerStorage(connectionStrings.DatabaseCS, new SqlServerStorageOptions
                {
                    CommandBatchMaxTimeout = TimeSpan.FromMinutes(5),
                    SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
                    QueuePollInterval = TimeSpan.Zero,
                    UseRecommendedIsolationLevel = true,
                    DisableGlobalLocks = true
                })
                .UseConsole());

            services.AddHangfireConsoleExtensions();
            services.AddHangfireServer();

            return services;
        }

        /// <summary>
        /// Configures the license validator.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <returns>
        /// The service collection.
        /// </returns>
        public static IServiceCollection ConfigureJobsLicenseValidation(this IServiceCollection services)
        {
            services.ConfigureLicenseValidation();

            services.AddTransient<IJobRunner, JobRunner>();

            return services;
        }
    }
}
