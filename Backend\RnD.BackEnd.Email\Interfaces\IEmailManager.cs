﻿using System.Threading.Tasks;
using RnD.BackEnd.Email.Entities;

namespace RnD.BackEnd.Email.Interfaces
{
    /// <summary>
    /// Email manager interface
    /// </summary>
    public interface IEmailManager
    {
        /// <summary>
        /// Sends the email.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// Send e-mail response, with Accepted HTTP status code in case of success.
        /// </returns>
        Task<SendEmailResponse> SendEmailAsync(EmailMessage request);
    }
}
