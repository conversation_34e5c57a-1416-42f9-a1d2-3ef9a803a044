﻿function addOrUpdateVehicle(vehicle) {
    let container = getContext().getCollection();
    let link = container.getSelfLink();
    let response = getContext().getResponse();

    let query = {
        query: "SELECT * FROM c WHERE c.id = @id",
        parameters: [{ name: "@id", value: vehicle.id }]
    };
    let queryOptions = { disableAutomaticIdGeneration: true };

    let queryAccepted = container.queryDocuments(
        link,
        query,
        (err, items, options) => {
            if (err) {
                throw err;
            }

            let foundVehicle = items[0];
            if (items && items.length >= 1) {

                let updateAccepted = container.replaceDocument(
                    foundVehicle._self,
                    vehicle,
                    (updateErr, item, updateOptions) => {
                        if (updateErr) {
                            throw updateErr;
                        }
                        response.setBody(item);
                    }
                );

                if (!updateAccepted) {
                    throw new Error("Update request was declined.");
                }
            }
            else {
                let createAccepted = container.createDocument(
                    link,
                    vehicle,
                    queryOptions,
                    (createErr, item, createOptions) => {
                        if (createErr) {
                            throw createErr;
                        }

                        response.setBody(item);
                    }
                );

                if (!createAccepted) {
                    throw new Error("Create request was declined.");
                }
            }
        }
    );

    if (!queryAccepted) {
        throw new Error("Query request was declined.");
    }
}