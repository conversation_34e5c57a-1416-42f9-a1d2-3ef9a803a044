import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { LinearProgress, Container } from "@mui/material";
import ClientTable from "./ClientTable";
import { filtersAPI } from "../../API/filtersAPI";
import useFilters from "../../Context/Hooks/useFilters";
import { getClients } from "../../Services/clientServices";
import { useTranslation } from "react-i18next";

const ClientListCost = () => {
      const { t } = useTranslation();
    useEffect(() => {
        document.title = `${t('titles:clientsCost')} | Portal Cloud Services`;
    }, []);

    const [clientList, setClientList] = useState([]);
    const [isLoading, setLoading] = useState(true);

    //--Filters--
    const [customFiltersList, setCustomFiltersList] = useState([]);
    const { filters } = useFilters();
    const [setSelectedCustomFilters] = useState([]);

    const getAvailableFilters = useCallback(async () => {
        try {
            const _customFiltersList = await filtersAPI.getAvailableFilters({
                context: "CLIENT",
            });

            if (_customFiltersList && !_customFiltersList.error) {
                setCustomFiltersList(_customFiltersList);
            }
        } catch (err) {
            console.error(err);
        }
    }, []);

    const getClientList = useCallback(async () => {
        setLoading(true);
        const client = await getClients();
        setLoading(false);

        if (client.length > 0) {
            setClientList(client);
        } else {
            toast.error("Erro ao procurar clientes");
        }
    }, []);

    useEffect(() => {
        getAvailableFilters();
    }, [getAvailableFilters]);

    useEffect(() => {
        getClientList();
    }, [filters, getClientList]);

    return (
        <Container maxWidth="lg">
            {isLoading && (
                <LinearProgress
                    variant="indeterminate"
                    sx={{ width: "100%", position: "absolute", top: "64px", left: 0 }}
                />
            )}
            <ClientTable
                clientList={clientList || []}
                isLoading={isLoading}
                customFiltersList={customFiltersList}
                onSelectedCustomFilters={setSelectedCustomFilters}
            />
        </Container>
    );
};

export default ClientListCost;
