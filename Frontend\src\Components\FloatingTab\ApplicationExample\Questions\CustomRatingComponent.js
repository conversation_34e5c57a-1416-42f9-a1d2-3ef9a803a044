import { styled } from '@mui/system';
import { Rating } from '@mui/material';
import { Star as StarIcon } from '@mui/icons-material';
import PropTypes from 'prop-types';

const CustomRating = styled(Rating)(({ theme }) => ({
  iconEmpty: {
    color: theme.palette.text.primary
  },
  iconFilled: {
    color: theme.palette.primary.main,
  },
  iconHover: {
    color: theme.palette.primary.light
  }
}));

const CustomRatingError = styled(Rating)(({ theme }) => ({
  iconEmpty: {
    color: theme.palette.error.light
  },
  iconFilled: {
    color: theme.palette.primary.light,
  },
  iconHover: {
    color: theme.palette.primary.light
  }
}));

const CustomRatingComponent = (props) => {
  const {
    errors,
    key,
    value,
    max,
    precision = false,
    onChange,
  } = props;

  const precisionProps = precision ? { precision: precision } : {};

  return (<>
    {errors[key] ?
      <CustomRatingError
        key={key}
        value={value}
        max={max}
        {...precisionProps}
        emptyIcon={<StarIcon fontSize="inherit" />}
        onChange={onChange}
        icon={<StarIcon style={{ pointerEvents: "auto" }} />}
      />
      :
      <CustomRating
        key={key}
        value={value}
        max={max}
        {...precisionProps}
        emptyIcon={<StarIcon fontSize="inherit" />}
        onChange={onChange}
        icon={<StarIcon style={{ pointerEvents: "auto" }} />}
      />}
  </>);
}

CustomRatingComponent.propTypes = {
  errors: PropTypes.object,
  key: PropTypes.string,
  value: PropTypes.func,
  max: PropTypes.number,
  precision: PropTypes.func,
  onChange: PropTypes.func
};

export default CustomRatingComponent;
