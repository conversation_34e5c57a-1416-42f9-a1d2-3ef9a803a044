const getTableLoadCode = () => {
    return `
    import { Skeleton, TableRow, TableCell } from '@mui/material';
    import PropTypes from 'prop-types';
    import React, { useEffect, useState } from 'react';

    const TableRowLoading = (props) => {
    const { headCells, idx } = props;
    return (
        <TableRow key={idx}>
        {headCells.map((headCell) => (
            <TableCell
            key={headCell.id}
            >
            <Skeleton
                variant="text"
                animation="wave"
            />
            </TableCell>
        ))}
        </TableRow>
    );
    };

    TableRowLoading.propTypes = {
    headCells: PropTypes.array.isRequired,
    idx: PropTypes.string
    };

    const TableLoading = React.memo((props) => {
    const { isLoading, headCells, numRows } = props;
    const [rows, setRows] = useState([]);

    useEffect(() => {
        const newRows = [];
        if (isLoading) {
        for (let i = 0; i < numRows; i++) {
            newRows.push(
            <TableRowLoading
                headCells={headCells}
                idx={i.toString()}
                key={i.toString()}
            />
            );
        }
        }
        setRows(newRows);
    }, [isLoading, headCells, numRows]);

    return (
        isLoading && (
        rows.map((row, index) => (
            <React.Fragment key={index}>{row}</React.Fragment>
        ))
        )
     );
    });
    export default TableLoading;
    `;
}

export default getTableLoadCode;
