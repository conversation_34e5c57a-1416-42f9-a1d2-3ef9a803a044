import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Alert
} from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import TextField from "@mui/material/TextField";
import ptBR from "date-fns/locale/pt-BR";
import { useTranslation } from "react-i18next";
import { getConsumoByClienteId, getConsumoSummaryByCliente } from "../../../Services/consumoSubscricaoService";
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import SummarizeIcon from '@mui/icons-material/Summarize';
import GroupWorkIcon from '@mui/icons-material/GroupWork';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import DescriptionIcon from '@mui/icons-material/Description';

const AzureConsumption = ({ clientId }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [consumos, setConsumos] = useState([]);
  const [summary, setSummary] = useState(null);
  const [error, setError] = useState(null);
  const [dataInicio, setDataInicio] = useState(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1)); // Mês anterior
  const [dataFim, setDataFim] = useState(new Date());
  const [resourceGroups, setResourceGroups] = useState([]);
  const [selectedResourceGroup, setSelectedResourceGroup] = useState("todos");
  const [filteredConsumos, setFilteredConsumos] = useState([]);
  const [noData, setNoData] = useState(false);
  
  // Carregar dados de consumo
  useEffect(() => {
    const loadConsumoData = async () => {
      try {
        setLoading(true);
        setError(null);
        setNoData(false);
        
        // Carregar consumos do cliente
        try {
          const clienteConsumos = await getConsumoByClienteId(clientId);
          setConsumos(clienteConsumos);
          
          // Extrair resource groups únicos
          const groups = ["todos", ...new Set(clienteConsumos.map(c => c.resourceGroup))];
          setResourceGroups(groups);
          
          // Filtrar consumos por datas
          filterConsumos(clienteConsumos, "todos");
        } catch (consumoError) {
          console.error("Erro ao carregar consumos do cliente:", consumoError);
          if (consumoError.response && consumoError.response.status === 404) {
            // Se não há consumos, definimos um array vazio
            setConsumos([]);
            setResourceGroups(["todos"]);
            setFilteredConsumos([]);
          } else {
            throw consumoError; // Propaga o erro se não for 404
          }
        }
        
        // Carregar resumo
        try {
          const summaryData = await getConsumoSummaryByCliente(clientId, dataInicio, dataFim);
          setSummary(summaryData);
        } catch (summaryError) {
          console.error("Erro ao carregar resumo de consumo:", summaryError);
          if (summaryError.response && summaryError.response.status === 404) {
            // Se não há resumo para o período, apenas definimos como null
            setSummary(null);
            setNoData(true);
          } else {
            throw summaryError; // Propaga o erro se não for 404
          }
        }
      } catch (err) {
        console.error("Erro ao carregar dados de consumo Azure:", err);
        setError("Erro ao carregar dados de consumo Azure. Tente novamente mais tarde.");
      } finally {
        setLoading(false);
      }
    };
    
    if (clientId) {
      loadConsumoData();
    }
  }, [clientId, dataInicio, dataFim]);
  
  // Filtrar consumos por resource group e datas
  const filterConsumos = (allConsumos, resourceGroup) => {
    if (!allConsumos || allConsumos.length === 0) {
      setFilteredConsumos([]);
      return;
    }
    
    let filtered = allConsumos;
    
    // Filtrar por datas
    filtered = filtered.filter(c => 
      new Date(c.startDate) >= new Date(dataInicio) && 
      new Date(c.endDate) <= new Date(dataFim)
    );
    
    // Filtrar por resource group se não for "todos"
    if (resourceGroup !== "todos") {
      filtered = filtered.filter(c => c.resourceGroup === resourceGroup);
    }
    
    setFilteredConsumos(filtered);
  };
  
  // Atualizar filtros quando as datas mudam
  useEffect(() => {
    if (consumos.length > 0) {
      filterConsumos(consumos, selectedResourceGroup);
      
      // Recarregar o resumo com as novas datas
      const loadNewSummary = async () => {
        try {
          setLoading(true);
          setNoData(false);
          const summaryData = await getConsumoSummaryByCliente(clientId, dataInicio, dataFim);
          setSummary(summaryData);
        } catch (err) {
          console.error("Erro ao atualizar resumo de consumo:", err);
          if (err.response && err.response.status === 404) {
            setSummary(null);
            setNoData(true);
          }
        } finally {
          setLoading(false);
        }
      };
      
      loadNewSummary();
    }
  }, [dataInicio, dataFim, selectedResourceGroup, clientId]);
  
  // Manipular mudança de resource group
  const handleResourceGroupChange = (event) => {
    const value = event.target.value;
    setSelectedResourceGroup(value);
    filterConsumos(consumos, value);
  };
  
  // Formatar valor monetário
  const formatCurrency = (value, currency = "EUR") => {
    return new Intl.NumberFormat('pt-PT', {
      style: 'currency',
      currency: currency
    }).format(value);
  };
  
  // Formatar data
  const formatDate = (date) => {
    if (!date) return "N/A";
    return new Date(date).toLocaleDateString('pt-PT');
  };
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 4, minHeight: '300px' }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>Carregando dados de consumo...</Typography>
      </Box>
    );
  }
  
  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }
  
  return (
    <Box sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card elevation={2}>
            <CardHeader
              avatar={<FilterAltIcon color="primary" />}
              title="Filtros"
              sx={{ pb: 0 }}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
                    <DatePicker
                      label="Data Inicial"
                      value={dataInicio}
                      onChange={(newValue) => setDataInicio(newValue)}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </LocalizationProvider>
                </Grid>
                <Grid item xs={12} md={4}>
                  <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
                    <DatePicker
                      label="Data Final"
                      value={dataFim}
                      onChange={(newValue) => setDataFim(newValue)}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </LocalizationProvider>
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel id="resource-group-label">
                      Grupo de Recursos
                    </InputLabel>
                    <Select
                      labelId="resource-group-label"
                      value={selectedResourceGroup}
                      label="Grupo de Recursos"
                      onChange={handleResourceGroupChange}
                    >
                      {resourceGroups.map((group) => (
                        <MenuItem key={group} value={group}>
                          {group === "todos" ? "Todos" : group}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        
        {noData && (
          <Grid item xs={12}>
            <Alert severity="info" icon={<SummarizeIcon />} sx={{ mb: 2 }}>
              Não há dados de consumo Azure para este cliente no período selecionado.
            </Alert>
          </Grid>
        )}
        
        {summary && (
          <Grid item xs={12}>
            <Card elevation={2}>
              <CardHeader
                avatar={<SummarizeIcon color="primary" />}
                title="Resumo"
                sx={{ pb: 0 }}
              />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Paper elevation={1} sx={{ p: 2, height: '100%', borderRadius: '8px', backgroundColor: '#f8f9fa' }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Custo Total
                      </Typography>
                      <Typography variant="h4" color="primary">
                        {formatCurrency(summary.totalCost, summary.currency)}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Paper elevation={1} sx={{ p: 2, height: '100%', borderRadius: '8px', backgroundColor: '#f8f9fa' }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Custo Total (USD)
                      </Typography>
                      <Typography variant="h4" color="primary">
                        {formatCurrency(summary.totalCostUSD, 'USD')}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Paper elevation={1} sx={{ p: 2, height: '100%', borderRadius: '8px', backgroundColor: '#f8f9fa' }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Número de Recursos
                      </Typography>
                      <Typography variant="h4">
                        {summary.resourceCount}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
                
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Período
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(summary.startDate)} - {formatDate(summary.endDate)}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        )}
        
        {/* Resource Groups Summary */}
        {summary && summary.resourceGroups && summary.resourceGroups.length > 0 && (
          <Grid item xs={12} md={6}>
            <Card elevation={2} sx={{ height: '100%' }}>
              <CardHeader
                avatar={<GroupWorkIcon color="primary" />}
                title="Resumo por Grupo de Recursos"
                sx={{ pb: 0 }}
              />
              <CardContent>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Grupo de Recursos</TableCell>
                        <TableCell align="right">Custo</TableCell>
                        <TableCell align="right">Núm. Recursos</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {summary.resourceGroups.map((group) => (
                        <TableRow key={group.resourceGroup}>
                          <TableCell>{group.resourceGroup}</TableCell>
                          <TableCell align="right">{formatCurrency(group.totalCost, summary.currency)}</TableCell>
                          <TableCell align="right">{group.resourceCount}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        )}
        
        {/* Locations Summary */}
        {summary && summary.locations && summary.locations.length > 0 && (
          <Grid item xs={12} md={6}>
            <Card elevation={2} sx={{ height: '100%' }}>
              <CardHeader
                avatar={<LocationOnIcon color="primary" />}
                title="Resumo por Localização"
                sx={{ pb: 0 }}
              />
              <CardContent>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Localização</TableCell>
                        <TableCell align="right">Custo</TableCell>
                        <TableCell align="right">Núm. Recursos</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {summary.locations.map((location) => (
                        <TableRow key={location.location}>
                          <TableCell>{location.location}</TableCell>
                          <TableCell align="right">{formatCurrency(location.totalCost, summary.currency)}</TableCell>
                          <TableCell align="right">{location.resourceCount}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        )}
        
        {/* Resources Detail */}
        <Grid item xs={12}>
          <Card elevation={2}>
            <CardHeader
              avatar={<DescriptionIcon color="primary" />}
              title="Detalhes dos Recursos"
              sx={{ pb: 0 }}
            />
            <CardContent>
              {filteredConsumos.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Nome do Recurso</TableCell>
                        <TableCell>Grupo de Recursos</TableCell>
                        <TableCell>Localização</TableCell>
                        <TableCell align="right">Custo</TableCell>
                        <TableCell>Período</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredConsumos.map((consumo) => (
                        <TableRow key={consumo.id}>
                          <TableCell>{consumo.resourceName}</TableCell>
                          <TableCell>{consumo.resourceGroup}</TableCell>
                          <TableCell>{consumo.resourceLocation}</TableCell>
                          <TableCell align="right">
                            {formatCurrency(consumo.cost, consumo.currency)}
                          </TableCell>
                          <TableCell>
                            {formatDate(consumo.startDate)} - {formatDate(consumo.endDate)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Alert severity="info" sx={{ mt: 2 }}>
                  Nenhum recurso encontrado para o período selecionado.
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

AzureConsumption.propTypes = {
  clientId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired
};

export default AzureConsumption; 