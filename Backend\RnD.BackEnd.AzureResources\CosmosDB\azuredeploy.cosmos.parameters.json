{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"databaseAccountName": {"value": "rnd-cosmos-dev"}, "databaseName": {"value": "FrameworkDB"}, "location": {"value": "west<PERSON>"}, "locationName": {"value": "West US"}, "defaultExperience": {"value": "Core (SQL)"}, "isZoneRedundant": {"value": false}, "totalThroughputLimit": {"value": 1000}, "enableFreeTier": {"value": true}}}