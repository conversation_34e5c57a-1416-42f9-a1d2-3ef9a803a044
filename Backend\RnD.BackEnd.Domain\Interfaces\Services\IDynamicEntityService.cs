﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using RnD.BackEnd.Domain.Entities.Generic;

namespace RnD.BackEnd.Domain.Interfaces.Services
{
    /// <summary>
    /// Dynamic entity service interface
    /// </summary>
    public interface IDynamicEntityService
    {
        /// <summary>
        /// Adds the entity.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The entity added.
        /// </returns>
        Task<ServiceOutput<Dictionary<string, object>>> AddAsync(Dictionary<string, object> request);

        /// <summary>
        /// Updates the entity.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="etag">The etag.</param>
        /// <returns>
        /// The entity updated.
        /// </returns>
        Task<ServiceOutput<Dictionary<string, object>>> UpdateAsync(Dictionary<string, object> request, string etag);

        /// <summary>
        /// Deletes the specified entity by id.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// True if operation succeeded, False otherwise.
        /// </returns>
        Task<ServiceOutput<bool>> DeleteAsync(string id);

        /// <summary>
        /// Gets the by identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The entity.
        /// </returns>
        Task<ServiceOutput<Dictionary<string, object>>> GetByIdAsync(string id);

        /// <summary>
        /// Get the list of entities.
        /// </summary>
        /// <param name="pageNumber">The page number.</param>
        /// <param name="maxItems">The maximum items.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// The list of entities.
        /// </returns>
        Task<ServiceOutput<List<Dictionary<string, object>>>> ListAsync(
            int? pageNumber,
            int? maxItems,
            string sortField,
            bool sortAscending,
            string continuationToken,
            CancellationToken cancellationToken);
    }
}
