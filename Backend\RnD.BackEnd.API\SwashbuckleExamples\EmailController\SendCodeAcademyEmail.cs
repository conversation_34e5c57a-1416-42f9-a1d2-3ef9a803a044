﻿using System.Collections.Generic;
using RnD.BackEnd.API.Model.Email;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.EmailController
{
    /// <summary>
    /// Send Code Academy Email request example
    /// </summary>
    public class SendCodeAcademyEmail : IExamplesProvider<EmailMessageDto<CodeAcademyTemplateDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to Send Code Academy Email.
        /// </returns>
        public EmailMessageDto<CodeAcademyTemplateDto> GetExamples()
        {
            return new EmailMessageDto<CodeAcademyTemplateDto>
            {
                TemplateData = new CodeAcademyTemplateDto
                {
                    Message = "This is a test e-mail",
                    Subject = "Test e-mail",
                    Session = "SendGrid email service",
                    Date = "19-07-2022"
                },
                Recipients = new List<string> { "<EMAIL>" }
            };
        }
    }
}