import { useState } from 'react';
import PropTypes from 'prop-types';
import CustomDataTable from '../../CustomDataTable/CustomDataTable';
import { Button, Typography } from '@mui/material';
// import { documentVariablesAPI } from '../../../../api/documentVariablesAPI'; --> TODO
import {
  Save as SaveIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import Modal from '../ModalTemplate';

const DocumentVariablesModal = (props) => {
  const { onClose, open, contextType, readOnly } = props;
  const [documentVariables, setDocumentVariables] = useState([]);
  // const mounted = useMounted();
  const [isSubmitting] = useState(false);

  const types = [
    {
      text: 'Número',
      value: 'NUMBER'
    },
    {
      text: 'Texto',
      value: 'TEXT'
    },
    {
      text: 'Entidade',
      value: 'IDENTITY'
    }
  ];

  const context = [];

  if (contextType === 'OPPORTUNITY') {
    context.push({ text: 'Oportunidade', value: 'OPPORTUNITY' });
  } else if (contextType === 'PROCESS') {
    context.push({ text: 'Processo', value: 'PROCESS' });
  }

  const formColumns = [
    {
      ElementID: 'variableName',
      ElementLabel: 'Nome',
      ElementType: 'TextField',
      Width: 160,

    },
    {
      ElementID: 'contextType',
      ElementLabel: 'Contexto',
      ElementType: 'DynamicCollection',
      Width: 180,
      Collection: context
    },
    {
      ElementID: 'type',
      ElementLabel: 'Tipo de Variável',
      ElementType: 'DynamicCollection',
      Collection: types,
      Width: 140
    },
    {
      ElementID: 'value',
      ElementLabel: 'Valor',
      ElementType: 'TextField',
      Width: 130
    },
    {
      ElementID: 'initialValue',
      ElementLabel: 'Valor Default',
      ElementType: 'TextField',
      Width: 140
    }
  ];

  const handleUpdateDocumentVariables = (value) => {
    setDocumentVariables(value);
  };

  return (
    <>
      <Modal
        size="lg"
        onClose={onClose}
        open={open}
        title="Editar Variáveis"
        customActions={
          <>
            <Button
              sx={{ ml: 1 }}
              color="primary"
              onClick={onClose}
              variant="outlined"
              startIcon={<CloseIcon />}
            >
              Cancelar
            </Button>
            <Button
              color="primary"
              variant="contained"
              disabled={isSubmitting || readOnly}
              startIcon={<SaveIcon />}
            >
              Confirmar
            </Button>
          </>
        }
        content={
          <>
            <Typography>
              Indique as variáveis dinâmicas a editar.
            </Typography>
            <CustomDataTable
              columns={formColumns}
              dynamicCollection={{}}
              initialData={documentVariables}
              onUpdateData={(value) => handleUpdateDocumentVariables(value)}
              ElementID={documentVariables.variableID}
              hidePagination
              viewOnly={readOnly}
            />
          </>
        }
      />
    </>
  );
};

DocumentVariablesModal.propTypes = {
  onClose: PropTypes.func,
  open: PropTypes.bool.isRequired,
  contextType: PropTypes.string,
  readOnly: PropTypes.bool,
};

export default DocumentVariablesModal;
