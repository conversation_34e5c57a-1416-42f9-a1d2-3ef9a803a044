import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Chip,
  CircularProgress,
  Alert,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select
} from '@mui/material';
import { format } from 'date-fns';
import ptBR from 'date-fns/locale/pt-BR';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import axios from 'axios';
import { useTranslation } from "react-i18next";

const API_BASE_URL = `${process.env.REACT_APP_WEBAPI_URL}/api`;

const LogsPage = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [logs, setLogs] = useState([]);
  const [arquivos, setArquivos] = useState([]);
  const [arquivoSelecionado, setArquivoSelecionado] = useState('todos');
  const [tipoLogSelecionado, setTipoLogSelecionado] = useState('todos');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { t } = useTranslation();

 useEffect(() => {
        document.title = `${t('titles:logs')} | Portal Cloud Services`;
    }, []);

  useEffect(() => {
    buscarDados();
  }, []);

  const buscarDados = async () => {
    setLoading(true);
    setError(null);
    try {
      // Buscar logs gerais
      const logsResponse = await axios.get(`${API_BASE_URL}/LogProcessamento`);
      setLogs(logsResponse.data);

      // Buscar arquivos processados
      const arquivosResponse = await axios.get(`${API_BASE_URL}/LogProcessamento/arquivos`);
      setArquivos(arquivosResponse.data);
    } catch (err) {
      console.error("Erro ao buscar dados:", err);
      setError("Não foi possível carregar os dados. Por favor, tente novamente mais tarde.");
    } finally {
      setLoading(false);
    }
  };

  const buscarLogsPorArquivo = async (fileId) => {
    if (fileId === 'todos') {
      await buscarDados();
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(`${API_BASE_URL}/LogProcessamento/arquivo/${fileId}`);
      setLogs(response.data);
    } catch (err) {
      console.error("Erro ao buscar logs por arquivo:", err);
      setError("Não foi possível carregar os logs deste arquivo.");
    } finally {
      setLoading(false);
    }
  };

  const buscarLogsPorTipo = async (tipo) => {
    if (tipo === 'todos') {
      await buscarDados();
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(`${API_BASE_URL}/LogProcessamento/tipo/${tipo}`);
      setLogs(response.data);
    } catch (err) {
      console.error("Erro ao buscar logs por tipo:", err);
      setError("Não foi possível carregar os logs deste tipo.");
    } finally {
      setLoading(false);
    }
  };

  const handleArquivoChange = (event) => {
    const fileId = event.target.value;
    setArquivoSelecionado(fileId);
    buscarLogsPorArquivo(fileId);
  };

  const handleTipoLogChange = (event) => {
    const tipo = event.target.value;
    setTipoLogSelecionado(tipo);
    buscarLogsPorTipo(tipo);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const formatarData = (dataString) => {
    try {
      const data = new Date(dataString);
      return format(data, 'dd/MM/yyyy HH:mm:ss', { locale: ptBR });
    } catch (err) {
      return dataString;
    }
  };

  const getTipoLogIcon = (tipo) => {
    switch (tipo) {
      case 'Erro':
        return <ErrorIcon color="error" />;
      case 'Aviso':
        return <WarningIcon color="warning" />;
      case 'Info':
      default:
        return <InfoIcon color="info" />;
    }
  };

  const getTipoLogColor = (tipo) => {
    switch (tipo) {
      case 'Erro':
        return "error";
      case 'Aviso':
        return "warning";
      case 'Info':
      default:
        return "info";
    }
  };

  const getLogTypeTranslation = (tipo) => {
    switch (tipo) {
      case 'Erro':
        return t('common:labels.processingError');
      case 'Aviso':
        return t('common:labels.processingWarning');
      case 'Info':
        return t('common:labels.infoLog');
      default:
        return tipo;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          {t("sidebar:options.logs")}
        </Typography>

        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Logs" />
          <Tab label={t("common:tableHeaders.logsArchive")} />
        </Tabs>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {activeTab === 0 && (
              <Box>
                <Box sx={{ mb: 3, display: 'flex', gap: 2 }}>
                  <FormControl sx={{ minWidth: 200 }}>
                    <InputLabel id="arquivo-select-label">{t('common:tableHeaders.file')}</InputLabel>
                    <Select
                      labelId="arquivo-select-label"
                      id="arquivo-select"
                      value={arquivoSelecionado}
                      label={t('common:tableHeaders.file')}
                      onChange={handleArquivoChange}
                    >
                      <MenuItem value="todos">{t('common:labels.allfiles')} </MenuItem>
                      {arquivos.map((arquivo) => (
                        <MenuItem key={arquivo.id} value={arquivo.id}>
                          {arquivo.nome}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl sx={{ minWidth: 200 }}>
                    <InputLabel id="tipo-log-select-label">{t('common:tableHeaders.logtype')}</InputLabel>
                    <Select
                      labelId="tipo-log-select-label"
                      id="tipo-log-select"
                      value={tipoLogSelecionado}
                      label={t('common:tableHeaders.logtype')}
                      onChange={handleTipoLogChange}
                    >
                      <MenuItem value="todos">{t('common:labels.alltypes')} </MenuItem>
                      <MenuItem value="Info">{t('common:labels.infoLog')}</MenuItem>
                      <MenuItem value="Aviso">{t('common:labels.processingWarning')}</MenuItem>
                      <MenuItem value="Erro">{t('common:labels.processingError')}</MenuItem>
                    </Select>
                  </FormControl>

                  <Button
                    variant="outlined"
                    onClick={buscarDados}
                    sx={{ ml: 'auto' }}
                  >
                    {t('common:buttons.update')}
                  </Button>
                </Box>

                <TableContainer component={Paper} variant="outlined">
                  <Table sx={{ minWidth: 650 }}>
                    <TableHead>
                      <TableRow sx={{ bgcolor: 'background.paper' }}>
                        <TableCell>  {t('common:tableHeaders.hourDate')}</TableCell>
                        <TableCell>{t('common:tableHeaders.fileID')}</TableCell>
                        <TableCell>{t('common:tableHeaders.logtype')}</TableCell>
                        <TableCell>{t('common:tableHeaders.message')}</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {logs.length > 0 ? (
                        logs.map((log) => (
                          <TableRow
                          key={log.id}
                            sx={{
                              ...(log.tipoLog === 'Erro' && { bgcolor: '#fff8f8' })
                            }}
                          >
                            <TableCell>{formatarData(log.dataRegistro)}</TableCell>
                            <TableCell>{log.fileID}</TableCell>
                            <TableCell>
                              <Chip
                                icon={getTipoLogIcon(log.tipoLog)}
                                label={getLogTypeTranslation(log.tipoLog)}
                                color={getTipoLogColor(log.tipoLog)}
                                size="small"
                                variant="outlined"
                              />
                            </TableCell>
                            <TableCell
                              sx={{
                                ...(log.tipoLog === 'Erro' && { color: 'error.main', fontWeight: 'bold' })
                              }}
                            >
                              {log.mensagem}
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={4} align="center">
                            {t('common:labels.noResultsFound')}
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}

            {activeTab === 1 && (
              <Box>
                <Button
                  variant="outlined"
                  onClick={buscarDados}
                  sx={{ mb: 3, ml: 'auto', display: 'block' }}
                >
                  {t('common:buttons.update')}
                </Button>

                <TableContainer component={Paper} variant="outlined">
                  <Table sx={{ minWidth: 650 }}>
                    <TableHead>
                      <TableRow sx={{ bgcolor: 'background.paper' }}>
                        <TableCell>ID</TableCell>
                        <TableCell>{t('common:tableHeaders.fileName')}</TableCell>
                        <TableCell>{t('common:labels.processingDate')}</TableCell>
                        <TableCell>{t('common:labels.processingStatus')}</TableCell>
                        <TableCell>{t('common:tableHeaders.recordsProcessed')}</TableCell>
                        <TableCell>{t('common:tableHeaders.errorMessage')}</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {arquivos.length > 0 ? (
                        arquivos.map((arquivo) => (
                          <TableRow
                          key={arquivo.id}
                            sx={{
                              ...(arquivo.registrosComErro > 0 && { bgcolor: '#fff8f8' })
                            }}
                          >
                            <TableCell>{arquivo.id}</TableCell>
                            <TableCell>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1
                                }}
                              >
                                <InsertDriveFileIcon fontSize="small" color="primary" />
                                {arquivo.nome}
                              </Box>
                            </TableCell>
                            <TableCell>{formatarData(arquivo.dataProcessamento)}</TableCell>
                            <TableCell>
                              <Chip
                                label={arquivo.processado ? t('common:labels.successfullyProcessed') : t('common:labels.processingStatus')}
                                color={arquivo.processado ? "success" : "warning"}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>{arquivo.registrosProcessados}</TableCell>
                            <TableCell>
                              {arquivo.registrosComErro > 0 ? (
                                <Chip
                                  label={arquivo.registrosComErro}
                                  color="error"
                                  size="small"
                                />
                              ) : (
                                "0"
                              )}
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} align="center">
                            {t('common:labels.noResultsFound')}
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}
          </>
        )}
      </Paper>
    </Container>
  );
};

export default LogsPage;
