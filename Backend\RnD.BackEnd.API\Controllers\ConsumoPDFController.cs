using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using RnD.BackEnd.API.Services;

namespace RnD.BackEnd.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ConsumoPDFController : ControllerBase
    {
        private readonly IConsumoPDFService _pdfService;
        private readonly ILogger<ConsumoPDFController> _logger;
        private readonly ApplicationDbContext _context;
        private readonly CrayonTestExtractorService _testExtractorService;

        public ConsumoPDFController(IConsumoPDFService pdfService, ILogger<ConsumoPDFController> logger, ApplicationDbContext context, CrayonTestExtractorService testExtractorService)
        {
            _pdfService = pdfService;
            _logger = logger;
            _context = context;
            _testExtractorService = testExtractorService;
        }

        /// <summary>
        /// Faz upload de um arquivo PDF para processamento
        /// </summary>
        /// <param name="file">Arquivo PDF a ser processado</param>
        /// <returns>Nome do arquivo no Azure Storage</returns>
        [HttpPost("upload")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> UploadPDF(IFormFile file)
        {
            try
            {
                _logger.LogInformation($"Iniciando upload do arquivo: {file?.FileName}");

                var fileName = await _pdfService.UploadPDFFile(file);

                // Processa o arquivo imediatamente após o upload
                var consumos = await _pdfService.ProcessPDFFile(fileName);

                return Ok(new
                {
                    message = $"Arquivo {fileName} processado com sucesso",
                    fileName = fileName,
                    consumos = consumos
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao fazer upload do arquivo PDF");
                return BadRequest(new
                {
                    message = "Erro ao fazer upload do arquivo PDF",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Processa um arquivo PDF já existente no Azure Storage
        /// </summary>
        /// <param name="fileName">Nome do arquivo PDF no Azure Storage</param>
        /// <param name="forceReprocessing">Se deve forçar o reprocessamento mesmo se o arquivo já foi processado</param>
        /// <returns>Lista de consumos extraídos do PDF</returns>
        [HttpPost("processar/{fileName}")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> ProcessarPDF(string fileName, [FromQuery] bool forceReprocessing = false)
        {
            try
            {
                _logger.LogInformation($"Iniciando processamento do arquivo: {fileName}, forçar reprocessamento: {forceReprocessing}");

                if (string.IsNullOrEmpty(fileName))
                {
                    return BadRequest("Nome do arquivo não pode ser vazio.");
                }

                if (!fileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest("Arquivo deve ter extensão .pdf");
                }

                var consumos = await _pdfService.ProcessPDFFile(fileName, forceReprocessing);

                if (consumos.Count == 0)
                {
                    if (forceReprocessing)
                    {
                        return Ok(new
                        {
                            message = "Reprocessamento solicitado, mas nenhum consumo foi extraído do PDF. Verifique se o formato do arquivo está correto.",
                            consumos = consumos
                        });
                    }
                    else
                    {
                        return Ok(new
                        {
                            message = "Nenhum consumo foi extraído do PDF. O arquivo já foi processado ou o formato não é compatível.",
                            consumos = consumos
                        });
                    }
                }

                return Ok(new
                {
                    message = $"PDF processado com sucesso. {consumos.Count} consumos extraídos.",
                    consumos = consumos
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erro ao processar o arquivo PDF {fileName}");
                return BadRequest(new
                {
                    message = "Erro ao processar o arquivo PDF",
                    error = ex.Message,
                    detalhes = ex.InnerException?.Message
                });
            }
        }
        /// <summary>
        /// Obtém todos os registros de ConsumoPDF
        /// </summary>
        /// <returns>Lista de todos os registros de ConsumoPDF</returns>
        [HttpGet]
        [ProducesResponseType(typeof(IEnumerable<ConsumoPDF>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetAll()
        {
            try
            {
                _logger.LogInformation("Buscando todos os registros de ConsumoPDF");
                var consumos = await _context.ConsumoPDF.ToListAsync();
                return Ok(consumos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar registros de ConsumoPDF");
                return StatusCode(StatusCodes.Status500InternalServerError, new { message = "Erro ao buscar registros de ConsumoPDF", error = ex.Message });
            }
        }

        /// <summary>
        /// Obtém os registros de ConsumoPDF de um cliente específico
        /// </summary>
        /// <param name="clienteId">ID do cliente</param>
        /// <returns>Lista de registros de ConsumoPDF do cliente</returns>
        [HttpGet("cliente/{clienteId}")]
        [ProducesResponseType(typeof(IEnumerable<ConsumoPDF>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(string), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetByClienteId(int clienteId)
        {
            try
            {
                _logger.LogInformation($"Buscando registros de ConsumoPDF para o cliente ID: {clienteId}");

                var consumos = await _context.ConsumoPDF
                    .Where(c => c.ClienteID == clienteId)
                    .ToListAsync();

                if (consumos.Count == 0)
                {
                    return NotFound(new { message = $"Nenhum registro de ConsumoPDF encontrado para o cliente ID: {clienteId}" });
                }

                return Ok(consumos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erro ao buscar registros de ConsumoPDF para o cliente ID: {clienteId}");
                return StatusCode(StatusCodes.Status500InternalServerError, new { message = "Erro ao buscar registros de ConsumoPDF", error = ex.Message });
            }
        }

        /// <summary>
        /// Obtém o texto bruto de todos os PDFs Crayon no container Azure
        /// </summary>
        /// <returns>Dicionário com nome do arquivo e texto extraído</returns>
        [HttpGet("crayon-raw-text")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> GetCrayonPDFsRawText()
        {
            try
            {
                _logger.LogInformation("Iniciando extração de texto bruto de todos os PDFs Crayon");

                var result = await _pdfService.GetCrayonPDFsRawText();

                return Ok(new
                {
                    message = $"Texto extraído de {result.Count} PDFs Crayon",
                    data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao extrair texto bruto dos PDFs Crayon");
                return BadRequest(new
                {
                    message = "Erro ao extrair texto bruto dos PDFs Crayon",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Obtém os dados extraídos de todos os PDFs Crayon no container Azure - VERSÃO DE TESTE (SEM PERSISTIR)
        /// </summary>
        /// <returns>Dicionário com nome do arquivo e dados extraídos formatados</returns>
        [HttpGet("crayon-extracted-data")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> GetCrayonPDFsExtractedData()
        {
            try
            {
                _logger.LogInformation("🔥 INICIANDO EXTRAÇÃO DE TESTE - SEM PERSISTIR NA BASE DE DADOS");

                var result = await _testExtractorService.GetCrayonPDFsExtractedData();

                return Ok(new
                {
                    message = $"✅ DADOS EXTRAÍDOS DE {result.Count} PDFs Crayon (TESTE - SEM PERSISTIR)",
                    data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao extrair dados dos PDFs Crayon");
                return BadRequest(new
                {
                    message = "Erro ao extrair dados dos PDFs Crayon",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Testa extração de um PDF específico de 2023 para debug
        /// </summary>
        /// <param name="fileName">Nome do arquivo PDF</param>
        /// <returns>Dados extraídos do PDF específico</returns>
        [HttpGet("test-pdf-2023/{fileName}")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> TestPDF2023(string fileName)
        {
            try
            {
                Console.WriteLine($"🚨🚨🚨 [ENDPOINT] TESTANDO PDF 2023: {fileName}");
                _logger.LogInformation($"🚨🚨🚨 [ENDPOINT] TESTANDO PDF 2023: {fileName}");

                var result = await _testExtractorService.GetSpecificPDFExtractedData(fileName);

                Console.WriteLine($"🚨🚨🚨 [ENDPOINT] RESULTADO OBTIDO");
                _logger.LogInformation($"🚨🚨🚨 [ENDPOINT] RESULTADO OBTIDO");

                return Ok(new
                {
                    message = $"✅ DADOS EXTRAÍDOS DO PDF {fileName} (TESTE - SEM PERSISTIR)",
                    data = result
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"🚨🚨🚨 [ENDPOINT] ERRO: {ex.Message}");
                _logger.LogError(ex, $"❌ ERRO na extração do PDF {fileName}");
                return BadRequest(new
                {
                    message = "Erro na extração de dados",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Debug de períodos extraídos de um PDF específico
        /// </summary>
        /// <param name="fileName">Nome do arquivo PDF</param>
        /// <returns>Informações de debug sobre períodos extraídos</returns>
        [HttpGet("debug-periods/{fileName}")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> DebugPeriods(string fileName)
        {
            try
            {
                var debugInfo = await _testExtractorService.DebugPeriodsAsync(fileName);
                return Ok(new
                {
                    message = $"🔍 DEBUG DE PERÍODOS DO PDF {fileName}",
                    data = debugInfo
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ ERRO no debug de períodos do PDF {fileName}");
                return BadRequest(new
                {
                    message = "Erro no debug de períodos",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Lista todos os PDFs Crayon disponíveis no container
        /// </summary>
        /// <returns>Lista de nomes dos PDFs Crayon</returns>
        [HttpGet("list-crayon-pdfs")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> ListCrayonPDFs()
        {
            try
            {
                var pdfList = await _testExtractorService.ListCrayonPDFsAsync();
                return Ok(new
                {
                    message = "📋 LISTA DE PDFs CRAYON DISPONÍVEIS",
                    totalPDFs = pdfList.Count,
                    pdfs = pdfList.OrderBy(name => name).ToList()
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ ERRO ao listar PDFs Crayon");
                return BadRequest(new
                {
                    message = "Erro ao listar PDFs Crayon",
                    error = ex.Message
                });
            }
        }
    }
}