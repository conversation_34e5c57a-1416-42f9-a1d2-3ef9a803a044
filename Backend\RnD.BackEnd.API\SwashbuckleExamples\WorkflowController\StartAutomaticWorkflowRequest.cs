﻿using System.Collections.Generic;
using RnD.BackEnd.API.Model.Workflow;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// Start Automatic Workflow request example
    /// </summary>
    public class StartAutomaticWorkflowRequest : IMultipleExamplesProvider<StartAutomaticWorkflowDto>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to Start Automatic Workflow.
        /// </returns>
        public IEnumerable<SwaggerExample<StartAutomaticWorkflowDto>> GetExamples()
        {
            yield return SwaggerExample.Create("Vacation - With Custom Email", new StartAutomaticWorkflowDto
            {
                SourceId = "Vacation/2023",
                SourceUrl = "www.framework_vacation_2023.pt",
                SourceMetadata = new Dictionary<string, string>()
                  {
                      { "VacationFlag", "true" }
                  },
                EmailRequest = new WorkflowEmailDto()
                {
                    TemplateId = "d-c43961e085e04f1c8e1f0192db1c69d8",
                    Recipients = new List<string>() { "<EMAIL>", "<EMAIL>" },
                    TemplateData = new Dictionary<string, string>()
                      {
                          { "Message", "This is an example of Email" }
                      },
                    Subject = "FrameWork Example of Email",
                    SendEmail = true
                }
            });
            yield return SwaggerExample.Create("Project - With Default Email", new StartAutomaticWorkflowDto
            {
                WorkflowIds = new List<string>() { "8374a0bf-2df0-441e-9c18-a1cc4e13e5ba", "74ba0ecf-b160-480e-9a72-fb9aa0fc8e70" },
                SourceId = "Project/2023",
                SourceUrl = "www.framework_project_2023.pt",
                SourceMetadata = new Dictionary<string, string>()
                  {
                      { "DirectionType", "1995" }
                  },
                EmailRequest = null
            });
        }
    }
}