using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using ExcelDataReader;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using Microsoft.EntityFrameworkCore;

namespace RnD.BackEnd.API.Services
{
    public interface IConsumoSubscricaoExcelService
    {
        Task<List<ConsumoSubscricaoExcelModel>> ProcessExcelFile(string fileName);
    }

    public class ConsumoSubscricaoExcelService : IConsumoSubscricaoExcelService
    {
        private readonly IConfiguration _configuration;
        private readonly BlobServiceClient _blobServiceClient;
        private readonly ApplicationDbContext _context;
        private readonly LogProcessamentoService _logService;
        private readonly ILogger<ConsumoSubscricaoExcelService> _logger;

        public ConsumoSubscricaoExcelService(
            IConfiguration configuration,
            ApplicationDbContext context,
            LogProcessamentoService logService,
            ILogger<ConsumoSubscricaoExcelService> logger)
        {
            _configuration = configuration;
            _context = context;
            _logService = logService;
            _logger = logger;
            _blobServiceClient = new BlobServiceClient(_configuration["StorageExcelAzure:ConnectionString"]);
        }

        public async Task<List<ConsumoSubscricaoExcelModel>> ProcessExcelFile(string fileName)
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient(_configuration["StorageExcelAzure:ContainerName"]);
            var blobClient = containerClient.GetBlobClient(fileName);
            
            // Verifica se o arquivo já foi processado
            var arquivoJaProcessado = await _context.ArquivosProcessados.FirstOrDefaultAsync(a => a.Nome == fileName);
            if (arquivoJaProcessado != null)
            {
                _logger.LogWarning($"Arquivo {fileName} já foi processado anteriormente. Ignorando.");
                await _logService.RegistrarLog(0, $"Arquivo {fileName} já foi processado anteriormente. Ignorando.", TipoLog.Aviso);
                return new List<ConsumoSubscricaoExcelModel>();
            }

            // Registra o novo arquivo antes do processamento
            var arquivoProcessado = new ArquivoProcessado
            {
                Nome = fileName,
                BlobUrl = blobClient.Uri.ToString(),
                DataUpload = DateTime.Now,
                Processado = false,
                RegistrosProcessados = 0,
                RegistrosComErro = 0
            };

            _context.ArquivosProcessados.Add(arquivoProcessado);
            await _context.SaveChangesAsync();
            int fileId = arquivoProcessado.Id;
            
            var memoryStream = new MemoryStream();
            await blobClient.DownloadToAsync(memoryStream);
            memoryStream.Position = 0;

            var consumos = new List<ConsumoSubscricaoExcelModel>();
            DateTime startDate = DateTime.MinValue;
            DateTime endDate = DateTime.MinValue;
            int sucessos = 0, erros = 0;
            
            try
            {
                using (var reader = ExcelReaderFactory.CreateReader(memoryStream))
                {
                    var result = reader.AsDataSet(new ExcelDataSetConfiguration()
                    {
                        ConfigureDataTable = (_) => new ExcelDataTableConfiguration()
                        {
                            UseHeaderRow = true
                        }
                    });

                    // Primeiro, vamos obter as datas da aba Summary
                    var summaryTable = result.Tables["Summary"];
                    if (summaryTable != null)
                    {
                        foreach (DataRow row in summaryTable.Rows)
                        {
                            var firstColumn = row[0]?.ToString();
                            if (firstColumn == "Start date:")
                            {
                                DateTime.TryParse(row[1]?.ToString(), out startDate);
                            }
                            else if (firstColumn == "End date:")
                            {
                                DateTime.TryParse(row[1]?.ToString(), out endDate);
                            }
                        }
                    }
                    else
                    {
                        _logger.LogError("Planilha 'Summary' não encontrada no arquivo Excel");
                        await _logService.RegistrarLog(0, "Planilha 'Summary' não encontrada no arquivo Excel", TipoLog.Erro);
                        return consumos;
                    }

                    // Agora vamos processar os dados da aba Data
                    var dataTable = result.Tables["Data"];
                    if (dataTable == null)
                    {
                        _logger.LogError("Planilha 'Data' não encontrada no arquivo Excel");
                        await _logService.RegistrarLog(0, "Planilha 'Data' não encontrada no arquivo Excel", TipoLog.Erro);
                        return consumos;
                    }

                    foreach (DataRow row in dataTable.Rows)
                    {
                        try
                        {
                            var consumo = new ConsumoSubscricaoExcelModel
                            {
                                SubscriptionId = row["SubscriptionId"]?.ToString(),
                                ResourceGroup = row["ResourceGroup"]?.ToString(),
                                ResourceName = row["Resource"]?.ToString(),
                                ResourceLocation = row["ResourceLocation"]?.ToString(),
                                Cost = decimal.Parse(row["Cost"]?.ToString() ?? "0"),
                                CostUSD = decimal.Parse(row["CostUSD"]?.ToString() ?? "0"),
                                Currency = row["Currency"]?.ToString(),
                                StartDate = startDate,
                                EndDate = endDate
                            };

                            consumos.Add(consumo);
                            sucessos++;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"Erro ao processar linha do Excel: {ex.Message}");
                            await _logService.RegistrarLog(0, $"Erro ao processar linha do Excel: {ex.Message}", TipoLog.Erro);
                            erros++;
                        }
                    }
                }

                // Atualiza o status do arquivo processado
                arquivoProcessado.Processado = true;
                arquivoProcessado.RegistrosProcessados = sucessos;
                arquivoProcessado.RegistrosComErro = erros;
                await _context.SaveChangesAsync();

                await _logService.RegistrarLog(fileId, $"Processamento concluído. Total: {sucessos} registros processados, {erros} com erro", TipoLog.Info);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Erro ao processar arquivo {fileName}: {ex.Message}");
                await _logService.RegistrarLog(fileId, $"Erro crítico no processamento: {ex.Message}", TipoLog.Erro);
                
                arquivoProcessado.Processado = true;
                arquivoProcessado.RegistrosComErro = 1;
                await _context.SaveChangesAsync();
            }

            return consumos;
        }
    }
} 