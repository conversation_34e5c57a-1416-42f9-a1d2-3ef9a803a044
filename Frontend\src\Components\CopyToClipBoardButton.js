import { IconButton } from '@mui/material';
import { PropTypes } from 'prop-types';
import { useState } from 'react';
import { ContentCopy as ContentCopyIcon } from '@mui/icons-material';
import { toast } from 'react-hot-toast';

const CopyToClipboardButton = ({ value, style }) => {
    const [open, setOpen] = useState(false)
    const handleClick = async (event) => {
        event.preventDefault();
        setOpen(open)
        navigator.clipboard.writeText(value)
        toast.success('Copied to Clipboard!');
    }

    return (
        <>
            <IconButton
                onClick={handleClick}
                style={style}
            >
                <ContentCopyIcon fontSize="small" />
            </IconButton>
        </>
    )
}
CopyToClipboardButton.propTypes = {
    value: PropTypes.string,
    style: PropTypes.any
}
export default CopyToClipboardButton;
