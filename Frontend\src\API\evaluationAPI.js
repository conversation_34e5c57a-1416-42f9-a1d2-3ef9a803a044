import handleMethod from '../Utils/APIutils/handleMethod';

class EvaluationAPI {
  async getQuizSurvey({ ratingQuizID, type, viewMode, evaluationMomentID }) {
    /* dados do formulário de avaliação */
    // RatingQuizID: RatingQuizID
    // Type: TYPE_PERFORMANCE
    // ViewMode:null
    // Reference:null
    ratingQuizID = ratingQuizID ? `RatingQuizID=${ratingQuizID}` : '';
    type = type ? `&Type=${type}` : '';
    viewMode = viewMode ? `&ViewMode=${viewMode}` : '';
    evaluationMomentID = evaluationMomentID ? `&evaluationMomentID=${evaluationMomentID}` : '';

    const Input = {
      type: 'GET',
      method: `GetQuizSurvey?${ratingQuizID}${type}${viewMode}${evaluationMomentID}`,
      params: {}
    };

    const response = await handleMethod(Input);

    return response;
  }

  async calculateQuizSurvey(params) {
    const Input = {
      type: 'GET',
      method: `calculateQuizSurvey`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async saveQuizSurvey(params) {
    /* dados do formulário de avaliação */
    // RatingQuizID: RatingQuizID
    // Type: TYPE_PERFORMANCE
    // ViewMode:null
    // Reference:null

    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/evaluation/SaveQuizSurvey`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async getEvaluationDetails({ viewType, evaluationMomentID }) {
    /*listas de avaliação: EvaluationList
     pedidos de avaliação efectuados: EvaluationRequestList
     pessoas disponíveis para pedido de avaliação: EvaluationRequestAvailable
      */
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/evaluation/getEvaluationDetails`,
      params: {
        Type: "TYPE_PERFORMANCE",
        ViewType: viewType,
        evaluationMomentID: evaluationMomentID
      }
    };

    const response = await handleMethod(Input);

    return response;
  }

  async getPeriodicEvaluationDetails({ ...props }) {
    /*listas de avaliação: EvaluationList
     pedidos de avaliação efectuados: EvaluationRequestList
     pessoas disponíveis para pedido de avaliação: EvaluationRequestAvailable
      */
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/evaluation/getEvaluationDetails`,
      params: {
        Type: "TYPE_PERIODIC",
        ViewMode: props.viewMode,
        ViewType: 'PERIODIC'
      }
    };

    const response = await handleMethod(Input);

    return response;
  }

  async getAdministrativeEvaluationDetails({ statusData = 'A', filters = [] }) {
    /*listas de avaliação: EvaluationList
     pedidos de avaliação efectuados: EvaluationRequestList
     pessoas disponíveis para pedido de avaliação: EvaluationRequestAvailable
      */
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/evaluation/GetAdministrativeEvaluationDetails`,
      params: {
        Type: "TYPE_PERFORMANCE",
        SYS_STATUS: statusData,
        filters: filters
      }
    };

    const response = await handleMethod(Input);

    return response;
  }

  async addEvaluationRequest(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/evaluation/addEvaluationRequest`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async GetEvaluationResume({ EvaluationMomentID }) {
    const Input = {
      type: 'GET',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/Evaluation/GetEvaluationResume?EvaluationMomentID=${EvaluationMomentID}`
    };

    const response = await handleMethod(Input);

    return response;
  }

  async scheduleMonitorization(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/evaluation/scheduleMonitorization`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async validateCreateFinalEvaluation(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/evaluation/validateCreateFinalEvaluation`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async createFinalEvaluation(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/evaluation/createFinalEvaluation`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async getUserPhoto() {
    // const Output = {
    //     Error: false
    // };

    // try {
    //     const data = {
    //         type: 'GET',
    //         method: '/api/Evaluation/GetUserPhoto?FileName=',
    //         params: props
    //     };

    //     const response = data;//await getBase64(data);

    //     if (response) {
    //         Output.Value = response.Value;
    //         if (response.Error) throw response.ExceptionMessages;
    //     } else {
    //         //BUSINESS MESSAGES
    //         throw "Error getting data.";
    //     }
    // } catch (err) {
    //     err = err ? err : "Error getting data";
    //     Output.Error = true;
    // }

    // return Output;
  }

  async getEvaluationDocumentsList(params) {
    const Input = {
      type: 'GET',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/evaluation/GetEvaluationDocumentsList?viewType=${params.viewType}&evaluationMomentID=${params.evaluationMomentID}`,
      params: {}
    };

    const response = await handleMethod(Input);

    return response;
  }

  async GetEvaluationHarmonization({ EvaluationMomentID }) {
    //type = RELEVANT, ADEQUATE, INADEQUATE
    const Input = {
      type: 'GET',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/EvaluationHarmonization?EvaluationMomentID=${EvaluationMomentID}`
    };

    const response = await handleMethod(Input);

    return response;
  }

  async SaveHarmonizationProposedEvaluations(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/EvaluationHarmonization/SaveProposedEvaluations`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async SubmitHarmonizationProposedEvaluations(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/EvaluationHarmonization/SubmitProposedEvaluations`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async RevertProposedEvaluations(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/EvaluationHarmonization/Revert`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async GetHarmonizedEvaluations(EvaluationMomentID) {
    const Input = {
      type: 'GET',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/EvaluationHarmonization/GetHarmonizedEvaluations?EvaluationMomentID=${EvaluationMomentID}`
    };

    const response = await handleMethod(Input);

    return response;
  }

  async EvaluationHarmonizationMove(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/EvaluationHarmonization/Move`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async SaveHarmonizedEvaluations(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/EvaluationHarmonization/SaveHarmonizedEvaluations`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async SubmitHarmonizedEvaluations(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/EvaluationHarmonization/SubmitHarmonizedEvaluations`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async ExcludeEmployeeEvaluation(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/evaluation/ExcludeEmployeeEvaluation`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async RevertEvaluation(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/evaluation/RevertEmployeeEvaluation`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async ValidateHarmonizedEvaluations(params) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/EvaluationHarmonization/ValidateHarmonizedEvaluations`,
      params: params
    };

    const response = await handleMethod(Input);

    return response;
  }

  async getEvaluationMoments(evaluationMomentID) {
    evaluationMomentID = evaluationMomentID ? `EvaluationMomentID=${evaluationMomentID}` : '';

    const Input = {
      type: 'GET',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/EvaluationPlanning/GetEvaluationMoments?${evaluationMomentID}`,
      params: {}
    };

    const response = await handleMethod(Input);

    return response;
  }
}

export const evaluationAPI = new EvaluationAPI();
