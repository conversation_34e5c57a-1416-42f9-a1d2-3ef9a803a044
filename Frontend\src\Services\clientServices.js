export const getClients = async () => {
    try {
        const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/Cliente`);
        if (!response.ok) throw new Error("Erro ao buscar clientes");
        const data = await response.json();
        console.log("🔍 Dados da API:", data);

        if (!Array.isArray(data)) {
            console.error("❌ ERRO: API não retornou um array!", data);
            return [];
        }

        return data.map(client => ({
            id: client.id || client.ID,
            name: client.name || client.Name,
            contact: client.contact || client.Contact || "0",
        }));
    } catch (error) {
        console.error("❌ ERRO ao conectar com a API:", error);
        return [];
    }
};

export const getClientById = async (id) => {
    try {
        const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/Cliente/${id}`);
        if (!response.ok) throw new Error(`Erro ao buscar cliente: ${response.status}`);
        const data = await response.json();
        console.log("🔍 Detalhes do cliente:", data);

        return {
            id: data.id || data.ID,
            name: data.name || data.Name,
            contact: data.contact || data.Contact || "0"
        };
    } catch (error) {
        console.error("❌ ERRO ao buscar detalhes do cliente:", error);
        throw error;
    }
};

export const updateClient = async (id, clientData) => {
    try {
        // Certifica-se que os campos correspondem ao esperado pelo backend
        const payload = {
            ID: Number(id),
            Name: clientData.Name,
            Contact: clientData.Contact && clientData.Contact.trim() !== ""
                ? clientData.Contact
                : "0" // Usar "0" em vez de "Sem contato" como no NewClient.js
        };

        console.log("Enviando dados atualizados para a API:", JSON.stringify(payload));

        // Primeira tentativa com application/json
        const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/Cliente/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Resposta da API (erro):", errorText);

            // Segunda tentativa com application/x-www-form-urlencoded (como em NewClient.js)
            console.log("Tentando com application/x-www-form-urlencoded");

            const formData = new URLSearchParams();
            formData.append('ID', Number(id));
            formData.append('Name', clientData.Name);
            formData.append('Contact', payload.Contact);

            const response2 = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/Cliente/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData,
            });

            if (!response2.ok) {
                const error2Text = await response2.text();
                console.error("Resposta da segunda tentativa (erro):", error2Text);
                throw new Error(`Erro ao atualizar cliente: ${response2.status}`);
            } else {
                console.log("Segunda tentativa funcionou!");
                return { success: true };
            }
        }

        console.log("Primeira tentativa funcionou!");
        return { success: true };
    } catch (error) {
        console.error("❌ ERRO ao atualizar cliente:", error);
        throw error;
    }
};

export const deleteClient = async (id) => {
    try {
        const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/Cliente/${id}`, {
            method: 'DELETE',
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Resposta da API (erro ao apagar):", errorText);
            throw new Error(`Erro ao apagar cliente: ${response.status}`);
        }

        return true;
    } catch (error) {
        console.error("❌ ERRO ao apagar cliente:", error);
        throw error;
    }
};

export const createClient = async (clientData) => {
    try {
        // Certifica-se que os campos correspondem ao esperado pelo backend
        const payload = {
            Name: clientData.Name,
            Contact: clientData.Contact && clientData.Contact.trim() !== ""
                ? clientData.Contact
                : "0"
        };

        console.log("Enviando dados para a API:", JSON.stringify(payload));

        // Primeira tentativa com application/json
        const response = await fetch(
          `${process.env.REACT_APP_WEBAPI_URL}/api/Cliente`,
          {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
          }
        );

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Resposta da API (texto completo):", errorText);

            // Segunda tentativa com application/x-www-form-urlencoded
            console.log("Tentando com application/x-www-form-urlencoded");

            const formData = new URLSearchParams();
            formData.append('Name', clientData.Name);
            formData.append('Contact', payload.Contact);

            const response2 = await fetch(
              `${process.env.REACT_APP_WEBAPI_URL}/api/Cliente`,
              {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData,
              }
            );

            if (!response2.ok) {
                const error2Text = await response2.text();
                console.error("Resposta da segunda tentativa (texto completo):", error2Text);
                throw new Error(`Erro ao criar cliente: ${response2.status}`);
            } else {
                // Segunda tentativa funcionou!
                console.log("Segunda tentativa funcionou!");
                return await response2.json();
            }
        } else {
            // Primeira tentativa funcionou!
            console.log("Primeira tentativa funcionou!");
            return await response.json();
        }
    } catch (error) {
        console.error("❌ ERRO ao criar cliente:", error);
        throw error;
    }
};
