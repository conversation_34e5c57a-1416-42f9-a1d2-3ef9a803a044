import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
    Container,
    Typo<PERSON>,
    TextField,
    Button,
    Box,
    CircularProgress,
    Paper,
    Grid,
    Snackbar,
    Alert
} from "@mui/material";
import SaveIcon from '@mui/icons-material/Save';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { createClient } from "../../Services/clientServices";
import { useTranslation } from "react-i18next";

function NewClient() {
    console.log("🚀 NewClient component function executed");

    useEffect(() => {
        console.log("🔄 NewClient component mounted");
        document.title = t("common:buttons.createClient") + " | Portal Cloud Services";
    }, []);

    const [client, setClient] = useState({
        Name: '',
        Contact: '0'
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(false);
    const navigate = useNavigate();
    const { t } = useTranslation();

    const handleChange = (e) => {
        const { name, value } = e.target;
        setClient(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError(null);

        try {
            // Validação básica
            if (!client.Name) {
                throw new Error("Nome é obrigatório");
            }

            // Garantir que Contact nunca seja vazio ou nulo
            if (!client.Contact) {
                setClient(prev => ({
                    ...prev,
                    Contact: "0"
                }));
            }

            // Usar a função de serviço para criar o cliente
            await createClient(client);

            setSuccess(true);

            // Redireciona para a página de lista de clientes após 1.5 segundos
            setTimeout(() => {
                navigate('/ClientsList');
            }, 1500);
        } catch (err) {
            console.error('Erro ao salvar cliente:', err);
            setError(err.message || "Ocorreu um erro ao criar o cliente");
        } finally {
            setLoading(false);
        }
    };

    const handleBack = () => {
        navigate('/ClientsList');
    };

    return (
        <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
            <Paper elevation={3} sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <Button
                        startIcon={<ArrowBackIcon />}
                        onClick={handleBack}
                        sx={{ mr: 2 }}
                    >
                        {t("common:buttons.return")}
                    </Button>
                    <Typography variant="h4" component="h1">
                        {t("common:buttons.createClient")}
                    </Typography>

                </Box>

                <Box component="form" onSubmit={handleSubmit} sx={{ mt: 3 }}>
                    <Grid container spacing={3}>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                required
                                fullWidth
                                label={t("common:tableHeaders.name")}
                                name="Name"
                                value={client.Name}
                                onChange={handleChange}
                                variant="outlined"
                                error={!client.Name && error}
                                helperText={!client.Name && error ? "Nome é obrigatório" : ""}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                required
                                fullWidth
                                label={t("common:tableHeaders.contact")}
                                name="Contact"
                                value={client.Contact}
                                onChange={handleChange}
                                variant="outlined"
                                error={!client.Contact && error}
                                helperText={!client.Contact && error ? "Contato é obrigatório" : ""}
                            />
                        </Grid>
                    </Grid>

                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4 }}>
                        <Button
                            variant="contained"
                            color="primary"
                            type="submit"
                            disabled={loading}
                            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                            size="large"
                        >
                            {loading ? "A Salvar..." : t("common:buttons.createClient")}
                        </Button>
                    </Box>
                </Box>
            </Paper>

            <Snackbar
                open={success}
                autoHideDuration={6000}
                onClose={() => setSuccess(false)}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
                <Alert severity="success" sx={{ width: '100%' }}>
                    Cliente criado com sucesso!
                </Alert>
            </Snackbar>

            <Snackbar
                open={!!error}
                autoHideDuration={6000}
                onClose={() => setError(null)}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
                <Alert severity="error" sx={{ width: '100%' }}>
                    {error}
                </Alert>
            </Snackbar>
        </Container>
    );
}

export default NewClient;
