/* eslint-disable */
import {
  <PERSON>,
  Divider,
  <PERSON><PERSON><PERSON>,
  IconButton,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardHeader,
  CardContent,
  Link,
} from "@mui/material";
import { useTheme } from '@emotion/react';
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import { useState } from "react";
import {
  ExpandMore as ExpandMoreIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  Inventory as InventoryIcon,
  Download as DownloadIcon,
  ContentCopy as ContentCopyIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import getTableActionIntro from "./mocks/tableActionIntro";
import getTableActionCode from "./mocks/tableActionCode";
import getTableActionApplication from "./mocks/tableActionApplication";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import { styled } from '@mui/system';

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh",
})

const linkStyle = {
  marginRight: 20,
  alignItems: "center",
  textDecoration: "none",
};

const headCells = [
  { id: "example1", label: "Example1" },
  { id: "example2", label: "Example2" },
  { id: "example3", label: "Example3" },
  { id: "example4", label: "Example4", align: "center" },
  { id: "example5", align: "right" },
];

export default function TableActions({ open: openProp }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const [intro, setIntro] = useState(getTableActionIntro);
  const [code, setCode] = useState(getTableActionCode);
  const [apply, setApply] = useState(getTableActionApplication);

  const [open, setOpen] = useState(openProp);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box
        sx={{
          m: "auto",
          textAlign: "center",
        }}
      >
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h2>Table Actions</h2>
          <p>
            When a table exists, we may want to interact with it in different
            ways, we may want to clone or delete rows, change the state of a
            selection, download, etc. This component will display a bar with
            several options whenever an item from the table is selected.
          </p>
        </Box>
        <Toolbar
          sx={(theme) => {
            return {
              backgroundColor: theme.palette.primary.transparent[15],
              width: "80%",
              m: "auto",
              alignItems: "right",
              justifyContent: "right",
            };
          }}
        >
          <Tooltip title={t("common:buttons.download")}>
            <IconButton edge="end">
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title={t("common:buttons.clone")}>
            <IconButton edge="end">
              <ContentCopyIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title={t("common:buttons.remove")}>
            <IconButton edge="end">
              <DeleteIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Active">
            <IconButton>
              <InventoryIcon />
            </IconButton>
          </Tooltip>
        </Toolbar>
        <Typography
          color="textPrimary"
          sx={{
            display: "flex",
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            mt: 2,
            mb: 2,
          }}
        >
          The following code snippet demonstrates how Table Actions component is
          displayed:
          <br />
          You can copy the full source code to your src folder.
        </Typography>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={open ? code : intro} />
                <Tooltip
                  title={
                    !open ? "Show full source code" : "Hide full source code"
                  }
                >
                  <IconButton
                    onClick={handleToggle}
                    sx={{
                      color: `${theme.palette.primary.main}`,
                      "&:hover": {
                        backgroundColor: `${theme.palette.secondary.main}`,
                        color: "primary.main",
                      },
                      height: 35,
                      width: 35,
                      mt: 1,
                    }}
                  >
                    {!open ? <ExpandMoreIcon /> : <KeyboardArrowUpIcon />}
                  </IconButton>
                </Tooltip>
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={open ? code : intro}
              onValueChange={
                open ? (_code) => setCode(_code) : (_intro) => setIntro(_intro)
              }
              highlight={
                open
                  ? (_code) => highlight(_code, languages.js)
                  : (_intro) => highlight(_intro, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Typography
          color="textPrimary"
          sx={{
            display: "flex",
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            mt: 2,
            mb: 2,
          }}
        >
          You will have to import the component as below:
        </Typography>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={apply} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={apply}
              onValueChange={(_apply) => setApply(_apply)}
              highlight={(_apply) => highlight(_apply, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          sx={{
            m: "auto",
            textAlign: "justify",
            maxWidth: "80%",
            color: "textPrimary",
          }}
        >
          <p>
            Table Actions receives the following props and is used in
            conjunction with
            <Link
              style={linkStyle}
              href="https://mui.com/material-ui/react-table/"
            >
              Material UI
            </Link>
            components:
          </p>
          <ul>
            <li>
              <b>enableBulkActions</b> - If any item in the list is selected, a
              toolbar will appear at the top containing additional actions
              and/or information on the selected items. This prop receives a
              Boolean.
            </li>
            <li>
              <b>selectedCounter</b> - Returns the length of selected values.The
              Toolbar will show information about the number of records
              selected.{" "}
            </li>
            <li>
              <b>handleRemoveAction</b> - When clicking the delete button, an
              event will be launched. It could be an alert, a confirmation, etc.
            </li>
            <li>
              <b>activeTab</b> - In cases where our page has different Tabs,
              different functionalities may appear depending on the selected
              Tab. Example: if we have an "Active" Tab we can have an option to
              archive a record, on the other hand if we have an "Inactive" Tab
              we can have an option to delete a record.
            </li>
            <li>
              <b>handleGetFile</b> - In cases where we want to download a file,
              we can define an action. Example, file format to be downloaded,
              destination, etc.{" "}
            </li>
            <li>
              <b>selectedDocument</b> - Returns selected values;
            </li>
            <li>
              <b>handleCloneAction</b> - Allows us to clone/duplicate a selected
              row;
            </li>
            <li>
              <b>enableRemove</b> - Shows Delete option;
            </li>
            <li>
              <b>enableArchived </b> - Shows archive option;
            </li>
            <li>
              <b>handleArchivedAction </b> - After selecting a row, pressing the
              button to archive will launch an event. We can archive directly or
              open a Modal.{" "}
            </li>
            <li>
              <b>customActions</b> - i.e: Cancel / Confirm an operation{" "}
            </li>
          </ul>
        </Box>
      </Box>
      <Box sx={{ textAlign: "center", mt: 5 }}>
        See also:
        <br />
        <Link style={linkStyle} href="/Components/FunctionalTableExample/">
          Functional Table Example
        </Link>
        <Link style={linkStyle} href="/Components/TableLoading/">
          Table Loading
        </Link>
        <Link style={linkStyle} href="/Components/TableActions/">
          Table Actions
        </Link>
        <Link style={linkStyle} href="/Components/TableHeader/">
          Table Header
        </Link>
      </Box>
      <ChangeLogEntry version="V1.0.0" changeLog={ChangeLog()} />
    </Box>
  );
}
const ChangeLog = () => {
  const theme = useTheme();
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content: "Imported Table Actions Component.",
      className: chips.green,
    },
  ];

  return changeLog;
};
