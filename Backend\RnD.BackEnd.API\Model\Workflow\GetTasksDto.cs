﻿using RnD.BackEnd.API.Model.Workflow.Enums;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Get tasks DTO class
    /// </summary>
    public class GetTasksDto
    {
        /// <summary>
        /// Gets or sets the type of the source.
        /// </summary>
        /// <value>
        /// The type of the source.
        /// </value>
        public string SourceType { get; set; }

        /// <summary>
        /// Gets or sets the user identifier.
        /// </summary>
        /// <value>
        /// The user identifier.
        /// </value>
        public string UserId { get; set; }

        /// <summary>
        /// Gets or sets the workflow task status.
        /// </summary>
        /// <value>
        /// The workflow task status.
        /// </value>
        public TaskStatus? TaskStatus { get; set; }

        /// <summary>
        /// Represents an event that is raised when a task either successfully or unsuccessfully completes.
        /// </summary>
        /// <value>
        ///   <c>true</c> if [task completed]; otherwise, <c>false</c>.
        /// </value>
        public bool TaskCompleted { get; set; }

        /// <summary>
        /// Gets or sets the name of the step.
        /// </summary>
        /// <value>
        /// The name of the step.
        /// </value>
        public string StepName { get; set; }

        /// <summary>
        /// Gets or sets the name of the workflow.
        /// </summary>
        /// <value>
        /// The name of the workflow.
        /// </value>
        public string WorkflowName { get; set; }

        /// <summary>
        /// Gets or sets the workflow status.
        /// </summary>
        /// <value>
        /// The workflow status.
        /// </value>
        public WorkflowStatus? WorkflowStatus { get; set; }

        /// <summary>
        /// Gets or sets the sort field.
        /// </summary>
        /// <value>
        /// The sort field.
        /// </value>
        public string SortField { get; set; }

        /// <summary>
        /// Gets or sets the sort direction.
        /// </summary>
        /// <value>
        /// The sort direction.
        /// </value>
        public string SortDirection { get; set; }

        /// <summary>
        /// Gets or sets the items per page.
        /// </summary>
        /// <value>
        /// The items per page.
        /// </value>
        public int? ItemsPerPage { get; set; }

        /// <summary>
        /// Gets or sets the current page.
        /// </summary>
        /// <value>
        /// The current page.
        /// </value>
        public int? CurrentPage { get; set; }
    }
}
