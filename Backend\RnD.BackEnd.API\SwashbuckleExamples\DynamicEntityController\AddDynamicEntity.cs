﻿using System.Collections.Generic;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController
{
    /// <summary>
    /// Add dynamic Entity request example
    /// </summary>
    public class AddDynamicEntity : IMultipleExamplesProvider<Dictionary<string, object>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The examples of requests to Add Dynamic Entity.
        /// </returns>
        public IEnumerable<SwaggerExample<Dictionary<string, object>>> GetExamples()
        {
            yield return SwaggerExample.Create("Company", new Dictionary<string, object>
            {
                {"entityId", "dive1" },
                {"companyId", "diveCenterPhi2" },
                {"name", "Dive Center Koh Phi Phi" },
                {"type", "Dive Center" },
                {"review", 4.5 },
                {"numberOfReviews", 104 },
                {"location", "Koh Phi Phi Island" },
                {"country", "Thailand" }
            });
            yield return SwaggerExample.Create("Car", new Dictionary<string, object>
            {
                {"entityId", "car1" },
                {"brand", "BMW" },
                {"brandId", "bmw"},
                {"modelName", "Serie 3"},
                {"fuelType", "Diesel"},
                {"version", "M"},
                {"year", 2022}
            });
            yield return SwaggerExample.Create("Person", new Dictionary<string, object>
            {
                {"entityId", "person1" },
                {"name", "Luís Rato" },
                {"profession", "Software Engineer" },
                {"professionId", "softwareEngineer"},
                {"Role", "Trainee"},
                {"nationality", "Portuguese"},
                {"gender", "M"},
                {"year", 2022}
            });
        }
    }
}