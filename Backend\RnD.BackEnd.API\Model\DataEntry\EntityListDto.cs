﻿using System.Collections.Generic;

namespace RnD.BackEnd.API.Model.DataEntry
{
    /// <summary>
    /// Data Entry entity list DTO class
    /// </summary>
    public class EntityListDto
    {
        /// <summary>
        /// Gets or sets the entities.
        /// </summary>
        /// <value>
        /// The entities.
        /// </value>
        public IList<EntityDto> Entities { get; set; }

        /// <summary>
        /// Gets or sets the total rows.
        /// </summary>
        /// <value>
        /// The total rows.
        /// </value>
        public int TotalRows { get; set; }
    }
}
