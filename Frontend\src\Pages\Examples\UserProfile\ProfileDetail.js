/*eslint-disable no-shadow*/
import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import clsx from "clsx";
import {
  Button,
  Card,
  TextField,
  Grid,
  Divider,
  FormControl,
  FormControlLabel,
  Checkbox,
  Typography,
  CardContent,
  CardActions,
  Box,
  ListItem,
  ListItemAvatar,
  Avatar,
  ListItemText,
  ListItemIcon,
  IconButton,
  Autocomplete
} from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useParams } from 'react-router-dom';
import ConfirmModal from "../../../Components/Modal/ConfirmModal";
import Scrollbar from "../../../Components/Scrollbar";
import { useNavigate } from 'react-router';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { userAPI } from "../../../API/userAPI";
import { styled } from '@mui/material/styles';

const StyledScrollbar = styled(Scrollbar)({
  width: "100%",
  maxHeight: "55vh",
  "&&&& .ps__rail-x": {
    display: "none",
  },
})

const StyledFromControl = styled(FormControl)(({ theme }) => ({
  width: "100%",
  "&:not(:last-of-type)": {
    marginBottom: theme.spacing(4)
  }
}))

const ModelRoles = () => {
  const [roles] = useState([]);
  const [selectedRole, setSelectedRole] = useState([]);
  const [allPermissions, setAllPermissions] = useState([]);
  const [newUsers] = useState([]);
  const [users, setUsers] = useState([]);
  const [roleName, setRoleName] = useState("");
  const [selectedPerm, setSelectedPerm] = useState([]);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [submitting] = useState(false);
  const navigate = useNavigate();
  const [newUser, setNewUser] = useState(null);
  const [usersList, setUsersList] = useState([]);
  const [usersNumber, setUsersNumber] = useState(0);
  const urlParams = useParams();
  const params = useParams();
  const { t } = useTranslation();

  function handleSetPermissions(list) {
    if (!list) return;

    const _selectedPerm = [[], []];

    list.forEach(itemList => {
      if (itemList.length > 0) {
        itemList.forEach(obj => {
          if (obj.parent.checked) {
            _selectedPerm[0].push(obj.parent.id);
            obj.children.forEach(child => {
              _selectedPerm[0].push(child.id);
            });
          } else {
            obj.children.forEach(child => {
              if (child.checked) {
                _selectedPerm[1].push(child.id);
              }
            });
          }
        });
      }
    });

    setSelectedPerm(_selectedPerm);
  }

  function splitToChunks(array, parts, isPermission) {
    if (array.length > 0) {
      const result = [];
      for (let i = parts; i >= 0; i--) {
        result.push(array.splice(0, Math.ceil(array.length / i)));
      }
      if (isPermission) {
        handleSetPermissions(result);
      }
      return result;
    }
  }

  const getRoleConfiguration = useCallback(async () => {
    //handleGetRoleConfiguration
    try {
      const result = await userAPI.GetRoleConfiguration(params.roleID, params['*' === "profile/new"]);
      if (result && result.error === false) {
        const object = result.value.find(obj => obj.roleIDs === params.roleID)
        setRoleName(object.roleName);
        const _userList = object.userList;
        const permissions = splitToChunks(object.permissionTypes, 1, true);
        setAllPermissions(permissions);
        setUsersNumber(object.allUsers.length);
        setUsers(_userList);
        setUsersList(object.allUsers);
      } else {
        result.exceptionMessages.messages.forEach(element => {
          toast.error(element.description);
        });
      }
    } catch (err) {
      console.error(err);
    }
  }, [params.roleID]);

  function handleCloseDeleteModal() {
    setOpenDeleteModal(false);
  }

  console.log(allPermissions)

  const handleValidateRoleName = () => {
    if (roleName === null || roleName === undefined) {
      return false
    }
    return true
  }

  const handleValidatePermissions = () => {
    if (!selectedPerm[0] || selectedPerm[0].length === 0) {
      return false
    }
    return true
  }

  function handleChangeRoleName(e) {
    const { target } = e;
    if (target) {
      const { value } = target;
      let _roleName = roleName;
      _roleName = value;
      setRoleName(_roleName);
    }
  }

  function handleCancel() {
    navigate(`/Examples/profilesList`);
  }

  function handleAllPermissionCheckbox(index, permission) {
    const _selectedPerm = [...selectedPerm];
    const exists =
      _selectedPerm[index] &&
      _selectedPerm[index].find((x) => x === permission.parent.id);
    if (!_selectedPerm[index]) {
      if (index === 0) {
        _selectedPerm.unshift([]);
      } else {
        _selectedPerm.push([], []);
      }
    }
    if (!exists) {
      _selectedPerm[index].push(permission.parent.id);
      for (const child of permission.children) {
        _selectedPerm[index].push(child.id);
      }
    } else {
      const excludeIDs = [permission.parent.id];
      for (const c of permission.children) {
        excludeIDs.push(c.id);
      }
      _selectedPerm[index] = _selectedPerm[index].filter(
        (x) => !excludeIDs.includes(x)
      );
    }
    setSelectedPerm(_selectedPerm);
  }

  function handlePermissionCheckbox(index, id, permission) {
    const _selectedPerm = [...selectedPerm];

    if (!_selectedPerm[index]) {
      _selectedPerm[index] = index === 0 ? [] : [[], []];
    }

    const isSelected = _selectedPerm[index].includes(id);
    const allChildrenSelected = permission.children.every(child => _selectedPerm[index].includes(child.id));

    if (!isSelected) {
      _selectedPerm[index].push(id);

      if (allChildrenSelected && !_selectedPerm[index].includes(permission.parent.id)) {
        _selectedPerm[index].push(permission.parent.id);
      }
    } else {
      _selectedPerm[index] = _selectedPerm[index].filter(item => item !== id);
      if (allChildrenSelected) {
        _selectedPerm[index] = _selectedPerm[index].filter(item => item !== permission.parent.id);
      }
    }
    setSelectedPerm(_selectedPerm);
  }

  function isChecked(index, perm) {
    if (selectedPerm[index]) {
      const exists = selectedPerm[index].find((x) => x === perm.id);
      if (exists) {
        return true;
      }
      return false;
    }
    return false;
  }

  async function handleAddNewUser() {
    if (newUser) {
      const _users = users ? [...users] : [];
      const _userList = [...usersList];
      const _newUsers = [...newUsers];
      _userList.splice(
        _userList.map((item) => item.userID).indexOf(newUser.userID),
        1
      );
      _users.push(newUser);
      _users.sort((x, y) => x.name.localeCompare(y.name));
      _newUsers.push(newUser);
      setUsersNumber(usersNumber - 1);
      setUsersList(_userList);
      setUsers(_users);
      setNewUser(null);
    }
  }

  const removeUser = (index) => {
    const _users = [...users];
    const _userList = [...usersList];
    const user = _users[index];
    _userList.push(user);
    _userList.sort((x, y) => x.name.localeCompare(y.name));
    _users.splice(index, 1);
    setUsersNumber(usersNumber + 1);
    setUsersList(_userList);
    setUsers(_users);
  };

  useEffect(() => {
    const id = !urlParams.id || urlParams.id === "NewRole" ? undefined : urlParams.id;
    setSelectedRole(id);
    getRoleConfiguration();
  }, []);

  return (
    <>
      <Box sx={{ py: 3 }}>
        <Card>
          <CardContent>
            <Grid
              container
              sx={{
                padding: "16px"
              }}
            >
              <Grid
                item
                xs={6}
              >
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    width: "100%",
                    mt: 1
                  }}
                >
                  <TextField
                    variant="outlined"
                    name="RoleDescription"
                    value={roleName || ""}
                    onChange={(e) => handleChangeRoleName(e)}
                    label={t('common:form.roleDescription')}
                    required
                    sx={{
                      minWidth: "250px",
                      mb: 0
                    }}
                    error={!handleValidateRoleName() && submitting}
                    helperText={!handleValidateRoleName() && submitting ? t('common:warnings.roleDescription') : ""}
                  />
                </Box>
                <Grid
                  container
                  spacing={2}
                >
                  <Grid
                    item
                    xs={12}
                    sx={{
                      borderRight: "solid 1px #eee",
                      marginRight: "16px",
                      paddingRight: "12px"
                    }}
                  >
                    <Divider />
                    <Typography
                      variant="h2"
                      sx={{
                        fontWeight: "bold",
                        padding: "12px"
                      }}
                    >
                      {t('common:labels.permissions')}
                      {!handleValidatePermissions() && submitting ?
                        <Typography
                          color="error"
                          sx={{
                            fontSize: "13px"
                          }}
                        >
                          {t('common:warnings.onePermission')}
                        </Typography> : null}
                    </Typography>
                    <StyledScrollbar>
                      <Grid
                        container
                        spacing={3}
                        direction="row"
                        alignItems="flex-start"
                      >
                        {
                          allPermissions && allPermissions.length > 0
                            ? allPermissions.map((permList, index) => (
                              permList.length > 0
                                ? permList.map((permission) => (
                                  <Grid
                                    item
                                    key={`${index}` + Math.floor(Math.random() * 10000)}
                                    xs={6}
                                  >
                                    <StyledFromControl
                                      component="fieldset"
                                      key={permission.id + "_" + Math.floor(Math.random() * 10000)}
                                    >
                                      <FormControlLabel
                                        sx={{ ml: "0px" }}
                                        control={
                                          <Checkbox
                                            checked={isChecked(
                                              index,
                                              permission.parent
                                            )}
                                            onChange={() =>
                                              handleAllPermissionCheckbox(
                                                index,
                                                permission
                                              )}
                                          />
                                        }
                                        label={
                                          <Typography
                                            color="textPrimary"
                                            sx={{
                                              textTransform: "uppercase"
                                            }}
                                          >
                                            {permission.parent.shortDesc}
                                          </Typography>
                                        }
                                      />
                                      {
                                        permission.children
                                          ? permission.children.map((child) => (
                                            <FormControlLabel
                                              key={child.id}
                                              control={
                                                <Checkbox
                                                  checked={isChecked(
                                                    index,
                                                    child
                                                  )}
                                                  onChange={() =>
                                                    handlePermissionCheckbox(
                                                      index,
                                                      child.id,
                                                      permission
                                                    )}
                                                />
                                              }
                                              label={
                                                <Typography
                                                  color="textPrimary"
                                                  variant="h9"
                                                >
                                                  {child.shortDesc}
                                                </Typography>
                                              }
                                            />
                                          ))
                                          : null
                                      }
                                    </StyledFromControl>
                                  </Grid>
                                ))
                                :
                                null
                            ))
                            : null
                        }
                      </Grid>
                    </StyledScrollbar>
                  </Grid>
                </Grid>
              </Grid>
              <Grid
                item
                xs={6}
              >
                <Grid
                  container
                  spacing={1}
                  sx={{ margin: 0 }}
                >
                  <Grid
                    item
                    xs={12}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        width: "100%",
                        mb: 2
                      }}
                    >
                      <Autocomplete
                        options={usersList}
                        value={newUser}
                        getOptionLabel={(option) => option.name}
                        onChange={(event, newValue) => setNewUser(newValue)}
                        sx={{
                          minWidth: "250px",
                          display: "inline-flex"
                        }}
                        renderInput={(_params) => (
                          <TextField
                            {..._params}
                            label={`${t('common:form.userSearch')} (${usersNumber})`}
                            variant="outlined"
                            placeholder={t('common:search.search')}
                          />
                        )}
                      />
                      <Button
                        sx={{
                          marginLeft: "auto",
                          marginRight: 2
                        }}
                        startIcon={<AddIcon fontSize="small" />}
                        variant="contained"
                        onClick={handleAddNewUser}
                      >
                        {t('common:buttons.user')}
                      </Button>
                    </Box>
                    <Divider />
                  </Grid>
                  <Scrollbar>
                    <Grid
                      item
                      xs={12}
                      container
                      spacing={2}
                      direction="row"
                      alignItems="flex-start"
                      margin={0}
                    >
                      {users && users.length > 0 ?
                        users.map((user, index) => (
                          <Grid
                            item
                            key={`users_${index}`}
                            xs={4}
                            sx={{ maxWidth: "100%" }}
                          >
                            <ListItem>
                              <ListItemAvatar>
                                <Avatar
                                  src=""
                                  alt={user.name}
                                />
                              </ListItemAvatar>
                              <ListItemText primary={user.name} />
                              <ListItemIcon>
                                <IconButton
                                  edge="end"
                                  aria-label="delete"
                                  onClick={() => removeUser(index)}
                                >
                                  <DeleteIcon sx={{ height: "0.8em", width: "0.8em" }} />
                                </IconButton>
                              </ListItemIcon>
                            </ListItem>
                          </Grid>
                        ))
                        : null}
                    </Grid>
                  </Scrollbar>
                </Grid>
              </Grid>
            </Grid>
            <Divider />
          </CardContent>
          <CardActions>
            <Button
              startIcon={<div className={clsx("icon-add")} />}
              variant="outlined"
              onClick={handleCancel}
              color="primary"
              id="closeButton"
            >
              {t('common:buttons.cancel')}
            </Button>
            <Button
              variant="outlined"
              onClick={() => setOpenDeleteModal(true)}
              disabled={!selectedRole}
            >
              {t('common:buttons.delete')}
            </Button>
            <Button
              startIcon={<div className={clsx("icon-add")} />}
              variant="contained"
            >
              {t('common:buttons.save')}
            </Button>
          </CardActions>
        </Card>
      </Box>
      <ConfirmModal
        open={openDeleteModal}
        selectedRoles={roles.filter((x) => selectedRole.indexOf(x.ID) !== -1)}
        onClose={handleCloseDeleteModal}
        title={t('common:warnings.deleteProfile')}
        message={t('common:warnings.deleteMessageUser')}
      />
    </>
  );
};

export default ModelRoles;
