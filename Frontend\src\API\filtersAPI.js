import handleMethod from '../Utils/APIutils/handleMethod';

class FiltersAPI {
  async getAvailableFilters({ context, id }) {
    const _id = id ? `&Id=${id}` : '';

    const Input = {
      type: 'GET',
      method: `GetAvailableFilters?Context=${context}${_id}`
    };

    const response = await handleMethod(Input);

    return response;
  }

  async getAvailableFilterData({ context = "PROFILE", id, key }) {
    const _id = id ? `&Id=${id}` : '';
    const Input = {
      type: 'GET',
      method: `GetAvailableFilterData?Context=${context}${_id}&key=${key}`
    };

    const response = await handleMethod(Input);

    return response;
  }
}
export const filtersAPI = new FiltersAPI();
