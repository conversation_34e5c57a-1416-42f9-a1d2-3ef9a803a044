import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Box, Stepper, Step, StepLabel, StepConnector, Typography } from '@mui/material';
import { styled } from '@mui/system';

const CustomStep = styled(Step)(({ theme }) => ({
  "& .MuiStepLabel-label": {
    color: theme.palette.text.primary,
    fontWeight: 600
  }
}));

const CustomStepLabel = styled(Box)(({ theme }) => ({
  position: "relative",
  color: theme.palette.primary.grad1,
  padding: "23px",
  width: "15px",
  borderRadius: "100%",
  height: "15px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  zIndex: 1,
  paddingBottom: "1px",
  fontWeight: 'bold',
}));

const ColorlibConnector = styled(StepConnector)(({ theme }) => ({
  alternativeLabel: {
    top: 22,
  },
  active: {
    '& $line': {
      backround: "lightgray"
    },
  },
  completed: {
    '& $line': {

    },
  },
  line: {
    height: 1,
    border: 0,
    backgroundColor: theme.palette.text.primary,
    borderRadius: 1,
  },
}));

const CustomStepper = ({ steps, showQualitative }) => {
  const [stepsCollection] = useState(steps);
  return (
    <div style={{
      margin: "auto",
      width: "40vw",
      minWidth: "768px"
    }}
    >
      <Stepper
        alternativeLabel
        connector={<ColorlibConnector />}
      >
        {stepsCollection.map((s, i) => (
          <CustomStep
            key={s.value + "_" + i.toString()}
          >
            <StepLabel
              StepIconComponent={
                () => {
                  <CustomStepLabel>
                    {s.value}
                  </CustomStepLabel>
                }
              }
            >
              {s.title}
              {showQualitative && s.note && s.note !== "" && (
                <>
                  <br />
                  <Typography variant="caption">{s.note}</Typography>
                </>
              )}
            </StepLabel>
          </CustomStep>
        ))}
      </Stepper>
    </div>
  );
};

CustomStepper.propTypes = {
  steps: PropTypes.array.isRequired,
  showQualitative: PropTypes.bool
};

export default CustomStepper;
