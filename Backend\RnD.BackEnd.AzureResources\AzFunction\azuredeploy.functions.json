{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"tenantId": {"type": "string", "defaultValue": "[subscription().tenantId]", "metadata": {"description": "Specifies the Azure Active Directory tenant ID that should be used for authenticating requests to the key vault. Get it by using Get-AzSubscription cmdlet."}}, "location": {"defaultValue": "[resourceGroup().location]", "type": "string", "metadata": {"description": "Provide the location for resources."}}, "function-Name": {"type": "string", "minLength": 1}, "app-service-Name": {"type": "string", "minLength": 1}, "always-on": {"type": "bool", "defaultValue": true, "allowedValues": [true, false], "metadata": {"description": "Specifies whether the App Services should have AlwaysOn enabled."}}, "keyvault-resourcegroup": {"type": "string", "minLength": 1}, "key-vault-Name": {"type": "string", "minLength": 1}, "keyVault-secretsPermissions-app-services": {"type": "array", "defaultValue": ["Get"], "metadata": {"description": "Specifies the permissions to secrets in the vault. Valid values are: all, get, list, set, delete, backup, restore, recover, and purge."}}}, "variables": {}, "resources": [{"type": "Microsoft.Web/sites", "apiVersion": "2018-11-01", "name": "[parameters('function-Name')]", "location": "[parameters('location')]", "identity": {"type": "SystemAssigned"}, "kind": "functionapp", "properties": {"enabled": true, "serverFarmId": "[resourceId('Microsoft.Web/serverfarms', parameters('app-service-Name'))]", "siteConfig": {"appSettings": [{"name": "FUNCTIONS_WORKER_RUNTIME", "value": "dotnet"}, {"name": "FUNCTIONS_EXTENSION_VERSION", "value": "~4"}]}}, "tags": {"displayName": "rnd-azure-function"}}, {"type": "Microsoft.Web/sites/config", "apiVersion": "2018-11-01", "name": "[concat(parameters('function-Name'), '/web')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Web/sites', parameters('function-Name'))]"], "tags": {"displayName": "rnd-azure-function-config"}, "properties": {"alwaysOn": "[parameters('always-on')]", "cors": {"allowedOrigins": ["https://portal.azure.com"], "supportCredentials": false}}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2019-05-01", "name": "keyvault-add-access-policies", "resourceGroup": "[parameters('keyvault-resourcegroup')]", "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": [{"type": "Microsoft.KeyVault/vaults/accessPolicies", "name": "[concat(parameters('key-vault-Name'), '/add')]", "apiVersion": "2016-10-01", "properties": {"accessPolicies": [{"tenantId": "[parameters('tenantId')]", "objectId": "[reference(concat('Microsoft.Web/sites/', parameters('function-Name')), '2018-11-01', 'Full').identity.principalId]", "permissions": {"secrets": "[parameters('keyVault-secretsPermissions-app-services')]"}}]}, "tags": {"displayName": "keyvault-add-access-policies"}}]}}}]}