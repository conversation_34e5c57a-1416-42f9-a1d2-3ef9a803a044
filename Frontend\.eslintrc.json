{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "airbnb", "airbnb/hooks"], "overrides": [], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react"], "rules": {"react/jsx-no-constructed-context-values": "off", "react-hooks/exhaustive-deps": "off", "react/jsx-no-useless-fragment": "off", "react/function-component-definition": "off", "linebreak-style": "off", "class-methods-use-this": "off", "comma-dangle": "off", "import/no-cycle": "off", "import/no-extraneous-dependencies": "off", "import/order": "off", "import/prefer-default-export": "off", "jsx-a11y/anchor-is-valid": "off", "max-len": "off", "no-console": "off", "no-param-reassign": "off", "no-plusplus": "off", "no-return-assign": "off", "object-curly-newline": "off", "react/forbid-prop-types": "off", "react/jsx-filename-extension": "off", "react/jsx-max-props-per-line": [1, {"when": "always"}], "react/jsx-props-no-spreading": "off", "react/react-in-jsx-scope": "off", "react/require-default-props": "off", "quotes": "off", "arrow-parens": "off", "react/jsx-indent-props": "off", "react/jsx-no-bind": "off", "react/jsx-indent": "off", "indent": "off", "no-restricted-syntax": "off", "react/jsx-wrap-multilines": "off", "operator-linebreak": "off", "spaced-comment": "off", "prefer-template": "off", "no-underscore-dangle": "off", "camelcase": "off", "quote-props": "off", "arrow-body-style": "off", "react/jsx-one-expression-per-line": "off", "no-nested-ternary": "off", "react/no-array-index-key": "off", "object-shorthand": "off", "no-await-in-loop": "off", "react/jsx-pascal-case": "off", "implicit-arrow-linebreak": "off", "consistent-return": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-static-element-interactions": "off", "react/jsx-closing-tag-location": "off", "react/jsx-key": "error", "no-eval": "warn", "jsx-a11y/label-has-associated-control": "off", "no-unneeded-ternary": "off", "semi": "off", "block-spacing": "off", "no-lone-blocks": "off", "no-unreachable": "off", "no-lonely-if": "off", "no-labels": "warn", "no-continue": "warn", "no-use-before-define": "off"}}