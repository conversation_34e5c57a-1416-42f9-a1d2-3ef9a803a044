import { useTranslation } from "react-i18next";
import { TextField, FormControl, FormGroup, FormControlLabel, Checkbox, Typography, Divider, Box } from "@mui/material";
import PropTypes from 'prop-types';

const CheckboxField = (props) => {
    const {
        setFieldSearch,
        handleSelectedValue,
        handleSelectValues,
        filteredSelectedTypeDetails,
        selectedValues,
    } = props;
    const { t } = useTranslation();

    const isIndeterminate = () => {
        return selectedValues?.length > 0 && selectedValues?.length < filteredSelectedTypeDetails.length;
    };

    const isChecked = () => {
        return selectedValues?.length === filteredSelectedTypeDetails?.length
    };

    const handleChange = (v) => (e) => {
        const val = e.target.value;
        const _selectedValues = [...selectedValues];
        const match = _selectedValues.findIndex(x => x.value === val);
        if (match >= 0) {
            _selectedValues.splice(match, 1);
        } else {
            _selectedValues.push(v);
        }
        handleSelectValues(_selectedValues);
    }

    return (<>
        <TextField
            label={t("common:tableActions.search")}
            sx={{
                pb: 2
            }}
            fullWidth
            onChange={(e) => setFieldSearch(e?.target?.value)}
        />
        <FormControl
            component="fieldset"
            sx={{
                width: "100%"
            }}
        >
            <FormGroup
                aria-label="select-all"
                row
            >
                <FormControlLabel
                    onChange={() => handleSelectedValue(filteredSelectedTypeDetails)}
                    control={
                        <Checkbox
                            color="primary"
                            checked={isChecked()}
                            indeterminate={isIndeterminate()}
                        />
                    }
                    color="primary"
                    label={
                        <Typography
                            variant="h6"
                        >
                            {t("common:tableActions.select_all")}
                        </Typography>
                    }
                    labelPlacement="end"
                />
            </FormGroup>
        </FormControl>
        <Divider
            sx={{
                mb: 1
            }}
        />
        <Box
            sx={{
                overflow: "auto",
                maxHeight: "300px"
            }}
        >
            <FormControl
                component="fieldset"
                sx={{
                    width: "100%"
                }}
            >
                {
                    filteredSelectedTypeDetails && filteredSelectedTypeDetails.length > 0 ?
                        filteredSelectedTypeDetails.map((v, i) => (
                            <FormGroup
                                key={i}
                                aria-label="select"
                                row
                            >
                                <FormControlLabel
                                    value={v.value || v.label}
                                    onChange={handleChange(v)}
                                    control={
                                        <Checkbox
                                            color="primary"
                                            checked={selectedValues.find(x => x.value === v.value) !== undefined}
                                        />
                                    }
                                    label={v.elementLabel || ""}
                                    labelPlacement="end"
                                />
                            </FormGroup>
                        ))
                        :
                        <Typography
                            variant="disabled"
                        >
                            {t("common:tableActions.noResults")}
                        </Typography>
                }
            </FormControl>
        </Box>
    </>);
}

CheckboxField.propTypes = {
    setFieldSearch: PropTypes.func,
    handleSelectedValue: PropTypes.func,
    handleSelectValues: PropTypes.func,
    filteredSelectedTypeDetails: PropTypes.array,
    selectedValues: PropTypes.object
}

export default CheckboxField;
