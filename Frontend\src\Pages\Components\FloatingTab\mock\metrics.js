const getMetrics = () => {
    return `
//(OPTIONAL)
import React from 'react';
import { Box, Slider, CircularProgress, LinearProgress, Typography } from '@mui/material';
import makeStyles from '@emotion/styled';

const useStyles = makeStyles(theme => ({
    root: {
        height: theme.spacing(4),
    },
    rail: {
        height: theme.spacing(4),
    },
    track: {
        height: theme.spacing(4),
    },
    mark: {
        height: theme.spacing(4),
        backgroundColor: theme.palette.background.default,
    },
    thumb: {
        display: 'none',
    },
    rootCircular: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: "center",
        padding: theme.spacing(2),
    },
    circle: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        position: "relative",
        width: "80px",
        height: "80px"
    },
    bar: {
        gridRow: 1,
        gridColumn: 1,
        margin: '0 auto',
        zIndex: 1,
    },
    circularTrack: {
        gridRow: 1,
        gridColumn: 1,
        margin: '0 auto',
        color: "gray"
    },
}));

const Metrics = ({ data, isLoading, max = 5, thickness = 5 }) => {
    const classes = useStyles();
    return (
        <>
            <Typography>
                <b>
                    {data.title}
                </b>
            </Typography>
            <Typography
                variant="subtitle2"
            >
                {data.subtitle}
            </Typography>
            {
                true ? <>
                    <Box
                        className={classes.rootCircular}
                    >
                        <Box
                            className={classes.circle}
                        >
                            <Box
                                sx={{
                                    width: "100%",
                                    height: "100%",
                                    position: "relative"
                                }}
                            >
                                <CircularProgress
                                    sx={{
                                        position: "relative",
                                        zIndex: 1,
                                    }}
                                    variant="determinate"
                                    thickness={thickness}
                                    size="100%"
                                    value={data.type && data.type === "%" ? parseFloat(data.value) : parseFloat((data.value / (data.max ? data.max : max)) * 100, 10)}
                                />
                                <CircularProgress
                                    sx={{
                                        position: "absolute",
                                        top: 0,
                                        left: 0,
                                        color: "lightgray"
                                    }}
                                    variant="determinate"
                                    thickness={thickness}
                                    size="100%"
                                    value={100}
                                />
                            </Box>
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    position: "absolute"
                                }}
                            >
                                <div
                                    style={{
                                        position: "relative",
                                        top: isLoading ? "1px" : "0px"
                                    }}
                                >
                                    <b>{data.value}</b>
                                </div>
                                {(isLoading) && <LinearProgress sx={{ height: "1px", display: "flex", width: "100%" }} />}
                            </Box>
                        </Box>

                    </Box>
                    <Box sx={{
                        textAlign: "center"
                    }}
                    >
                        <Typography><b>{data.note}</b></Typography>
                    </Box> </>
                    :
                    <Slider
                        value={data.value}
                        min={0}
                        max={5}
                        marks
                        valueLabelDisplay="off"
                        classes={{
                            root: classes.root,
                            rail: classes.rail,
                            track: classes.track,
                            thumb: classes.thumb,
                            mark: classes.mark,
                        }}
                    />
            }
        </>
    );
};

Metrics.propTypes = {
    data: PropTypes.object,
    isLoading: PropTypes.oneOfType([
        PropTypes.bool,
        PropTypes.number
    ]),
    max: PropTypes.number,
    thickness: PropTypes.number
};

export default Metrics;
    `;
}

export default getMetrics;
