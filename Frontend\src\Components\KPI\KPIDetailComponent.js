/* eslint-disable no-unused-vars*/
import { useEffect, useState, useCallback } from 'react';
import { useParams } from 'react-router';
import { Grid, Box } from '@mui/material';
import toast from 'react-hot-toast';
import { goalsAPI } from '../../API/goalsAPI';
import PropTypes from 'prop-types';
import CardKPI from './CardKPI';

const KPIDetailComponent = (props) => {
  const params = useParams();
  const [isLoading, setIsLoading] = useState(false);
  const { userView, username, periodicView, evaluationCycleID } = props;
  const [momentsList, setMomentslist] = useState([]);
  const [selectedMoment, setSelectedMoment] = useState({ text: "Ciclo de Avaliação" });
  const [weightKPIs, setWeightKPIs] = useState([]);

  const goalsUsername = username ? username : (params && params.user ? params.user : undefined);

  /*API call to get Employee goals
    params: username, evaluationMomentID, isCurrentUser = false
  */
  const getEmployeeGoals = useCallback(async (cycle) => {
    try {
      setIsLoading(true);
      const data = await goalsAPI.GetEmployeeGoalsDetails(goalsUsername, (goalsUsername === undefined) || userView, cycle.value);
      if (!data.error) {
        if (data.value.individualWeights) {
          setWeightKPIs([
            //Information that will appear on the cards
            {
              label: "Lorem ipsum",
              description: "(Lorem ipsum dolor sit amet)",
              value: data.value.strategyPercentage,
              hidden: data.value.hideEmptyWeights && data.value.strategyPercentage === 0
            },
            {
              label: "Pellentesque",
              description: "(Lorem ipsum dolor sit amet)",
              value: data.value.areaPercentage,
              hidden: data.value.hideEmptyWeights && data.value.areaPercentage === 0
            },
            {
              label: "Quisque",
              description: "(Lorem ipsum dolor sit amet)",
              value: data.value.individualPercentage,
              hidden: data.value.hideEmptyWeights && data.value.individualPercentage === 0,
              extraContent: {
                variant: "linearContent",
                description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit",
                tooltip: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed eu odio vulputate, vestibulum magna id, maximus risus.",
                progress: data.value.individualAssignedPercentage
              }
            }
          ]);
        }
        setIsLoading(false);
      }
    } catch (err) {
      console.log(err);
      toast.error("Erro ao obter objectivos");
    }
  }, []);

  function handleSetMomentsList(list) {
    const mList = [];
    for (const obj of list) {
      mList.push({
        text: obj.evaluationMomentName,
        value: obj.evaluationMomentID,
        key: obj.evaluationMomentID
      });
    }
    setMomentslist(mList);
    if (!selectedMoment?.value && mList.length && !periodicView) {
      if (!params?.evaluationCycle) {
        setSelectedMoment(mList[0]);
      } else {
        const moment_ = mList?.find(x => x.value === params.evaluationCycle);
        if (moment_ !== undefined) {
          setSelectedMoment(moment_);
        } else if (mList.length > 0) {
          setSelectedMoment(mList[0]);
        }
      }
    }
  }

  async function handleGetGoalTypes() {
    const data = await goalsAPI.GetGoalTypes("INDIVIDUAL");
    if (!data.error) {
      handleSetMomentsList(data.value.evaluationMoments);
    } else {
      toast.error(data.description);
    }
  }

  useEffect(() => {
    handleGetGoalTypes();
  }, []);

  useEffect(() => {
    if (selectedMoment?.value) {
      getEmployeeGoals(selectedMoment);
    }
  }, [selectedMoment]);

  useEffect(() => {
    if (periodicView && evaluationCycleID && momentsList.length) {
      const moment_ = momentsList.filter(x => x.value === evaluationCycleID)[0];
      setSelectedMoment(moment_);
    }
  }, [evaluationCycleID, momentsList]);

  return (
    <Grid
      container
      spacing={2}
    >
      {
        weightKPIs.filter(x => x.hidden !== true).map((kpi, kpi_i) => (
          <Grid
            key={"cardkpi_" + kpi_i}
            item
            xs={12}
            sm={4}
            md={kpi.extraContent ? 4 : 3}
          >
            <CardKPI
              title={kpi.label}
              value={kpi.value}
              unit="%"
              description={kpi.description}
              extraContent={kpi.extraContent}
            />
          </Grid>
        ))
      }
    </Grid>
  );
};

KPIDetailComponent.propTypes = {
  username: PropTypes.string,
  userView: PropTypes.bool,
  periodicView: PropTypes.bool,
  evaluationCycleID: PropTypes.string,
};

export default KPIDetailComponent;
