﻿using AutoMapper;
using RnD.BackEnd.Infrastructure.DataModels.Weather;
using RnD.BackEnd.Domain.Entities.Weather;

namespace RnD.BackEnd.Infrastructure.MappingProfiles
{
    /// <summary>
    /// Weather mapping profile
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class WeatherProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="WeatherProfile"/> class.
        /// </summary>
        public WeatherProfile()
        {
            CreateMap<WeatherModel, Weather>();

            CreateMap<Weather, WeatherModel>();
        }
    }
}
