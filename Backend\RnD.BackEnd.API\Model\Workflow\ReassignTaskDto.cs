﻿using System;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Reassign task DTO class
    /// </summary>
    public class ReassignTaskDto
    {
        /// <summary>
        /// Gets or sets the task identifier.
        /// </summary>
        /// <value>
        /// The task identifier.
        /// </value>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the new userid for the task.
        /// </summary>
        /// <value>
        /// The new user identifier.
        /// </value>
        public string NewUserId { get; set; }

        /// <summary>
        /// Gets or sets the comments.
        /// </summary>
        /// <value>
        /// The comments.
        /// </value>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the workflow email.
        /// </summary>
        /// <value>
        /// The workflow email.
        /// </value>
        public WorkflowEmailDto EmailRequest { get; set; }
    }
}