import { TextField } from "@mui/material";
import { DesktopDatePicker } from '@mui/x-date-pickers';
import moment from "moment";
import PropTypes from 'prop-types';

const DateField = ({ handleSelectValues }) => {
    return (
        <DesktopDatePicker
            inputFormat="dd-MM-yyyy"
            onChange={(e) => handleSelectValues(moment(e.target.value))}
            renderInput={(params) => <TextField
                fullWidth
                {...params}
                helperText={false}
                error={false}
            />}
        />
    );
}

DateField.propTypes = {
    handleSelectValues: PropTypes.func,
}

export default DateField;
