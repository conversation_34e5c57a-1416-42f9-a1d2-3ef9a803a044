﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>RnD.BackEnd.Database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{de3328f2-67d1-468c-98d8-43c692039b0c}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.SqlAzureV12DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>Rnd.BackEnd.Database</RootNamespace>
    <AssemblyName>Rnd.BackEnd.Database</AssemblyName>
    <ModelCollation>1033, CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <TargetDatabase>RnD.BackEnd.Database</TargetDatabase>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo" />
    <Folder Include="dbo\Tables" />
    <Folder Include="dbo\StoredProcedures" />
    <Folder Include="dbo\StoredProcedures\Weather" />
    <Folder Include="dbo\PostDeploymentScripts" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Tables\Weather.sql" />
    <Build Include="dbo\StoredProcedures\Weather\spGetWeathers.sql" />
    <Build Include="dbo\StoredProcedures\Weather\spInsertUpdateDeleteWeather.sql" />
    <Build Include="dbo\Tables\User.sql" />
    <Build Include="dbo\Tables\Permission.sql" />
    <Build Include="dbo\Tables\Cliente.sql" />
    <Build Include="dbo\Tables\Consumo.sql" />
    <Build Include="dbo\Tables\User_Permission_1.sql" />
    <Build Include="dbo\Tables\Consumo_Cliente.sql" />
    <Build Include="dbo\Tables\LogsProcessamento.sql" />
    <Build Include="dbo\Tables\ArquivosProcessados.sql" />
    <Build Include="dbo\Tables\Subscription.sql" />
    <Build Include="dbo\Tables\ConsumoSubscricao.sql" />
    <Build Include="dbo\Tables\ConsumoPDF.sql" />
    <Build Include="dbo\Tables\ClienteSubscricao.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Script.PostDeployment.sql" />
    <PostDeploy Include="dbo\PostDeploymentScripts\Weather.sql" />
  </ItemGroup>
  <ItemGroup>
    <RefactorLog Include="RnD.BackEnd.Database.refactorlog" />
  </ItemGroup>
</Project>