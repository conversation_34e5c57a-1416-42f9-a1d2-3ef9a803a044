/* eslint-disable no-constant-condition */
import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Divider,
  Typography,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  IconButton,
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardContent,
  Link,
} from "@mui/material";
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import { useTheme } from '@emotion/react';
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import CustomFilters from "../../../Components/CustomFilters";
import {
  FilterList as FilterListIcon,
  ExpandMore as ExpandMoreIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from "@mui/icons-material";
import { filtersAPI } from "../../../API/filtersAPI";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import getCode from "./mocks/code";
import getIndexIntro from "./mocks/indexIntro";
import getIndexCode from "./mocks/indexCode";
import getPopover from "./mocks/popover";
import getSelectPopover from "./mocks/selectPopover";
import getMuiSelectIntro from "./mocks/muiSelectIntro";
import getCustomMuiSelect from "./mocks/customMuiSelect";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import { styled } from "@mui/system";

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh",
})

const StyledBadge = styled(Badge)({
  ".MuiBadge-badge": {
    padding: "0 4px !important",
    minWidth: "17px !important",
    height: "17px !important",
    fontSize: "0.6rem !important",
    right: "-3px",
  },
})

const CustomEditor = styled(Editor)(({ theme }) => ({
  padding: 10,
  fontFamily: '"Fira code", "Fira Mono", monospace',
  fontSize: 12,
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
}))

const linkStyle = {
  marginRight: 5,
  marginLeft: 5,
};

export default function CustomFiltersComponent() {
  const [customFiltersList, setCustomFiltersList] = useState([]);
  const [customFiltersToggled, setCustomFiltersToggled] = useState(
    customFiltersList && customFiltersList.length > 0
  );
  const [selectedCustomFilters, setSelectedCustomFilters] = useState(
    customFiltersList && customFiltersList.length > 0 ? customFiltersList : []
  );
  const theme = useTheme();
  const { t } = useTranslation();
  const [code, setCode] = useState(getCode);
  const [idx, setIdx] = useState(getIndexIntro);
  const [codeIndex, setCodeIndex] = useState(getIndexCode);
  const [popover, setPopover] = useState(getPopover);
  const [codeSelectPopover, setcodeSelectPopover] = useState(getSelectPopover);
  const [muiSelectIntro, setMuiSelectIntro] = useState(getMuiSelectIntro);
  const [customMuiSelect, setCustomMuiSelect] = useState(getCustomMuiSelect);

  const [open, setOpen] = useState({
    index: false,
    selectPopover: false,
    muiSelect: false,
  });

  const handleToggle = (e) => {
    const name = e?.currentTarget?.name;
    setOpen({
      ...open,
      [name]: !open[name],
    });
  };

  //Here we get the Filters
  const getFilters = async () => {
    try {
      const data = await filtersAPI.getAvailableFilters({ context: "PROCESS" });
      if (!data?.error) {
        setCustomFiltersList(data?.response);
      } else if (data.exceptionMessages && data.exceptionMessages.hasMessages) {
        data.exceptionMessages.messages.forEach((m) => {
          toast.error(m.description);
        });
      } else {
        toast.error("Ocorreu um erro inesperado");
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleResetCustomFilters = () => {
    if (customFiltersList > 0) {
      setSelectedCustomFilters([]);
    }
  };

  const handleAddCustomFilter = (value) => {
    const _selectedCustomFilters = [...selectedCustomFilters];
    const matchIndex = _selectedCustomFilters.findIndex(
      (x) => x.elementID === value.elementID
    );

    if (false && matchIndex >= 0) {
      if (Array.isArray(_selectedCustomFilters[matchIndex].values)) {
        _selectedCustomFilters[matchIndex].values.push("&");
        _selectedCustomFilters[matchIndex].values.concat(value.values);
      } else {
        _selectedCustomFilters[
          matchIndex
        ].values += ` < b > && </b > ${value.values} `;
      }
    } else {
      _selectedCustomFilters.push(value);
    }
    setSelectedCustomFilters(_selectedCustomFilters);
  };

  const handleDeleteCustomFilter = (value) => {
    const _customFilters = [...selectedCustomFilters];
    _customFilters.splice(value, 1);
    setSelectedCustomFilters(_customFilters);
  };

  function handleTooltipTitle(condition) {
    if (condition) return "Show full source Code";
    return "Hide full Source Code"
  }

  useEffect(() => {
    getFilters();
  }, []);

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box
        sx={{
          m: "auto",
          textAlign: "center",
        }}
      >
        {/* Title */}
        <Box
          color="textPrimary"
          sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
        >
          <h2>Custom Filters Component</h2>
          <p>
            The following code snippet demonstrates how custom filters component
            is used:
          </p>
        </Box>
        {/*Filter Example*/}
        <Box
          xs={12}
          sx={{
            maxWidth: "80%",
            margin: "auto",
            marginTop: 2,
            marginBottom: 10,
          }}
        >
          <Tooltip title={t("common:common.view")}>
            <IconButton
              color="primary"
              onClick={() => setCustomFiltersToggled(!customFiltersToggled)}
              sx={{ background: "white" }}
            >
              <StyledBadge
                badgeContent={1}
                color="primary"
              >
                <FilterListIcon />
              </StyledBadge>
            </IconButton>
          </Tooltip>
          <Grid
            container
            sx={{
              pb: customFiltersToggled ? 2 : 0,
            }}
          >
            <Grid
              item
              xs={12}
            >
              <CustomFilters
                customFiltersToggled={customFiltersToggled}
                context="PROCESS"
                customFiltersList={customFiltersList}
                selectedCustomFilters={selectedCustomFilters}
                resetCustomFilters={handleResetCustomFilters}
                addCustomFilter={handleAddCustomFilter}
                deleteCustomFilter={handleDeleteCustomFilter}
              />
            </Grid>
          </Grid>
        </Box>
        {/*Custom Filters Example Code*/}
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={code} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <CustomEditor
              value={code}
              onValueChange={(_code) => setCode(_code)}
              highlight={(_code) => highlight(_code, languages.js)}
            />
          </CardContent>
        </StyledEditorBox>
        <Box sx={{ m: "auto", mt: 5, textAlign: "left", maxWidth: "80%" }}>
          <Typography color="textPrimary">
            This Custom Filter Component allows you to customize the way you
            want your filters to appear. <br />
            You can define your search by choosing some of the Material UI
            components: (i.e: TextField, CheckBox, etc;)
            <br />
            Please see more information here:
            <Link
              to="https://mui.com/"
              underline="none"
            >
              {" "}
              Material UI Website
            </Link>
            .
            <br />
            <br />
            To get started, copy the following content into your folder. You can
            call it index.js:
            <br />
            <i>
              (Please note that the 3 code snippets shown below will be required
              for the component to run.)
            </i>
          </Typography>
        </Box>
        {/*Copy from index code */}
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={open?.index ? codeIndex : idx} />
                <Tooltip
                  title={handleTooltipTitle(!open.muiSelect)}
                >
                  <IconButton
                    name="index"
                    onClick={handleToggle}
                    sx={{
                      color: `${theme.palette.primary.main}`,
                      "&:hover": {
                        backgroundColor: `${theme.palette.secondary.main}`,
                        color: "primary.main",
                      },
                      height: 35,
                      width: 35,
                      mt: 1,
                    }}
                  >
                    {!open?.index ? (
                      <ExpandMoreIcon />
                    ) : (
                      <KeyboardArrowUpIcon />
                    )}
                  </IconButton>
                </Tooltip>
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            {open?.index ?
              <CustomEditor
                value={codeIndex}
                onValueChange={(_codeIndex) => setCodeIndex(_codeIndex)}
                highlight={(_codeIndex) => highlight(_codeIndex, languages.js)}
              />
              :
              <CustomEditor
                value={idx}
                onValueChange={(_idx) => setIdx(_idx)}
                highlight={(_idx) => highlight(_idx, languages.js)}
              />}
          </CardContent>
        </StyledEditorBox>
        <Typography
          color="textPrimary"
          sx={{ m: "auto", mt: 5, textAlign: "left", maxWidth: "80%" }}
        >
          Also copy the content below to the same folder:
          <br />
          This component will draw the Popover that the user will see after
          clicking on Add Filters.
        </Typography>
      </Box>
      {/*Copy from Custom Select Popover code */}
      <StyledEditorBox>
        <CardHeader
          action={
            <>
              <CopyToClipboardButton
                value={open?.selectPopover ? codeSelectPopover : popover}
              />
              <Tooltip
                title={handleTooltipTitle(!open?.selectPopover)}
              >
                <IconButton
                  name="selectPopover"
                  onClick={handleToggle}
                  sx={{
                    color: `${theme.palette.primary.main}`,
                    "&:hover": {
                      backgroundColor: `${theme.palette.secondary.main}`,
                      color: "primary.main",
                    },
                    height: 35,
                    width: 35,
                    mt: 1,
                  }}
                >
                  {!open?.selectPopover ? (
                    <ExpandMoreIcon />
                  ) : (
                    <KeyboardArrowUpIcon />
                  )}
                </IconButton>
              </Tooltip>
            </>
          }
        />
        <Divider />
        <CardContent
          sx={{
            maxHeight: "50vh",
            overflowY: "scroll",
          }}
        >
          {open?.selectPopover ?
            <CustomEditor
              value={codeSelectPopover}
              onValueChange={(_codeSelectPopover) => setcodeSelectPopover(_codeSelectPopover)}
              highlight={(_codeSelectPopover) => highlight(_codeSelectPopover, languages.js)}
              padding={10}
            />
            :
            <CustomEditor
              value={popover}
              onValueChange={(_popover) => setPopover(_popover)}
              highlight={(_popover) => highlight(_popover, languages.js)}
            />}
        </CardContent>
      </StyledEditorBox>
      <Typography
        color="textPrimary"
        sx={{ m: "auto", mt: 5, textAlign: "left", maxWidth: "80%" }}
      >
        You will also need the Custom Mui Select Component.
        <br />
        Depending on the project, we may have to pass different props.
        <br />
        For more information on this component, please see
        <Link
          sx={linkStyle}
          underline="none"
          to="/Components/CustomMuiSelect"
        >
          {" "}
          Mui Select{" "}
        </Link>
        customization page!
      </Typography>
      {/*Copy from Custom Mui Select code */}
      <StyledEditorBox>
        <CardHeader
          action={
            <>
              <CopyToClipboardButton
                value={open?.muiSelect ? customMuiSelect : muiSelectIntro}
              />
              <Tooltip
                title={handleTooltipTitle(!open?.muiSelect)}
              >
                <IconButton
                  name="muiSelect"
                  onClick={handleToggle}
                  sx={{
                    color: `${theme.palette.primary.main}`,
                    "&:hover": {
                      backgroundColor: `${theme.palette.secondary.main}`,
                      color: "primary.main",
                    },
                    height: 35,
                    width: 35,
                    mt: 1,
                  }}
                >
                  {!open?.muiSelect ? (
                    <ExpandMoreIcon />
                  ) : (
                    <KeyboardArrowUpIcon />
                  )}
                </IconButton>
              </Tooltip>
            </>
          }
        />
        <Divider />
        <CardContent
          sx={{
            maxHeight: "50vh",
            overflowY: "scroll",
          }}
        >
          {open?.muiSelect ?
            <CustomEditor
              value={customMuiSelect}
              onValueChange={(_customMuiSelect) => setCustomMuiSelect(_customMuiSelect)}
              highlight={(_customMuiSelect) => highlight(_customMuiSelect, languages.js)}
            />
            :
            <CustomEditor
              value={muiSelectIntro}
              onValueChange={(_muiSelectIntro) => setMuiSelectIntro(_muiSelectIntro)}
              highlight={(_muiSelectIntro) => highlight(_muiSelectIntro, languages.js)}
            />}
        </CardContent>
      </StyledEditorBox>
      <Box
        sx={{
          m: "auto",
          textAlign: "justify",
          maxWidth: "80%",
          color: "textPrimary",
        }}
      >
        <p>Custom Filters receive the following Props:</p>
        <ul>
          <li>
            <b>oid</b> - ID value;
          </li>
          <li>
            <b>customFiltersToggled</b> - Open and Close Add Filters Tab;
          </li>
          <li>
            <b>customFiltersList</b> - The list containing all the items we want
            to include in our filter;
          </li>
          <li>
            <b>selectedCustomFilters</b> - The Selected Filters from Add Filter;
          </li>
          <li>
            <b>addCustomFilter</b> - When we select a filter, a Chip will be
            created and added to the bar;
          </li>
          <li>
            <b>deleteCustomFilter</b> - Delete Filter;
          </li>
          <li>
            <b>resetCustomFilters</b> - Reset chosen filters;
          </li>
          <li>
            <b>context</b> - In case we want to pass a specific context.{" "}
          </li>
        </ul>
      </Box>
      <ChangeLogEntry
        version="V1.0.0"
        changeLog={ChangeLog()}
      />
    </Box>
  );
}
const ChangeLog = () => {
  const theme = useTheme();
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content:
        "Imported Custom Filters Component. Two Filtering options: CheckBox and TextField",
      className: chips.green,
    },
  ];

  return changeLog;
};
