﻿param(
   [string][parameter(Mandatory = $true)] $vaultName,
   [string][parameter(Mandatory = $true)] $applicationId,
   [string][parameter(Mandatory = $true)] $resourceGroupName
)

Write-Host Get Service Principal Id for application $applicationId
$spn= Get-AzureRmADServicePrincipal -spn $applicationId

Write-Host Setting KeyVault access policy for Service Principal Id: $spn.Id
Set-AzureRmKeyVaultAccessPolicy -ObjectId $spn.Id -ResourceGroupName $resourceGroupName -VaultName $vaultName -PermissionsToSecrets get,list,set