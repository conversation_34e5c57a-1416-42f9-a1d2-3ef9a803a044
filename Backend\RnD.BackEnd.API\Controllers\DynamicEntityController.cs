﻿using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.SwashbuckleExamples;
using RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Interfaces.Services;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.Controllers
{
    /// <summary>
    /// Dynamic entity controller
    /// </summary>
    /// <seealso cref="Microsoft.AspNetCore.Mvc.ControllerBase" />
    [Produces("application/json")]
    [Route("api/[controller]")]
    [ApiController]
    public class DynamicEntityController : ControllerBase
    {
        private readonly IDynamicEntityService _dynamicEntityService;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicEntityController"/> class.
        /// </summary>
        /// <param name="dynamicEntityService">The dynamic entity service.</param>
        /// <param name="mapper">The mapper.</param>
        public DynamicEntityController(IDynamicEntityService dynamicEntityService, IMapper mapper)
        {
            _dynamicEntityService = dynamicEntityService;
            _mapper = mapper;
        }

        /// <summary>
        /// Adds the entity in the request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The added entity.
        /// </returns>
        /// <response code="200">Returns The added entity.</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpPost]
        [ProducesResponseType(typeof(ApiOutput<Dictionary<string, object>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(Dictionary<string, object>), typeof(AddDynamicEntity))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(AddDynamicEntity200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> Add([FromBody] Dictionary<string, object> request)
        {
            ServiceOutput<Dictionary<string, object>> response = await _dynamicEntityService.AddAsync(request);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Dictionary<string, object>>, ApiOutput<Dictionary<string, object>>>(response));
        }

        /// <summary>
        /// Updates the entity in the request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="etag">The etag.</param>
        /// <returns>
        /// The updated entity.
        /// </returns>
        /// <response code="200">Returns The updated entity.</response>
        /// <response code="400">Malformed request</response>
        /// <response code="412">Error updating entity.</response>
        /// <response code="500">Internal error</response>
        [HttpPut]
        [ProducesResponseType(typeof(ApiOutput<Dictionary<string, object>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status412PreconditionFailed)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(Dictionary<string, object>), typeof(UpdateDynamicEntity))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(UpdateDynamicEntity200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.PreconditionFailed, typeof(UpdateDynamicEntity412))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> Update([FromBody] Dictionary<string, object> request, [FromQuery] string etag)
        {
            ServiceOutput<Dictionary<string, object>> response = await _dynamicEntityService.UpdateAsync(request, etag);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Dictionary<string, object>>, ApiOutput<Dictionary<string, object>>>(response));
        }

        /// <summary>
        /// Deletes an entity by identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// True if entity was deleted.
        /// </returns>
        /// <response code="204">No content response</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpDelete]
        [Route("{id}")]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> Delete([FromRoute] string id)
        {
            ServiceOutput<bool> response = await _dynamicEntityService.DeleteAsync(id);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<bool>, ApiOutput<bool>>(response));
        }

        /// <summary>
        /// Gets an entity by identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The entity.
        /// </returns>
        /// <response code="200">Returns The entity.</response>
        /// <response code="204">No content response</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [Route("{id}")]
        [ProducesResponseType(typeof(ApiOutput<Dictionary<string, object>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(GetDynamicEntity200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> Get([FromRoute] string id)
        {
            ServiceOutput<Dictionary<string, object>> response = await _dynamicEntityService.GetByIdAsync(id);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Dictionary<string, object>>, ApiOutput<Dictionary<string, object>>>(response));
        }

        /// <summary>
        /// Lists the entities.
        /// </summary>
        /// <param name="pageNumber">The page number.</param>
        /// <param name="maxItems">The maximum items.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// The list of entities.
        /// </returns>
        /// <response code="200">Returns The list of entities.</response>
        /// <response code="204">No content response</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [ProducesResponseType(typeof(ApiOutput<List<Dictionary<string, object>>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(ListDynamicEntity200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> List(
            [FromQuery] int? pageNumber,
            [FromQuery] int? maxItems,
            [FromQuery] string sortField,
            [FromQuery] bool sortAscending,
            [FromQuery] string continuationToken,
            CancellationToken cancellationToken)
        {
            ServiceOutput<List<Dictionary<string, object>>> response = await _dynamicEntityService.ListAsync(pageNumber, maxItems, sortField, sortAscending, continuationToken, cancellationToken);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<List<Dictionary<string, object>>>, ApiOutput<List<Dictionary<string, object>>>>(response));
        }
    }
}