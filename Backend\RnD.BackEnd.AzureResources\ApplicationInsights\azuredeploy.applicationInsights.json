{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"location": {"defaultValue": "[resourceGroup().location]", "type": "string", "metadata": {"description": "Provide the location for resources."}}, "application-insights-Name": {"type": "string", "minLength": 1}}, "variables": {}, "resources": [{"type": "microsoft.insights/components", "apiVersion": "2020-02-02", "name": "[parameters('application-insights-Name')]", "location": "[parameters('location')]", "kind": "web", "properties": {"Application_Type": "web", "Request_Source": "IbizaWebAppExtensionCreate"}, "tags": {"displayName": "application-insights"}}], "outputs": {"ApplicationInsights-InstrumentationKey": {"value": "[reference(resourceId('Microsoft.Insights/components', parameters('application-insights-Name')), '2020-02-02').InstrumentationKey]", "type": "string"}, "ApplicationInsights-AppId": {"value": "[reference(resourceId('Microsoft.Insights/components', parameters('application-insights-Name')), '2015-05-01').AppId]", "type": "string"}}}