import React from 'react';
import { Link as RouterLink, } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Drawer, Box, useMediaQuery } from '@mui/material';
import NavSection from '../../Components/Sidebar/NavSection';
import Scrollbar from '../../Components/Scrollbar';
import { knownRoutes } from '../../Routes/routes';
import { useTheme } from '@emotion/react';

const DashboardSidebar = ({ onCloseSidebar, openSidebar }) => {
  const lgDown = useMediaQuery((theme) => theme.breakpoints.down('lg'));
  const theme = useTheme();

  const content = (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%'
      }}
    >
      <Scrollbar options={{ suppressScrollX: true }}>
        <Box
          sx={{
            display: {
              lg: 'none',
              xs: 'flex'
            },
            justifyContent: 'center'
          }}
        >
          <RouterLink to="/">
            <Box
              sx={{
                '& img': {
                  width: 100,
                  height: 36
                }
              }}
            >
              <img
                alt={process.env.REACT_APP_PLATFORM_NAME}
                src="/static/images/logo.svg"
              />
            </Box>
          </RouterLink>
        </Box>
        <Box sx={{ pl: 2, pr: 2 }}>
          {knownRoutes.filter(x => x.title !== undefined).map((route, route_index) => (
            <NavSection
              key={route.title + "_" + route_index}
              title={route.title}
              path={route.path}
              _children={route.children}
            />
          ))}
        </Box>
      </Scrollbar>
    </Box>
  );

  return (
    <Drawer
      anchor="left"
      onClose={onCloseSidebar}
      open={openSidebar}
      PaperProps={{
        sx: {
          backgroundColor: 'background.paper',
          width: "256px",
          marginTop: lgDown ? "initial" : "64px",
          borderRight: `1px solid ${theme.palette.navBar}`
        }
      }}
      variant={lgDown ? "temporary" : "persistent"}
    >
      {content}
    </Drawer>
  );
};

DashboardSidebar.propTypes = {
  onCloseSidebar: PropTypes.func,
  openSidebar: PropTypes.bool
};

export default DashboardSidebar;
