import { useState } from "react";
import { Link as RouterLink } from "react-router-dom";
import PropTypes from "prop-types";
import {
  Avatar,
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TablePagination,
  TableRow,
  TextField,
  Link,
  Button,
} from "@mui/material";
import { Search as SearchIcon, Add as AddIcon } from "@mui/icons-material";
import Scrollbar from "../../Components/Scrollbar";
import TableHeader from "../../Components/TableUtils/TableHeader";
import TableLoading from "../../Components/TableUtils/TableLoading";
import {
  applyPagination,
  applySort,
} from "../../Components/TableUtils/TableUtils";
import { useTranslation } from "react-i18next";

const ClientTable = ({ clientList, isLoading }) => {
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const [query, setQuery] = useState("");
  const [orderBy, setOrderBy] = useState("name");
  const [order, setOrder] = useState("asc");
  const { t } = useTranslation();

  const headerCells = [
    {
      id: "name",
      label: t("common:tableHeaders.name"),
      sort: true,
    },
    {
      id: "contact",
      label: t("common:tableHeaders.contact"),
      sort: true,
    },
  ];

  const handleQueryChange = (event) => {
    setQuery(event.target.value);
    setPage(0);
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleLimitChange = (event) => {
    setLimit(parseInt(event.target.value, 10));
  };

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  // Função para filtrar clientes
  const filterClients = (clients, searchQuery) => {
    if (!searchQuery) return clients;

    const searchTerm = searchQuery.toLowerCase();
    return clients.filter(client =>
      client.name.toLowerCase().includes(searchTerm) ||
      (client.contact && client.contact.toLowerCase().includes(searchTerm)));
  };

  const filteredClientList = filterClients(clientList, query);
  const sortedClientList = applySort(filteredClientList, orderBy, order);
  const paginatedClientList = applyPagination(sortedClientList, page, limit);
  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Card>
        <Box sx={{ alignItems: "center", display: "flex", m: 2, height: "88px" }}>
          <TextField
            sx={{ width: "458px", height: "56px" }}
            InputProps={{
              startAdornment: (
                <SearchIcon fontSize="small" />
              ),
            }}
            onChange={handleQueryChange}
            placeholder={t("common:search.searchClient")}
            value={query}
            variant="outlined"
          />
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            component={RouterLink}
            to="/ClientsList/new"
            sx={{ ml: "auto" }}
          >
            {t("common:buttons.createClient")}
          </Button>
        </Box>
        <Scrollbar>
          <Box sx={{ p: 2, pt: 0, minWidth: 700 }}>
            <Table stickyHeader>
              <TableHeader hasCheckbox={false} headCells={headerCells} onSortClick={handleRequestSort} orderBy={orderBy} order={order} />
              <TableBody>
                <TableLoading isLoading={isLoading} headCells={headerCells} numRows={limit} />
                {paginatedClientList.map((client) => (
                  <TableRow hover key={client.id} sx={{ height: "77px" }}>
                    <TableCell>
                      <Box sx={{ display: "flex" }}>
                        <Avatar
                          mx={2}
                          key={`avatar-${client.id}}`}
                          alt={client.name}
                        />
                        <Link
                          color="inherit"
                          component={RouterLink}
                          to={`/ClientsList/${client.id}`}
                          sx={{
                            color: "primary.main",
                            textDecoration: "none",
                            fontStyle: "normal",
                            fontWeight: 700,
                            fontSize: "14px",
                            lineHeight: "20px",
                            alignSelf: "center",
                            marginLeft: 1,
                          }}
                        >
                          {client.name}
                        </Link>
                      </Box>
                    </TableCell>
                    <TableCell>{client.contact}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Box>
        </Scrollbar>
        <TablePagination
          component="div"
          count={filteredClientList.length}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleLimitChange}
          page={page}
          rowsPerPage={limit}
          rowsPerPageOptions={[5, 10, 25]}
          showFirstButton
          showLastButton
          labelRowsPerPage={t("common:tableActions.rowsPerPage")}
        />
      </Card>
    </Box>
  );
};

ClientTable.propTypes = {
  clientList: PropTypes.array.isRequired,
  isLoading: PropTypes.bool,
};

export default ClientTable;
