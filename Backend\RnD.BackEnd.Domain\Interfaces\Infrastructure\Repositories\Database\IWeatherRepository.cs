﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using RnD.BackEnd.Domain.DataModels;
using RnD.BackEnd.Domain.Entities.Weather;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database.Base;

namespace RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database
{
    /// <summary>
    /// Weather repository interface
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database.Base.IBaseSqlRepository&lt;RnD.BackEnd.Domain.Entities.Weather.Weather&gt;" />
    public interface IWeatherRepository : IBaseSqlRepository<Weather>
    {
        /// <summary>
        /// Lists the weather records.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="location">The location.</param>
        /// <param name="itemsPerPage">The number of items per page.</param>
        /// <param name="page">The page (starts with 1)</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// The list of weather records.
        /// </returns>
        Task<ResponseModel<IList<Weather>>> ListAsync(
            Guid id,
            string location,
            int? itemsPerPage,
            int? page,
            string sortField = null,
            bool sortAscending = false,
            CancellationToken? cancellationToken = null);
    }
}
