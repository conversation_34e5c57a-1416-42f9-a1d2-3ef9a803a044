﻿using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using RnD.BackEnd.Domain.BusinessMessages;
using RnD.BackEnd.Domain.Constants;
using RnD.BackEnd.Domain.DataModels;
using RnD.BackEnd.Domain.Entities.Exception;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Extensions;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos;
using RnD.BackEnd.Domain.Interfaces.Services;
using RnD.BackEnd.Domain.Validators.DynamicEntity;

namespace RnD.BackEnd.Service.Services
{
    /// <summary>
    /// Dynamic entity service class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Services.IDynamicEntityService" />
    public class DynamicEntityService : IDynamicEntityService
    {
        private readonly IDynamicEntityRepository _repository;

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicEntityService" /> class.
        /// </summary>
        /// <param name="repository">The repository.</param>
        public DynamicEntityService(IDynamicEntityRepository repository)
        {
            _repository = repository;
        }

        /// <summary>
        /// Adds the entity.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The entity added.
        /// </returns>
        public async Task<ServiceOutput<Dictionary<string, object>>> AddAsync(Dictionary<string, object> request)
        {
            ServiceOutput<Dictionary<string, object>> output = new ServiceOutput<Dictionary<string, object>>();
            ModelException validationErrors = EntityValidations.IsCreateRequestValid(request, _repository.PartitionKeyFieldName);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                ResponseModel<Dictionary<string, object>> response = await _repository.AddAsync(request);

                if (response.Code != HttpStatusCode.Created)
                {
                    output.CustomErrorCode(response.Code, DynamicEntityMessages.Error_Create);
                }
                else
                {
                    output.Value = response.Value;
                }
            }

            return output;
        }

        /// <summary>
        /// Updates the entity.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="etag">The etag.</param>
        /// <returns>
        /// The entity updated.
        /// </returns>
        public async Task<ServiceOutput<Dictionary<string, object>>> UpdateAsync(Dictionary<string, object> request, string etag)
        {
            ServiceOutput<Dictionary<string, object>> output = new ServiceOutput<Dictionary<string, object>>();

            ModelException validationErrors = EntityValidations.IsUpdateRequestValid(
                request,
                _repository.IdFieldName,
                _repository.PartitionKeyFieldName);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                ResponseModel<Dictionary<string, object>> response = await _repository.UpdateAsync(request, etag);

                if (response.Code != HttpStatusCode.OK)
                {
                    output.CustomErrorCode(response.Code, DynamicEntityMessages.Error_Update);
                }
                else
                {
                    output.Value = response.Value;
                }
            }

            return output;
        }

        /// <summary>
        /// Deletes the specified entity by id.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// True if operation succeeded, False otherwise.
        /// </returns>
        public async Task<ServiceOutput<bool>> DeleteAsync(string id)
        {
            ServiceOutput<bool> output = new ServiceOutput<bool>();

            ModelException validationErrors = EntityValidations.IsIdValid(id);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                if (!await _repository.DeleteAsync(id))
                {
                    output.InternalServerError(DynamicEntityMessages.Error_Delete);
                }
                else
                {
                    output.NoContent();
                }
            }

            return output;
        }

        /// <summary>
        /// Gets the by identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The entity.
        /// </returns>
        public async Task<ServiceOutput<Dictionary<string, object>>> GetByIdAsync(string id)
        {
            ServiceOutput<Dictionary<string, object>> output = new ServiceOutput<Dictionary<string, object>>();

            ModelException validationErrors = EntityValidations.IsIdValid(id);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                ResponseModel<Dictionary<string, object>> response = await _repository.GetAsync(id);

                if (response.Code == HttpStatusCode.NotFound)
                {
                    output.NotFound(DynamicEntityMessages.Not_Found);
                }
                else if (response.Code == HttpStatusCode.OK)
                {
                    output.Value = response.Value;
                }
                else
                {
                    output.CustomErrorCode(response.Code, string.Format(DynamicEntityMessages.Error_Get, id));
                }
            }

            return output;
        }

        /// <summary>
        /// Get the list of entities.
        /// </summary>
        /// <param name="pageNumber">The page number.</param>
        /// <param name="maxItems">The maximum items.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// The list of entities.
        /// </returns>
        public async Task<ServiceOutput<List<Dictionary<string, object>>>> ListAsync(
            int? pageNumber,
            int? maxItems,
            string sortField,
            bool sortAscending,
            string continuationToken,
            CancellationToken cancellationToken)
        {
            ServiceOutput<List<Dictionary<string, object>>> output = new ServiceOutput<List<Dictionary<string, object>>>();

            ResponseModel<List<Dictionary<string, object>>> response = await _repository.ListAsync(pageNumber, maxItems, sortField, sortAscending, continuationToken, cancellationToken);

            if (response.Code != HttpStatusCode.OK)
            {
                output.CustomErrorCode(response.Code, DynamicEntityMessages.Error_List);
            }
            else
            {
                output.Value = response.Value;
                if (response.ContinuationToken != null)
                {
                    output.Properties.Add(SystemConstants.CONTINUATION_TOKEN, response.ContinuationToken);
                }
            }

            return output;
        }
    }
}
