import React, { useState, useEffect } from 'react';
import { Container, Typography, Box } from '@mui/material';
import { getSubscriptions } from '../../Services/subscriptionServices';
import SubscriptionTable from './SubscriptionTable';
import { useTranslation } from "react-i18next";

const SubscriptionList = () => {
    const { t } = useTranslation();
    useEffect(() => {
        document.title = `${t('titles:subscriptions')} | Portal Cloud Services`;
    }, []);

    const [subscriptions, setSubscriptions] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchTerm] = useState('');
    const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().slice(0, 7));

    useEffect(() => {
        const fetchSubscriptions = async () => {
            try {
                const data = await getSubscriptions();
                setSubscriptions(data);
                setError(null);
            } catch (err) {
                console.error('Erro ao carregar subscrições:', err);
                setError('Não foi possível carregar as subscrições. Por favor, tente novamente mais tarde.');
            } finally {
                setIsLoading(false);
            }
        };

        fetchSubscriptions();
    }, []);

    const filteredSubscriptions = subscriptions
        .filter(subscription => subscription.subscriptionId.toLowerCase().includes(searchTerm.toLowerCase()))
        .map(subscription => {
            const hasDataInMonth = subscription.resources.some(resource => {
                if (!resource.startDate) return false;
                const resourceDate = new Date(resource.startDate);
                if (Number.isNaN(resourceDate.getTime())) return false;
                const resourceMonth = resourceDate.toISOString().slice(0, 7);
                return resourceMonth === selectedMonth;
            });

            if (!hasDataInMonth) {
                return {
                    ...subscription,
                    totalCost: 0,
                    totalCostUSD: 0,
                    resourceCount: 0,
                    startDate: null,
                    endDate: null,
                    hasNoData: true
                };
            }

            return subscription;
        });

    if (isLoading) {
        return (
            <Container maxWidth="lg">
                <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
                    <Typography>Carregando...</Typography>
                </Box>
            </Container>
        );
    }

    if (error) {
        return (
            <Container maxWidth="lg">
                <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
                    <Typography color="error">{error}</Typography>
                </Box>
            </Container>
        );
    }

    return (
        <Container maxWidth="lg">
            <Box my={4}>
                <SubscriptionTable
                    subscriptions={filteredSubscriptions}
                    isLoading={isLoading}
                    selectedMonth={selectedMonth}
                    onMonthChange={setSelectedMonth}
                />
            </Box>
        </Container>
    );
};

export default SubscriptionList;
