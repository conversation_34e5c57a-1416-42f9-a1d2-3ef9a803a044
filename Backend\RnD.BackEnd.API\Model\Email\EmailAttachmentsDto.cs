﻿using System.Collections.Generic;
using Microsoft.AspNetCore.Http;

namespace RnD.BackEnd.API.Model.Email
{
    /// <summary>
    /// Email with attachments DTO class
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <seealso cref="RnD.BackEnd.API.Model.Email.EmailMessageDto&lt;T&gt;" />
    public class EmailAttachmentsDto<T> : EmailMessageDto<T> where T : class
    {
        /// <summary>
        /// Gets or sets the attachments.
        /// </summary>
        /// <value>
        /// The attachments.
        /// </value>
        public List<IFormFile> Attachments { get; set; }
    }
}
