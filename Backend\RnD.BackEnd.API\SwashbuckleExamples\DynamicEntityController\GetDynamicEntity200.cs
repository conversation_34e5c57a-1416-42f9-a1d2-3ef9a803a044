﻿using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController
{
    /// <summary>
    /// Get Dynamic Entity response example for 200
    /// </summary>
    public class GetDynamicEntity200 : IExamplesProvider<ApiOutput<Dictionary<string, object>>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a Get Dynamic Entity 200 response.
        /// </returns>
        public ApiOutput<Dictionary<string, object>> GetExamples()
        {
            return new ApiOutput<Dictionary<string, object>>
            {
                Code = HttpStatusCode.OK,
                Value = new Dictionary<string, object>
                {
                    {"entityId", "dive1" },
                    {"companyId", "diveCenterPhi1"},
                    {"name", "Dive Center Koh Phi Phi"},
                    {"type", "Dive Center"},
                    {"review", 4.5},
                    {"numberOfReviews", 104},
                    {"location", "Koh Phi Phi Island"},
                    {"country", "Thailand"},
                    {"id", "dive1:bfd6b932-9e6a-43f2-b739-0b65213bc333"},
                    {"_rid", "DMV6ANEfcywBAAAAAAAAAA=="},
                    {"_self", "dbs/DMV6AA==/colls/DMV6ANEfcyw=/docs/DMV6ANEfcywBAAAAAAAAAA==/" },
                    {"_etag", "\"510015a5-0000-0700-0000-63f8fbe10000\""},
                    {"_attachments", "attachments/"},
                    { "_ts", 1677261793}
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}