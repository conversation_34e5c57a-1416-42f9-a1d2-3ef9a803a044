﻿using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using RnD.BackEnd.Domain.DataModels;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos.Base;
using RnD.BackEnd.Domain.Settings.Cosmos;
using RnD.BackEnd.Infrastructure.DataModels.Base;
using RnD.BackEnd.Infrastructure.Interfaces;

namespace RnD.BackEnd.Infrastructure.Repositories.Cosmos.Base
{
    /// <summary>
    /// Cosmos DB base model repository class
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <typeparam name="D">Data type</typeparam>
    /// <seealso cref="RnD.BackEnd.Infrastructure.Repositories.Cosmos.Base.CosmosOperations" />
    /// <seealso cref="Domain.Interfaces.Infrastructure.Repositories.Cosmos.Base.IBaseCosmosRepository&lt;T&gt;" />
    public abstract class BaseCosmosRepository<T, D> : CosmosOperations, IBaseCosmosRepository<T> where T : class where D : BaseCosmosModel
    {
        protected readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="BaseCosmosRepository{T, D}" /> class.
        /// </summary>
        /// <param name="cosmosDbManager">The cosmos database manager.</param>
        /// <param name="cosmosSettings">The cosmos settings.</param>
        /// <param name="mapper">The mapper.</param>
        protected BaseCosmosRepository(ICosmosDbManager cosmosDbManager, IOptions<CosmosSettings> cosmosSettings, IMapper mapper)
            : base(cosmosDbManager, cosmosSettings)
        {
            _mapper = mapper;
        }

        /// <summary>
        /// Adds the item.
        /// </summary>
        /// <param name="item">The item.</param>
        /// <returns>
        /// The added item.
        /// </returns>
        public async Task<ResponseModel<T>> AddAsync(T item)
        {
            D model = _mapper.Map<T, D>(item);
            model.Id = GenerateId(model);
            ResponseModel<D> result = await AddItemAsync(model, model.Id);
            return _mapper.Map<ResponseModel<D>, ResponseModel<T>>(result);
        }

        /// <summary>
        /// Deletes the item.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// True if operation succeeded. False otherwise.
        /// </returns>
        public async Task<ResponseModel<bool>> DeleteAsync(string id)
        {
            ResponseModel<bool> result = new ResponseModel<bool>();

            ItemResponse<D> response = await _container.DeleteItemAsync<D>(id, ResolvePartitionKey(id));
            result.Code = response.StatusCode;
            result.Value = response.StatusCode == HttpStatusCode.NoContent;

            return result;
        }

        /// <summary>
        /// Updates the item.
        /// </summary>
        /// <param name="item">The item.</param>
        /// <param name="itemId">The item identifier.</param>
        /// <param name="etag">The etag.</param>
        /// <returns>
        /// The updated item.
        /// </returns>
        public async Task<ResponseModel<T>> UpdateAsync(T item, string etag = default)
        {
            D model = _mapper.Map<T, D>(item);
            ResponseModel<D> result = await UpdateItemAsync(model, model.Id, etag);
            return _mapper.Map<ResponseModel<D>, ResponseModel<T>>(result);
        }

        /// <summary>
        /// Gets the item.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The item.
        /// </returns>
        public async Task<ResponseModel<T>> GetAsync(string id)
        {
            ResponseModel<D> result = await GetItemAsync<D>(id);
            result.Etag = result.Value?.ETag;
            return _mapper.Map<ResponseModel<D>, ResponseModel<T>>(result);
        }

        /// <summary>
        /// Gets the list of items.
        /// </summary>
        /// <param name="queryString">The query string.</param>
        /// <param name="parameters">The parameters.</param>
        /// <param name="pageSize">Size of the page.</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <returns>
        /// The list of items.
        /// </returns>
        public async Task<ResponseModel<List<T>>> GetItemsAsync(string queryString, Dictionary<string, object> parameters = null, int? pageSize = default, string continuationToken = default, CancellationToken cancellationToken = default)
        {
            ResponseModel<List<D>> result = await GetItemsAsync<D>(queryString, parameters, pageSize, continuationToken, cancellationToken);
            return _mapper.Map<ResponseModel<List<D>>, ResponseModel<List<T>>>(result);
        }
    }
}
