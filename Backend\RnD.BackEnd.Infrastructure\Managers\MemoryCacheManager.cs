﻿using System;
using Microsoft.Extensions.Caching.Memory;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Managers;

namespace RnD.BackEnd.Infrastructure.Managers
{
    /// <summary>
    /// Memory cache manager class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Infrastructure.Managers.ICacheManager" />
    public class MemoryCacheManager : ICacheManager
    {
        private readonly long CACHE_SIZE_LIMIT = 50;

        public MemoryCache _cache { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="MemoryCacheManager"/> class.
        /// </summary>
        public MemoryCacheManager()
        {
            _cache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = CACHE_SIZE_LIMIT
            });
        }

        /// <summary>
        /// Gets a cached object by it's key.
        /// </summary>
        /// <param name="cacheKey">The cache key.</param>
        /// <returns>
        /// The cached object.
        /// </returns>
        public T Get<T>(string cacheKey)
        {
            T output = default;

            if (_cache.TryGetValue(cacheKey, out T cacheEntry))
            {
                output = cacheEntry;
            }

            return output;
        }

        /// <summary>
        /// Adds or Updates an object into cache, with a given cache key.
        /// </summary>
        /// <param name="cacheKey">The cache key.</param>
        /// <param name="value">The value.</param>
        public void Set<T>(string cacheKey, T value)
        {
            _cache.Set(cacheKey, value, new MemoryCacheEntryOptions
            {
                Priority = CacheItemPriority.NeverRemove,
                Size = 1
            });
        }

        /// <summary>
        /// Adds or Updates an object into cache, with a given cache key.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="cacheKey">The cache key.</param>
        /// <param name="value">The value.</param>
        /// <param name="expirationHours">The expiration hours.</param>
        public void Set<T>(string cacheKey, T value, int expirationHours)
        {
            _cache.Set(cacheKey, value, new MemoryCacheEntryOptions
            {
                Size = 1,
                AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(expirationHours)
            });
        }

        /// <summary>
        /// Removes the cached object by it's cache key.
        /// </summary>
        /// <param name="cacheKey">The cache key.</param>
        public void Remove(string cacheKey)
        {
            _cache.Remove(cacheKey);
        }

        /// <summary>
        /// Resets this instance.
        /// </summary>
        public void Reset()
        {
            _cache.Dispose();

            _cache = new MemoryCache(new MemoryCacheOptions
            {
                SizeLimit = CACHE_SIZE_LIMIT
            });
        }
    }
}
