﻿using System;
using RnD.BackEnd.API.Model.Vehicle;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.VehicleController
{
    /// <summary>
    /// Vehicle add or update request example
    /// </summary>
    public class VehicleAddOrUpdate : IExamplesProvider<VehicleDto>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to add or update.
        /// </returns>
        public VehicleDto GetExamples()
        {
            return new VehicleDto
            {
                Id = Guid.NewGuid().ToString(),
                Brand = "Honda",
                BrandId = "0004",
                FuelType = "PETROL",
                ModelName = "Accord",
                Version = "322",
                Year = 1998
            };
        }
    }
}
