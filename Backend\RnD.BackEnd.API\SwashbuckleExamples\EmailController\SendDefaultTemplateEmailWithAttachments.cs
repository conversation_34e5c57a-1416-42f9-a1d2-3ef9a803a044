﻿using System.Collections.Generic;
using RnD.BackEnd.API.Model.Email;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.EmailController
{
    /// <summary>
    /// Send Template Email With Attachments request example
    /// </summary>
    public class SendDefaultTemplateEmailWithAttachments : IExamplesProvider<EmailAttachmentsDto<EmailDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to Send Default Template Email With Attachments.
        /// </returns>
        public EmailAttachmentsDto<EmailDto> GetExamples()
        {
            return new EmailAttachmentsDto<EmailDto>
            {
                Attachments = new List<Microsoft.AspNetCore.Http.IFormFile> { },
                TemplateData = new EmailDto
                {
                    Subject = "An e-mail with attachments",
                    Message = "The attachments email message."
                },
                Recipients = new List<string> { "<EMAIL>" }
            };
        }
    }
}
