﻿CREATE PROCEDURE [dbo].[spGetWeathers]
	@Id UNIQUEIDENTIFIER = NULL,
	@Location NVARCHAR(100) = NULL,
	@Temperature INT = NULL,
	@Summary NVARCHAR(256) = NULL,
	@SortField NVARCHAR(50) = 'Id',
	@SortOrder NVARCHAR(4) = 'ASC',
	@Skip INT = 0,
	@Take INT = 1000000
AS
	SELECT 
		[Id],
		[Location],
		[Temperature], 
		[Summary],
		[SYS_STATUS] SysStatus,
		[SYS_CREATE_DATE] SysCreateDate,
		[SYS_CREATE_USER_ID] SysCreateUserId,
		[SYS_MODIFY_DATE] SysModifyDate,
		[SYS_MODIFY_USER_ID] SysModifyUserId,
		[SYS_ROWVERSION] SysRowVersion
	FROM [dbo].[Weather]
	WHERE (@Id IS NULL OR Id = @Id)
	AND (@Location IS NULL OR Location = @Location)
	AND (@Temperature IS NULL OR Temperature = @Temperature)
	AND (@Summary IS NULL OR Summary = @Summary)
	AND SYS_STATUS = 'A'
	ORDER BY 
		CASE WHEN @SortOrder = 'ASC' AND @SortField = 'Id' THEN [Id] END ASC,
		CASE WHEN @SortOrder = 'DESC' AND @SortField = 'Id' THEN [Id] END DESC,
		CASE WHEN @SortOrder = 'ASC' AND @SortField = 'Location' THEN [Location] END ASC,
		CASE WHEN @SortOrder = 'DESC' AND @SortField = 'Location' THEN [Location] END DESC,
		CASE WHEN @SortOrder = 'ASC' AND @SortField = 'Temperature' THEN [Temperature] END ASC,
		CASE WHEN @SortOrder = 'DESC' AND @SortField = 'Temperature' THEN [Temperature] END DESC,
		CASE WHEN @SortOrder = 'ASC' AND @SortField = 'Summary' THEN [Summary] END ASC,
		CASE WHEN @SortOrder = 'DESC' AND @SortField = 'Summary' THEN [Summary] END DESC,

		CASE WHEN @SortOrder = 'ASC' AND @SortField = 'SysStatus' THEN [SYS_STATUS] END ASC,
		CASE WHEN @SortOrder = 'DESC' AND @SortField = 'SysStatus' THEN [SYS_STATUS] END DESC,
		CASE WHEN @SortOrder = 'ASC' AND @SortField = 'SysCreateDate' THEN [SYS_CREATE_DATE] END ASC,
		CASE WHEN @SortOrder = 'DESC' AND @SortField = 'SysCreateDate' THEN [SYS_CREATE_DATE] END DESC,
		CASE WHEN @SortOrder = 'ASC' AND @SortField = 'SysCreateUserId' THEN [SYS_CREATE_USER_ID] END ASC,
		CASE WHEN @SortOrder = 'DESC' AND @SortField = 'SysCreateUserId' THEN [SYS_CREATE_USER_ID] END DESC,
		CASE WHEN @SortOrder = 'ASC' AND @SortField = 'SysModifyDate' THEN [SYS_MODIFY_DATE] END ASC,
		CASE WHEN @SortOrder = 'DESC' AND @SortField = 'SysModifyDate' THEN [SYS_MODIFY_DATE] END DESC,
		CASE WHEN @SortOrder = 'ASC' AND @SortField = 'SysModifyUserId' THEN [SYS_MODIFY_USER_ID] END ASC,
		CASE WHEN @SortOrder = 'DESC' AND @SortField = 'SysModifyUserId' THEN [SYS_MODIFY_USER_ID] END DESC
	OFFSET @Skip ROWS
	FETCH NEXT @Take ROWS ONLY
RETURN 0