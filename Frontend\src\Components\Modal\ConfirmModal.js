import React from 'react';
import PropTypes from 'prop-types';
import {
    Button,
    Dialog,
    DialogTitle,
    DialogActions,
    DialogContent,
    Divider,
    Typography
} from '@mui/material';
import {
    Check as CheckIcon,
    Close as CloseIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { styled } from '@mui/system';

const StyledActions = styled(DialogActions)(({ theme }) => ({
    padding: theme.spacing(2),
    display: 'flex',
    justifyContent: 'flex-end'
}));

const StyledSaveButton = styled(Button)(({ theme }) => ({
    background: theme.palette.primary.main,
    marginLeft: theme.spacing(2),
    width: 120
}));
function ConfirmModal({ open, onClose, title, onConfirm, message }) {
    const { t } = useTranslation();

    function breakMessageLine(text) {
        const newText = [];
        if (text.includes(".")) {
            const splitted = text.split(".");

            for (let i = 0; i < splitted.length; i++) {
                newText[i] = splitted[i]

                if (i < splitted.length - 1) {
                    newText[i] += '.';
                }
            }
        }
        if (newText.length > 0) {
            return <>
                {newText.map((txt) => {
                    return (<>
                        {txt}
                        <br />
                        <br />
                    </>
                    )
                })}
            </>
        }
        return text;
    }

    return (
        <Dialog
            maxWidth="sm"
            onClose={onClose}
            open={open}
        >
            <DialogTitle>
                {title}
            </DialogTitle>
            <Divider />
            <DialogContent style={{ minHeight: 80, minWidth: 600 }}>
                <Typography
                    align="left"
                    gutterBottom
                    variant="h4"
                >
                    {breakMessageLine(message)}
                </Typography>
            </DialogContent>
            <Divider />
            <StyledActions>
                <Button
                    startIcon={<CloseIcon fontSize="small" />}
                    sx={{ width: 120 }}
                    // component={RouterLink}
                    // to="/"
                    variant="outlined"
                    onClick={onClose}
                >
                    {t('common:buttons.cancel')}
                </Button>
                <StyledSaveButton
                    startIcon={<CheckIcon fontSize="small" />}
                    // component={RouterLink}
                    // to="/"
                    variant="contained"
                    onClick={onConfirm}
                >
                    {t('common:buttons.confirm')}
                </StyledSaveButton>
            </StyledActions>
        </Dialog>
    );
}

ConfirmModal.propTypes = {
    onClose: PropTypes.func,
    open: PropTypes.bool,
    title: PropTypes.string,
    onConfirm: PropTypes.func,
    message: PropTypes.string,
};

export default ConfirmModal;
