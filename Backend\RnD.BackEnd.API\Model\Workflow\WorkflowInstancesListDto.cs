﻿using System.Collections.Generic;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Workflow instances list DTO class
    /// </summary>
    public class WorkflowInstancesListDto
    {
        /// <summary>
        /// Gets or sets the workflow instances.
        /// </summary>
        /// <value>
        /// The workflow instances.
        /// </value>
        public IList<WorkflowInstanceDto> WorkflowInstances { get; set; }

        /// <summary>
        /// Gets or sets the total rows.
        /// </summary>
        /// <value>
        /// The total rows.
        /// </value>
        public int TotalRows { get; set; }
    }
}
