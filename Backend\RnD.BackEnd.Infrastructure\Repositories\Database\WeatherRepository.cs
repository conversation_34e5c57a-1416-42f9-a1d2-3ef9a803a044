﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Dapper;
using RnD.BackEnd.Domain.DataModels;
using RnD.BackEnd.Domain.Entities.Weather;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database;
using RnD.BackEnd.Infrastructure.Constants;
using RnD.BackEnd.Infrastructure.DataModels.Weather;
using RnD.BackEnd.Infrastructure.Repositories.Database.Base;

namespace RnD.BackEnd.Infrastructure.Repositories.Database
{
    /// <summary>
    /// Weather repository class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Infrastructure.Repositories.Database.Base.BaseSqlRepository&lt;RnD.BackEnd.Domain.Entities.Weather.Weather, RnD.BackEnd.Infrastructure.DataModels.Weather.WeatherModel&gt;" />
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database.IWeatherRepository" />
    public class WeatherRepository : BaseSqlRepository<Weather, WeatherModel>, IWeatherRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="WeatherRepository" /> class.
        /// </summary>
        /// <param name="mapper">The mapper.</param>
        public WeatherRepository(IMapper mapper)
            : base(mapper, SqlStoredProcedures.InsertUpdateDeleteWeather, SqlStoredProcedures.GetWeathers)
        {
        }

        /// <summary>
        /// Lists the weather records.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="location">The location.</param>
        /// <param name="itemsPerPage">The number of items per page.</param>
        /// <param name="page">The page (starts with 1)</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">The sort ascending.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// The list of weather records.
        /// </returns>
        public async Task<ResponseModel<IList<Weather>>> ListAsync(
            Guid id,
            string location,
            int? itemsPerPage,
            int? page,
            string sortField = null,
            bool sortAscending = false,
            CancellationToken? cancellationToken = null)
        {
            ResponseModel<IList<Weather>> result = new ResponseModel<IList<Weather>>();
            DynamicParameters parameters = new DynamicParameters();

            if (id != Guid.Empty)
            {
                parameters.Add("@Id", id, DbType.Guid);
            }

            if (!string.IsNullOrWhiteSpace(location))
            {
                parameters.Add("@Location", location, DbType.String);
            }

            AddPaginationAndSortToQuery(parameters, itemsPerPage, page, sortField, sortAscending);

            IList<WeatherModel> response = await QuerySPAsync<WeatherModel>(_queryStoredProcedure, parameters, null, cancellationToken);
            result.Value = _mapper.Map<IList<WeatherModel>, IList<Weather>>(response);

            return result;
        }
    }
}
