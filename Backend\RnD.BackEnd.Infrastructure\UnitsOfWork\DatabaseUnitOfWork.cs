﻿using System;
using System.Data;
using System.Data.SqlClient;
using Microsoft.Extensions.Options;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.UnitsOfWork;
using RnD.BackEnd.Domain.Settings;

namespace RnD.BackEnd.Infrastructure.UnitsOfWork
{
    /// <summary>
    /// Database UnitOfWork class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Infrastructure.UnitsOfWork.IDatabaseUnitOfWork" />
    public class DatabaseUnitOfWork : IDatabaseUnitOfWork
    {
        private readonly string _connectionString;
        private IWeatherRepository _weatherRepository;

        private IDbConnection connection;
        private IDbTransaction transaction;
        private bool disposed;

        /// <summary>
        /// Initializes a new instance of the <see cref="DatabaseUnitOfWork" /> class.
        /// </summary>
        /// <param name="weatherRepository">The weather repository.</param>
        /// <param name="connectionStrings">The connection strings.</param>
        public DatabaseUnitOfWork(IWeatherRepository weatherRepository, IOptions<ConnectionStrings> connectionStrings)
        {
            _connectionString = connectionStrings.Value.DatabaseCS;
            connection = new SqlConnection(_connectionString);
            _weatherRepository = weatherRepository;
        }

        /// <summary>
        /// Gets the weather repository.
        /// </summary>
        /// <value>
        /// The weather repository.
        /// </value>
        public IWeatherRepository WeatherRepository
        {
            get
            {
                _weatherRepository.Initialize(connection, transaction);
                return _weatherRepository;
            }
        }


        /// <summary>
        /// Begins a transaction for all repositories
        /// </summary>
        public void Begin()
        {
            if (connection != null && connection.State != ConnectionState.Open)
            {
                connection.Open();
            }
            else if (connection == null)
            {
                connection = new SqlConnection(_connectionString);
                connection.Open();
            }

            transaction = connection.BeginTransaction();
            BeginTransactionOnRepositories();
        }

        /// <summary>
        /// Handles transaction commit, rollback and dispose
        /// </summary>
        public void Commit()
        {
            try
            {
                transaction.Commit();
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
            finally
            {
                transaction.Dispose();
                ResetRepositories();
            }
        }

        /// <summary>
        /// Rollbacks the transaction.
        /// </summary>
        public void Rollback()
        {
            transaction.Rollback();
            transaction.Dispose();
            ResetRepositories();
        }

        /// <summary>
        /// Disposes the transaction and connection
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Begins the transaction on repositories.
        /// </summary>
        private void BeginTransactionOnRepositories()
        {
            _weatherRepository.Initialize(connection, transaction);
        }

        /// <summary>
        /// Resets the repositories.
        /// </summary>
        private void ResetRepositories()
        {
            _weatherRepository = null;
        }

        /// <summary>
        /// Releases unmanaged and - optionally - managed resources.
        /// </summary>
        /// <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    if (transaction != null)
                    {
                        transaction.Dispose();
                        transaction = null;
                    }
                    if (connection != null)
                    {
                        connection.Dispose();
                        connection = null;
                    }
                }

                disposed = true;
            }
        }
    }
}
