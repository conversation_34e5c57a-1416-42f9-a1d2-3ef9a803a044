﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Microsoft.Graph;
using Microsoft.Identity.Client;
using RnD.BackEnd.Domain.Settings;

namespace RnD.BackEnd.Domain.Utilities
{
    /// <summary>
    /// Graph utilities class
    /// </summary>
    public static class GraphUtilities
    {
        /// <summary>
        /// Gets the graph client for user.
        /// </summary>
        /// <param name="authSettings">The authentication settings.</param>
        /// <param name="userAccessToken">The user access token.</param>
        /// <returns>
        /// The graph client for user.
        /// </returns>
        public static GraphServiceClient GetGraphClientForUser(AzureAd authSettings, string userAccessToken)
        {
            try
            {
                // Cria um HttpClient que inclui o token de acesso do usuário
                var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", userAccessToken);

                // Usa o HttpClient diretamente com o GraphServiceClient
                return new GraphServiceClient(httpClient);
            }
            catch (Exception ex)
            {
                throw new Exception("Erro ao criar GraphServiceClient para usuário: " + ex.Message);
            }
        }

        /// <summary>
        /// Gets the graph client for application.
        /// </summary>
        /// <param name="authSettings">The authentication settings.</param>
        /// <returns>
        /// The grapht client for app.
        /// </returns>
        public static async Task<GraphServiceClient> GetGraphClientForAppAsync(AzureAd authSettings)
        {
            try
            {
                // Cria uma aplicação confidencial para autenticação
                var app = ConfidentialClientApplicationBuilder
                    .Create(authSettings.ClientId)
                    .WithTenantId(authSettings.TenantId)
                    .WithClientSecret(authSettings.ClientSecret)
                    .Build();

                // Obtém o token para o escopo do Graph
                var scopes = new[] { authSettings.GraphDefaultScope };
                var authResult = await app.AcquireTokenForClient(scopes).ExecuteAsync();

                // Cria um HttpClient que inclui o token de acesso
                var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authResult.AccessToken);

                // Usa o HttpClient diretamente com o GraphServiceClient
                return new GraphServiceClient(httpClient);
            }
            catch (Exception ex)
            {
                throw new Exception("Erro ao criar GraphServiceClient para aplicação: " + ex.Message);
            }
        }
    }
}
