import PropTypes from "prop-types";
import Bread from "../../Components/Bread/BreadDynamic";
import { Container } from "@mui/material";
import useSettings from "../../Context/Hooks/useSettings";

const Viewport = ({ children }) => {
    const { settings } = useSettings();

    return (
        <Container maxWidth={settings.compact ? 'xl' : false}>
            <Bread />
            {children}
        </Container>
    );
};

Viewport.propTypes = {
    children: PropTypes.node,
};

export default Viewport;
