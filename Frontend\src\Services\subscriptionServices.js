import axios from 'axios';
import handleMethod from '../Utils/APIutils/handleMethod';

const API_BASE_URL = `${process.env.REACT_APP_WEBAPI_URL}/api`;

export const getSubscriptions = async () => {
    try {
        const response = await handleMethod({
            method: `${API_BASE_URL}/ConsumoSubscricao`,
            type: 'GET'
        });

        console.log("🔍 Dados da API:", response);

        if (!response || response.error) {
            throw new Error(`Erro ao buscar subscrições: ${response?.description || 'Erro desconhecido'}`);
        }

        const data = response.data || response;
        if (!Array.isArray(data)) {
            console.error("❌ ERRO: API não retornou um array!", data);
            return [];
        }

        const subscriptionsMap = new Map();

        data.forEach(item => {
            const subscriptionId = item.subscriptionID || item.SubscriptionID;
            if (!subscriptionId) {
                console.warn("⚠️ Subscrição sem ID:", item);
                return;
            }

            if (!subscriptionsMap.has(subscriptionId)) {
                subscriptionsMap.set(subscriptionId, {
                    subscriptionId,
                    totalCost: 0,
                    totalCostUSD: 0,
                    resourceCount: 0,
                    startDate: null,
                    endDate: null,
                    resources: []
                });
            }

            const subscription = subscriptionsMap.get(subscriptionId);
            subscription.totalCost += parseFloat(item.cost || 0);
            subscription.totalCostUSD += parseFloat(item.costUSD || 0);
            subscription.resourceCount++;
            subscription.resources.push(item);

            if (!subscription.startDate || new Date(item.startDate) < new Date(subscription.startDate)) {
                subscription.startDate = item.startDate;
            }

            if (!subscription.endDate || new Date(item.endDate) > new Date(subscription.endDate)) {
                subscription.endDate = item.endDate;
            }
        });

        const subscriptions = Array.from(subscriptionsMap.values());
        console.log("📦 Subscrições agrupadas:", subscriptions);
        return subscriptions;
    } catch (error) {
        console.error("❌ ERRO ao buscar subscrições:", error);
        return [];
    }
};

export const getSubscriptionById = async (subscriptionId) => {
    try {
        const response = await handleMethod({
            method: `${API_BASE_URL}/ConsumoSubscricao/subscription/${subscriptionId}`,
            type: 'GET'
        });

        if (!response || response.error) {
            throw new Error(`Erro ao buscar detalhes da subscrição: ${response?.description || 'Erro desconhecido'}`);
        }

        const data = response.data || response;
        if (!Array.isArray(data)) {
            console.error("❌ ERRO: API não retornou um array!", data);
            return null;
        }

        const subscription = {
            subscriptionId,
            totalCost: 0,
            totalCostUSD: 0,
            resourceCount: data.length,
            startDate: null,
            endDate: null,
            resources: data.map(item => ({
                id: item.id,
                resourceName: item.resourceName,
                resourceGroup: item.resourceGroup,
                cost: parseFloat(item.cost || 0),
                costUSD: parseFloat(item.costUSD || 0),
                startDate: item.startDate,
                endDate: item.endDate
            }))
        };

        data.forEach(item => {
            subscription.totalCost += parseFloat(item.cost || 0);
            subscription.totalCostUSD += parseFloat(item.costUSD || 0);

            if (!subscription.startDate || new Date(item.startDate) < new Date(subscription.startDate)) {
                subscription.startDate = item.startDate;
            }

            if (!subscription.endDate || new Date(item.endDate) > new Date(subscription.endDate)) {
                subscription.endDate = item.endDate;
            }
        });

        return subscription;
    } catch (error) {
        console.error("❌ ERRO ao buscar detalhes da subscrição:", error);
        return null;
    }
};

export const getSubscriptionByPeriod = async (subscriptionId, startDate, endDate) => {
    try {
        const formattedStartDate = startDate.toISOString().split('T')[0];
        const formattedEndDate = endDate.toISOString().split('T')[0];

        const response = await handleMethod({
            method: `${API_BASE_URL}/ConsumoSubscricao/period?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
            type: 'GET'
        });

        if (!response || response.error) {
            throw new Error(`Erro ao buscar consumo por período: ${response?.description || 'Erro desconhecido'}`);
        }

        const data = response.data || response;
        if (!Array.isArray(data)) {
            console.error("❌ ERRO: API não retornou um array!", data);
            return [];
        }

        const filteredData = data.filter(item =>
            (item.subscriptionID || item.SubscriptionID) === subscriptionId);

        return filteredData.map(item => ({
            id: item.id,
            resourceName: item.resourceName,
            resourceGroup: item.resourceGroup,
            cost: parseFloat(item.cost || 0),
            costUSD: parseFloat(item.costUSD || 0),
            startDate: item.startDate,
            endDate: item.endDate
        }));
    } catch (error) {
        console.error("❌ ERRO ao buscar consumo por período:", error);
        return [];
    }
};
