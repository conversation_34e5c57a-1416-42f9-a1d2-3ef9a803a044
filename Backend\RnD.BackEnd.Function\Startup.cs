﻿using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Managers;
using RnD.BackEnd.Infrastructure.Managers;
using RnD.BackEnd.Licensing.Extensions;

[assembly: FunctionsStartup(typeof(RnD.BackEnd.Function.Startup))]

namespace RnD.BackEnd.Function
{
    /// <summary>
    /// Startup for the Azure Function. Allows registration of DI services
    /// </summary>
    /// <seealso cref="Microsoft.Azure.Functions.Extensions.DependencyInjection.FunctionsStartup" />
    public class Startup : FunctionsStartup
    {
        /// <summary>
        /// Configures the specified builder.
        /// </summary>
        /// <param name="builder">The builder.</param>
        public override void Configure(IFunctionsHostBuilder builder)
        {
            builder.Services.AddMemoryCache();

            builder.Services.ConfigureLicenseValidation();

            builder.Services.AddSingleton<IApiConnectorManager, ApiConnectorManager>();
        }
    }
}
