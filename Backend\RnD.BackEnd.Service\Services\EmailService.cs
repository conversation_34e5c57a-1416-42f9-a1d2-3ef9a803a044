﻿using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using RnD.BackEnd.Domain.Entities.Email;
using RnD.BackEnd.Domain.Entities.Exception;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Extensions;
using RnD.BackEnd.Domain.Interfaces.Services;
using RnD.BackEnd.Domain.Settings;
using RnD.BackEnd.Email.BusinessMessages;
using RnD.BackEnd.Email.Entities;
using RnD.BackEnd.Email.Interfaces;

namespace RnD.BackEnd.Service.Services
{
    /// <summary>
    /// The Email service class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Services.IEmailService" />
    public class EmailService : IEmailService
    {
        private readonly IEmailManager _emailManager;
        private readonly EmailSettings _emailSettings;

        /// <summary>
        /// Initializes a new instance of the <see cref="EmailService" /> class.
        /// </summary>
        /// <param name="settings">The settings.</param>
        /// <param name="emailManager">The email manager.</param>
        public EmailService(IOptions<AppSettings> settings, IEmailManager emailManager)
        {
            _emailSettings = settings.Value.EmailSettings;
            _emailManager = emailManager;
        }

        /// <summary>
        /// Sends the templated email.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="templateId">The template identifier.</param>
        /// <returns>
        /// The service response.
        /// </returns>
        public async Task<ServiceOutput<bool>> SendTemplatedEmail(EmailMessage<object> request, string templateId)
        {
            return await SendEmail(new EmailMessage()
            {
                FromEmailAddress = _emailSettings.FromEmailAddress,
                FromUserName = _emailSettings.FromUserName,
                EmailData = request.TemplateData,
                Recipients = request.Recipients,
                RecipientsBCC = request.RecipientsBCC,
                RecipientsCC = request.RecipientsCC,
                TemplateId = templateId
            });
        }

        /// <summary>
        /// Sends the code academy email.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The service response.
        /// </returns>
        public async Task<ServiceOutput<bool>> SendCodeAcademyEmail(EmailMessage<CodeAcademyTemplate> request)
        {
            return await SendEmail(new EmailMessage()
            {
                FromEmailAddress = _emailSettings.FromEmailAddress,
                FromUserName = _emailSettings.FromUserName,
                EmailData = request.TemplateData,
                Recipients = request.Recipients,
                RecipientsBCC = request.RecipientsBCC,
                RecipientsCC = request.RecipientsCC,
                TemplateId = _emailSettings.CodeAcademyTemplateId
            });
        }

        /// <summary>
        /// Sends the default template email.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The service response.
        /// </returns>
        public async Task<ServiceOutput<bool>> SendDefaultTemplateEmail(EmailMessage<BaseEmail> request)
        {
            return await SendEmail(new EmailMessage()
            {
                FromEmailAddress = _emailSettings.FromEmailAddress,
                FromUserName = _emailSettings.FromUserName,
                EmailData = request.TemplateData,
                Recipients = request.Recipients,
                RecipientsBCC = request.RecipientsBCC,
                RecipientsCC = request.RecipientsCC,
                TemplateId = _emailSettings.DefaultTemplateId
            });
        }

        /// <summary>
        /// Sends the default template email with attachments.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The service response.
        /// </returns>
        public async Task<ServiceOutput<bool>> SendDefaultTemplateEmailWithAttachments(EmailAttachments<BaseEmail> request)
        {
            List<EmailAttachment> attachments = new List<EmailAttachment>();
            if (request.Attachments != null && request.Attachments.Count > 0)
            {
                foreach (IFormFile file in request.Attachments)
                {
                    byte[] fileBytes = null;
                    using (MemoryStream ms = new MemoryStream())
                    {
                        file.CopyTo(ms);
                        fileBytes = ms.ToArray();
                    }

                    attachments.Add(new EmailAttachment()
                    {
                        File = fileBytes,
                        FileName = file.FileName
                    });
                }
            }

            return await SendEmail(new EmailMessage()
            {
                Attachments = attachments,
                FromEmailAddress = _emailSettings.FromEmailAddress,
                FromUserName = _emailSettings.FromUserName,
                EmailData = request.TemplateData,
                Recipients = request.Recipients,
                RecipientsBCC = request.RecipientsBCC,
                RecipientsCC = request.RecipientsCC,
                TemplateId = _emailSettings.DefaultTemplateId
            });
        }

        #region Private methods

        /// <summary>
        /// Sends the email.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <returns>
        /// The service response.
        /// </returns>
        private async Task<ServiceOutput<bool>> SendEmail(EmailMessage message)
        {
            ServiceOutput<bool> output = new ServiceOutput<bool>()
            {
                Value = true
            };

            // Send email and return response
            SendEmailResponse response = await _emailManager.SendEmailAsync(message);

            output.Code = response.Code;

            if (output.Code == HttpStatusCode.BadRequest)
            {
                output.Value = false;
                output.BadRequest(new ModelException() { Messages = response.ExceptionMessages });
            }
            else if (output.Code != HttpStatusCode.OK)
            {
                output.Value = false;
                output.InternalServerError(string.Join(';', response.ExceptionMessages), EmailMessages.Error_Sending_Email);
            }

            return output;
        }

        #endregion Private methods
    }
}