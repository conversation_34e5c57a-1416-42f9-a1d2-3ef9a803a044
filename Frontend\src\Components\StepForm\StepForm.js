/* eslint-disable */
import React, { forwardRef, useState, useEffect } from 'react';
import { Box, Card, CardHeader, TextField, Grid, CardContent, Autocomplete, Typography, Button, Divider, FormHelperText, IconButton, Collapse } from '@mui/material';
import PropTypes from 'prop-types';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import Scrollbar from '../Scrollbar';
import {
  DragIndicator as DragIndicatorIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { StepFormMenu, StepModuleForm } from './';

const StepForm = React.memo(forwardRef((props, ref) => {
  const { dragging, index, options, process, setFieldValue, setFieldTouched, errors, touched, handleRemoveStep, handleRemoveModule,
    processType, handleAddNewModule, handleModuleDragEnd, getProcessStepDocument, opportunityView, disableAll, provided, dragHandleProps,
    handleFormikBlur, handleFormikChange, activeStepCard, setActiveStepCard, activeModuleCard, setActiveModuleCard, setWasModified, wasModified, processTypeCode, ...other } = props;
  const [renaming, setRenaming] = useState(true);
  const [stepsOpened, setStepsOpened] = useState(true);
  const [toggle, setToggle] = useState(true);

  const handleDragEnd = ({ source, destination }) => {
    handleModuleDragEnd({ source, destination }, index);
  };

  const handleModified = () => {
    if (setWasModified) setWasModified(true);
  }

  const handleToggle = () => {
    if (toggle) return <KeyboardArrowUpIcon fontSize="small" />;
    return <KeyboardArrowDownIcon fontSize="small" />;
  }

  const handleDragStart = async ({ source, destination, ...rest }) => {
    if (true) return;
    console.log(source);
    console.log(destination);
  };

  const handleOnBlur = (e) => {
    if (process.stepName !== "") {
      setRenaming(!renaming);
    }
    handleFormikBlur(e);
  };

  const handleOnChange = (e) => {
    setFieldValue(`stepsList[${index}].stepName`, e.target.value);
    handleModified();
    handleFormikChange(e);
  };

  useEffect(() => {
    if (process.moduleList.length > 0) {
      setStepsOpened(true);
    }
  }, [process.moduleList.length]);

  function handleSetActiveStepCard(index) {
    if (index !== activeStepCard)
      setActiveModuleCard(undefined)
    setActiveStepCard(index)
  }

  const getError = (fieldName) => Boolean(touched?.[fieldName] && errors?.[fieldName]);

  const processTypeContinuos = "CONTINUOUS";

  const isEqualOptionValue = (option, value) => {
    return option?.value && value?.value && option.value === value.value;
  }

  const getOptions = (_options) => {
    return _options || [];
  }

  const isDisabled = (disableAll, processTypeCode, processTypeContinuos) => {
    return disableAll && processTypeCode !== processTypeContinuos;
  };

  return (
    <Box
      ref={ref}
      sx={{
        outline: 'none',
        py: 1
      }}
      {...other}
    >
      <Grid
        container
        spacing={3}
      >
        <Grid
          item
          xs={12}
        >
          <Card
            className={activeStepCard === index ? "activeCard" : ""}
            raised={dragging}
            sx={{
              ...(dragging && {
                backgroundColor: 'background.paper'
              }),
              width: '100%',
            }}
            variant={dragging ? 'elevation' : 'outlined'}
            onClick={() => handleSetActiveStepCard(index)}
          >
            <CardHeader
              title={
                <>
                  <div style={{
                    display: "flex",
                    alignItems: "center"
                  }}
                  >
                    <div
                      style={{
                        flex: 1,
                        maxWidth: 24
                      }}
                      {...provided.dragHandleProps}
                    >
                      <DragIndicatorIcon />
                    </div>
                    <div
                      style={{
                        flex: 1
                      }}
                    >
                      {
                        renaming && !opportunityView ?
                          <TextField
                            name="stepName"
                            placeholder="Nova Fase"
                            onBlur={(e) => handleOnBlur(e)}
                            onChange={(e) => handleOnChange(e)}
                            value={process.stepName}
                            variant="standard"
                            //autoFocus={process.stepName === ""}
                            sx={{
                              minWidth: "15vw",
                              maxWidth: "300px"
                            }}
                            error={getError('stepName')}
                            helperText={errors?.stepName}
                          />
                          : process.stepName
                      }
                      {
                        (errors?.stepName) && !renaming && (
                          <Box sx={{ mt: 2 }}>
                            <FormHelperText error>
                              {errors?.stepName}
                            </FormHelperText>
                          </Box>
                        )
                      }
                    </div>
                  </div>
                </>
              }
              action={!opportunityView && (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center"
                  }}
                >
                  <StepFormMenu
                    handleRenaming={() => setRenaming(!renaming)}
                    handleRemoveStep={() => handleRemoveStep(index)}
                  />
                  <div>
                    <IconButton
                      aria-label="collapse"
                      size="small"
                      onClick={() => setToggle(!toggle)}
                    >
                      {handleToggle()}
                    </IconButton>
                  </div>
                </div>
              )}
            />
            <Divider />
            <Collapse in={toggle}>
              <CardContent>
                <Grid
                  container
                  spacing={3}
                >
                  <Grid
                    item
                    md={6}
                    xs={12}
                  >
                    <Autocomplete
                      getOptionLabel={(option) => option.text}
                      isOptionEqualToValue={isEqualOptionValue}
                      options={getOptions(options?.rolesList) || []}
                      multiple
                      disabled={isDisabled()}
                      value={process?.rolePermissions || null}
                      onChange={(e, value) => { setFieldValue(`stepsList[${index}].rolePermissions`, value); handleModified(); }}
                      renderInput={(params) => (
                        <TextField
                          fullWidth
                          label="Permissões"
                          name="rolePermissions"
                          variant="outlined"
                          onBlur={() => setFieldTouched(`stepsList[${index}].rolePermissions`)}
                          error={getError('rolePermissions')}
                          helperText={errors?.rolePermissions}
                          {...params}
                        />
                      )}
                    />
                  </Grid>
                  <Grid
                    item
                    md={6}
                    xs={12}
                  >
                    <Autocomplete
                      getOptionLabel={(option) => option.text}
                      isOptionEqualToValue={isEqualOptionValue}
                      options={getOptions(options?.statusList)}
                      disabled={opportunityView}
                      multiple
                      value={process?.statusList || null}
                      onChange={(e, value) => { setFieldValue(`stepsList[${index}].statusList`, value); handleModified(); }}
                      renderInput={(params) => {
                        params.inputProps.autoComplete = 'new-password';
                        return (
                          <TextField
                            fullWidth
                            label="Estados"
                            name="statusList"
                            variant="outlined"
                            // onBlur={setFieldTouched(`stepsList[${index}].rolePermissions`)}
                            error={getError('statusList')}
                            helperText={errors?.statusList}
                            {...params}
                          />
                        );
                      }}
                    />
                  </Grid>
                </Grid>
                <DragDropContext onDragEnd={handleDragEnd} onDragStart={handleDragStart}>
                  <Box
                    sx={{ pt: 3 }}
                  >
                    <Typography
                      variant="formTitle"
                    >
                      {`Etapa${process.moduleList.length > 1 ? 's' : ''} da fase ${process.moduleList.length > 0 ? `(${process.moduleList.length})` : ''}`}
                    </Typography>
                  </Box>
                  <Scrollbar style={{ paddingBottom: "20px" }}>
                    <Droppable
                      droppableId="droppableId-2"
                      key={2}
                      type="list"
                    >
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                        >
                          {
                            process.moduleList.map((module, i) => (
                              <Draggable
                                draggableId={`mod_dragabble-${i}`}
                                index={i}
                                key={`mod_dragabbleKey-${i.toString()}`}
                                isDragDisabled={opportunityView}
                              >
                                {(_provided, snapshot) => (
                                  <>
                                    {_provided.placeholder}
                                    <StepModuleForm
                                      dragging={snapshot.isDragging}
                                      style={{ ..._provided.draggableProps.style }}
                                      key={`Module-${module.id}`}
                                      provided={_provided}
                                      dragProps={{ ..._provided.dragHandleProps }}
                                      process={module}
                                      step={process}
                                      options={options}
                                      stepIndex={index}
                                      moduleIndex={i}
                                      processType={processType.toString()}
                                      setFieldValue={setFieldValue}
                                      touched={touched && touched.moduleList && touched.moduleList[i] ? touched.moduleList[i] : []}
                                      errors={errors && errors.moduleList && errors.moduleList[i] ? errors.moduleList[i] : []}
                                      getProcessStepDocument={getProcessStepDocument}
                                      handleRemoveModule={handleRemoveModule}
                                      opportunityView={opportunityView}
                                      disableAll={disableAll}
                                      handleFormikBlur={handleFormikBlur}
                                      handleFormikChange={handleFormikChange}
                                      activeModuleCard={activeModuleCard}
                                      setActiveModuleCard={setActiveModuleCard}
                                      setWasModified={setWasModified}
                                      wasModified={wasModified}
                                      processTypeCode={processTypeCode}
                                    />
                                  </>
                                )}
                              </Draggable>
                            ))
                          }
                        </div>)}
                    </Droppable>
                    {!opportunityView &&
                      <Button
                        color="primary"
                        startIcon={<AddIcon fontSize="small" />}
                        sx={{ m: 1 }}
                        variant="outlined"
                        onClick={() => handleAddNewModule(index)}
                        disabled={opportunityView}
                      >
                        Etapa
                      </Button>}
                  </Scrollbar>
                  {(errors?.moduleList) && touched?.moduleList && (typeof errors.moduleList === 'string' || errors.moduleList instanceof String) && (
                    <Box sx={{ mt: 2 }}>
                      <FormHelperText
                        error
                      >
                        {errors?.moduleList}
                      </FormHelperText>
                    </Box>
                  )}
                </DragDropContext>
              </CardContent>
            </Collapse>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}));

StepForm.propTypes = {
  dragging: PropTypes.bool,
  opportunityView: PropTypes.bool,
  index: PropTypes.string,
  processType: PropTypes.number,
  options: PropTypes.object,
  process: PropTypes.object.isRequired,
  setFieldValue: PropTypes.func.isRequired,
  setFieldTouched: PropTypes.func.isRequired,
  errors: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.array,
  ]),
  touched: PropTypes.array,
  handleRemoveStep: PropTypes.func,
  handleRemoveModule: PropTypes.func,
  handleAddNewModule: PropTypes.func,
  handleModuleDragEnd: PropTypes.func,
  getProcessStepDocument: PropTypes.func,
  provided: PropTypes.object,
  disableAll: PropTypes.bool,
  dragHandleProps: PropTypes.object,
  handleFormikBlur: PropTypes.func,
  handleFormikChange: PropTypes.func,
  activeStepCard: PropTypes.number,
  setActiveStepCard: PropTypes.func,
  activeModuleCard: PropTypes.number,
  setActiveModuleCard: PropTypes.func,
  setWasModified: PropTypes.func,
  wasModified: PropTypes.bool,
  processTypeCode: PropTypes.string
};

StepForm.defaultProps = {
  dragging: false
};

export default StepForm;
