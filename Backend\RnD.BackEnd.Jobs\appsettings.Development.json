{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Hangfire": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },

  "LicenseKey": "-FKDhZ0ywBJ-GAHA-5bz2VMCBcoGnVOSkurgzT74AXAaAZvab502yZ7fYnCoWWZnAxE7LFS958FeEGOXznQ4kHbW-n1tm6utwpTB0FCBQh_MxS7nvcD_mR44suUrJtDpavmpqSJdi2MPqrNTNmYWbIfoq99wIoB_0avEaHkwSur1Hw6YeQp_blBKPTl4h1f6m9QYhON9bB4jRBBQhxYZR_anCV-tiWUNBPjdE_jWU4Lp-NE773MjO52o4OlbpVs7zvheSUTBoTWytjrSBP4rYg",

  "ConnectionStrings": {
    "DatabaseCS": "Server=tcp:rnd-sql-server-bi4all-framework.database.windows.net,1433;Initial Catalog=rnd-sql-database;Persist Security Info=False;User ID=sqlUserFramework;Password=************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  },

  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "Domain": "bi4all.pt",
    "TenantId": "41bcb72d-c1e9-42e2-b451-eaded8635d70",
    "ClientId": "2b7321d3-dce0-43d8-b42d-1a348882eb40",
    "ClientSecret": "*********************************"
  },

  // See https://crontab.guru/ for CRON expressions
  "JobsSettings": {
    "launchJobs": true,
    "launchWorkflowJobs": false,
    "monthlyInterval": "0 14 1 * *",
    "checkOverdueTasksInterval": "0 2 * * *",
    "processBackgroundWorkflowsInterval": "*/5 * * * *"
  },

  "AppSettings": {
    "EmailSettings": {
      "AllowSendingEmails": true,
      "SendAlwaysToAddresses": [],
      "ApiKey": "*********************************************************************",
      "FromEmailAddress": "<EMAIL>",
      "FromUserName": "Knowledge & Innovation"
    },

    "WorkflowSettings": {
      "BaseUrl": "https://wfe-app-webapi-dev.azurewebsites.net",
      "AllowWorkflowEngine": true,
      "OverdueTasksSourceTypes": [
        "DOCUMENT",
        "VACATION",
        "PROJECT",
        "TIMESHEET"
      ],
      "CompleteBackgroundTasksSourceTypes": [
        "DOCUMENT",
        "VACATION",
        "PROJECT",
        "TIMESHEET"
      ],
      "TokenEndpoint": "/api/Authorization/GetToken",
      "OverdueTasksEndpoint": "/api/Tasks/CheckOverdueTasks",
      "InstancesRequiredForProcessingEndpoint": "/api/Instances/GetWorkflowInstancesRequiredForProcessing",
      "UpdateInstanceEndpoint": "/api/Instances/UpdateWorkflowInstance",
      "Secret": "5A7234753778214125442A472D4A614E645267556B58703273357638792F423F4528482B4D6250655368566D597133743677397A24432646294A404E63526654"
    }
  }
}