import React from 'react';
import PropTypes from 'prop-types';
import { Link as RouterLink } from 'react-router-dom';
import {
    Breadcrumbs,
    Link,
    Typography,
    Grid
} from '@mui/material';
import { ChevronRight } from '@mui/icons-material';
import { useTheme } from '@mui/styles';

const Bread = ({ levels, additionalContent, sx }) => {
    const theme = useTheme();
    if (!levels || levels.length <= 0) {
        return
    }
    return (
        levels && levels.length > 0 ?
            <Grid
                container
                spacing={2}
                sx={{ ...sx }}
            >
                <Grid
                    item
                    xs={12}
                >
                    <div
                        style={{
                            display: "flex",
                            alignItems: "center"
                        }}
                    >
                        <div>
                            <Typography
                                color="textPrimary"
                                variant="h3"
                            >
                                <b>
                                    {
                                        levels[levels.length - 1]?.label ?? levels[levels.length - 2]?.label
                                    }
                                </b>
                            </Typography>
                            {
                                levels.length > 1 &&
                                <Breadcrumbs
                                    aria-label="breadcrumb"
                                    separator={
                                        <ChevronRight
                                            fontSize="small"
                                            htmlColor="#B8BBC2"
                                        />
                                    }
                                >
                                    {
                                        levels.map((level, level_i) => (
                                            level.label && (
                                                <Typography
                                                    key={"breadcrumb_" + level_i}
                                                    color={level.color ? level.color : "#B8BBC2"}
                                                    variant="subtitle1"
                                                >
                                                    {
                                                        level.link ?
                                                            <Link
                                                                underline="none"
                                                                color="grey"
                                                                component={RouterLink}
                                                                to={level.link}
                                                                variant="subtitle1"
                                                            >
                                                                {level.label}
                                                            </Link>
                                                            :
                                                            level.label
                                                    }
                                                </Typography>
                                            )
                                        ))
                                    }
                                </Breadcrumbs>
                            }
                        </div>
                        <div
                            style={{
                                marginLeft: theme.spacing(1),
                                marginTop: "auto"
                            }}
                            className="additionalContent"
                        >
                            {
                                additionalContent
                            }
                        </div>
                    </div>
                </Grid>
            </Grid>
            :
            null
    )
}

Bread.propTypes = {
    levels: PropTypes.array,
    additionalContent: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.object
    ]),
    sx: PropTypes.object
}

export default Bread;
