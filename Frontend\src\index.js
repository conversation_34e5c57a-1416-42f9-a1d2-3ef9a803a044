import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import { HelmetProvider } from 'react-helmet-async';
import { PublicClientApplication } from "@azure/msal-browser";
import { MsalProvider } from '@azure/msal-react';
import { msalConfig } from "./Utils/APIutils/msalConfig";
import { AuthProvider } from './Context/CustomContext';
import { FiltersProvider } from './Context/FiltersContext';

const root = ReactDOM.createRoot(document.getElementById('root'));
const msalInstance = new PublicClientApplication(msalConfig);
root.render(
  <React.StrictMode>
    <MsalProvider instance={msalInstance}>
      <HelmetProvider>
        <FiltersProvider>
          <AuthProvider instance={msalInstance}>
            <App />
          </AuthProvider>
        </FiltersProvider>
      </HelmetProvider>
    </MsalProvider>
  </React.StrictMode>
);
