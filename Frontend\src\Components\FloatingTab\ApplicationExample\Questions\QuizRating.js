import React from 'react';
import PropTypes from 'prop-types';
import { Typography, Box, Checkbox, FormControl, FormControlLabel, FormGroup, TextField, FormHelperText } from '@mui/material';
import { styled } from '@mui/system';
import { useTheme } from '@emotion/react';
import CustomRatingComponent from './CustomRatingComponent';

const RenderTypography = ({ size, children, ...props }) => {
  if (size === "large") {
    return <Typography {...props}>{children}</Typography>;
  }
  return null;
};

RenderTypography.propTypes = {
  size: PropTypes.string,
  children: PropTypes.node
};

const ErrorCheckbox = styled(Checkbox)(({ theme }) => ({
  color: theme.palette.error.main,
  '&.Mui-checked': {
    color: theme.palette.error.main,
  },
}));

const QuizRating = ({ question, updateQuizAnswer, updateQuizYesOrNo, canEdit, size = "large", errors = {}, ...rest }) => {
  const theme = useTheme();

  function isQuestionEditable() {
    return canEdit && !question.readOnly;
  }
  const isEditable = isQuestionEditable();

  const handleUpdateQuizAnswer = (clickedQuestion, value) => {
    if (!isEditable || clickedQuestion.readOnlyQuestion) {
      return;
    }
    if (!clickedQuestion.isPersonalObjective) {
      const questionScore = question.quizSurveyScore.find(x => value === x.score);
      if (questionScore) {
        updateQuizAnswer(clickedQuestion, questionScore.quizSurveyScoreID);
      }
    } else {
      updateQuizAnswer(clickedQuestion, value);
    }
  };

  const handleUpdateQuizYesOrNo = (clickedQuestion, value) => {
    if (!isEditable || clickedQuestion.readOnlyQuestion) {
      return;
    }
    updateQuizYesOrNo(clickedQuestion, value === "" ? null : value);
  };

  const getScoreIndex = (scoreID) => {
    if (question && question.quizSurveyScore && scoreID) {
      if (!question.isPersonalObjective) {
        const score_index = question.quizSurveyScore.find(x => x.quizSurveyScoreID.toUpperCase() === scoreID.toUpperCase());
        if (score_index) {
          return score_index.score;
        }
      }
      return scoreID;
    }
    return 0;
  };

  const calculatePrecision = () => {
    if (!question.ratingPrecision || question.ratingPrecision === 0) {
      return 1;
    }
    return question.ratingPrecision;
  }

  const calculateMaxValue = () => {
    if (question.maxValue && question.maxValue !== "") {
      return parseInt(question.maxValue, 10)
    }
    return 5;
  }

  const getDescription = (condition, defaultValue = '') => {
    if (!question.quizSurveyScore) {
      return defaultValue;
    }

    const item = question.quizSurveyScore.find(condition);
    return item ? item.description : defaultValue;
  };

  let content;

  switch (question.questionType) {
    case "Rating":
      content = (
        <Box textAlign="center">
          <RenderTypography
            size={size}
            variant="h1"
            sx={{ p: 2 }}
          >
            Avaliação
          </RenderTypography>
          <CustomRatingComponent
            errors={errors}
            key={question.quizSurveyID}
            value={question.currentValue}
            max={calculateMaxValue()}
            onChange={(event, newValue) => { handleUpdateQuizYesOrNo(question, newValue?.toString()); }}
          />
          <RenderTypography
            size={size}
            component="legend"
            align="center"
          >
            {getDescription(x => x.score === parseInt(question.currentValue, 10))}
          </RenderTypography>
        </Box>
      );
      break;
    case "Rating Description":
      content = (
        <Box textAlign="center">
          <RenderTypography
            size={size}
            variant="h1"
            sx={{ p: 2 }}
          >
            Avaliação
          </RenderTypography>
          <CustomRatingComponent
            errors={errors}
            key={question.quizSurveyID}
            value={getScoreIndex(question.currentValue)}
            max={5}
            precision={calculatePrecision()}
            onChange={(event, newValue) => { handleUpdateQuizAnswer(question, newValue); }}
          />
          <RenderTypography
            size={size}
            component="legend"
            align="center"
          >
            {!question.isPersonalObjective && getDescription(x => x.quizSurveyScoreID?.toUpperCase() === question.currentValue)}
          </RenderTypography>
          <RenderTypography
            size={size}
            component="legend"
            align="center"
          >
            {question.isPersonalObjective && getDescription(x => x.score?.toString()?.toUpperCase() === question.currentValue?.toString())}
          </RenderTypography>
        </Box>
      );
      break;
    case "Single Choice":
      if (question.quizSurveyScore && question.quizSurveyScore.length > 0) {
        content = (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              p: 2,
              ...rest.sx
            }}
          >
            <FormControl component="fieldset">
              <FormGroup
                aria-label="position"
                row
                sx={{
                  flexDirection: "column"
                }}
              >
                {
                  question.quizSurveyScore.map((qss, qss_index) => (
                    <FormControlLabel
                      key={"qss_" + qss_index}
                      checked={parseInt(question.currentValue, 10) === qss.score}
                      onChange={() => handleUpdateQuizYesOrNo(question, qss.score)}
                      control={errors[question.quizSurveyID] ? <ErrorCheckbox color="default" /> : <Checkbox color="primary" />}
                      label={qss.description}
                      labelPlacement="end"
                      sx={{
                        '& .MuiFormControlLabel-label': {
                          color: errors[question.quizSurveyID] ? theme.palette.error.main : 'inherit',
                        },
                      }}
                    />
                  ))
                }
              </FormGroup>
            </FormControl>
          </Box>
        )
      }
      break;
    case "Text":
      content = (
        <Box
          sx={{
            ...rest.sx
          }}
        >
          <TextField
            multiline
            fullWidth
            defaultValue={question.currentValue}
            sx={{
              mb: 2
            }}
            id={question.quizSurveyID}
            onBlur={(e) => { handleUpdateQuizYesOrNo(question, e.target.value) }}
            error={errors[question.quizSurveyID]}
            // onChange={e => { (event, newValue) => { handleUpdateQuizAnswer(question, newValue); } }}
            minRows={2}
            maxRows={10}
            inputProps={
              { readOnly: !isEditable }
            }
          />
        </Box>
      );
      break;
    default:
      content = (<Box
        component="fieldset"
        mb={3}
        borderColor="transparent"
        sx={{
          textAlign: "center",
          ...rest.sx
        }}
      >
        <RenderTypography
          size={size}
          variant="h1"
          sx={{
            p: 2
          }}
        >
          Avaliação
        </RenderTypography>
        <FormControl component="fieldset">
          <FormGroup
            aria-label="position"
            row
          >
            <FormControlLabel
              value="start"
              checked={question.currentValue === 5}
              onChange={() => handleUpdateQuizYesOrNo(question, 5)}
              control={errors[question.quizSurveyID] ? <ErrorCheckbox color="default" /> : <Checkbox color="primary" />}
              label="Sim"
              labelPlacement="start"
              sx={{
                ml: "initial",
                '& .MuiFormControlLabel-label': {
                  color: errors[question.quizSurveyID] ? theme.palette.error.main : 'inherit',
                },
              }}
            />
            <FormControlLabel
              value="start"
              checked={question.currentValue === 0}
              onChange={() => handleUpdateQuizYesOrNo(question, 0)}
              control={errors[question.quizSurveyID] ? <ErrorCheckbox color="default" /> : <Checkbox color="primary" />}
              label="Não"
              labelPlacement="start"
              sx={{
                '& .MuiFormControlLabel-label': {
                  color: errors[question.quizSurveyID] ? theme.palette.error.main : 'inherit',
                },
              }}
            />
          </FormGroup>
        </FormControl>
      </Box>
      );
      break;
  }

  return (
    <>
      {content}
      {
        errors[question.quizSurveyID] &&
        <Box>
          <FormHelperText error>
            {errors[question.quizSurveyID]}
          </FormHelperText>
        </Box>
      }
    </>
  );
};

QuizRating.propTypes = {
  question: PropTypes.object.isRequired,
  updateQuizAnswer: PropTypes.func.isRequired,
  updateQuizYesOrNo: PropTypes.func.isRequired,
  size: PropTypes.string,
  canEdit: PropTypes.bool,
  errors: PropTypes.object
};

export default QuizRating;
