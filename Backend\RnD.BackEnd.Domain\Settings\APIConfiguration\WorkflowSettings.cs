﻿using System.Collections.Generic;

namespace RnD.BackEnd.Domain.Settings.APIConfiguration
{
    public class WorkflowSettings : ApiSettings
    {
        public string Secret { get; set; }
        public bool AllowWorkflowEngine { get; set; }
        public string CollectFeedbackTemplateId { get; set; }
        public string TokenEndpoint { get; set; }
        public string ListDefinitionsEndpoint { get; set; }
        public string InstancesBySourceIDEndpoint { get; set; }
        public string InstanceByIDEndpoint { get; set; }
        public string TaskByIDEndpoint { get; set; }
        public string TasksEndpoint { get; set; }
        public string UserWorkflowInstancesEndpoint { get; set; }
        public string UpdateTaskEndpoint { get; set; }
        public string UpdateTasksEndpoint { get; set; }
        public string CancelWorkflowEndpoint { get; set; }
        public string ReassignTaskEndpoint { get; set; }
        public string StartWorkflowEndpoint { get; set; }
        public string OverdueTasksEndpoint { get; set; }
        public List<string> OverdueTasksSourceTypes { get; set; }
        public List<string> CompleteBackgroundTasksSourceTypes { get; set; }
        public string AutomaticTriggerWorkflowEndpoint { get; set; }
        public string InstancesRequiredForProcessingEndpoint { get; set; }
        public string UpdateInstanceEndpoint { get; set; }
        public string DeleteOrCancelBySourceIdEndpoint { get; set; }
    }
}