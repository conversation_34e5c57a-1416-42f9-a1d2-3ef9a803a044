﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Controller_SelectedScaffolderID>MvcControllerEmptyScaffolder</Controller_SelectedScaffolderID>
    <Controller_SelectedScaffolderCategoryPath>root/Common/MVC/Controller</Controller_SelectedScaffolderCategoryPath>
    <ActiveDebugProfile>RnD.Backend.API</ActiveDebugProfile>
    <NameOfLastUsedPublishProfile>C:\Users\<USER>\OneDrive - BI4ALL\Desktop\a\PortalCloudServices\Backend\RnD.BackEnd.API\Properties\PublishProfiles\backend-portalcloudservices - Web Deploy1.pubxml</NameOfLastUsedPublishProfile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebuggerFlavor>ProjectDebugger</DebuggerFlavor>
  </PropertyGroup>
</Project>