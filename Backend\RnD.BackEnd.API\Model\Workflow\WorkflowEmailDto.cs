﻿using System.Collections.Generic;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Workflow Email DTO class
    /// </summary>
    public class WorkflowEmailDto
    {
        /// <summary>
        /// Gets or sets the template identifier.
        /// </summary>
        /// <value>
        /// The template identifier.
        /// </value>
        public string TemplateId { get; set; }

        /// <summary>
        /// Gets or sets the recipients.
        /// </summary>
        /// <value>
        /// The recipients.
        /// </value>
        public List<string> Recipients { get; set; }

        /// <summary>
        /// Gets or sets the template data.
        /// </summary>
        /// <value>
        /// The template data.
        /// </value>
        public object TemplateData { get; set; }

        /// <summary>
        /// Gets or sets the subject.
        /// </summary>
        /// <value>
        /// The subject.
        /// </value>
        public string Subject { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [send email].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [send email]; otherwise, <c>false</c>.
        /// </value>
        public bool SendEmail { get; set; }
    }
}