using Microsoft.AspNetCore.Mvc;
using RnD.BackEnd.API.Models;
using RnD.BackEnd.API.Services;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Data;

namespace RnD.BackEnd.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LogProcessamentoController : ControllerBase
    {
        private readonly LogProcessamentoService _logService;
        private readonly ApplicationDbContext _context;

        public LogProcessamentoController(
            LogProcessamentoService logService,
            ApplicationDbContext context)
        {
            _logService = logService;
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<LogProcessamento>>> GetLogs()
        {
            return Ok(await _logService.ObterUltimosLogs(100));
        }

        [HttpGet("arquivo/{fileId}")]
        public async Task<ActionResult<IEnumerable<LogProcessamento>>> GetLogsPorArquivo(int fileId)
        {
            return Ok(await _logService.ObterLogsPorArquivo(fileId));
        }

        [HttpGet("tipo/{tipo}")]
        public async Task<ActionResult<IEnumerable<LogProcessamento>>> GetLogsPorTipo(string tipo)
        {
            if (Enum.TryParse<TipoLog>(tipo, true, out var tipoLog))
            {
                return Ok(await _logService.ObterLogsPorTipo(tipoLog));
            }
            
            return BadRequest("Tipo de log inválido. Valores válidos: Info, Aviso, Erro");
        }

        [HttpGet("arquivos")]
        public async Task<ActionResult<IEnumerable<ArquivoProcessado>>> GetArquivosProcessados()
        {
            return await _context.ArquivosProcessados
                .OrderByDescending(a => a.DataProcessamento)
                .ToListAsync();
        }

        [HttpGet("arquivos/{id}")]
        public async Task<ActionResult<ArquivoProcessado>> GetArquivoProcessado(int id)
        {
            var arquivo = await _context.ArquivosProcessados.FindAsync(id);
            
            if (arquivo == null)
            {
                return NotFound();
            }
            
            return arquivo;
        }
    }
} 