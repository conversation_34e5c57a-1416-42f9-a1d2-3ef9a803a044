﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using RnD.BackEnd.Domain.DataModels;

namespace RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos.Base
{
    /// <summary>
    /// A generic repository for CRUD operations on domain models
    /// </summary>
    /// <typeparam name="T">The domain type</typeparam>
    public interface IBaseCosmosRepository<T>
    {
        /// <summary>
        /// Adds the item
        /// </summary>
        /// <param name="item">The item.</param>
        /// <returns>The item</returns>
        Task<ResponseModel<T>> AddAsync(T item);

        /// <summary>
        /// Deletes the item
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>True if operation succeeded, False otherwise.</returns>
        Task<ResponseModel<bool>> DeleteAsync(string id);

        /// <summary>
        /// Updates the item
        /// </summary>
        /// <param name="item">The item.</param>
        /// <param name="etag">The etag.</param>
        /// <returns>The item</returns>
        Task<ResponseModel<T>> UpdateAsync(T item, string etag = default);

        /// <summary>
        /// Gets an item.
        /// </summary>
        /// <param name="id">The item identifier.</param>
        /// <returns>The item</returns>
        Task<ResponseModel<T>> GetAsync(string id);

        /// <summary>
        /// Gets a list of items
        /// </summary>
        /// <param name="queryString">The query string.</param>
        /// <param name="parameters">The parameters.</param>
        /// <param name="pageSize">Size of the page.</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>The list of items</returns>
        Task<ResponseModel<List<T>>> GetItemsAsync(string queryString, Dictionary<string, object> parameters = null, int? pageSize = default, string continuationToken = default, CancellationToken cancellationToken = default);
    }
}
