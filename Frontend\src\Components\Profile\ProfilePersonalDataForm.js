import PropTypes from 'prop-types';
import {
  Autocomplete,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  TextField,
  Typography
} from '@mui/material';
import { useFormikContext } from 'formik';
import { useTranslation } from 'react-i18next';

const ProfilePersonalDataForm = (props) => {
  const { options } = props;
  const { t } = useTranslation();

  //useFormikContext() is a custom React hook that will return all Formik state and helpers via React Context.
  const formikContext = useFormikContext();

  const getError = (fieldName, touched, errors) => Boolean(touched?.[fieldName] && errors?.[fieldName]);
  const getHelperText = (fieldName, touched, errors) => {
    return touched[fieldName] && errors[fieldName] ? errors[fieldName] : '';
  };

  return (
    <Card>
      <CardHeader
        sx={{ height: "56px" }}
        title={t('profile:common.personalData')}
      />
      <Divider />
      <CardContent>
        <Grid
          container
          spacing={3}
        >
          <Grid
            item
            md={9}
            xs={12}
          >
            <TextField
              error={getError("fullname", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("fullname", formikContext.touched, formikContext.errors)}
              value={formikContext.values.fullname}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.fullName')}
              name="fullname"
              variant="outlined"
            />
          </Grid>
          <Grid
            item
            md={3}
            xs={12}
          >
            <TextField
              error={getError("birthday", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("birthday", formikContext.touched, formikContext.errors)}
              fullWidth
              label={t('profile:common.DOB')}
              value={formikContext.values.birthday}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              name="birthday"
              type="date"
              variant="outlined"
            />
          </Grid>
          <Grid
            item
            md={12}
          >
            <Typography
              color="textSecondary"
              variant="subtitle2"
            >
              {t('profile:common.contacts')}
            </Typography>
          </Grid>
          <Grid
            item
            md={4}
            xs={12}
          >
            <TextField
              error={getError("contactNo", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("contactNo", formikContext.touched, formikContext.errors)}
              value={formikContext.values.contactNo}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.tel')}
              name="contactNo"
              variant="outlined"
              type="tel"
            />

          </Grid>
          <Grid
            item
            md={4}
            xs={12}
          >
            <TextField
              error={getError("email", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("email", formikContext.touched, formikContext.errors)}
              value={formikContext.values.email}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.email')}
              name="email"
              type="email"
              variant="outlined"
              autoComplete="new-password"
            />
          </Grid>
          <Grid
            item
            md={4}
            xs={12}
          >
            <TextField
              error={getError("personalEmail", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("personalEmail", formikContext.touched, formikContext.errors)}
              value={formikContext.values.personalEmail}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.personalEmail')}
              name="personalEmail"
              type="email"
              variant="outlined"
              autoComplete="new-password"
            />
          </Grid>
          <Grid
            item
            md={12}
          >
            <Typography
              color="textSecondary"
              variant="subtitle2"
            >
              {t('profile:common.addresses')}
            </Typography>
          </Grid>
          <Grid
            item
            md={12}
            xs={12}
          >
            <TextField
              error={getError("address1", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("address1", formikContext.touched, formikContext.errors)}
              value={formikContext.values.address1}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.address')}
              name="address1"
              variant="outlined"
            />
          </Grid>
          <Grid
            item
            md={10}
            xs={12}
          >
            <TextField
              error={getError("address2", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("address2", formikContext.touched, formikContext.errors)}
              value={formikContext.values.address2}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.city')}
              name="address2"
              variant="outlined"
            />
          </Grid>
          <Grid
            item
            md={3}
            xs={12}
          >
            <TextField
              error={getError("zipCode", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("zipCode", formikContext.touched, formikContext.errors)}
              value={formikContext.values.zipCode}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.zipCode')}
              name="zipCode"
              ariant="outlined"
            />
          </Grid>
          <Grid
            item
            md={5}
            xs={12}
          >
            <TextField
              error={getError("zipCodeCity", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("zipCodeCity", formikContext.touched, formikContext.errors)}
              value={formikContext.values.zipCodeCity}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.postalLocation')}
              name="zipCodeCity"
              variant="outlined"
            />
          </Grid>
          <Grid
            item
            md={12}
          >
            <Typography
              color="textSecondary"
              variant="subtitle2"
            >
              {t('profile:common.taxData')}
            </Typography>
          </Grid>
          <Grid
            item
            md={4}
            xs={12}
          >
            <TextField
              error={getError("idCard", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("idCard", formikContext.touched, formikContext.errors)}
              value={formikContext.values.idCard}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.id')}
              name="idCard"
              variant="outlined"
            />
          </Grid>
          <Grid
            item
            md={3}
            xs={12}
          >
            <TextField
              error={getError("idCardExpirationDate", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("idCardExpirationDate", formikContext.touched, formikContext.errors)}
              value={formikContext.values.idCardExpirationDate}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.expDate')}
              name="idCardExpirationDate"
              type="date"
              variant="outlined"
            />
          </Grid>
          <Grid
            item
            md={3}
            xs={12}
          >
            <Autocomplete
              getOptionLabel={(option) => option?.text ?? option}
              options={options?.civilStatusOptions || []}
              value={formikContext.values.maritialStatus}
              onChange={(e, value) => formikContext.setFieldValue('maritialStatus', value)}
              renderInput={(parameters) => (
                <TextField
                  error={getError("maritialStatus", formikContext.touched, formikContext.errors)}
                  helperText={getHelperText("maritialStatus", formikContext.touched, formikContext.errors)}
                  fullWidth
                  label={t('profile:common.maritalStatus')}
                  name="maritialStatus"
                  variant="outlined"
                  onChange={formikContext.handleChange}
                  onBlur={formikContext.handleBlur}
                  {...parameters}
                />
              )}
            />
          </Grid>
          <Grid
            item
            md={4}
            xs={12}
          >
            <TextField
              fullWidth
              label={t('profile:common.socialSecurity')}
              error={getError("socialSecurityNo", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("socialSecurityNo", formikContext.touched, formikContext.errors)}
              value={formikContext.values.socialSecurityNo}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              name="socialSecurityNo"
              variant="outlined"
            />
          </Grid>
          <Grid
            item
            md={4}
            xs={12}
          >
            <TextField
              error={getError("taxNo", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("taxNo", formikContext.touched, formikContext.errors)}
              value={formikContext.values.taxNo}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.tax')}
              name="taxNo"
              iant="outlined"
            />
          </Grid>
          <Grid
            item
            md={2}
            xs={12}
          >
            <Autocomplete
              getOptionLabel={(option) => option.text ?? option}
              options={options?.incomeHolderOptions || []}
              value={formikContext.values.incomeHolderID}
              onChange={(e, value) => formikContext.setFieldValue('incomeHolderID', value)}
              renderInput={(parameters) => (
                <TextField
                  error={getError("incomeHolderID", formikContext.touched, formikContext.errors)}
                  helperText={getHelperText("incomeHolderID", formikContext.touched, formikContext.errors)}
                  fullWidth
                  onChange={formikContext.handleChange}
                  onBlur={formikContext.handleBlur}
                  label={t('profile:common.incomeHolder')}
                  name="incomeHolderID"
                  variant="outlined"
                  {...parameters}
                />

              )}
            />
          </Grid>
          <Grid
            item
            md={2}
            xs={12}
          >
            <TextField
              error={getError("dependents", formikContext.touched, formikContext.errors)}
              helperText={getHelperText("dependents", formikContext.touched, formikContext.errors)}
              value={formikContext.values.dependents || 0}
              onChange={formikContext.handleChange}
              onBlur={formikContext.handleBlur}
              fullWidth
              label={t('profile:common.dependents')}
              name="dependents"
              variant="outlined"
              min={0}
              type="number"
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
}
ProfilePersonalDataForm.propTypes = {
  options: PropTypes.object
};
export default ProfilePersonalDataForm;
