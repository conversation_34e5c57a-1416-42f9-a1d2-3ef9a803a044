import PropTypes from 'prop-types';
import Modal from '../ModalTemplate';

const StepRemoveModal = (props) => {
  const { onClose, open, onRemoveProcessStep } = props;

  return (
    <Modal
      open={open}
      title="Remover Fase"
      content="Tem a certeza que deseja remover a fase seleccionada?"
      onClose={onClose}
      onConfirm={onRemoveProcessStep}
      onCancel={onClose}
    />
  )
};

StepRemoveModal.propTypes = {
  onClose: PropTypes.func,
  open: PropTypes.bool.isRequired,
  onRemoveProcessStep: PropTypes.func,
};

export default StepRemoveModal;
