﻿using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RnD.BackEnd.API.Model.Email;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.SwashbuckleExamples;
using RnD.BackEnd.API.SwashbuckleExamples.EmailController;
using RnD.BackEnd.Domain.Entities.Email;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Interfaces.Services;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.Controllers
{
    /// <summary>
    /// Email controller
    /// </summary>
    /// <seealso cref="Microsoft.AspNetCore.Mvc.ControllerBase" />
    [Produces("application/json")]
    [Route("api/[controller]")]
    [ApiController]
    public class EmailController : ControllerBase
    {
        private readonly IEmailService _emailService;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="EmailController"/> class.
        /// </summary>
        /// <param name="emailService">The email service.</param>
        /// <param name="mapper">The mapper.</param>
        public EmailController(IEmailService emailService, IMapper mapper)
        {
            _emailService = emailService;
            _mapper = mapper;
        }

        /// <summary>
        /// Sends the code academy email.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// True if email was sent, False otherwise.
        /// </returns>
        /// <response code="200">Returns true if e-mail was sent, otherwise False</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpPost("SendCodeAcademyEmail")]
        [ProducesResponseType(typeof(ApiOutput<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(EmailMessageDto<CodeAcademyTemplateDto>), typeof(SendCodeAcademyEmail))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(ResponseBoolExample200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> SendCodeAcademyEmail([FromBody] EmailMessageDto<CodeAcademyTemplateDto> request)
        {
            EmailMessage<CodeAcademyTemplate> emailContent = _mapper.Map<EmailMessageDto<CodeAcademyTemplateDto>, EmailMessage<CodeAcademyTemplate>>(request);

            ServiceOutput<bool> response = await _emailService.SendCodeAcademyEmail(emailContent);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<bool>, ApiOutput<bool>>(response));
        }

        /// <summary>
        /// Sends the email with the default template.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// True if email was sent, False otherwise.
        /// </returns>
        /// <response code="200">Returns true if e-mail was sent, otherwise False</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpPost("SendDefaultTemplateEmail")]
        [ProducesResponseType(typeof(ApiOutput<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(EmailMessageDto<EmailDto>), typeof(SendDefaultTemplateEmail))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(ResponseBoolExample200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> SendDefaultTemplateEmail([FromBody] EmailMessageDto<EmailDto> request)
        {
            EmailMessage<BaseEmail> emailContent = _mapper.Map<EmailMessageDto<EmailDto>, EmailMessage<BaseEmail>>(request);

            ServiceOutput<bool> response = await _emailService.SendDefaultTemplateEmail(emailContent);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<bool>, ApiOutput<bool>>(response));
        }

        /// <summary>
        /// Sends the email with a specific template Id.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// True if email was sent, False otherwise.
        /// </returns>
        /// <response code="200">Returns true if e-mail was sent, otherwise False</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpPost("SendTemplatedEmail")]
        [ProducesResponseType(typeof(ApiOutput<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(SpecificTemplateDto), typeof(SendTemplatedEmail))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(ResponseBoolExample200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> SendTemplatedEmail([FromBody] SpecificTemplateDto request)
        {
            EmailMessage<object> emailContent = _mapper.Map<SpecificTemplateDto, EmailMessage<object>>(request);

            ServiceOutput<bool> response = await _emailService.SendTemplatedEmail(emailContent, request.TemplateId);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<bool>, ApiOutput<bool>>(response));
        }

        /// <summary>
        /// Sends the default template email with attachments.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// True if email was sent, False otherwise.
        /// </returns>
        /// <response code="200">Returns true if e-mail was sent, otherwise False</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpPost("SendDefaultTemplateEmailWithAttachments")]
        [ProducesResponseType(typeof(ApiOutput<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(EmailAttachmentsDto<EmailDto>), typeof(SendDefaultTemplateEmailWithAttachments))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(ResponseBoolExample200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> SendDefaultTemplateEmailWithAttachments([FromForm] EmailAttachmentsDto<EmailDto> request)
        {
            EmailAttachments<BaseEmail> emailContent = _mapper.Map<EmailAttachmentsDto<EmailDto>, EmailAttachments<BaseEmail>>(request);

            ServiceOutput<bool> response = await _emailService.SendDefaultTemplateEmailWithAttachments(emailContent);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<bool>, ApiOutput<bool>>(response));
        }
    }
}
