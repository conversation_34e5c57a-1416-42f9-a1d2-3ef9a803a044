﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RnD.BackEnd.API.Helpers;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Workflow;
using RnD.BackEnd.API.SwashbuckleExamples;
using RnD.BackEnd.API.SwashbuckleExamples.WorkflowController;
using RnD.BackEnd.Domain.Constants;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Entities.Workflow;
using RnD.BackEnd.Domain.Interfaces.Services;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.Controllers
{
    /// <summary>
    /// Workflow controller that interacts with WFE API, for demonstration purpose
    /// </summary>
    /// <seealso cref="Microsoft.AspNetCore.Mvc.ControllerBase" />
    [Authorize(Policy = AuthenticationPolicy.AZURE_AD)]
    [Route("api/[controller]")]
    [ApiController]
    [TypeFilter(typeof(WorkflowEngineAllowedAttribute))]
    public class WorkflowController : ControllerBase
    {
        private readonly IWorkflowService _workflowService;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkflowController"/> class.
        /// </summary>
        /// <param name="workflowService">The workflow service.</param>
        /// <param name="mapper">The mapper.</param>
        public WorkflowController(IWorkflowService workflowService, IMapper mapper)
        {
            _workflowService = workflowService;
            _mapper = mapper;
        }

        #region Workflow Definitions

        /// <summary>
        /// Gets the workflow definitions.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The list of workflow definitions.
        /// </returns>
        /// <response code="200">Returns a List of definitions</response>
        /// <response code="204">No content response</response>
        /// <response code="400">Malformed request</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [Route("definitions")]
        [ProducesResponseType(typeof(ApiOutput<IList<WorkflowDefinitionDto>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(GetDefinitions200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> GetDefinitions([FromQuery] GetDefinitionsDto request)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            ServiceOutput<IList<WorkflowDefinition>> response = await _workflowService.GetDefinitions(
                request.SourceType,
                request.WorkflowType?.ToString(),
                request.FetchInactive,
                userToken);

            return response.Code == HttpStatusCode.NoContent
                ? StatusCode((int)response.Code)
                : StatusCode((int)response.Code, _mapper.Map<ServiceOutput<IList<WorkflowDefinition>>, ApiOutput<IList<WorkflowDefinitionDto>>>(response));
        }

        #endregion Workflow Definitions

        #region Workflow Instances

        /// <summary>
        /// Gets the list of workflow instances by a list of sourceIds and/or by a list of status.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The list of workflow instances for the source identifier.
        /// </returns>
        /// <response code="200">Returns a List of Instances by a list of sourceIds and/or by a list of status</response>
        /// <response code="204">No content response</response>
        /// <response code="400">Malformed request</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpPost]
        [Route("instances/list")]
        [ProducesResponseType(typeof(ApiOutput<IList<WorkflowInstanceDto>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(WorkflowInstanceBySourceIdsDto), typeof(GetInstancesBySourceIdsAndStatus))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(GetInstances200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> GetInstances([FromBody] WorkflowInstanceBySourceIdsDto request)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            WorkflowInstanceBySourceIds instanceBySourceIds = _mapper.Map<WorkflowInstanceBySourceIdsDto, WorkflowInstanceBySourceIds>(request);

            ServiceOutput<IList<WorkflowInstance>> response = await _workflowService.GetInstances(instanceBySourceIds, userToken);

            return response.Code == HttpStatusCode.NoContent
                ? StatusCode((int)response.Code)
                : StatusCode((int)response.Code, _mapper.Map<ServiceOutput<IList<WorkflowInstance>>, ApiOutput<IList<WorkflowInstanceDto>>>(response));
        }

        /// <summary>
        /// Gets the list of workflow instances initiated by current user.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The list of workflow instances.
        /// </returns>
        /// <response code="200">Returns a list of User Instances</response>
        /// <response code="204">No content response</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [Route("instances")]
        [ProducesResponseType(typeof(ApiOutput<IList<WorkflowInstancesListDto>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(GetUserInstances200))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> GetUserInstances([FromQuery] GetUserInstancesDto request)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            ServiceOutput<WorkflowInstancesList> response = await _workflowService.GetUserInstances(
                request.WorkflowStatus.ToString(),
                request.WorkflowType.ToString(),
                request.WorkflowName,
                request.SourceType,
                request.Purpose.ToString(),
                request.SortField,
                request.SortDirection,
                request.ItemsPerPage,
                request.CurrentPage,
                userToken);

            return response.Code == HttpStatusCode.NoContent
                ? StatusCode((int)response.Code)
                : StatusCode((int)response.Code, _mapper.Map<ServiceOutput<WorkflowInstancesList>, ApiOutput<WorkflowInstancesListDto>>(response));
        }

        /// <summary>
        /// Gets the workflow instance by identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The workflow instance.
        /// </returns>
        /// <response code="200">Returns a Instance by Id</response>
        /// <response code="204">No content response</response>
        /// <response code="400">Malformed request</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [Route("instance/{id}")]
        [ProducesResponseType(typeof(ApiOutput<WorkflowInstanceDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(GetInstances200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> GetInstance([FromRoute] string id)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            ServiceOutput<WorkflowInstance> response = await _workflowService.GetInstance(id, userToken);

            return response.Code == HttpStatusCode.NoContent
                ? StatusCode((int)response.Code)
                : StatusCode((int)response.Code, _mapper.Map<ServiceOutput<WorkflowInstance>, ApiOutput<WorkflowInstanceDto>>(response));
        }

        /// <summary>
        /// Cancels the workflow instance.
        /// </summary>
        /// <param name="instanceId">The instance identifier.</param>
        /// <param name="userId">The user identifier.</param>
        /// <param name="request">The request.</param>
        /// <returns>
        /// True if workflow instance was cancelled, False otherwise.
        /// </returns>
        /// <response code="200">Returns True if sucess, false otherwise</response>
        /// <response code="400">Malformed request</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpDelete]
        [Route("instance/{instanceId}/{userId}")]
        [ProducesResponseType(typeof(ApiOutput<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(WorkflowEmailDto), typeof(WorkflowEmailRequest))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(ResponseBoolExample200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> CancelWorkflow([FromRoute] Guid instanceId, string userId, [FromBody] WorkflowEmailDto request)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            WorkflowEmail emailRequest = _mapper.Map<WorkflowEmailDto, WorkflowEmail>(request);

            ServiceOutput<bool> response = await _workflowService.CancelWorkflow(instanceId, userId, userToken, emailRequest);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<bool>, ApiOutput<bool>>(response));
        }

        /// <summary>
        /// Deletes the or cancel workflows by source identifier.
        /// </summary>
        /// <param name="sourceId">The source identifier.</param>
        /// <param name="request">The request.</param>
        /// <param name="isDeleteOperation">if set to <c>true</c> [is delete operation].</param>
        /// <returns>
        /// True if operation succeeded, False otherwise.
        /// </returns>
        /// <response code="200">Returns True if sucess, false otherwise</response>
        /// <response code="204">No content response</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpDelete]
        [Route("instances/deleteOrCancel/{sourceId}")]
        [ProducesResponseType(typeof(ApiOutput<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(WorkflowEmailDto), typeof(WorkflowEmailRequest))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(ResponseBoolExample200))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> DeleteOrCancelWorkflowsBySourceId([FromRoute] string sourceId, [FromBody] WorkflowEmailDto request, [FromQuery] bool isDeleteOperation = false)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");
            WorkflowEmail emailRequest = _mapper.Map<WorkflowEmailDto, WorkflowEmail>(request);
            ServiceOutput<bool> response = await _workflowService.DeleteOrCancelWorkflowsBySourceId(sourceId, isDeleteOperation, userToken, emailRequest);

            return response.Code == HttpStatusCode.NoContent
                ? StatusCode((int)response.Code)
                : StatusCode((int)response.Code, _mapper.Map<ServiceOutput<bool>, ApiOutput<bool>>(response));
        }

        #endregion Workflow Instances

        #region Workflow Tasks

        /// <summary>
        /// Gets the workflow task by identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The workflow task.
        /// </returns>
        /// <response code="200">Returns Task by Id</response>
        /// <response code="204">No content response</response>
        /// <response code="400">Malformed request</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [Route("tasks/{id}")]
        [ProducesResponseType(typeof(ApiOutput<WorkflowTaskDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(GetTaskById200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> GetTask([FromRoute] string id)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            ServiceOutput<WorkflowTask> response = await _workflowService.GetTask(id, userToken);

            return response.Code == HttpStatusCode.NoContent
                ? StatusCode((int)response.Code)
                : StatusCode((int)response.Code, _mapper.Map<ServiceOutput<WorkflowTask>, ApiOutput<WorkflowTaskDto>>(response));
        }

        /// <summary>
        /// Gets the list of workflow tasks.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The list of workflow tasks.
        /// </returns>
        /// <response code="200">Returns a list of Tasks</response>
        /// <response code="204">No content response</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [Route("tasks")]
        [ProducesResponseType(typeof(ApiOutput<WorkflowTasksListDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(GetTasks200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> GetTasks([FromQuery] GetTasksDto request)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            ServiceOutput<WorkflowTasksList> response = await _workflowService.GetTasks(
                request.UserId,
                request.TaskStatus.ToString(),
                request.TaskCompleted,
                request.StepName,
                request.WorkflowName,
                request.WorkflowStatus.ToString(),
                request.SourceType,
                request.SortField,
                request.SortDirection,
                request.ItemsPerPage,
                request.CurrentPage,
                userToken);

            return response.Code == HttpStatusCode.NoContent
                ? StatusCode((int)response.Code)
                : StatusCode((int)response.Code, _mapper.Map<ServiceOutput<WorkflowTasksList>, ApiOutput<WorkflowTasksListDto>>(response));
        }

        /// <summary>
        /// Updates the task.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        /// <response code="200">Returns True if sucess, false otherwise</response>
        /// <response code="400">Malformed request</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpPut]
        [Route("task/update")]
        [ProducesResponseType(typeof(ApiOutput<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(UpdateTaskDto), typeof(UpdateTaskRequest))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(ResponseBoolExample200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> UpdateTask([FromBody] UpdateTaskDto request)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            UpdateTask taskToUpdate = _mapper.Map<UpdateTaskDto, UpdateTask>(request);

            ServiceOutput<bool> response = await _workflowService.UpdateTask(taskToUpdate, userToken);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<bool>, ApiOutput<bool>>(response));
        }

        /// <summary>
        /// Updates multiple tasks.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// True if operations succeeded, False otherwise.
        /// </returns>
        /// <response code="200">Returns the update output for each task</response>
        /// <response code="400">Malformed request</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpPut]
        [Route("tasks/update")]
        [ProducesResponseType(typeof(ApiOutput<List<UpdateTaskOutputDto>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(List<UpdateTaskDto>), typeof(UpdateTasksRequest))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(UpdateTasks200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> UpdateTasks([FromBody] List<UpdateTaskDto> request)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            List<UpdateTask> tasksToUpdate = _mapper.Map<List<UpdateTaskDto>, List<UpdateTask>>(request);

            ServiceOutput<List<UpdateTaskOutput>> response = await _workflowService.UpdateTasks(tasksToUpdate, userToken);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<List<UpdateTaskOutput>>, ApiOutput<List<UpdateTaskOutputDto>>>(response));
        }

        /// <summary>
        /// Reassings the task.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// True if task was reassigned, False otherwise.
        /// </returns>
        /// <response code="200">Returns True if sucess, false otherwise</response>
        /// <response code="400">Malformed request</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpPut]
        [Route("task/reassign")]
        [ProducesResponseType(typeof(ApiOutput<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(ReassignTaskDto), typeof(ReassignRequest))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(ResponseBoolExample200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> ReassingTask([FromBody] ReassignTaskDto request)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            ReassignTask taskToReassign = _mapper.Map<ReassignTaskDto, ReassignTask>(request);

            ServiceOutput<bool> response = await _workflowService.ReassignTask(taskToReassign, userToken);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<bool>, ApiOutput<bool>>(response));
        }

        #endregion Workflow Tasks

        #region Triggers

        /// <summary>
        /// Starts the workflow.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The workflow instance that was started.
        /// </returns>
        /// <response code="200">Returns the workflow that was started</response>
        /// <response code="400">Malformed request</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpPost]
        [Route("trigger/start")]
        [ProducesResponseType(typeof(ApiOutput<WorkflowInstanceDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(StartWorkflowDto), typeof(StartWorkflowRequest))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(StartWorkflow200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> StartWorkflow([FromBody] List<StartWorkflowDto> request)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            List<StartWorkflow> workflow = _mapper.Map<List<StartWorkflowDto>, List<StartWorkflow>>(request);

            ServiceOutput<WorkflowInstance> response = await _workflowService.StartWorkflow(workflow, userToken);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<WorkflowInstance>, ApiOutput<WorkflowInstanceDto>>(response));
        }

        /// <summary>
        /// Triggers the automatic workflow.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The workflow instance that was started.
        /// </returns>
        /// <response code="200">Returns the workflow that was Automatic started</response>
        /// <response code="400">Malformed request</response>
        /// <response code="401">User Unauthorized</response>
        /// <response code="500">Internal error</response>
        [HttpPost]
        [Route("trigger/automatic")]
        [ProducesResponseType(typeof(ApiOutput<WorkflowInstanceDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(StartAutomaticWorkflowDto), typeof(StartAutomaticWorkflowRequest))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(StartWorkflow200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> TriggerAutomaticWorkflow([FromBody] StartAutomaticWorkflowDto request)
        {
            string userToken = await HttpContext.GetTokenAsync(CustomAuthenticationSchemes.AZURE_AD, "access_token");

            StartAutomaticWorkflow workflow = _mapper.Map<StartAutomaticWorkflowDto, StartAutomaticWorkflow>(request);

            ServiceOutput<WorkflowInstance> response = await _workflowService.TriggerAutomaticWorkflow(workflow, userToken);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<WorkflowInstance>, ApiOutput<WorkflowInstanceDto>>(response));
        }

        #endregion Triggers
    }
}