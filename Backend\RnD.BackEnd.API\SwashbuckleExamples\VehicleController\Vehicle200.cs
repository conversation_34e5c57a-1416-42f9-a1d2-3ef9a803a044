﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Vehicle;
using RnD.BackEnd.Domain.Constants;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.VehicleController
{
    /// <summary>
    /// Vehicle response example for 200
    /// </summary>
    public class Vehicle200 : IExamplesProvider<ApiOutput<VehicleDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 response.
        /// </returns>
        public ApiOutput<VehicleDto> GetExamples()
        {
            var id = Guid.NewGuid().ToString();
            return new ApiOutput<VehicleDto>
            {
                Code = HttpStatusCode.OK,
                Value = new VehicleDto
                {
                    Id = id,
                    Brand = "Ford",
                    BrandId = "0001",
                    FuelType = "DIESEL",
                    ModelName = "Fiesta",
                    Version = "567",
                    Year = 2022
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>(
                    new List<KeyValuePair<string, object>>
                    {
                        new KeyValuePair<string, object>
                        (
                            key:SystemConstants.ETAG,
                            value:"\"00000107-0000-0700-0000-63652f960000\""
                        )
                    })
            };
        }
    }
}
