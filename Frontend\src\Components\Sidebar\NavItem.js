import { useState } from 'react';
import { NavLink as RouterLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Box, Button, Collapse, List, ListItem } from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const NavItem = ({ active, children, icon, info, open: openProp, path, title, ...other }) => {
  const [open, setOpen] = useState(openProp);
  const { t } = useTranslation();

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };
  // Branch
  if (children) {
    return (
      <ListItem
        key={`Title-${t(children.title)}`}
        disableGutters
        sx={{
          flexDirection: "column"
        }}
        {...other}
      >
        <Button
          endIcon={!open ? <ChevronRightIcon />
            : <ExpandMoreIcon />}
          onClick={handleToggle}
          startIcon={icon}
          sx={{
            color: 'navItem.secondary',
            fontWeight: 'fontWeightMedium',
            justifyContent: 'flex-start',
            textAlign: 'left',
            textTransform: 'none',
            width: '100%',
            px: 0,
            '&:hover': {
              backgroundColor: 'navItem.hover',
              color: 'primary.main',
            },
            '&:focus': {
              backgroundColor: 'navItem.hover',
              color: 'primary.main',
              fontWeight: 700
            }
          }}
          variant="text"
        >
          <Box sx={{ flexGrow: 1 }}>
            {t(title).toUpperCase()}
          </Box>
          {info}
        </Button>
        <Collapse
          in={open}
          sx={{
            width: "100%"
          }}
        >
          <List>
            {children}
          </List>
        </Collapse>
      </ListItem>
    );
  }

  // Leaf
  return (
    <ListItem
      key={`Title-${t(title)}`}
      disableGutters
      sx={{
        display: 'flex'
      }}
    >
      <Button
        component={path && RouterLink}
        startIcon={icon}
        sx={{
          color: 'navItem.primary',
          fontWeight: 'fontWeightMedium',
          justifyContent: 'flex-start',
          textAlign: 'left',
          textTransform: 'none',
          width: '100%',
          '&:hover': {
            backgroundColor: 'navItem.hover',
            color: 'primary.main',
          },
          '&:focus': {
            backgroundColor: 'navItem.hover',
            color: 'primary.main',
            fontWeight: 700
          }
        }}
        variant="text"
        to={path}
      >
        <Box sx={{ flexGrow: 1 }}>
          {t(title)}
        </Box>
        {info}
      </Button>
    </ListItem>
  );
};

NavItem.propTypes = {
  active: PropTypes.bool,
  children: PropTypes.node,
  icon: PropTypes.node,
  info: PropTypes.node,
  open: PropTypes.bool,
  path: PropTypes.string,
  title: PropTypes.string
};

NavItem.defaultProps = {
  active: false,
  open: true
};

export default NavItem;
