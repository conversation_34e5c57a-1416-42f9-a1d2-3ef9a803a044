﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace RnD.BackEnd.Domain.Extensions
{
    /// <summary>
    /// Dictionary extension methods
    /// </summary>
    public static class DictionaryExtensions
    {
        /// <summary>
        /// Adds or updates a key-value pair to the dictionary
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dictionary">The dictionary</param>
        /// <param name="key">The key</param>
        /// <param name="value">The value</param>
        /// <returns>
        /// The updated dictionary
        /// </returns>
        public static IDictionary<string, T> AddOrUpdate<T>(this IDictionary<string, T> dictionary, string key, T value)
        {
            if (!dictionary.ContainsKey(key))
            {
                dictionary.Add(key, value);
            }
            else
            {
                dictionary[key] = value;
            }

            return dictionary;
        }

        /// <summary>
        /// Gets a string value by key.
        /// </summary>
        /// <param name="dictionary">The dictionary.</param>
        /// <param name="key">The key.</param>
        /// <returns>
        /// The string value
        /// </returns>
        public static string GetStringValue(this IDictionary<string, object> dictionary, string key)
        {
            return dictionary != null && dictionary.ContainsKey(key) && dictionary[key] != null
                ? dictionary[key].ToString()
                : string.Empty;
        }

        /// <summary>
        /// Gets the unique identifier value.
        /// </summary>
        /// <param name="dictionary">The dictionary.</param>
        /// <param name="key">The key.</param>
        /// <returns>
        /// The Guid value.
        /// </returns>
        public static Guid GetGuidValue(this IDictionary<string, object> dictionary, string key)
        {
            return dictionary != null && dictionary.ContainsKey(key) && dictionary[key] != null && Guid.TryParse(dictionary[key].ToString(), out Guid guidValue)
                ? guidValue
                : Guid.Empty;
        }

        /// <summary>
        /// Gets the int value.
        /// </summary>
        /// <param name="dictionary">The dictionary.</param>
        /// <param name="key">The key.</param>
        /// <returns>
        /// The int value.
        /// </returns>
        public static int GetIntValue(this IDictionary<string, object> dictionary, string key)
        {
            return dictionary != null && dictionary.ContainsKey(key) && dictionary[key] != null && int.TryParse(dictionary[key].ToString(), out int intValue)
                ? intValue
                : -1;
        }

        /// <summary>
        /// Gets a string list by key.
        /// </summary>
        /// <param name="dictionary">The dictionary.</param>
        /// <param name="key">The key.</param>
        /// <returns>
        /// The string list
        /// </returns>
        public static List<string> GetStringList(this IDictionary<string, object> dictionary, string key)
        {
            return dictionary != null && dictionary.ContainsKey(key) && dictionary[key] != null && (dictionary[key] as string[]) is not null
                ? (dictionary[key] as string[]).ToList()
                : new List<string>();
        }

        /// <summary>
        /// Gets a boolean value by key.
        /// </summary>
        /// <param name="dictionary">The dictionary.</param>
        /// <param name="key">The key.</param>
        /// <returns>
        /// The boolean value
        /// </returns>
        public static bool GetBooleanValue(this IDictionary<string, object> dictionary, string key)
        {
            return dictionary != null && dictionary.ContainsKey(key) && dictionary[key] != null
                ? Convert.ToBoolean(dictionary[key].ToString())
                : false;
        }
    }
}
