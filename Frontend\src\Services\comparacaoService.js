import {
  getConsumosByClienteId,
  getConsumoSummaryByResourceGroup,
} from "./consumoService";
import { getConsumoPDFByClienteId } from "./consumoPDFService";
import {
  getConsumoSummaryByCliente,
  getConsumoResellerByCliente,
} from "./azureConsumptionService";
import { getConsumoByClienteId } from "./consumoSubscricaoService";
import axios from "axios";

// URL da API
const API_BASE_URL = `${process.env.REACT_APP_WEBAPI_URL}/api`;

/**
 * Busca e processa todos os dados necessários para a comparação de consumos
 * @param {string} clienteId - ID do cliente
 * @param {Date} dataInicio - Data inicial para filtrar os dados
 * @param {Date} dataFim - Data final para filtrar os dados
 * @returns {Promise<Object>} Objeto com todos os dados processados para comparação
 */
export const getComparacaoData = async (
  clienteId,
  dataInicio = null,
  dataFim = null
) => {
  console.log("Iniciando getComparacaoData:", {
    clienteId,
    dataInicio,
    dataFim,
  });
  try {
    // Buscar todos os dados necessários em paralelo
    const [
      resellerData,
      azureData,
      pdfData,
      consumoSubscricaoData,
      resellerConsumptionData,
    ] = await Promise.all([
      getConsumoSummaryByResourceGroup(clienteId, "Geral", dataInicio, dataFim),
      getConsumoSummaryByCliente(clienteId, dataInicio, dataFim),
      getConsumoPDFByClienteId(clienteId),
      getConsumoByClienteId(clienteId),
      getConsumoResellerByCliente(clienteId, dataInicio, dataFim),
    ]);

    console.log("Dados brutos do Azure recebidos:", azureData);
    console.log("Dados brutos do Reseller recebidos:", resellerConsumptionData);

    console.log("Dados recebidos:", {
      resellerData,
      azureData,
      pdfData,
      consumoSubscricaoData,
      resellerConsumptionData,
    });

    // Calcular o total da fatura do PDF SOMENTE para documentos dentro do período
    let totalFatura = 0;
    if (Array.isArray(pdfData) && pdfData.length > 0) {
      // Se datas de filtro existirem, filtrar os documentos pelo período
      let filteredPDFs = pdfData;
      if (dataInicio || dataFim) {
        filteredPDFs = pdfData.filter((item) => {
          // Tentar obter a data do documento (pode ser dataFatura, dataInicio, dataFim, etc)
          const dataDoc = new Date(
            item.dataFatura ||
              item.dataInicio ||
              item.dataFim ||
              item.data ||
              item.DataFatura
          );
          let passes = true;
          if (dataInicio && dataDoc < new Date(dataInicio)) passes = false;
          if (dataFim && dataDoc > new Date(dataFim)) passes = false;
          return passes;
        });
      }
      totalFatura = filteredPDFs.reduce((sum, item) => {
        const valor = item.valorTotal || item.totalFatura || item.valor || 0;
        return sum + (parseFloat(valor) || 0);
      }, 0);
    }

    console.log("Total da fatura calculado:", totalFatura);

    // Processar os dados para o formato esperado pelo componente
    // Calcular o valor total do Azure somando todos os custos da tabela ConsumoSubscricao
    let azureTotalCost = 0;

    // Verificar se temos dados de consumo da subscrição
    if (
      Array.isArray(consumoSubscricaoData) &&
      consumoSubscricaoData.length > 0
    ) {
      // Somar todos os valores de Cost da mesma subscrição
      azureTotalCost = consumoSubscricaoData.reduce(
        (sum, item) => sum + (parseFloat(item.cost) || 0),
        0
      );
      console.log(
        "Valor total do Azure calculado a partir da tabela ConsumoSubscricao:",
        azureTotalCost
      );
    } else if (azureData && typeof azureData === "object") {
      // Fallback para o método anterior caso não tenha dados da subscrição
      // Verificar se é um array (caso seja retornado um array de resumos)
      if (Array.isArray(azureData) && azureData.length > 0) {
        azureTotalCost = azureData.reduce(
          (sum, item) => sum + (item.TotalCost || 0),
          0
        );
      } else {
        // Se for um objeto único
        azureTotalCost = azureData.TotalCost || 0;
      }
      console.log(
        "Valor do custo total do Azure processado pelo método alternativo:",
        azureTotalCost
      );
    }
    console.log("Valor final do custo total do Azure:", azureTotalCost);

    // Priorizar os novos dados de consumo reseller da API específica, caso contrário usar dados antigos
    const countryCustomerTotal =
      resellerConsumptionData?.countryCustomerTotal ??
      resellerData?.countryCustomerTotal ??
      0;
    const countryResellerTotal =
      resellerConsumptionData?.countryResellerTotal ??
      resellerData?.countryResellerTotal ??
      0;
    const countryListTotal =
      resellerConsumptionData?.countryListTotal ??
      resellerData?.countryListTotal ??
      0;

    const processedData = {
      resellerConsumption: {
        CountryCustomerTotal: countryCustomerTotal,
        CountryResellerTotal: countryResellerTotal,
        CountryListTotal: countryListTotal,
        TotalGeral:
          countryCustomerTotal + countryResellerTotal + countryListTotal,
      },
      azureConsumption: {
        TotalCost: azureTotalCost,
        TotalCostUSD: azureData?.TotalCostUSD || 0,
        ResourceCount: azureData?.ResourceCount || 0,
        Currency: azureData?.Currency || "EUR",
        TotalCostEUR: azureTotalCost, // Garantindo que o valor em euros seja o mesmo do TotalCost
      },
      resellerInvoice: {
        TotalFatura: totalFatura,
        Documentos: pdfData || [],
      },
    };

    console.log("Dados processados para comparação:", processedData);
    return processedData;
  } catch (error) {
    console.error("Erro ao buscar dados para comparação:", error);
    console.log("Detalhes do erro:", {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
    });

    // Retornar objeto vazio com a estrutura esperada em caso de erro
    return {
      resellerConsumption: {
        CountryCustomerTotal: 0,
        CountryResellerTotal: 0,
        CountryListTotal: 0,
        TotalGeral: 0,
      },
      azureConsumption: {
        TotalCost: 0,
        TotalCostUSD: 0,
        ResourceCount: 0,
        Currency: "EUR",
        TotalCostEUR: 0, // Adicionando o valor do custo total em euros
      },
      resellerInvoice: {
        TotalFatura: 0,
        Documentos: [],
      },
    };
  }
};

/**
 * Calcula a diferença percentual entre dois valores
 * @param {number} value1 - Primeiro valor
 * @param {number} value2 - Segundo valor
 * @returns {number} Diferença percentual
 */
export const calculateDifference = (value1, value2) => {
  console.log("Calculando diferença:", { value1, value2 });

  // Se ambos os valores forem zero ou muito próximos de zero, retornar 0
  if (Math.abs(value1) < 0.01 && Math.abs(value2) < 0.01) return 0;

  // Se apenas o segundo valor for zero ou muito pequeno, verificar o primeiro valor
  if (Math.abs(value2) < 0.01) {
    // Se o primeiro valor for significativo, retornar 1000% (valor máximo)
    if (Math.abs(value1) >= 0.01) return 1000;
    return 0;
  }

  // Se apenas o primeiro valor for zero ou muito pequeno, retornar -100%
  if (Math.abs(value1) < 0.01) return -100;

  // Calcular a diferença percentual e limitar a um valor razoável
  const diff = ((value1 - value2) / value2) * 100;
  return Math.max(Math.min(diff, 1000), -100); // Limita entre -100% e 1000%
};

/**
 * Calcula a diferença absoluta entre dois valores
 * @param {number} value1 - Primeiro valor
 * @param {number} value2 - Segundo valor
 * @returns {number} Diferença absoluta
 */
export const calculateAbsoluteDifference = (value1, value2) => {
  console.log("Calculando diferença absoluta:", { value1, value2 });

  // Converter para números e garantir que são valores válidos
  const num1 = parseFloat(value1) || 0;
  const num2 = parseFloat(value2) || 0;

  // Calcular a diferença absoluta
  return num1 - num2;
};

/**
 * Formata um valor monetário
 * @param {number} value - Valor a ser formatado
 * @param {string} currency - Moeda (padrão: EUR)
 * @returns {string} Valor formatado
 */
export const formatCurrency = (value, currency = "EUR") => {
  console.log("Formatando valor monetário:", { value, currency });
  if (!value) return "0,00 €";
  return new Intl.NumberFormat("pt-PT", {
    style: "currency",
    currency: currency,
  }).format(value);
};

/**
 * Formata um valor percentual
 * @param {number} value - Valor a ser formatado (em decimal)
 * @returns {string} Valor formatado como percentual
 */
export const formatPercentage = (value) => {
  console.log("Formatando porcentagem:", { value });
  return new Intl.NumberFormat("pt-PT", {
    style: "percent",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value / 100);
};

export default {
  getComparacaoData,
  calculateDifference,
  calculateAbsoluteDifference,
  formatCurrency,
  formatPercentage,
};
