import axios from 'axios';

const API_BASE_URL = `${process.env.REACT_APP_WEBAPI_URL}/api`;

export const getSubscricoesByCliente = async (clienteId) => {
    try {
        const response = await axios.get(`${API_BASE_URL}/ClienteSubscricao/cliente/${clienteId}/details`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        return response.data;
    } catch (error) {
        if (error.response && error.response.status === 404) {
            console.log('Cliente não encontrado ou sem subscrições');
            return [];
        }
        console.error('Erro ao buscar subscrições:', error);
        throw new Error('Erro ao buscar subscrições do cliente');
    }
};

export const getSubscriptionDetailById = async (subscriptionId) => {
    try {
        const response = await handleMethod({
            method: `${API_BASE_URL}/Subscription/${subscriptionId}`,
            type: 'GET'
        });

        if (!response || response.error) {
            throw new Error(`Erro ao buscar detalhes da subscrição: ${response?.description || 'Erro desconhecido'}`);
        }

        return response.data || response;
    } catch (error) {
        console.error("❌ ERRO ao buscar detalhes da subscrição:", error);
        throw error;
    }
};

export const addSubscricao = async (clienteId, subscriptionId) => {
    try {
        const payload = {
            ClienteID: clienteId,
            SubscriptionID: subscriptionId
        };

        const response = await axios.post(
            `${API_BASE_URL}/ClienteSubscricao`,
            payload,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}` // se usas JWT, ajusta aqui
                }
            }
        );

        return response.data;
    } catch (error) {
        console.error("❌ ERRO ao adicionar subscrição:", error);
        throw error;
    }
};

export const removeSubscricao = async (clienteId, subscriptionId) => {
    try {
        const response = await axios.delete(`${API_BASE_URL}/ClienteSubscricao/cliente/${clienteId}/subscricao/${subscriptionId}`);
        return response.data;
    } catch (error) {
        console.error("erro a remover subscrição:", error);
        throw error;
    }
};

export const getAvailableSubscriptions = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/ClienteSubscricao/available`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Erro ao buscar subscrições disponíveis:', error);
        throw new Error('Erro ao buscar subscrições disponíveis');
    }
};
