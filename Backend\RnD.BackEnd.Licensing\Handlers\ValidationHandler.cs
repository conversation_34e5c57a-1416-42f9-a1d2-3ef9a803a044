﻿using System;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using RestSharp;
using RestSharp.Serializers.NewtonsoftJson;
using RnD.BackEnd.Licensing.Entities;
using RnD.BackEnd.Licensing.Settings;

namespace RnD.BackEnd.Licensing.Handlers
{
    /// <summary>
    /// License Validation handler class
    /// </summary>
    public class ValidationHandler
    {
        private readonly ValidationSettings _validationSettings;
        private readonly string _domainNameHeader = "DomainName";
        private readonly string _validationTypeHeader = "ValidationType";
        private readonly string _envClaim = "env";

        /// <summary>
        /// Initializes a new instance of the <see cref="ValidationHandler" /> class.
        /// </summary>
        /// <param name="validationSettings">The validation settings.</param>
        /// <exception cref="ArgumentNullException">Validation Settings</exception>
        public ValidationHandler(ValidationSettings validationSettings)
        {
            _validationSettings = validationSettings;

            if (_validationSettings == null)
            {
                throw new ArgumentNullException("Validation Settings");
            }
        }

        /// <summary>
        /// Validates the licence asynchronous.
        /// </summary>
        /// <param name="domainName">Name of the domain.</param>
        /// <param name="encryptionKeys">The encryption keys.</param>
        /// <param name="validationType">Type of the validation.</param>
        /// <returns>
        /// True if license is valid, False otherwise.
        /// </returns>
        public async Task<bool> ValidateLicenseAsync(string domainName, EncryptionKeys encryptionKeys, ValidationType? validationType = null)
        {
            string payload = GetPayloadToValidate();
            ServiceOutput encryptedToken = await ValidateLicense(payload, domainName, validationType);
            return encryptedToken.Code == HttpStatusCode.OK && ValidateToken(encryptedToken.Value, domainName, encryptionKeys);
        }

        /// <summary>
        /// Gets the keys (Encryption and Signing) from cache.
        /// </summary>
        /// <returns>
        /// Encryption and Signing Keys
        /// </returns>
        public EncryptionKeys GetEncryptionAndSigningKeys()
        {
            // Get encryption key
            RSA ek = RSA.Create();
            ek.FromXmlString(EncryptionSettings.StrPrivEncKey);
            RsaSecurityKey encryptionKey = new RsaSecurityKey(ek) { KeyId = EncryptionSettings.EncryptionKid };

            // Get signing key
            // If using .NET < 5, use ConvertExtentions.HexStringToByteArray
            byte[] publicKeyBytes = Convert.FromHexString(EncryptionSettings.StrPubSignKey);

            ECDsa sk = ECDsa.Create(new ECParameters
            {
                Curve = ECCurve.NamedCurves.nistP256,
                Q = new ECPoint
                {
                    X = publicKeyBytes.Skip(1).Take(32).ToArray(),
                    Y = publicKeyBytes.Skip(33).ToArray()
                }
            });

            ECDsaSecurityKey signingKey = new ECDsaSecurityKey(sk) { KeyId = EncryptionSettings.SigningKid };

            return new EncryptionKeys()
            {
                EncryptionKey = encryptionKey,
                SigningKey = signingKey
            };
        }

        #region Private methods

        /// <summary>
        /// Hashes the payload.
        /// </summary>
        /// <returns>
        /// The payload to validate
        /// </returns>
        private string GetPayloadToValidate()
        {
            using SHA256 hash = SHA256.Create();

            // If using .NET < 5, use ConvertExtentions.ByteArrayToHexString
            string rnd = Convert.ToHexString(hash.ComputeHash(Encoding.UTF8.GetBytes(Guid.NewGuid().ToString())));

            string salt = rnd.Substring(rnd[0] % 16);

            string payloadToValidate =
                _validationSettings?.Application
                + _validationSettings?.CustomerKey
                + _validationSettings?.Customer
                + salt;

            // If using .NET < 5, use ConvertExtentions.ByteArrayToHexString
            string data = Convert.ToHexString(hash.ComputeHash(Encoding.UTF8.GetBytes(payloadToValidate.ToUpperInvariant())));

            return "\"" + Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(new { a = rnd, b = data }))) + "\"";
        }

        /// <summary>
        /// Calls license validation and gets License token.
        /// </summary>
        /// <param name="payload">The payload.</param>
        /// <param name="domainName">Name of the domain.</param>
        /// <param name="validationType">Type of the validation.</param>
        /// <returns>
        /// ServiceOutput with null if license is invalid, else returns Token
        /// </returns>
        private async Task<ServiceOutput> ValidateLicense(string payload, string domainName, ValidationType? validationType = null)
        {
            ServiceOutput output = new ServiceOutput();

            var options = new RestClientOptions(_validationSettings?.BaseURL ?? string.Empty);
            using (var client = new RestClient(options, configureSerialization: s => s.UseNewtonsoftJson()))
            {
                RestRequest apiRequest = new RestRequest(_validationSettings?.ValidationEndpoint);
                apiRequest.AddHeader(_domainNameHeader, domainName);
                // Header to specify validation type
                apiRequest.AddHeader(_validationTypeHeader, validationType.HasValue ? validationType.Value : ValidationType.DOMAIN);
                apiRequest.AddJsonBody(payload);

                RestResponse<ServiceOutput> serviceResponse = await client.ExecutePostAsync<ServiceOutput>(apiRequest);

                if (!serviceResponse.IsSuccessStatusCode)
                {
                    output.Code = serviceResponse.StatusCode;
                }
                else
                {
                    output = serviceResponse.Data;
                }
            }

            return output;
        }

        /// <summary>
        /// Validates the token.
        /// </summary>
        /// <param name="encryptedToken">The encrypted token.</param>
        /// <param name="domainName">Name of the domain.</param>
        /// <param name="encryptionKeys">The encryption keys.</param>
        /// <returns>
        /// If token valid returns true, else returns false
        /// </returns>
        private bool ValidateToken(string encryptedToken, string domainName, EncryptionKeys encryptionKeys)
        {
            JsonWebTokenHandler handler = new JsonWebTokenHandler();
            TokenValidationResult result = handler.ValidateToken(
                encryptedToken,
                new TokenValidationParameters
                {
                    ValidAudience = _validationSettings?.Application?.ToUpperInvariant(),
                    ValidIssuer = _validationSettings?.CustomerKey?.ToUpperInvariant(),
                    IssuerSigningKey = encryptionKeys.SigningKey,
                    TokenDecryptionKey = encryptionKeys.EncryptionKey
                });

            return result.IsValid && (string)result.Claims[_envClaim] == domainName.ToUpperInvariant();
        }

        #endregion
    }
}
