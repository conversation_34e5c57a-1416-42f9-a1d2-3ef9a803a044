const getAuthProvider = () => {
    return `
    import { PublicClientApplication } from "@azure/msal-browser";
    import { <PERSON>al<PERSON>rovider } from '@azure/msal-react';
    import { msalConfig } from "./Utils/APIutils/msalConfig";
    import { AuthProvider } from './Context/CustomContext';

    //In this case we also use MSAL:

    const root = ReactDOM.createRoot(document.getElementById('root'));
    const msalInstance = new PublicClientApplication(msalConfig);
    <Your Code...>
        <AuthProvider instance={msalInstance}>
            <App />
        </AuthProvider>
    </Your Code...>
    );
    `;
}

export default getAuthProvider;
