﻿using System;
using RnD.BackEnd.API.Model.Weather;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController
{
    /// <summary>
    /// Weather update request example
    /// </summary>
    public class UpdateWeather : IExamplesProvider<WeatherDto>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to update.
        /// </returns>
        public WeatherDto GetExamples()
        {
            return new WeatherDto
            {
                Id = Guid.NewGuid(),
                Location = "Lisbon",
                Summary = null,
                Temperature = 27
            };
        }
    }
}