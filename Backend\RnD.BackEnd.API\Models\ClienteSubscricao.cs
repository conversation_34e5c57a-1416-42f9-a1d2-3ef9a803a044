﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RnD.BackEnd.API.Models
{
    public class ClienteSubscricao
    {
        [Key]
        public int ID { get; set; }

        [Required]
        [ForeignKey("Cliente")]
        public int ClienteID { get; set; }

        [Required]
        [ForeignKey("Subscription")]
        public int SubscriptionID { get; set; }

        public virtual Cliente Cliente { get; set; }
        public virtual Subscription Subscription { get; set; }
    }
}