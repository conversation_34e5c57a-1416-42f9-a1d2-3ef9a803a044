﻿param(
   [string][parameter(Mandatory = $true)] $vaultName,
   [string][parameter(Mandatory = $true)] $tenantId,
   [string][parameter(Mandatory = $true)] $clientId,
   [string][parameter(Mandatory = $true)] $clientSecret,
   [string][parameter(Mandatory = $true)] $bi4allLicense,
   [string][parameter(Mandatory = $true)] $dataEntryPassword,
   [string][parameter(Mandatory = $true)] $dataEntryUsername,
   [string][parameter(Mandatory = $true)] $sendGridApiKey,
   [string][parameter(Mandatory = $true)] $wfeSecret
)

function Set-Secret()
{
    [CmdletBinding()]
    param
    (
        [string][parameter(Mandatory = $true)]$secretName,
        [string][parameter(Mandatory = $true)]$value,
        [string][parameter(Mandatory = $true)]$vaultName
    )
    BEGIN
    {
        Write-Host -ForegroundColor Green "Updating KeyVault: $secretName ...STARTING..."

        $secretValue = ConvertTo-SecureString $value -AsPlainText -Force

        Set-AzureKeyVaultSecret -VaultName $vaultName -Name $secretName -SecretValue $secretValue
        Write-Host Setting azureAdClientId written to $secretName

        Write-Host -ForegroundColor Green "Updating KeyVault: $secretName ...FINISHED!"
    }
}

Set-Secret -secretName "TenantId" -value $tenantId -vaultName $vaultName
Set-Secret -secretName "AzureAd-ClientId" -value $clientId -vaultName $vaultName
Set-Secret -secretName "AzureAd-ClientSecret" -value $clientSecret -vaultName $vaultName

Set-Secret -secretName "LicenseKey" -value $bi4allLicense -vaultName $vaultName

Set-Secret -secretName "DataEntry-Password" -value $dataEntryPassword -vaultName $vaultName
Set-Secret -secretName "DataEntry-Username" -value $dataEntryUsername -vaultName $vaultName

Set-Secret -secretName "SendGrid-ApiKey" -value $sendGridApiKey -vaultName $vaultName

Set-Secret -secretName "WFE-Secret" -value $wfeSecret -vaultName $vaultName