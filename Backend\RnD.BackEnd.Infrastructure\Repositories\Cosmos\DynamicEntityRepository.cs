﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using RnD.BackEnd.Domain.DataModels;
using RnD.BackEnd.Domain.Extensions;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos;
using RnD.BackEnd.Domain.Settings.Cosmos;
using RnD.BackEnd.Infrastructure.Interfaces;
using RnD.BackEnd.Infrastructure.Repositories.Cosmos.Base;

namespace RnD.BackEnd.Infrastructure.Repositories.Cosmos
{
    /// <summary>
    /// Dynamic entity repository class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Infrastructure.Repositories.Cosmos.Base.CosmosOperations" />
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos.IDynamicEntityRepository" />
    public class DynamicEntityRepository : CosmosOperations, IDynamicEntityRepository
    {
        /// <summary>
        /// The name of the partition key field.
        /// </summary>
        public string PartitionKeyFieldName { get; } = "entityId";

        /// <summary>
        /// The name of the identifier field.
        /// </value>
        public string IdFieldName { get; } = "id";

        /// <summary>
        /// Name of the container.
        /// </summary>
        public override string ContainerName { get; } = "DynamicEntities";

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicEntityRepository" /> class.
        /// </summary>
        /// <param name="cosmosDbManager">The cosmos database manager.</param>
        /// <param name="cosmosSettings">The cosmos settings.</param>
        public DynamicEntityRepository(ICosmosDbManager cosmosDbManager, IOptions<CosmosSettings> cosmosSettings)
            : base(cosmosDbManager, cosmosSettings)
        {
        }

        /// <summary>
        /// Generates the identifier.
        /// </summary>
        /// <param name="entity">The entity.</param>
        /// <returns>
        /// The identifier
        /// </returns>
        public override string GenerateId(object model) => $"{((Dictionary<string, object>)model).GetStringValue(PartitionKeyFieldName)}:{Guid.NewGuid()}";

        /// <summary>
        /// Resolves the partition key.
        /// </summary>
        /// <param name="partitionKey">The partition key.</param>
        /// <returns>
        /// The partition key.
        /// </returns>
        public override PartitionKey ResolvePartitionKey(string partitionKey) => new PartitionKey(partitionKey.Split(':')[0]);

        /// <summary>
        /// Adds the specified entity.
        /// </summary>
        /// <param name="entity">The entity.</param>
        /// <returns>
        /// The entity added.
        /// </returns>
        public async Task<ResponseModel<Dictionary<string, object>>> AddAsync(Dictionary<string, object> entity)
        {
            string id = GenerateId(entity);
            entity.AddOrUpdate(IdFieldName, id);

            return await AddItemAsync(item: entity, itemId: id);
        }

        /// <summary>
        /// Updates the specified entity.
        /// </summary>
        /// <param name="entity">The entity.</param>
        /// <param name="etag">The etag.</param>
        /// <returns>
        /// The entity updated.
        /// </returns>
        public async Task<ResponseModel<Dictionary<string, object>>> UpdateAsync(Dictionary<string, object> entity, string etag)
        {
            string id = entity.GetStringValue(IdFieldName);
            return await UpdateItemAsync(item: entity, itemId: id, etag: etag);
        }

        /// <summary>
        /// Deletes the entity.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// True if operation succeeded, False otherwise.
        /// </returns>
        public async Task<bool> DeleteAsync(string id)
        {
            return await DeleteItemAsync<Dictionary<string, object>>(id: id);
        }

        /// <summary>
        /// Gets the specified entity.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The entity
        /// </returns>
        public async Task<ResponseModel<Dictionary<string, object>>> GetAsync(string id)
        {
            return await GetItemAsync<Dictionary<string, object>>(id: id);
        }

        /// <summary>
        /// Lists the entities.
        /// </summary>
        /// <param name="pageNumber">The page number.</param>
        /// <param name="maxItems">The maximum items.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// The list of entities.
        /// </returns>
        public async Task<ResponseModel<List<Dictionary<string, object>>>> ListAsync(
            int? pageNumber,
            int? maxItems,
            string sortField,
            bool sortAscending,
            string continuationToken,
            CancellationToken cancellationToken)
        {
            StringBuilder builder = new("SELECT * FROM c WHERE 1=1 ");
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            AddPaginationAndSorting(builder, parameters, pageNumber, maxItems, sortField, sortAscending);

            return await GetItemsAsync<Dictionary<string, object>>(builder.ToString(), parameters, maxItems, continuationToken, cancellationToken);
        }
    }
}
