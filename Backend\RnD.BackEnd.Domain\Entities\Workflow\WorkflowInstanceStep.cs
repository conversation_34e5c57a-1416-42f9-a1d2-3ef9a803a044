﻿using System;
using System.Collections.Generic;

namespace RnD.BackEnd.Domain.Entities.Workflow
{
    public class WorkflowInstanceStep
    {
        public Guid Id { get; set; }
        public Guid? StepId { get; set; }
        public Guid InstanceId { get; set; }
        public string DisplayName { get; set; }
        public bool AllUsersMustApprove { get; set; }
        public bool SkipOverOtherTasks { get; set; }
        public int Order { get; set; }
        public bool IsActive { get; set; }
        public string StepStatus { get; set; }
        public int NumberOfActionsRequired { get; set; }
        public int NumberOfActionsCompleted { get; set; }
        public IList<WorkflowTask> Tasks { get; set; }
    }
}