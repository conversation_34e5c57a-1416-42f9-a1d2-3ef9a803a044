﻿using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace RnD.BackEnd.API.Helpers
{
    /// <summary>
    /// Localization middleware class
    /// </summary>
    public class LocalizationMiddleware
    {
        readonly RequestDelegate _next;

        /// <summary>
        /// Initializes a new instance of the <see cref="LocalizationMiddleware" /> class.
        /// </summary>
        /// <param name="next">The next.</param>
        /// <exception cref="ArgumentNullException">next</exception>
        public LocalizationMiddleware(RequestDelegate next)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
        }

        /// <summary>
        /// Invokes the specified HTTP context.
        /// </summary>
        /// <param name="httpContext">The HTTP context.</param>
        /// <exception cref="System.ArgumentNullException">httpContext</exception>
        public async Task Invoke(HttpContext httpContext)
        {
            if (httpContext == null)
            {
                throw new ArgumentNullException(nameof(httpContext));
            }

            string[] userLanguages = httpContext.Request.GetTypedHeaders().AcceptLanguage?.OrderByDescending(x => x.Quality ?? 1) // Quality defines priority from 0 to 1, where 1 is the highest.
                   .Select(x => x.Value.ToString())
                   .ToArray() ?? Array.Empty<string>();

            CultureInfo myCultureInfo = new CultureInfo(userLanguages.Length > 0 ? userLanguages.First() : "pt");
            CultureInfo.DefaultThreadCurrentUICulture = myCultureInfo;

            await _next(httpContext);
        }
    }
}
