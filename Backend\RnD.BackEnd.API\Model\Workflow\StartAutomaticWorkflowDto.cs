﻿using System.Collections.Generic;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Start automatic workflow request DTO class
    /// </summary>
    public class StartAutomaticWorkflowDto
    {
        /// <summary>
        /// Gets or sets the workflow ids.
        /// </summary>
        /// <value>
        /// The workflow ids.
        /// </value>
        public List<string> WorkflowIds { get; set; }

        /// <summary>
        /// Gets or sets the source identifier.
        /// </summary>
        /// <value>
        /// The source identifier.
        /// </value>
        public string SourceId { get; set; }

        /// <summary>
        /// Gets or sets the source URL.
        /// </summary>
        /// <value>
        /// The source URL.
        /// </value>
        public string SourceUrl { get; set; }

        /// <summary>
        /// Gets or sets the source metadata.
        /// </summary>
        /// <value>
        /// The source metadata.
        /// </value>
        public Dictionary<string, string> SourceMetadata { get; set; }

        /// <summary>
        /// Gets or sets the workflow email.
        /// </summary>
        /// <value>
        /// The workflow email.
        /// </value>
        public WorkflowEmailDto EmailRequest { get; set; }
    }
}