﻿using System;

namespace RnD.BackEnd.Domain.Entities.Workflow
{
    public class WorkflowTask
    {
        public Guid? Id { get; set; }
        public Guid WorkflowInstanceId { get; set; }
        public Guid StepId { get; set; }
        public string UserId { get; set; }
        public string TaskStatus { get; set; }
        public bool TaskCompleted { get; set; }
        public bool TaskActivated { get; set; }
        public string Comments { get; set; }
        public string SourceId { get; set; }
        public string SourceUrl { get; set; }
        public string WorkflowStatus { get; set; }
        public string WorkflowName { get; set; }
        public string StepName { get; set; }
        public int StepOrder { get; set; }
        public DateTimeOffset SysModifyDate { get; set; }
        public int TotalRows { get; set; }
        public bool UserAllowedToEdit { get; set; }
    }
}