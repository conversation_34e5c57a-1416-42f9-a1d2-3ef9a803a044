﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Hangfire" Version="1.8.6" />
    <PackageReference Include="Hangfire.Console" Version="1.4.2" />
    <PackageReference Include="Hangfire.Console.Extensions" Version="1.0.5" />
    <PackageReference Include="Hangfire.SqlServer" Version="1.8.6" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RnD.BackEnd.Domain\RnD.BackEnd.Domain.csproj" />
    <ProjectReference Include="..\RnD.BackEnd.Infrastructure\RnD.BackEnd.Infrastructure.csproj" />
    <ProjectReference Include="..\RnD.BackEnd.Licensing\RnD.BackEnd.Licensing.csproj" />
    <ProjectReference Include="..\RnD.BackEnd.Service\RnD.BackEnd.Service.csproj" />
  </ItemGroup>

  
</Project>
