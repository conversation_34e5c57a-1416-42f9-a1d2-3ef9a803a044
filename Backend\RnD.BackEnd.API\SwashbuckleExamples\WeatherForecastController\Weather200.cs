﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Weather;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController
{
    /// <summary>
    /// Weather Forecast response example for 200
    /// </summary>
    public class Weather200 : IExamplesProvider<ApiOutput<WeatherDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 response.
        /// </returns>
        public ApiOutput<WeatherDto> GetExamples()
        {
            return new ApiOutput<WeatherDto>
            {
                Code = HttpStatusCode.OK,
                Value = new WeatherDto
                {
                    Id = Guid.NewGuid(),
                    Temperature = 21,
                    Summary = "Cloudy",
                    Location = "Arusha"
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}