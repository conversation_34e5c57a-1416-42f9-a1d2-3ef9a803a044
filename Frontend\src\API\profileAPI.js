import handleMethod from '../Utils/APIutils/handleMethod';

class ProfileAPI {
  async getProfile() {
    const Input = {
      type: 'GET',
      method: `getProfile`,
      params: {}
    };

    const response = await handleMethod(Input);

    return response;
  }

  async getOptions() {
    const Input = {
      type: 'GET',
      method: `getOptions`,
      params: {}
    };

    const response = await handleMethod(Input);

    return response;
  }
}

export const profileAPI = new ProfileAPI();
