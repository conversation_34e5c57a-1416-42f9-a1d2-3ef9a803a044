import merge from 'lodash/merge';
import { createTheme, responsiveFontSizes } from '@mui/material/styles';
import palette from './palette';
import typography from './typography';
import components from './components';
import themeConfig from './themeConfig';
import { ptPT, enUS } from '@mui/material/locale';

const THEMES = {
  LIGHT: 'LIGHT',
  DARK: 'DARK',
  NATURE: 'NATURE'
};

// Theme configuration
const themesOptions = {
  [THEMES.LIGHT]: {
    spacing: themeConfig.spacing,
    palette,
    typography,
    components
  }
};

export const createCustomTheme = (config = {}) => {
  let themeOptions = themesOptions[config.theme];

  if (!themeOptions) {
    console.warn(new Error(`The theme ${config.theme} is not valid`));
    themeOptions = themesOptions[THEMES.LIGHT];
  }

  //Localization -> The process of adapting a product or content to a specific locale or market.
  const locale = config.language === 'en' ? enUS : ptPT;

  //Lodash's merge() function copies the 2nd object's own properties and inherited properties into the first object.
  let theme = createTheme(merge({}, themeOptions, {
    ...(config.roundedCorners && {
      shape: {
        borderRadius: 4
      }
    })
  }, {
    direction: config.direction,
  }, locale));

  if (config.responsiveFontSizes) {
    theme = responsiveFontSizes(theme);
  }

  return theme;
};
