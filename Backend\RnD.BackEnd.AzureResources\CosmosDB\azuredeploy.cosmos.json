{"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"databaseAccountName": {"type": "string"}, "databaseName": {"type": "string"}, "location": {"type": "string"}, "locationName": {"type": "string"}, "defaultExperience": {"type": "string", "defaultValue": "Core (SQL)"}, "isZoneRedundant": {"type": "bool", "defaultValue": false}, "totalThroughputLimit": {"type": "int", "defaultValue": 1000}, "enableFreeTier": {"type": "bool", "defaultValue": true}}, "variables": {}, "resources": [{"apiVersion": "2022-02-15-preview", "kind": "GlobalDocumentDB", "type": "Microsoft.DocumentDb/databaseAccounts", "name": "[parameters('databaseAccountName')]", "location": "[parameters('location')]", "properties": {"databaseAccountOfferType": "Standard", "publicNetworkAccess": "Enabled", "enableAutomaticFailover": false, "enableMultipleWriteLocations": false, "isVirtualNetworkFilterEnabled": false, "virtualNetworkRules": [], "disableKeyBasedMetadataWriteAccess": false, "enableFreeTier": "[parameters('enableFreeTier')]", "enableAnalyticalStorage": false, "analyticalStorageConfiguration": {"schemaType": "WellDefined"}, "defaultIdentity": "FirstPartyIdentity", "networkAclBypass": "None", "disableLocalAuth": false, "consistencyPolicy": {"defaultConsistencyLevel": "Session", "maxIntervalInSeconds": 5, "maxStalenessPrefix": 100}, "locations": [{"id": "[concat(parameters('databaseAccountName'), '-', parameters('location'))]", "failoverPriority": 0, "locationName": "[parameters('locationName')]", "isZoneRedundant": "[parameters('isZoneRedundant')]"}], "cors": [], "capabilities": [], "ipRules": [], "backupPolicy": {"type": "Periodic", "periodicModeProperties": {"backupIntervalInMinutes": 240, "backupRetentionIntervalInHours": 8, "backupStorageRedundancy": "Local"}}, "networkAclBypassResourceIds": [], "diagnosticLogSettings": {"enableFullTextQuery": "None"}, "capacity": {"totalThroughputLimit": "[parameters('totalThroughputLimit')]"}}, "tags": {"defaultExperience": "[parameters('defaultExperience')]", "hidden-cosmos-mmspecial": "", "displayName": "CosmosDB Account"}}, {"type": "Microsoft.DocumentDB/databaseAccounts/sqlDatabases", "apiVersion": "2021-11-15-preview", "name": "[concat(parameters('databaseAccountName'), '/', parameters('databaseName'))]", "dependsOn": ["[resourceId('Microsoft.DocumentDB/databaseAccounts', parameters('databaseAccountName'))]"], "properties": {"resource": {"id": "[parameters('databaseName')]"}}, "tags": {"displayName": "Cosmos Database"}}, {"type": "Microsoft.DocumentDB/databaseAccounts/sqlRoleDefinitions", "apiVersion": "2021-11-15-preview", "name": "[concat(parameters('databaseAccountName'), '/********-0000-0000-0000-********0001')]", "dependsOn": ["[resourceId('Microsoft.DocumentDB/databaseAccounts', parameters('databaseAccountName'))]"], "properties": {"roleName": "Cosmos DB Built-in Data Reader", "type": "BuiltInRole", "assignableScopes": ["[resourceId('Microsoft.DocumentDB/databaseAccounts', parameters('databaseAccountName'))]"], "permissions": [{"dataActions": ["Microsoft.DocumentDB/databaseAccounts/readMetadata", "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/executeQuery", "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/readChangeFeed", "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/items/read"], "notDataActions": []}]}, "tags": {"displayName": "Cosmos DB Built-in Data Reader"}}, {"type": "Microsoft.DocumentDB/databaseAccounts/sqlRoleDefinitions", "apiVersion": "2021-11-15-preview", "name": "[concat(parameters('databaseAccountName'), '/********-0000-0000-0000-************')]", "dependsOn": ["[resourceId('Microsoft.DocumentDB/databaseAccounts', parameters('databaseAccountName'))]"], "properties": {"roleName": "Cosmos DB Built-in Data Contributor", "type": "BuiltInRole", "assignableScopes": ["[resourceId('Microsoft.DocumentDB/databaseAccounts', parameters('databaseAccountName'))]"], "permissions": [{"dataActions": ["Microsoft.DocumentDB/databaseAccounts/readMetadata", "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/*", "Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers/items/*"], "notDataActions": []}]}, "tags": {"displayName": "Cosmos DB Built-in Data Contributor"}}], "outputs": {"Cosmos-Endpoint": {"type": "string", "value": "[reference(resourceId('Microsoft.DocumentDb/databaseAccounts', parameters('databaseAccountName')), '2022-02-15-preview').documentEndpoint]"}, "Cosmos-PrimaryKey": {"type": "string", "value": "[listKeys(resourceId('Microsoft.DocumentDb/databaseAccounts', parameters('databaseAccountName')), '2022-02-15-preview').primaryMasterKey]"}}}