﻿using System.Collections.Generic;
using RnD.BackEnd.API.Model.Workflow;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// Instances by SourceIds request example
    /// </summary>
    public class GetInstancesBySourceIdsAndStatus : IMultipleExamplesProvider<WorkflowInstanceBySourceIdsDto>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// Examples of requests to Get Instances by SourceIds.
        /// </returns>
        public IEnumerable<SwaggerExample<WorkflowInstanceBySourceIdsDto>> GetExamples()
        {
            yield return SwaggerExample.Create("Multiple SourceIds", new WorkflowInstanceBySourceIdsDto
            {
                SourceIds = new List<string> { "Vacation/2023", "Project/2023" },
                Status = new List<string> { "IN_PROGRESS" }
            });
            yield return SwaggerExample.Create("Without Status", new WorkflowInstanceBySourceIdsDto
            {
                SourceIds = new List<string> { "Vacation/2023", "Project/2023" },
            });
            yield return SwaggerExample.Create("Multiple Status", new WorkflowInstanceBySourceIdsDto
            {
                SourceIds = new List<string> { "Vacation/2023", "Project/2023" },
                Status = new List<string> { "IN_PROGRESS", "CANCELLED" }
            });
        }
    }
}
