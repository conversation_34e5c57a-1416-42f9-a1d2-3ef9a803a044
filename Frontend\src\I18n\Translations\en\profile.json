{"common": {"about": "About...", "completedProfile": "Completed Profile", "personalData": "Personal Data", "fullName": "Full Name", "DOB": "Date of Birth", "contacts": "Contacts", "tel": "Telephone", "email": "Professional Email", "personalEmail": "Personal Email", "addresses": "Addresses", "address": "Address", "city": "City", "zipCode": "Zip Code", "postalLocation": "Zip Code City", "taxData": "Tax Data", "id": "Id Card Number", "expDate": "Expire Date", "maritalStatus": "Marital Status", "socialSecurity": "Social Security No", "tax": "Tax Identification", "incomeHolder": "Income Holder", "dependents": "Dependents", "empNo": "Employee Number", "profile": "Profile"}, "formik": {"error": "There are validation errors", "name": "Full name is required", "DOB": "Date of birth is required", "DOBval": "The date of birth cannot be later than the current date", "contact": "Mobile phone is required", "contactVal": "Mobile phone is not valid", "email": "Email is required", "emailVal": "The professional email is not valid", "personalEmail": "Personal email is required", "personalEmailVal": "Personal email is not valid", "address1": "Address is required", "address2": "City is required", "zipCode": "Postal code is required", "zipCodeVal": "The postal code is not valid", "zipCodeCity": "Zip Code City is required", "idCard": "Citizen card is mandatory", "idCardVal": "Citizen card is mandatory", "expDate": "Expiration date is required", "expDateVal": "The expiration date cannot be less than the current date", "taxNo": "The tax identification number is mandatory", "taxNoVal": "The tax identification number is not valid", "socialSecurityNo": "Social security number is mandatory", "socialSecurityNoVal": "The social security number is not valid", "incomeHolderID": "Income holder is mandatory", "dependents": "The number of dependents is mandatory", "dependentsVal": "The number of dependents cannot be less than 0", "maritialStatus": "Marital status is required", "profile": "It is necessary to associate at least on profile", "username": "The username is mandatory", "password": "Password is mandatory for API users."}}