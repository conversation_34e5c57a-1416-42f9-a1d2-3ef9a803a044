﻿using System;
using System.Data;
using System.Threading.Tasks;
using RnD.BackEnd.Domain.DataModels;

namespace RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database.Base
{
    /// <summary>
    /// Base SQL repository interface
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public interface IBaseSqlRepository<T>
    {
        /// <summary>
        /// Adds the item.
        /// </summary>
        /// <param name="item">The item.</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// The added item.
        /// </returns>
        Task<ResponseModel<T>> AddAsync(T item, string userId);

        /// <summary>
        /// Updates the item.
        /// </summary>
        /// <param name="item">The item.</param>
        /// <param name="userId">The user identifier.</param>
        /// <param name="rowversion">The rowversion</param>
        /// <returns>
        /// The updated item
        /// </returns>
        Task<ResponseModel<T>> UpdateAsync(T item, string userId, string rowversion);

        /// <summary>
        /// Deletes the item.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// True if operation succeeded. False otherwise.
        /// </returns>
        Task<ResponseModel<bool>> DeleteAsync(Guid id, string userId);

        /// <summary>
        /// Gets the object by Id.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The object
        /// </returns>
        Task<ResponseModel<T>> GetAsync(Guid id);

        /// <summary>
        /// Init the repository with the database context from the Unit of Work
        /// </summary>
        /// <param name="connection">The connection</param>
        /// <param name="transaction">The transaction</param>
        void Initialize(IDbConnection connection, IDbTransaction transaction);
    }
}
