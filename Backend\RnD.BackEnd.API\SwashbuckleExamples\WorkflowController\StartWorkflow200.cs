﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Workflow;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// Start Workflow response example for 200
    /// </summary>
    public class StartWorkflow200 : IExamplesProvider<ApiOutput<WorkflowInstanceDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a Start Workflow 200 response.
        /// </returns>
        public ApiOutput<WorkflowInstanceDto> GetExamples()
        {
            return new ApiOutput<WorkflowInstanceDto>
            {
                Code = HttpStatusCode.OK,
                Value = new WorkflowInstanceDto
                {
                    Id = Guid.Parse("92b25840-1e4e-4f16-9a58-8dde276ba78f"),
                    DisplayName = "Vacation Approval",
                    SourceId = "Vacation/2023",
                    SourceUrl = "www.framework_vacation_2023.pt",
                    WorkflowStatus = "IN_PROGRESS",
                    SourceType = "VACATION",
                    WorkflowType = "MANUAL",
                    Purpose = "APPROVAL",
                    StateProcessingRequired = false,
                    SysCreateUserId = "<EMAIL>",
                    SysModifyDate = Convert.ToDateTime("2023-02-09T17:58:23.907+00:00")
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}