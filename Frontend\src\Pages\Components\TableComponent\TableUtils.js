/* eslint-disable react/no-unescaped-entities */
import {
  <PERSON>,
  Divider,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  Link,
} from "@mui/material";
import { useTheme } from '@emotion/react';
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import { useState } from "react";
import getTableUtilsCode from "./mocks/tableUtilsCode";
import getNameNormalize from "./mocks/nameNormalize";
import getDescendingComparator from "./mocks/descendingComparator";
import getComparatorCode from "./mocks/getComparator";
import getLike from "./mocks/like";
import getApplySort from "./mocks/applySort";
import getApplyPagination from "./mocks/applyPagination";
import getApplyFilters from "./mocks/applyFilters";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import { styled } from '@mui/system';

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh"
})

const linkStyle = {
  marginRight: 20,
  alignItems: "center",
  textDecoration: "none",
};

export default function TableUtils() {
  const theme = useTheme();
  const [code, setCode] = useState(getTableUtilsCode);
  const [nameNormalize, setNameNormalize] = useState(getNameNormalize);
  const [descendingComparator, setDescendingComparator] = useState(
    getDescendingComparator
  );
  const [getComparator, setGetComparator] = useState(getComparatorCode);
  const [like, setLike] = useState(getLike);
  const [applySort, setApplySort] = useState(getApplySort);
  const [applyPagination, setApplyPagination] = useState(getApplyPagination);
  const [applyFilters, setApplyFilters] = useState(getApplyFilters);

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box
        sx={{
          mt: 3,
          m: "auto",
          textAlign: "center"
        }}
      >
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h2>Table Utils</h2>
          <p>
            The following snippet contains several actions that we see in almost
            every table. You can copy this code snippet to your src folder:
          </p>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={code} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={code}
              onValueChange={(_code) => setCode(_code)}
              highlight={(_code) => highlight(_code, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h4>Name Normalize</h4>
          <p>
            This function allows us to "normalize" a text format.
            <br />
            Normalize()ing to NFD Unicode normal form decomposes combined
            graphemes into the combination of simple ones.For example, if we
            have a word like "Crème", the è of Crème ends up expressed as e + .
            <br />
            Name Normalize receives the following prop:
          </p>
          <ul>
            <li>
              <b>name</b> - The string we want the system to transform;
            </li>
          </ul>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={nameNormalize} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={nameNormalize}
              onValueChange={
                (_nameNormalize) =>
                  setNameNormalize(_nameNormalize)
              }
              highlight={
                (_nameNormalize) =>
                  highlight(_nameNormalize, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h4>Descending Comparator</h4>
          <p>Descending Comparator receives the following props:</p>
          <ul>
            <li>
              <b>a</b> - First Value;
            </li>
            <li>
              <b>b</b> - Second Value;
            </li>
            <li>
              <b>orderBy</b> - Rows being sorted by the values of one or more
              columns ;
            </li>
          </ul>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={descendingComparator} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={descendingComparator}
              onValueChange={
                (_descendingComparator) =>
                  setDescendingComparator(_descendingComparator)
              }
              highlight={
                (_descendingComparator) =>
                  highlight(_descendingComparator, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h4>Get Comparator</h4>
          <p>Descending Comparator receives the following props:</p>
          <ul>
            <li>
              <b>order</b> - Sort Data by Ascending or Descending order.;
            </li>
            <li>
              <b>orderBy</b> - Rows being sorted by the values of one or more
              columns ;
            </li>
          </ul>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={getComparator} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={getComparator}
              onValueChange={
                (_getComparator) =>
                  setGetComparator(_getComparator)
              }
              highlight={
                (_getComparator) =>
                  highlight(_getComparator, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h4>Like</h4>
          <p>
            This function allows us to check whether the data entered in the
            search field matches any of the data present in tables, lists, etc.
            Like receives the following props:
          </p>
          <ul>
            <li>
              <b>word</b>
            </li>
            <li>
              <b>searchInput</b>
            </li>
          </ul>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={like} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={like}
              onValueChange={(_like) => setLike(_like)}
              highlight={(_like) => highlight(_like, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h4>Apply Sort</h4>
          <p>
            This function allows us to sort the information, for example,
            alphabetically, by creation date, state, etc. Apply Sort receives
            the following props:
          </p>
          <ul>
            <li>
              <b>array</b>
            </li>
            <li>
              <b>orderBy</b>
            </li>
            <li>
              <b>orderDirection</b>
            </li>
          </ul>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={applySort} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={applySort}
              onValueChange={(_applySort) => setApplySort(_applySort)}
              highlight={(_applySort) => highlight(_applySort, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h4>Apply Pagination</h4>
          <p>
            Allows us to define the number of items per page, exceeding this
            limit, the remaining items move to the next page. Apply Pagination
            receives the following props:
          </p>
          <ul>
            <li>
              <b>array</b>
            </li>
            <li>
              <b>page</b>
            </li>
            <li>
              <b>limit</b>
            </li>
          </ul>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={applyPagination} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={applyPagination}
              onValueChange={
                (_applyPagination) =>
                  setApplyPagination(_applyPagination)
              }
              highlight={
                (_applyPagination) =>
                  highlight(_applyPagination, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h4>Apply Filters</h4>
          <p>Apply Filters receives the following props:</p>
          <ul>
            <li>
              <b>headCells</b>
            </li>
            <li>
              <b>array</b>
            </li>
            <li>
              <b>query</b>
            </li>
            <li>
              <b>filters</b>
            </li>
          </ul>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={applyFilters} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={applyFilters}
              onValueChange={(_applyFilters) => setApplyFilters(_applyFilters)}
              highlight={
                (_applyFilters) =>
                  highlight(_applyFilters, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box sx={{ textAlign: "center", mt: 5 }}>
          See also:
          <br />
          <Link
            style={linkStyle}
            href="/Components/FunctionalTableExample/"
          >
            Functional Table Example
          </Link>
          <Link
            style={linkStyle}
            href="/Components/TableLoading/"
          >
            Table Loading
          </Link>
          <Link
            style={linkStyle}
            href="/Components/TableActions/"
          >
            Table Actions
          </Link>
          <Link
            style={linkStyle}
            href="/Components/TableHeader/"
          >
            Table Header
          </Link>
        </Box>
      </Box>
      <ChangeLogEntry
        version="V1.0.0"
        changeLog={ChangeLog()}
      />
    </Box>
  );
}
const ChangeLog = () => {
  const theme = useTheme();
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content: "Imported Table Utils Component.",
      className: chips.green,
    },
  ];

  return changeLog;
};
