﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Workflow;
using RnD.BackEnd.API.Model.Workflow.Enums;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// Update tasks response example for 200
    /// </summary>
    public class UpdateTasks200 : IExamplesProvider<ApiOutput<List<UpdateTaskOutputDto>>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 for a list response.
        /// </returns>
        public ApiOutput<List<UpdateTaskOutputDto>> GetExamples()
        {
            return new ApiOutput<List<UpdateTaskOutputDto>>
            {
                Code = HttpStatusCode.OK,
                Value = new List<UpdateTaskOutputDto>
                {
                    new UpdateTaskOutputDto()
                    {
                        SourceType = "DOCUMENT",
                        TaskId = Guid.NewGuid(),
                        WorkflowInstanceId = Guid.NewGuid(),
                        WorkflowPurpose = WorkflowPurpose.APPROVAL.ToString(),
                        WorkflowStatus = WorkflowStatus.APPROVED.ToString()
                    },
                    new UpdateTaskOutputDto()
                    {
                        SourceType = "VACATION",
                        TaskId = Guid.NewGuid(),
                        WorkflowInstanceId = Guid.NewGuid(),
                        WorkflowPurpose = WorkflowPurpose.APPROVAL.ToString(),
                        WorkflowStatus = WorkflowStatus.REJECTED.ToString()
                    }
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}

