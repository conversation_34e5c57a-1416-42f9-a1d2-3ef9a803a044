﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace RnD.BackEnd.Domain.Utilities
{
    /// <summary>
    /// Dictionary helper class
    /// </summary>
    public static class DictionaryHelper
    {
        /// <summary>
        /// Converts to dictionary.
        /// </summary>
        /// <param name="source">The source.</param>
        /// <returns>
        /// The dictionary.
        /// </returns>
        public static IDictionary<string, object> ToDictionary(this object source)
        {
            if (source == null)
            {
                throw new ArgumentNullException(nameof(source), "Unable to convert object to a dictionary. The source object is null.");
            }

            return source.GetType().GetProperties().ToDictionary
            (
                propInfo => propInfo.Name,
                propInfo => propInfo.GetValue(source, null)
            );
        }
    }
}