﻿using AutoMapper;
using RnD.BackEnd.API.Model.Workflow;
using RnD.BackEnd.Domain.Entities.Workflow;

namespace RnD.BackEnd.API.MappingProfiles
{
    /// <summary>
    /// Workflow mapping profile
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class WorkflowProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="WorkflowProfile"/> class.
        /// </summary>
        public WorkflowProfile()
        {
            CreateMap<WorkflowDefinition, WorkflowDefinitionDto>();

            CreateMap<WorkflowInstance, WorkflowInstanceDto>();
            CreateMap<WorkflowInstanceStep, WorkflowInstanceStepDto>();
            CreateMap<WorkflowInstancesList, WorkflowInstancesListDto>();
            CreateMap<WorkflowInstanceBySourceIdsDto, WorkflowInstanceBySourceIds>();

            CreateMap<WorkflowTask, WorkflowTaskDto>();
            CreateMap<WorkflowTask, WorkflowInstanceTaskDto>();
            CreateMap<WorkflowTasksList, WorkflowTasksListDto>();
            CreateMap<UpdateTaskOutput, UpdateTaskOutputDto>();
            CreateMap<UpdateTaskDto, UpdateTask>();
            CreateMap<ReassignTaskDto, ReassignTask>();

            CreateMap<CustomStepDto, CustomStep>();
            CreateMap<StartWorkflowDto, StartWorkflow>();
            CreateMap<StartAutomaticWorkflowDto, StartAutomaticWorkflow>();

            CreateMap<WorkflowEmailDto, WorkflowEmail>();
        }
    }
}