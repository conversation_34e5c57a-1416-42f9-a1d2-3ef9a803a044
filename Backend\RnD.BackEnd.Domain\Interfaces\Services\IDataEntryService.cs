﻿using System.Threading.Tasks;
using RnD.BackEnd.Domain.Entities.DataEntry;
using RnD.BackEnd.Domain.Entities.Generic;

namespace RnD.BackEnd.Domain.Interfaces.Services
{
    /// <summary>
    /// Interface for Data Entry connector service example.
    /// </summary>
    public interface IDataEntryService
    {
        /// <summary>
        /// Gets the data entry entities.
        /// </summary>
        /// <returns>
        /// The list of entities.
        /// </returns>
        Task<ServiceOutput<DataEntryEntitiesList>> GetDataEntryEntities();
    }
}
