﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Data;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RnD.BackEnd.API.Model;
using RnD.BackEnd.API.Models;

namespace RnD.BackEnd.API.Controllers
{
    [Route("api/permissions")]
    [ApiController]
    public class PermissionsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public PermissionsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/permissions
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Permission>>> GetPermissions()
        {
            return await _context.Permission.ToListAsync();
        }

        // POST: api/permissions
        [HttpPost]
        public async Task<ActionResult<Permission>> CreatePermission(Permission permission)
        {
            _context.Permission.Add(permission);
            await _context.SaveChangesAsync();
            return CreatedAtAction(nameof(GetPermissions), new { id = permission.Id }, permission);
        }
    }
}
