using Azure.Storage.Blobs;
using ExcelDataReader;
using Microsoft.Extensions.Configuration;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Threading.Tasks;
using System;
using System.Linq;
using Microsoft.Extensions.Logging;
using RnD.BackEnd.API.Services;
using Microsoft.EntityFrameworkCore;

public class ExcelAzureReaderService
{
    private readonly string _connectionString;
    private readonly string _containerName;
    private readonly ApplicationDbContext _context;
    private readonly LogProcessamentoService _logService;
    private readonly ILogger<ExcelAzureReaderService> _logger;

    public ExcelAzureReaderService(
        IConfiguration configuration,
        ApplicationDbContext context,
        LogProcessamentoService logService,
        ILogger<ExcelAzureReaderService> logger)
    {
        _connectionString = configuration["AzureStorage:ConnectionString"];
        _containerName = configuration["AzureStorage:ContainerName"];
        _context = context;
        _logService = logService;
        _logger = logger;

        System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
    }

    private decimal ParseDecimalValue(string value)
    {
        if (string.IsNullOrWhiteSpace(value)) return 0;

        // Remove espaços em branco
        value = value.Trim();

        // Substituir ponto por vírgula, caso esteja no formato americano
        value = value.Replace('.', ',');

        // Tenta com a cultura pt-BR (vírgula como separador decimal)
        if (decimal.TryParse(value, NumberStyles.Any, CultureInfo.GetCultureInfo("pt-BR"), out decimal result))
        {
            // Arredonda o valor para 2 casas decimais
            result = Math.Round(result, 2);
            return result;
        }

        // Tenta com cultura invariante (ponto como separador decimal)
        if (decimal.TryParse(value, NumberStyles.Any, CultureInfo.InvariantCulture, out result))
        {
            // Arredonda o valor para 2 casas decimais
            result = Math.Round(result, 2);
            return result;
        }

        return 0;
    }

    public async Task<List<Consumo>> ImportarConsumosFromAzureExcelAsync()
    {
        var blobClient = new BlobContainerClient(_connectionString, _containerName);
        await blobClient.CreateIfNotExistsAsync();

        var blobs = blobClient.GetBlobs()
            .OrderByDescending(b => b.Properties.LastModified)
            .ToList();

        if (!blobs.Any()) return new List<Consumo>();

        var consumosProcessados = new List<Consumo>();

        foreach (var blobItem in blobs)
        {
            string blobName = blobItem.Name;

            // Verifica se o arquivo já está na DB
            bool existeNaDB = await _context.ArquivosProcessados.AnyAsync(a => a.Nome == blobName);
            if (existeNaDB)
            {
                await _logService.RegistrarLog(0, $"Arquivo {blobName} já foi processado antes. Ignorando.", TipoLog.Aviso);
                continue; // Pula para o próximo arquivo
            }

            // regista o novo arquivo na DB antes de processar
            var arquivoProcessado = new ArquivoProcessado
            {
                Nome = blobName,
                BlobUrl = blobClient.GetBlobClient(blobName).Uri.ToString(),
                DataUpload = blobItem.Properties.LastModified?.UtcDateTime ?? DateTime.UtcNow,
                Processado = false,
                RegistrosProcessados = 0,
                RegistrosComErro = 0
            };

            _context.ArquivosProcessados.Add(arquivoProcessado);
            await _context.SaveChangesAsync();
            int fileId = arquivoProcessado.Id;

            await _logService.RegistrarLog(fileId, $"Iniciando processamento do arquivo {blobName}", TipoLog.Info);

            try
            {
                var blob = blobClient.GetBlobClient(blobName);
                using var stream = new MemoryStream();
                await blob.DownloadToAsync(stream);
                stream.Position = 0;

                using var reader = ExcelReaderFactory.CreateReader(stream);
                var dataSet = reader.AsDataSet(new ExcelDataSetConfiguration()
                {
                    ConfigureDataTable = (_) => new ExcelDataTableConfiguration()
                    {
                        UseHeaderRow = true
                    }
                });
                var sheet = dataSet.Tables[0];

                await _logService.RegistrarLog(fileId, $"Planilha carregada com {sheet.Rows.Count} linhas", TipoLog.Info);

                var consumos = new List<Consumo>();
                int sucessos = 0, erros = 0;
                var consumoClienteList = new List<Consumo_Cliente>();

                for (int i = 0; i < sheet.Rows.Count; i++)
                {
                    try
                    {
                        var row = sheet.Rows[i];
                        var clienteNome = row[3]?.ToString();
                        
                        if (string.IsNullOrWhiteSpace(clienteNome))
                        {
                            await _logService.RegistrarLog(fileId, $"Nome do cliente vazio na linha {i}", TipoLog.Aviso);
                            erros++;
                            continue;
                        }
                        
                        // Tenta encontrar o cliente pelo nome
                        var cliente = _context.Cliente.FirstOrDefault(c => c.Name == clienteNome);

                        // Se não encontrou, cria um novo cliente
                        if (cliente == null)
                        {
                            await _logService.RegistrarLog(fileId, $"Cliente não encontrado na linha {i}: {clienteNome}. Criando novo cliente...", TipoLog.Aviso);
                            
                            cliente = new Cliente
                            {
                                Name = clienteNome,
                                Contact = "0" // Contato padrão
                            };
                            
                            _context.Cliente.Add(cliente);
                            await _context.SaveChangesAsync();
                            
                            await _logService.RegistrarLog(fileId, $"Novo cliente criado: {cliente.Name} (ID: {cliente.ID})", TipoLog.Info);
                        }

                        var consumo = new Consumo
                        {
                            ClienteID = cliente.ID,
                            TipoServico = row[1]?.ToString() ?? "N/A",
                            Moeda = row[7]?.ToString() ?? "N/A",
                            DataInicio = DateTime.TryParse(row[5]?.ToString(), out var di) ? di : DateTime.MinValue,
                            DataFim = DateTime.TryParse(row[6]?.ToString(), out var df) ? df : DateTime.MinValue,
                            CountryListTotal = ParseDecimalValue(row[8]?.ToString()),
                            CountryResellerTotal = ParseDecimalValue(row[9]?.ToString()),
                            CountryCustomerTotal = ParseDecimalValue(row[10]?.ToString()),
                            Regiao = row[11]?.ToString() ?? "N/A",
                        };

                        consumos.Add(consumo);
                        sucessos++;
                    }
                    catch (Exception ex)
                    {
                        await _logService.RegistrarLog(fileId, $"Erro ao processar linha {i}: {ex.Message}", TipoLog.Erro);
                        erros++;
                    }
                }

                if (consumos.Any())
                {
                    await _logService.RegistrarLog(fileId, $"Salvando {consumos.Count} consumos no banco de dados", TipoLog.Info);
                    _context.Consumo.AddRange(consumos);
                    await _context.SaveChangesAsync();

                    // Agora cria os vínculos na tabela Consumo_Cliente
                    foreach (var consumo in consumos)
                    {
                        consumoClienteList.Add(new Consumo_Cliente
                        {
                            ClienteID = consumo.ClienteID,
                            ConsumoID = consumo.ID
                        });
                    }

                    _context.Consumo_Cliente.AddRange(consumoClienteList);
                    await _context.SaveChangesAsync(); // Salva os vínculos

                    await _logService.RegistrarLog(fileId, $"Processamento concluído. Total: {sucessos} registros processados, {erros} com erro", TipoLog.Info);
                }
                else
                {
                    await _logService.RegistrarLog(fileId, "Nenhum consumo processado do arquivo", TipoLog.Aviso);
                }

                // Marcar o arquivo como processado
                arquivoProcessado.Processado = true;
                arquivoProcessado.RegistrosProcessados = sucessos;
                arquivoProcessado.RegistrosComErro = erros;
                await _context.SaveChangesAsync();

                consumosProcessados.AddRange(consumos);
            }
            catch (Exception ex)
            {
                await _logService.RegistrarLog(fileId, $"Erro crítico no processamento: {ex.Message}", TipoLog.Erro);
                arquivoProcessado.Processado = true;
                arquivoProcessado.RegistrosComErro = 1;
                await _context.SaveChangesAsync();
            }
        }

        return consumosProcessados;
    }
}