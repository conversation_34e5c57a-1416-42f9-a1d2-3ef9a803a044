﻿using System;
using System.Collections.Generic;

namespace RnD.BackEnd.Domain.Entities.Workflow
{
    public class WorkflowInstance
    {
        public Guid? Id { get; set; }
        public Guid WorkflowId { get; set; }
        public string DisplayName { get; set; }
        public string SourceId { get; set; }
        public string SourceUrl { get; set; }
        public string WorkflowStatus { get; set; }
        public string SourceType { get; set; }
        public string WorkflowType { get; set; }
        public string Purpose { get; set; }
        public bool? StateProcessingRequired { get; set; }
        public string ActiveStepName { get; set; }
        public IList<WorkflowInstanceStep> Steps { get; set; }
        public string SysCreateUserId { get; set; }
        public DateTimeOffset SysModifyDate { get; set; }
        public int TotalRows { get; set; }
    }
}