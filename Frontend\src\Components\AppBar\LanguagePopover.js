import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Box,
    IconButton,
    ListItemIcon,
    ListItemText,
    MenuItem,
    Popover,
    Typography
} from '@mui/material';

const LanguagePopover = () => {
    const anchorRef = useRef(null);
    const { i18n, t } = useTranslation();
    const [open, setOpen] = useState(false);

    //Handles Language options
    const availableLanguages = process.env.REACT_APP_AVAILABLE_LANGUAGES.split(';');

    const languageOptions = {
        en: {
            icon: '/static/icons/uk_flag.svg',
            label: t('common:common.en'),
            available: availableLanguages.some((language) => language === 'en')
        },
        pt: {
            icon: '/static/icons/pt_flag.svg',
            label: t('common:common.pt'),
            available: availableLanguages.some((language) => language === 'pt')
        }
    };

    const handleOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    const handleChangeLanguage = (language) => {
        i18n.changeLanguage(language);
        localStorage.setItem('preferedLanguage', language);
        setOpen(false);
    };

    const selectedOption = languageOptions[i18n.language];

    return (
        <>
            {/*Flag of selected language 36px 36px*/}
            <IconButton
                onClick={handleOpen}
                ref={anchorRef}
                sx={{ height: "36px", width: "36px" }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        '& img': {
                            width: '100%',
                        }
                    }}
                >
                    <img
                        alt={selectedOption.label}
                        src={selectedOption.icon}
                    />
                </Box>
            </IconButton>
            {/* Menu */}
            <Popover
                anchorEl={anchorRef.current}
                anchorOrigin={{
                    horizontal: 'center',
                    vertical: 'bottom'
                }}
                keepMounted
                onClose={handleClose}
                open={open}
                PaperProps={{
                    sx: { width: 240 }
                }}
            >
                {Object.keys(languageOptions).map((language, index) => (
                    <React.Fragment
                        key={index.toString()}
                    >
                        {
                            languageOptions[language].available &&
                            <MenuItem
                                onClick={() => handleChangeLanguage(language)}
                            >
                                {/* Populates the menu with the Flag */}
                                <ListItemIcon>
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            height: 20,
                                            width: 20,
                                            '& img': {
                                                width: '100%'
                                            }
                                        }}
                                    >
                                        <img
                                            alt={languageOptions[language].label}
                                            src={languageOptions[language].icon}
                                        />
                                    </Box>
                                </ListItemIcon>
                                {/*and the respective name of the country */}
                                <ListItemText
                                    primary={(
                                        <Typography
                                            color="textPrimary"
                                            variant="subtitle2"
                                        >
                                            {languageOptions[language].label}
                                        </Typography>
                                    )}
                                />
                            </MenuItem>
                        }
                    </React.Fragment>
                ))}
            </Popover>
        </>
    );
};

export default LanguagePopover;
