﻿namespace RnD.BackEnd.Domain.Interfaces.Infrastructure.Managers
{
    /// <summary>
    /// Cache manager interface
    /// </summary>
    public interface ICacheManager
    {
        /// <summary>
        /// Gets a cached object by it's key.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="cacheKey">The cache key.</param>
        /// <returns>
        /// The cached object.
        /// </returns>
        T Get<T>(string cacheKey);

        /// <summary>
        /// Adds or Updates an object into cache, with a given cache key.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="cacheKey">The cache key.</param>
        /// <param name="value">The value.</param>
        void Set<T>(string cacheKey, T value);

        /// <summary>
        /// Adds or Updates an object into cache, with a given cache key.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="cacheKey">The cache key.</param>
        /// <param name="value">The value.</param>
        /// <param name="expirationHours">The expiration hours.</param>
        void Set<T>(string cacheKey, T value, int expirationHours);

        /// <summary>
        /// Removes the cached object by it's cache key.
        /// </summary>
        /// <param name="cacheKey">The cache key.</param>
        void Remove(string cacheKey);

        /// <summary>
        /// Resets this instance.
        /// </summary>
        void Reset();
    }
}
