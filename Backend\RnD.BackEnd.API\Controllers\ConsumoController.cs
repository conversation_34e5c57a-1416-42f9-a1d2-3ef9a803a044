﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Azure.Storage.Blobs;

namespace RnD.BackEnd.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ConsumoController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ExcelAzureReaderService _excelReader;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ConsumoController> _logger;
        private readonly LogProcessamentoService _logService;

        public ConsumoController(
            ApplicationDbContext context, 
            ExcelAzureReaderService excelReader,
            IConfiguration configuration = null,
            ILogger<ConsumoController> logger = null,
            LogProcessamentoService logService = null)
        {
            _context = context;
            _excelReader = excelReader;
            _configuration = configuration;
            _logger = logger;
            _logService = logService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Consumo>>> GetConsumo()
        {
            return await _context.Consumo.ToListAsync();
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Consumo>> GetConsumo(int id)
        {
            var consumo = await _context.Consumo.FindAsync(id);
            if (consumo == null)
            {
                return NotFound();
            }
            return consumo;
        }

        [HttpPost]
        public async Task<ActionResult<Consumo>> PostConsumo(Consumo consumo)
        {
            _context.Consumo.Add(consumo);
            await _context.SaveChangesAsync();
            return CreatedAtAction(nameof(GetConsumo), new { id = consumo.ID }, consumo);
        }

        [HttpGet("FromExcel")]
        public async Task<ActionResult<IEnumerable<Consumo>>> GetConsumoFromExcel()
        {
            var consumos = await _excelReader.ImportarConsumosFromAzureExcelAsync();
            return Ok(consumos);
        }

        /// <summary>
        /// Faz upload de um arquivo Excel para processamento no container Reseller
        /// </summary>
        /// <param name="file">Arquivo Excel a ser processado</param>
        /// <returns>Nome do arquivo no Azure Storage</returns>
        [HttpPost("upload")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> UploadExcel(IFormFile file)
        {
            try
            {
                // Verificar se as dependências necessárias foram injetadas
                if (_configuration == null || _logger == null || _logService == null)
                {
                    return BadRequest("Serviço não configurado corretamente para upload de arquivos.");
                }
                
                _logger.LogInformation($"Iniciando upload do arquivo Excel Reseller: {file?.FileName}");
                
                if (file == null || file.Length == 0)
                    return BadRequest("Arquivo não fornecido");

                // Verificar se é um arquivo Excel
                string contentType = file.ContentType;
                if (!contentType.Contains("excel") && !contentType.Contains("spreadsheetml"))
                {
                    if (!file.FileName.EndsWith(".xlsx") && !file.FileName.EndsWith(".xls"))
                    {
                        return BadRequest("O arquivo deve ser um Excel (.xlsx ou .xls)");
                    }
                }

                var connectionString = _configuration["AzureStorage:ConnectionString"];
                var containerName = _configuration["AzureStorage:ContainerName"];
                
                if (string.IsNullOrEmpty(connectionString))
                {
                    return BadRequest("Connection string do Azure Storage não configurada.");
                }
                
                var blobServiceClient = new BlobServiceClient(connectionString);
                var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
                await containerClient.CreateIfNotExistsAsync();

                // Gera um nome único para o arquivo
                string fileName = $"{Guid.NewGuid()}-{file.FileName}";
                var blobClient = containerClient.GetBlobClient(fileName);

                using (var stream = file.OpenReadStream())
                {
                    await blobClient.UploadAsync(stream, true);
                }

                // Registra o arquivo no banco de dados para processamento posterior
                var arquivoProcessado = new ArquivoProcessado
                {
                    Nome = fileName,
                    BlobUrl = blobClient.Uri.ToString(),
                    DataUpload = DateTime.Now,
                    Processado = false,
                    RegistrosProcessados = 0,
                    RegistrosComErro = 0
                };

                _context.ArquivosProcessados.Add(arquivoProcessado);
                await _context.SaveChangesAsync();
                int fileId = arquivoProcessado.Id;

                await _logService.RegistrarLog(fileId, $"Arquivo Excel Reseller {fileName} carregado com sucesso", TipoLog.Info);
                
                // Inicia processamento imediato (opcional)
                // Você pode decidir se quer processar imediatamente ou deixar o processamento periódico fazer isso
                // Task.Run(async () => await _excelReader.ImportarConsumosFromAzureExcelAsync());
                
                return Ok(new { 
                    message = $"Arquivo {fileName} carregado com sucesso",
                    fileName = fileName
                });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Erro ao fazer upload do arquivo Excel Reseller");
                return BadRequest(new { 
                    message = "Erro ao fazer upload do arquivo Excel Reseller",
                    error = ex.Message
                });
            }
        }
    }
}
