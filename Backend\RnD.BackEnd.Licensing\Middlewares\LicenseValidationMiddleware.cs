﻿using System;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using RnD.BackEnd.Licensing.Entities;
using RnD.BackEnd.Licensing.Interfaces;

namespace RnD.BackEnd.Licensing.Middlewares
{
    /// <summary>
    /// Validation Middleware
    /// </summary>
    public class LicenseValidationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILicenseValidationService _licenseValidationService;

        /// <summary>
        /// Initializes a new instance of the <see cref="LicenseValidationMiddleware" /> class.
        /// </summary>
        /// <param name="licenseValidationService">The license validation service.</param>
        /// <param name="next">The next.</param>
        public LicenseValidationMiddleware(ILicenseValidationService licenseValidationService, RequestDelegate next)
        {
            _next = next;
            _licenseValidationService = licenseValidationService;
        }

        /// <summary>
        /// Invokes the specified context.
        /// </summary>
        /// <param name="context">The context.</param>
        /// <exception cref="System.ArgumentNullException">context</exception>
        public async Task Invoke(HttpContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }

            if (await _licenseValidationService.ValidateLicenseAsync(context.Request.Host.Value))
            {
                await _next(context);
            }
            else
            {
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                context.Response.ContentType = "application/json";
                await context.Response.WriteAsync(JsonConvert.SerializeObject(new ServiceOutput()
                {
                    Description = "The current License is invalid.",
                    Code = HttpStatusCode.Unauthorized
                }));
            }
        }
    }
}
