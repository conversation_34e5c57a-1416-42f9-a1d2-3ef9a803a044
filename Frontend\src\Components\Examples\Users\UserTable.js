import { useState } from "react";
import { Link as RouterLink } from "react-router-dom";
import PropTypes from "prop-types";
import {
  Avatar,
  Box,
  Card,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TablePagination,
  TableRow,
  TextField,
  Link,
  Tooltip,
  Grid,
  IconButton,
  Badge,
  Button,
} from "@mui/material";
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Add as AddIcon,
} from "@mui/icons-material";
import Scrollbar from "../../Scrollbar";
import TableHeader from "../../TableUtils/TableHeader";
import TableLoading from "../../TableUtils/TableLoading";
import {
  applyPagination,
  applySort,
  applyFilters,
} from "../../TableUtils/TableUtils";
import { useTranslation } from "react-i18next";
import CustomFilters from "../../CustomFilters";
import useFilters from "../../../Context/Hooks/useFilters";

const UserTable = (props) => {
  const {
    userList,
    isLoading,
    customFiltersList,
    onSelectedCustomFilters,
    ...other
  } = props;

  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const [selectedRole] = useState();
  const [filteredUserList] = useState();
  const [query, setQuery] = useState("");

  const [orderBy, setOrderBy] = useState("name");
  const [order, setOrder] = useState("asc");
  const { t } = useTranslation();

  const headerCells = [
    {
      id: "name",
      label: t("common:tableHeaders.name"),
      sort: true,
      filter: true,
    },
    {
      id: "username",
      label: t("common:tableHeaders.username"),
      sort: true,
      filter: true,
    },
    {
      id: "permissions",
      label: t("common:tableHeaders.permissions"),
      sort: true,
      filter: false,
    },
  ];

  const handleQueryChange = (event) => {
    setQuery(event.target.value);
    setPage(0);
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleLimitChange = (event) => {
    setLimit(parseInt(event.target.value, 10));
  };

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const filtereduserlist = applyFilters(
    headerCells,
    selectedRole ? filteredUserList : userList,
    query
  );
  const sorteduserlist = applySort(filtereduserlist, orderBy, order);
  const paginateduserlist = applyPagination(sorteduserlist, page, limit);

  //---------------------Filters---------------------------------
  const { filters, saveFilters } = useFilters();
  const [customFiltersToggled, setCustomFiltersToggled] = useState(
    filters.PROFILE && filters.PROFILE.length > 0
  );

  //RESET
  const handleResetCustomFilters = () => {
    if (filters.PROFILE.length > 0) {
      saveFilters("PROFILE", []);
      onSelectedCustomFilters([]);
    }
  };
  //ADD
  const handleAddCustomFilter = (value) => {
    const _selectedCustomFilters = [...filters.PROFILE];
    const matchIndex = _selectedCustomFilters.findIndex(
      (x) => x.elementID === value.elementID
    );
    /*
         @TODO: Encadeamento
    */
    if (matchIndex === 123456 && matchIndex >= 0) {
      if (Array.isArray(_selectedCustomFilters[matchIndex].values)) {
        _selectedCustomFilters[matchIndex].values.push("&");
        _selectedCustomFilters[matchIndex].values.concat(value.values);
      } else {
        _selectedCustomFilters[
          matchIndex
        ].values += ` <b> && </b> ${value.values}`;
      }
    } else {
      _selectedCustomFilters.push(value);
    }
    saveFilters("PROFILE", _selectedCustomFilters);
    onSelectedCustomFilters(_selectedCustomFilters);
  };
  //DELETE
  const handleDeleteCustomFilter = (value) => {
    const _customFilters = [...filters.PROFILE];
    _customFilters.splice(value, 1);
    saveFilters("PROFILE", _customFilters);
    onSelectedCustomFilters(_customFilters);
  };

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Card {...other}>
        <Box
          sx={{
            alignItems: "center",
            display: "flex",
            flexWrap: "wrap",
            m: 2,
            height: "88px",
          }}
        >
          <Box>
            <Tooltip title={t("common:common.view")}>
              <IconButton
                color="primary"
                onClick={() => setCustomFiltersToggled(!customFiltersToggled)}
              >
                <Badge
                  color="primary"
                  sx={{
                    ".MuiBadge-badge": {
                      padding: "0 4px !important",
                      minWidth: "17px !important",
                      height: "17px !important",
                      fontSize: "0.6rem !important",
                      right: "-3px",
                    },
                  }}
                >
                  <FilterListIcon />
                </Badge>
              </IconButton>
            </Tooltip>
          </Box>
          {/* Search TextField */}
          <Box
            xs={12}
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              padding: "6px 16px",
            }}
          >
            <TextField
              sx={{
                borderRadius: "4px",
                boxSizing: "border-box",
                gap: "8px",
                width: "458px",
                height: "56px",
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" />
                  </InputAdornment>
                ),
              }}
              onChange={handleQueryChange}
              placeholder={t("common:search.searchUser")}
              value={query}
              variant="outlined"
            />
          </Box>
          {/* Button: Add User */}
          <Button
            startIcon={<AddIcon fontSize="small" />}
            sx={{ ml: "auto" }}
            component={RouterLink}
            to="user/new"
            variant="contained"
          >
            {t("common:buttons.user")}
          </Button>
        </Box>
        <Grid
          container
          sx={{
            pb: customFiltersToggled ? 2 : 0,
          }}
        >
          <Grid
            item
            xs={12}
          >
            <CustomFilters
              customFiltersToggled={customFiltersToggled}
              context="PROFILE"
              customFiltersList={customFiltersList}
              selectedCustomFilters={
                filters.PROFILE && filters.PROFILE.length > 0
                  ? filters.PROFILE
                  : []
              }
              resetCustomFilters={handleResetCustomFilters}
              addCustomFilter={handleAddCustomFilter}
              deleteCustomFilter={handleDeleteCustomFilter}
            />
          </Grid>
        </Grid>
        <Scrollbar>
          <Box sx={{ p: 2, pt: 0, minWidth: 700 }}>
            <Table stickyHeader>
              <TableHeader
                hasCheckbox={false}
                headCells={headerCells}
                onSortClick={handleRequestSort}
                orderBy={orderBy}
                order={order}
              />
              <TableBody>
                <TableLoading
                  isLoading={isLoading}
                  headCells={headerCells}
                  numRows={limit}
                />
                {paginateduserlist.map((user) => {
                  return (
                    <TableRow
                      hover
                      key={user.username}
                      sx={{ height: "77px" }}
                    >
                      <TableCell>
                        <Box sx={{ display: "flex" }}>
                          <Avatar
                            mx={2}
                            key={`avatar-${user.username}`}
                            alt={user.name}
                          />

                          <Link
                            color="inherit"
                            component={RouterLink}
                            to={`user/${user.username}`}
                            sx={{
                              color: "primary.main",
                              textDecoration: "none",
                              fontStyle: "normal",
                              fontWeight: 700,
                              fontSize: "14px",
                              lineHeight: "20px",
                              alignSelf: "center",
                              marginLeft: 1,
                            }}
                          >
                            {user.name}
                          </Link>
                        </Box>
                      </TableCell>
                      <TableCell>{user.username}</TableCell>
                      <TableCell align="left">
                        {user.userPermissions?.length
                          ? user.userPermissions.map((p) => p.permission?.name).join(", ")
                          : "Sem permissões"}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </Box>
        </Scrollbar>
        <TablePagination
          component="div"
          count={filtereduserlist.length}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleLimitChange}
          page={page}
          rowsPerPage={limit}
          rowsPerPageOptions={[5, 10, 25]}
          showFirstButton
          showLastButton
        />
      </Card>
    </Box>
  );
};

UserTable.propTypes = {
  userList: PropTypes.array.isRequired,
  photoMap: PropTypes.object,
  roles: PropTypes.array,
  handleGetPhotos: PropTypes.func,
  isLoading: PropTypes.bool,
  customFiltersList: PropTypes.array,
  onSelectedCustomFilters: PropTypes.func,
};

export default UserTable;
