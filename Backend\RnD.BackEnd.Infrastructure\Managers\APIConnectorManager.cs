﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json;
using RestSharp;
using RestSharp.Serializers.NewtonsoftJson;
using RnD.BackEnd.Domain.BusinessMessages;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Extensions;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Managers;

namespace RnD.BackEnd.Infrastructure.Managers
{
    /// <summary>
    /// API Connector manager class.
    /// </summary>
    public class ApiConnectorManager : IApiConnectorManager
    {
        private RestClient _client;
        private bool disposed;

        /// <summary>
        /// Initializes a new instance of the <see cref="ApiConnectorManager" /> class.
        /// </summary>
        public ApiConnectorManager()
        {
            var options = new RestClientOptions();
            _client = new RestClient(options, configureSerialization: s => s.UseNewtonsoftJson());
        }

        /// <summary>
        /// Calls a DELETE and returns a ServiceOutput with a typed object.
        /// </summary>
        /// <typeparam name="T">The typed object returned with ServiceOutput.</typeparam>
        /// <param name="baseUrl">The base URL.</param>
        /// <param name="endpoint">The endpoint.</param>
        /// <param name="token">The token.</param>
        /// <param name="request">The request.</param>
        /// <param name="timeout">The timeout.</param>
        /// <returns>
        /// ServiceOutput with a typed object.
        /// </returns>
        public async Task<ServiceOutput<T>> DeleteAsync<T>(string baseUrl, string endpoint, string token, object request, int? timeout = null)
        {
            RestRequest apiRequest = InitializeRequest(baseUrl, endpoint, token, timeout, Method.Delete);

            apiRequest.AddJsonBody(request);

            return HandleResponse(await _client.ExecuteAsync<ServiceOutput<T>>(apiRequest));
        }

        /// <summary>
        /// Calls a GET and returns a ServiceOutput with a typed object.
        /// </summary>
        /// <typeparam name="T">The typed object returned with ServiceOutput.</typeparam>
        /// <param name="baseUrl">The base URL.</param>
        /// <param name="endpoint">The endpoint.</param>
        /// <param name="token">The token.</param>
        /// <param name="queryStringParameters">The query string parameters.</param>
        /// <param name="timeout">The timeout.</param>
        /// <returns>
        /// ServiceOutput with a typed object.
        /// </returns>
        public async Task<ServiceOutput<T>> GetAsync<T>(
            string baseUrl,
            string endpoint,
            string token = null,
            Dictionary<string, string> queryStringParameters = null,
            int? timeout = null)
        {
            RestRequest apiRequest = InitializeRequest(baseUrl, endpoint, token, timeout);

            if (queryStringParameters != null && queryStringParameters.Count > 0)
            {
                foreach (KeyValuePair<string, string> queryParameter in queryStringParameters)
                {
                    apiRequest.AddQueryParameter(queryParameter.Key, queryParameter.Value);
                }
            }

            return HandleResponse(await _client.ExecuteGetAsync<ServiceOutput<T>>(apiRequest));
        }

        /// <summary>
        /// Calls a POST and returns a ServiceOutput with a typed object.
        /// </summary>
        /// <typeparam name="T">The typed object returned with ServiceOutput.</typeparam>
        /// <param name="baseUrl">The base URL.</param>
        /// <param name="endpoint">The endpoint.</param>
        /// <param name="request">The request.</param>
        /// <param name="token">The token.</param>
        /// <param name="timeout">The timeout.</param>
        /// <returns>
        /// ServiceOutput with a typed object.
        /// </returns>
        public async Task<ServiceOutput<T>> PostAsync<T>(string baseUrl, string endpoint, object request, string token = null, int? timeout = null)
        {
            RestRequest apiRequest = InitializeRequest(baseUrl, endpoint, token, timeout);

            apiRequest.AddJsonBody(request);

            return HandleResponse(await _client.ExecutePostAsync<ServiceOutput<T>>(apiRequest));
        }

        /// <summary>
        /// Calls a PUT and returns a ServiceOutput with a typed object.
        /// </summary>
        /// <typeparam name="T">The typed object returned with ServiceOutput.</typeparam>
        /// <param name="baseUrl">The base URL.</param>
        /// <param name="endpoint">The endpoint.</param>
        /// <param name="request">The request.</param>
        /// <param name="token">The token.</param>
        /// <param name="timeout">The timeout.</param>
        /// <returns>
        /// ServiceOutput with a typed object.
        /// </returns>
        public async Task<ServiceOutput<T>> PutAsync<T>(string baseUrl, string endpoint, object request, string token = null, int? timeout = null)
        {
            RestRequest apiRequest = InitializeRequest(baseUrl, endpoint, token, timeout);

            apiRequest.AddJsonBody(request);

            return HandleResponse(await _client.ExecutePutAsync<ServiceOutput<T>>(apiRequest));
        }

        #region Dispose methods

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Releases unmanaged and - optionally - managed resources.
        /// </summary>
        /// <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing && _client != null)
                {
                    _client.Dispose();
                }

                disposed = true;
            }
        }

        #endregion Dispose methods

        #region Private methods

        /// <summary>
        /// Initializes the request with token, timeout and method, if provided.
        /// </summary>
        /// <param name="baseUrl">The base URL.</param>
        /// <param name="endpoint">The endpoint.</param>
        /// <param name="token">The token.</param>
        /// <param name="timeout">The timeout.</param>
        /// <param name="method">The method.</param>
        /// <returns>
        /// The Rest request.
        /// </returns>
        private RestRequest InitializeRequest(string baseUrl, string endpoint, string token = null, int? timeout = null, Method? method = null)
        {
            RestRequest request;

            if (method.HasValue)
            {
                request = new RestRequest(endpoint, method.Value);
            }
            else
            {
                request = new RestRequest(endpoint);
            }

            if (!string.IsNullOrWhiteSpace(token))
            {
                request.AddOrUpdateHeader("Authorization", string.Format("Bearer {0}", token));
            }

            if (timeout.HasValue)
            {
                request.Timeout = timeout.Value;
            }

            // Criar novo cliente com a URL base
            if (_client != null)
            {
                _client.Dispose();
            }
            
            var options = new RestClientOptions(baseUrl);
            _client = new RestClient(options, configureSerialization: s => s.UseNewtonsoftJson());

            return request;
        }

        /// <summary>
        /// Handles the rest response and returns a service output.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="serviceResponse">The service response.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        private static ServiceOutput<T> HandleResponse<T>(RestResponse<ServiceOutput<T>> serviceResponse)
        {
            ServiceOutput<T> output = new ServiceOutput<T>()
            {
                Code = serviceResponse.StatusCode
            };

            if (!serviceResponse.IsSuccessStatusCode)
            {
                output.CustomErrorCode(
                    serviceResponse.StatusCode,
                    serviceResponse.ErrorException != null && !string.IsNullOrWhiteSpace(serviceResponse.ErrorException.Message)
                        ? serviceResponse.ErrorException.Message
                        : CommonMessages.RequestError);
            }
            else if (serviceResponse.Data != null)
            {
                output = serviceResponse.Data;
            }
            else if (!string.IsNullOrWhiteSpace(serviceResponse.Content))
            {
                output = JsonConvert.DeserializeObject<ServiceOutput<T>>(serviceResponse.Content);
            }

            return output;
        }

        #endregion Private methods
    }
}