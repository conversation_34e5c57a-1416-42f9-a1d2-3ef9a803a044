{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.API\\RnD.BackEnd.API.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.API\\RnD.BackEnd.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.API\\RnD.BackEnd.API.csproj", "projectName": "RnD.BackEnd.API", "projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.API\\RnD.BackEnd.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj"}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Email\\RnD.BackEnd.Email.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Email\\RnD.BackEnd.Email.csproj"}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Infrastructure\\RnD.BackEnd.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Infrastructure\\RnD.BackEnd.Infrastructure.csproj"}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Licensing\\RnD.BackEnd.Licensing.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Licensing\\RnD.BackEnd.Licensing.csproj"}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Logging\\RnD.BackEnd.Logging.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Logging\\RnD.BackEnd.Logging.csproj"}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Service\\RnD.BackEnd.Service.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Service\\RnD.BackEnd.Service.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.0, )"}, "Azure.Storage.Blobs": {"target": "Package", "version": "[12.24.0, )"}, "Docnet.Core": {"target": "Package", "version": "[2.6.0, )"}, "ExcelDataReader": {"target": "Package", "version": "[3.7.0, )"}, "ExcelDataReader.DataSet": {"target": "Package", "version": "[3.7.0, )"}, "LinqKit.Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.1.8, )"}, "Microsoft.ApplicationInsights.AspNetCore": {"target": "Package", "version": "[2.22.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.JsonPatch": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.2, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Graph": {"target": "Package", "version": "[5.75.0, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.70.0, )"}, "Microsoft.Kiota.Abstractions": {"target": "Package", "version": "[1.17.1, )"}, "PdfPig": {"target": "Package", "version": "[0.1.10, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}, "Swashbuckle.AspNetCore.Filters": {"target": "Package", "version": "[7.0.6, )"}, "Swashbuckle.AspNetCore.Newtonsoft": {"target": "Package", "version": "[6.5.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}, "System.Net.Http.Json": {"target": "Package", "version": "[8.0.0, )"}, "Verify.PdfPig": {"target": "Package", "version": "[2.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj", "projectName": "RnD.BackEnd.Domain", "projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Azure.Identity": {"target": "Package", "version": "[1.13.2, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.AspNetCore.JsonPatch": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Graph": {"target": "Package", "version": "[5.75.0, )"}, "Microsoft.Graph.Auth": {"target": "Package", "version": "[1.0.0-preview.7, )"}, "Microsoft.Graph.Core": {"target": "Package", "version": "[3.2.4, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.70.0, )"}, "Microsoft.Kiota.Authentication.Azure": {"target": "Package", "version": "[1.17.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Email\\RnD.BackEnd.Email.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Email\\RnD.BackEnd.Email.csproj", "projectName": "RnD.BackEnd.Email", "projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Email\\RnD.BackEnd.Email.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Email\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.0, )"}, "SendGrid": {"target": "Package", "version": "[9.28.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Infrastructure\\RnD.BackEnd.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Infrastructure\\RnD.BackEnd.Infrastructure.csproj", "projectName": "RnD.BackEnd.Infrastructure", "projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Infrastructure\\RnD.BackEnd.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "Dapper": {"target": "Package", "version": "[2.1.15, )"}, "Dapper.Contrib": {"target": "Package", "version": "[2.0.78, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.WebUtilities": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Azure.Cosmos": {"target": "Package", "version": "[3.35.3, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "RestSharp": {"target": "Package", "version": "[110.2.0, )"}, "RestSharp.Serializers.NewtonsoftJson": {"target": "Package", "version": "[110.2.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Licensing\\RnD.BackEnd.Licensing.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Licensing\\RnD.BackEnd.Licensing.csproj", "projectName": "RnD.BackEnd.Licensing", "projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Licensing\\RnD.BackEnd.Licensing.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Licensing\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.WebUtilities": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Caching.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.IdentityModel.JsonWebTokens": {"target": "Package", "version": "[7.2.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RestSharp": {"target": "Package", "version": "[110.2.0, )"}, "RestSharp.Serializers.NewtonsoftJson": {"target": "Package", "version": "[110.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Logging\\RnD.BackEnd.Logging.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Logging\\RnD.BackEnd.Logging.csproj", "projectName": "RnD.BackEnd.Logging", "projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Logging\\RnD.BackEnd.Logging.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Logging\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Service\\RnD.BackEnd.Service.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Service\\RnD.BackEnd.Service.csproj", "projectName": "RnD.BackEnd.Service", "projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Service\\RnD.BackEnd.Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Service\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj"}, "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Email\\RnD.BackEnd.Email.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Email\\RnD.BackEnd.Email.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Hangfire.Console.Extensions": {"target": "Package", "version": "[1.0.5, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.0, )"}, "Polly": {"target": "Package", "version": "[8.2.0, )"}, "Polly.Extensions.Http": {"target": "Package", "version": "[3.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.6.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}