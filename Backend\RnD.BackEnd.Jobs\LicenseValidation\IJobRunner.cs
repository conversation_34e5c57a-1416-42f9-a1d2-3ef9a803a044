﻿using System.Threading.Tasks;

namespace RnD.BackEnd.Jobs.LicenseValidation
{
    /// <summary>
    /// Job runner interface to validate license before execution
    /// </summary>
    public interface IJobRunner
    {
        #region JobsService Jobs

        /// <summary>
        /// Executes <strong>OutputWeatherForecast</strong> job in <strong>JobsService</strong>.
        /// </summary>
        Task OutputWeatherForecast();

        #endregion

        #region Workflows Jobs

        /// <summary>
        /// Executes <strong>CheckOverdueTasks</strong> job in <strong>WorkflowService</strong>.
        /// </summary>
        Task CheckOverdueTasks();

        /// <summary>
        /// Executes <strong>CompleteBackgroundWorkflows</strong> job in <strong>WorkflowService</strong>.
        /// </summary>
        Task CompleteBackgroundWorkflows();

        #endregion
    }
}
