﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Workflow;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// Get Instances response example for 200
    /// </summary>
    public class GetInstances200 : IExamplesProvider<ApiOutput<List<WorkflowInstanceDto>>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 for a list response.
        /// </returns>
        public ApiOutput<List<WorkflowInstanceDto>> GetExamples()
        {
            return new ApiOutput<List<WorkflowInstanceDto>>
            {
                Code = HttpStatusCode.OK,
                Value = new List<WorkflowInstanceDto>
                {
                    new WorkflowInstanceDto
                    {
                        Id = Guid.Parse("1949db44-5cd7-409c-9244-07a5210fa56f"),
                        DisplayName = "Not Billable Project Approval",
                        SourceId = "Project/2023",
                        SourceUrl = null,
                        WorkflowStatus = "IN_PROGRESS",
                        SourceType = "DOCUMENT",
                        WorkflowType = "AUTOMATIC",
                        Purpose = "APPROVAL",
                        StateProcessingRequired = false,
                        ActiveStepName = "Approval",
                        Steps = new List<WorkflowInstanceStepDto>()
                        {
                            new WorkflowInstanceStepDto()
                            {
                                Id = Guid.Parse("53ab3926-3311-4565-b732-dbe2b8d156ac"),
                                StepId = null,
                                InstanceId = Guid.Parse("1949db44-5cd7-409c-9244-07a5210fa56f"),
                                DisplayName = "Approval",
                                AllUsersMustApprove = true,
                                SkipOverOtherTasks = false,
                                Order = 1,
                                IsActive = true,
                                StepStatus = "TODO",
                                NumberOfActionsRequired = 2,
                                NumberOfActionsCompleted = 0,
                                Tasks = new List<WorkflowInstanceTaskDto>()
                                {
                                    new WorkflowInstanceTaskDto
                                    {
                                        Id = Guid.Parse("5c84086e-1bd7-4b06-b6f7-e17df63b8cd3"),
                                        UserId = "<EMAIL>",
                                        TaskStatus = "TODO",
                                        TaskCompleted = false,
                                        TaskActivated = true,
                                        Comments = "",
                                        SysModifyDate = Convert.ToDateTime("2023-01-18T15:59:42.457+00:00")
                                    },
                                    new WorkflowInstanceTaskDto
                                    {
                                        Id = Guid.Parse("f80c4b6d-61ce-406a-8cd1-d66361adc977"),
                                        UserId = "<EMAIL>",
                                        TaskStatus = "TODO",
                                        TaskCompleted = false,
                                        TaskActivated = true,
                                        Comments = "",
                                        SysModifyDate = Convert.ToDateTime("2023-01-18T15:59:43.713+00:00")
                                    }
                                }
                            }
                        }
                    }
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}
