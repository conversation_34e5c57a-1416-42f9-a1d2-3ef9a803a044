﻿using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController
{
    /// <summary>
    /// The example for a 412 response.
    /// </summary>
    public class UpdateDynamicEntity412 : IExamplesProvider<ApiOutput<object>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example for a Update Dynamic Entity 412 response.
        /// </returns>
        public ApiOutput<object> GetExamples()
        {
            return new ApiOutput<object>
            {
                Code = HttpStatusCode.BadRequest,
                Description = "Error updating entity.",
                Error = true,
                ExceptionMessages = new Model.Exception.ModelException()
                {
                    Messages = new List<string>
                    {
                        "PreconditionFailed - Error updating entity."
                    }
                },
                Properties = new Dictionary<string, object>()
            };
        }
    }
}