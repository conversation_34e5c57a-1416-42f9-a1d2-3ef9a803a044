﻿using System.Collections.Generic;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController
{
    /// <summary>
    /// Update dynamic Entity request example
    /// </summary>
    public class UpdateDynamicEntity : IExa<PERSON>sProvider<Dictionary<string, object>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to Update Dynamic Entity.
        /// </returns>
        public Dictionary<string, object> GetExamples()
        {
            return new Dictionary<string, object>
            {
                {"entityId", "dive1" },
                {"companyId", "diveCenterPhi1" },
                {"name", "Dive Center Koh Phi Phi" },
                {"type", "Dive Center" },
                {"review", 4.5 },
                {"numberOfReviews", 104 },
                {"location", "Koh Phi Phi Island" },
                { "country", "Thailand" },
                {"id", "dive1:bfd6b932-9e6a-43f2-b739-0b65213bc333" }
            };
        }
    }
}