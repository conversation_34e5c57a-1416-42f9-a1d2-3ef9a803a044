import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';
import Modal from '../ModalTemplate';

const StepRemoveModal = (props) => {
  const { onClose, open, onRemoveProcessStep } = props;

  return (
    <Modal
      onClose={onClose}
      open={open}
      title="Remover Etapa"
      onCancel={onClose}
      onConfirm={onRemoveProcessStep}
      content={
        <Box>
          <Typography
            align="center"
            color="textSecondary"
            variant="subtitle2"
          >
            Tem a certeza que deseja remover a etapa seleccionada?
          </Typography>
        </Box>
      }
    />
  );
};

StepRemoveModal.propTypes = {
  onClose: PropTypes.func,
  open: PropTypes.bool.isRequired,
  onRemoveProcessStep: PropTypes.func,
};

export default StepRemoveModal;
