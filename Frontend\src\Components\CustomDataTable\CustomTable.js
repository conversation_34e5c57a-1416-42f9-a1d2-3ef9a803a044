import { React, useState, useEffect, isValidElement } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Avatar, Box, IconButton, Checkbox, Table, TableBody, TableCell, TablePagination, TableRow, Link, Typography } from '@mui/material';
import Scrollbar from '../Scrollbar';
import TableHeader from '../TableUtils/TableHeader';
import TableLoading from '../TableUtils/TableLoading';
import TableAction from '../TableUtils/TableAction';
import { applyPagination, applySort, applyFilters } from '../TableUtils/TableUtils';
import { useTranslation } from 'react-i18next';
import TableRemoveModal from '../Modal/FunctionalTable/TableRemoveCardModal';

const CustomTable = ({ getProfiles, data = [], dataHeaders = [], query, isLoading, orderByString = 'displayName', modelid, pagination, handleSort, sortOrder, sortOrderBy }) => {
    const [page, setPage] = useState(0);
    const [limit, setLimit] = useState(10);
    const [orderBy, setOrderBy] = useState(orderByString);
    const [order, setOrder] = useState('asc');
    const [selectedProfiles, setSelectedProfiles] = useState([]);
    const [openProfileRemoveModal, setOpenProfileRemoveModal] = useState(false);
    const { t } = useTranslation();
    const navigate = useNavigate();

    const addPropertiesToLink = (link, item) => {
        const regex = /\$\w*/;
        const propertyName = regex.exec(link)[0].replace('$', '');
        link = link.replace(regex, item[propertyName]);
        link = link.replace('$modelid', modelid)
        return link;
    }

    //Selection
    const handleSelectAllProfiles = (event) => {
        setSelectedProfiles(event.target.checked
            ? data.map((profile) => profile.id)
            : []);
    };

    const handleSelectOneProfile = (event, id) => {
        if (!selectedProfiles.includes(id)) {
            setSelectedProfiles((prevSelected) => [...prevSelected, id]);
        } else {
            setSelectedProfiles((prevSelected) => prevSelected.filter((_id) => _id !== id));
        }
    };

    //---------------TABLE ACTION-----------------------
    const handleOpenProfileRemoveModal = () => {
        setOpenProfileRemoveModal(true);
    };

    const handleCloseProfileRemoveModal = (success) => {
        setOpenProfileRemoveModal(false);
        if (success) {
            setSelectedProfiles([]);
            getProfiles();
        }
    };

    const handleEditAction = () => {
        navigate(`profile/${selectedProfiles}`)
    }
    //---------------TABLE UTILS------------------------
    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handleLimitChange = (event) => {
        setLimit(parseInt(event.target.value, 10));
    };

    const handleRequestSort = (property) => {
        const isAsc = orderBy === property && order === 'asc';
        setOrder(isAsc ? 'desc' : 'asc');
        setOrderBy(property);
    };

    const filteredList = applyFilters(dataHeaders, data, query);
    const sortedList = applySort(filteredList, orderBy, order);
    const paginatedList = applyPagination(sortedList, page, limit);
    const enableBulkActions = selectedProfiles.length > 0;

    useEffect(() => {
        // update limit if pagination data comes from parent component
        if (pagination && data.length > 0) {
            setLimit(data.length)
        }
    }, [pagination])

    return (
        <>
            <TableAction
                handleRemoveAction={handleOpenProfileRemoveModal}
                enableBulkActions={enableBulkActions}
                selectedCounter={selectedProfiles.length}
                enableRemove
                enableEdit
                handleEditAction={handleEditAction}
            />
            <Scrollbar>
                <Box sx={{ p: 2, pt: 0, minWidth: 700 }}>
                    <Table stickyHeader>
                        <TableHeader
                            onSelectAllClick={handleSelectAllProfiles}
                            headCells={dataHeaders}
                            onSortClick={handleSort ?? handleRequestSort}
                            orderBy={sortOrderBy ?? orderBy}
                            order={sortOrder ?? order}
                        />
                        <TableBody>
                            {isLoading ?
                                <TableLoading
                                    isLoading={isLoading}
                                    headCells={dataHeaders}
                                    numRows={limit}
                                />
                                : <>
                                    {
                                        paginatedList.length <= 0 && query !== "" && (
                                            <TableRow>
                                                <TableCell
                                                    colSpan={100}
                                                >
                                                    <Typography>
                                                        {t('common:labels.noResultsFound')}
                                                    </Typography>
                                                </TableCell>
                                            </TableRow>
                                        )
                                    }
                                    {paginatedList.map((row) => {
                                        const isprofileselected = selectedProfiles.includes(row.id);

                                        return (
                                            <TableRow
                                                hover
                                                key={row.id}
                                                selected={isprofileselected}
                                            >
                                                <TableCell
                                                    padding="checkbox"
                                                    sx={{
                                                        width: "52px",
                                                        height: "40px"
                                                    }}
                                                >
                                                    <Checkbox
                                                        checked={isprofileselected}
                                                        color="primary"
                                                        onChange={(event) => handleSelectOneProfile(event, row.id)}
                                                        value={isprofileselected}
                                                    />
                                                </TableCell>
                                                {dataHeaders.map((cell, cell_id) => {
                                                    let cellContent = null;

                                                    if (cell.link) {
                                                        cellContent = <Box
                                                            sx={{ display: 'flex' }}
                                                        >
                                                            <Link
                                                                component={RouterLink}
                                                                to={addPropertiesToLink(cell.link, row)}
                                                                color="primary"
                                                                sx={{
                                                                    fontWeight: 'bold',
                                                                    alignSelf: 'center',
                                                                    marginLeft: 1
                                                                }}
                                                            >
                                                                {row[cell.id]}
                                                            </Link>
                                                        </Box>
                                                    } else if (cell.avatar) {
                                                        cellContent = <Box
                                                            sx={{ display: 'flex', alignItems: 'center' }}
                                                        >
                                                            <Avatar
                                                                mx={2}
                                                                alt={row[cell.id].toString()}
                                                                src=""
                                                                sx={{
                                                                    marginRight: 1
                                                                }}
                                                            />
                                                            {row[cell.id]}
                                                        </Box>
                                                    } else if (cell.action) {
                                                        const label = isValidElement(cell.label) ? cell.label : cell.label(row);
                                                        cellContent = label ? (
                                                            <Box
                                                                sx={{ display: 'flex', justifyContent: 'center' }}
                                                            >
                                                                <IconButton
                                                                    aria-controls="simple-menu"
                                                                    aria-haspopup="true"
                                                                    onClick={(e) => cell.action(e, row)}
                                                                >
                                                                    {label}
                                                                </IconButton>
                                                            </Box>
                                                        ) : undefined;
                                                    } else {
                                                        cellContent = row[cell.id]
                                                    }
                                                    return <TableCell
                                                        key={`cell_${cell_id}`}
                                                        align={cell.align}
                                                    >
                                                        {cellContent}
                                                    </TableCell>
                                                })}

                                            </TableRow>
                                        )
                                    })}
                                </>}
                        </TableBody>
                    </Table>
                </Box>
            </Scrollbar>
            {
                pagination ? pagination : <TablePagination
                    component="div"
                    count={filteredList.length}
                    onPageChange={handlePageChange}
                    onRowsPerPageChange={handleLimitChange}
                    page={page}
                    rowsPerPage={limit}
                    rowsPerPageOptions={[5, 10, 25]}
                />
            }
            {/*This component is just an example! */}
            <TableRemoveModal
                oid={selectedProfiles[0]}
                selectedProcess={selectedProfiles}
                open={openProfileRemoveModal}
                onClose={handleCloseProfileRemoveModal}
            />
        </>
    );
};

CustomTable.propTypes = {
    data: PropTypes.array.isRequired,
    dataHeaders: PropTypes.array,
    query: PropTypes.string,
    isLoading: PropTypes.bool,
    orderByString: PropTypes.string,
    modelid: PropTypes.string,
    pagination: PropTypes.element,
    handleSort: PropTypes.func,
    sortOrder: PropTypes.string,
    sortOrderBy: PropTypes.string,
    getProfiles: PropTypes.func
};

export default CustomTable;
