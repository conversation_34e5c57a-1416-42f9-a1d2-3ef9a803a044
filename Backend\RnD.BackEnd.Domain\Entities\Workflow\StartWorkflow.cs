﻿using System.Collections.Generic;

namespace RnD.BackEnd.Domain.Entities.Workflow
{
    public class StartWorkflow
    {
        public string DisplayName { get; set; }
        public string SourceId { get; set; }
        public string SourceUrl { get; set; }
        public string SourceType { get; set; }
        public string WorkflowType { get; set; }
        public string Purpose { get; set; }
        public string UserId { get; set; }
        public bool AutoComplete { get; set; }
        public string WorkflowStatus { get; set; }
        public IList<CustomStep> Steps { get; set; }
        public WorkflowEmail EmailRequest { get; set; }
    }
}