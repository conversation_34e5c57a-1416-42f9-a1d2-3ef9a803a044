﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Vehicle;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.VehicleController
{
    /// <summary>
    /// Vehicle example of a 201 response
    /// </summary>
    public class Vehicle201 : IExamplesProvider<ApiOutput<VehicleDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a vehicle response for 201.
        /// </returns>
        public ApiOutput<VehicleDto> GetExamples()
        {
            return new ApiOutput<VehicleDto>
            {
                Code = HttpStatusCode.Created,
                Value = new VehicleDto
                {
                    Id = Guid.NewGuid().ToString(),
                    Brand = "Ford",
                    BrandId = "0001",
                    FuelType = "DIESEL",
                    ModelName = "Fiesta",
                    Version = "567",
                    Year = 2022
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}
