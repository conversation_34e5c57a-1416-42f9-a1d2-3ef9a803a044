﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.JsonPatch;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Entities.Vehicle;

namespace RnD.BackEnd.Domain.Interfaces.Services
{
    /// <summary>
    /// Vehicles service interface
    /// </summary>
    public interface IVehiclesService
    {
        /// <summary>
        /// Adds the vehicle.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The vehicle added.
        /// </returns>
        Task<ServiceOutput<Vehicle>> AddAsync(Vehicle request);

        /// <summary>
        /// Adds the specified vehicles.
        /// </summary>
        /// <param name="vehicles">The vehicles.</param>
        /// <returns>
        /// The vehicles added.
        /// </returns>
        Task<ServiceOutput<List<Vehicle>>> AddAsync(List<Vehicle> vehicles);

        /// <summary>
        /// Updates the vehicle.
        /// </summary>
        /// <param name="request">The object</param>
        /// <param name="id">The id of the vehicle to be updated</param>
        /// <returns>
        /// The vehicle updated.
        /// </returns>
        Task<ServiceOutput<Vehicle>> UpdateAsync(Vehicle request, string id, string etag);

        /// <summary>
        /// Updates the vehicle.
        /// </summary>
        /// <param name="changes">The changes</param>
        /// <param name="id">The id of the vehicle to be updated</param>
        /// <returns>
        /// The vehicle updated.
        /// </returns>
        Task<ServiceOutput<Vehicle>> UpdateAsync(JsonPatchDocument<Vehicle> changes, string id);

        /// <summary>
        /// Deletes the specified vehicle by id.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// True if operation succeeded, False otherwise.
        /// </returns>
        Task<ServiceOutput<object>> DeleteAsync(string id);

        /// <summary>
        /// Gets the vehicle by identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The vehicle.
        /// </returns>
        Task<ServiceOutput<Vehicle>> GetByIdAsync(string id);

        /// <summary>
        /// Get the list of vehicles.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="brandId">The brand identifier.</param>
        /// <param name="modelName">Name of the model.</param>
        /// <param name="fuelType">Type of the fuel.</param>
        /// <param name="version">The version.</param>
        /// <param name="year">The year.</param>
        /// <param name="pageNumber">The page number.</param>
        /// <param name="maxItems">The maximum items.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// The list of vehicles.
        /// </returns>
        Task<ServiceOutput<List<Vehicle>>> ListAsync(
            List<string> id,
            List<string> brandId,
            List<string> modelName,
            List<string> fuelType,
            List<string> version,
            List<int> year,
            int? pageNumber,
            int? maxItems,
            string sortField,
            bool sortAscending,
            string continuationToken,
            CancellationToken cancellationToken);

        /// <summary>
        /// Adds or updates the vehicle.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The vehicle added or updated.
        /// </returns>
        Task<ServiceOutput<Vehicle>> AddOrUpdateAsync(Vehicle request);
    }
}
