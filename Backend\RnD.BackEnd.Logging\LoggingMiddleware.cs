﻿using System;
using System.Net;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace RnD.BackEnd.Logging
{
    /// <summary>
    /// Logging middleware class
    /// </summary>
    public class LoggingMiddleware
    {
        readonly RequestDelegate _next;
        private readonly LogUtils _logUtils;

        /// <summary>
        /// Initializes a new instance of the <see cref="LoggingMiddleware"/> class.
        /// </summary>
        /// <param name="next">The next.</param>
        /// <param name="logger">The logger.</param>
        /// <exception cref="System.ArgumentNullException">next</exception>
        public LoggingMiddleware(RequestDelegate next, ILogger<LoggingMiddleware> logger)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
            _logUtils = new LogUtils(logger);
        }

        /// <summary>
        /// Invokes the specified HTTP context.
        /// </summary>
        /// <param name="httpContext">The HTTP context.</param>
        /// <exception cref="System.ArgumentNullException">httpContext</exception>
        public async Task Invoke(HttpContext httpContext)
        {
            if (httpContext == null)
            {
                throw new ArgumentNullException(nameof(httpContext));
            }

            try
            {
                await _next(httpContext);
            }
            catch (Exception ex)
            {
                string user = httpContext.User.FindFirst(ClaimTypes.Name) != null ? httpContext.User.FindFirst(ClaimTypes.Name).Value : "Unknown User";
                string currentMethod = AppUtil.GetModuleMethod(ex.TargetSite);

                httpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                httpContext.Response.ContentType = "application/json";

                await httpContext.Response.WriteAsync(JsonConvert.SerializeObject(new ServiceOutput()
                {
                    Description = string.Format("Error : {0}", ex.Message),
                    Error = true
                }));

                _logUtils.LogError(currentMethod, user, ex.Message, ex);
            }
        }
    }
}
