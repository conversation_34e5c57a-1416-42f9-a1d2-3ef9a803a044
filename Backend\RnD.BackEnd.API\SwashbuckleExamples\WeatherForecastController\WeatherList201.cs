﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Weather;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController
{
    /// <summary>
    /// The example of a response 201 for a list of created Weathers.
    /// </summary>
    public class WeatherList201 : IExamplesProvider<ApiOutput<List<WeatherDto>>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a response 201 for a list of created Weathers
        /// </returns>
        public ApiOutput<List<WeatherDto>> GetExamples()
        {
            return new ApiOutput<List<WeatherDto>>
            {
                Code = HttpStatusCode.Created,
                Value = new List<WeatherDto>
                {
                    new WeatherDto
                    {
                        Id = Guid.NewGuid(),
                        Location = "Lisbon",
                        Summary = null,
                        Temperature = 27
                    },
                    new WeatherDto
                    {
                        Id = Guid.NewGuid(),
                        Location = "Porto",
                        Summary = null,
                        Temperature = 25
                    }
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}