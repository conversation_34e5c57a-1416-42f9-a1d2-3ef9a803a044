﻿using System.Collections.Generic;
using RnD.BackEnd.API.Model.Vehicle;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.VehicleController
{
    /// <summary>
    /// Multiple vehicle create request example
    /// </summary>
    public class VehicleMultipleCreate : IMultipleExamplesProvider<List<CreateVehicleDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to create multiple vehicles.
        /// </returns>
        public IEnumerable<SwaggerExample<List<CreateVehicleDto>>> GetExamples()
        {
            yield return SwaggerExample.Create("Ford", new List<CreateVehicleDto>
            {
                new CreateVehicleDto
                {
                    Brand = "Ford",
                    BrandId = "0001",
                    FuelType = "DIESEL",
                    ModelName = "Fiesta",
                    Version = "567",
                    Year = 2022
                },
                new CreateVehicleDto
                {
                    Brand = "Ford",
                    BrandId = "0001",
                    FuelType = "PETROL",
                    ModelName = "Focus",
                    Version = "223",
                    Year = 2021
                }
            });

            yield return SwaggerExample.Create("Honda", new List<CreateVehicleDto>
            {
                new CreateVehicleDto
                {
                    Brand = "Honda",
                    BrandId = "0004",
                    FuelType = "PETROL",
                    ModelName = "Civic",
                    Version = "123",
                    Year = 2022
                },
                new CreateVehicleDto
                {
                    Brand = "Honda",
                    BrandId = "0004",
                    FuelType = "PETROL",
                    ModelName = "Accord",
                    Version = "322",
                    Year = 1998
                }
            });
        }
    }
}
