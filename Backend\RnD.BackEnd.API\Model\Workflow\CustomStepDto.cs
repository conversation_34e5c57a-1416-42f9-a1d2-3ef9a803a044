﻿using System.Collections.Generic;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Custom step request DTO class
    /// </summary>
    public class CustomStepDto
    {
        /// <summary>
        /// Gets or sets the step users.
        /// </summary>
        /// <value>
        /// The step users.
        /// </value>
        public List<string> StepUsers { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [require all to approve].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [require all to approve]; otherwise, <c>false</c>.
        /// </value>
        public bool RequireAllToApprove { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [skip over other tasks].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [skip over other tasks]; otherwise, <c>false</c>.
        /// </value>
        public bool SkipOverOtherTasks { get; set; }

        /// <summary>
        /// Gets or sets the name of the step.
        /// </summary>
        /// <value>
        /// The name of the step.
        /// </value>
        public string StepName { get; set; }

        /// <summary>
        /// Gets or sets the order.
        /// </summary>
        /// <value>
        /// The order.
        /// </value>
        public int Order { get; set; }
    }
}
