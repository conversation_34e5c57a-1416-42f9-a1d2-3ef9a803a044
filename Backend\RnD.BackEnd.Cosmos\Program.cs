﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Reflection;
using System.Threading.Tasks;
using CommandLine;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Fluent;
using Microsoft.Azure.Cosmos.Scripts;
using Microsoft.Extensions.Configuration;
using RnD.BackEnd.Cosmos.Helpers;
using RnD.BackEnd.Domain.Settings.Cosmos;

namespace RnD.BackEnd.Cosmos
{
    class Program
    {
        static void Main(string[] args)
        {
            Parser.Default.ParseArguments<CommandLineArguments>(args)
               .WithParsed(a =>
               {
                   // Read Cosmos settings from appsettings.json
                   CosmosSettings settings = ReadSettings();
                   if (settings != null)
                   {
                       // Create Cosmos client connection
                       using (CosmosClient cosmosClient = new CosmosClient(a.EndpointUri, a.<PERSON>))
                       {
                           // Deploy containers and stored procedores
                           DeployAndConfigureCosmos(
                               cosmosClient,
                               a.DatabaseName,
                               settings.Containers,
                               a.MinThroughputAutoScale,
                               a.MinThroughputManualScale,
                               a.MaxThroughputAutoScale).Wait();
                       }

                       Console.WriteLine("Finished Cosmos Containers deployment and configuration!");
                   }
               });
        }

        /// <summary>
        /// Deploys and configures Cosmos.
        /// </summary>
        /// <param name="cosmosClient">The cosmos client.</param>
        /// <param name="databaseName">Name of the database.</param>
        /// <param name="containers">The containers.</param>
        /// <param name="minAutoScale">The minimum automatic scale.</param>
        /// <param name="minManualScale">The minimum manual scale.</param>
        /// <param name="maxAutoScale">The maximum automatic scale.</param>
        private static async Task DeployAndConfigureCosmos(
            CosmosClient cosmosClient,
            string databaseName,
            List<CosmosContainerInfo> containers,
            int? minAutoScale,
            int? minManualScale,
            int? maxAutoScale)
        {
            Console.WriteLine("Deploying Cosmos Containers and Stored Procedures...");
            Console.WriteLine("");

            Database database = cosmosClient.GetDatabase(databaseName);

            foreach (CosmosContainerInfo container in containers)
            {
                Console.WriteLine($"Setup container {container.Name}.");

                // Define indexing policy
                IndexingPolicy indexingPolicy = new IndexingPolicy
                {
                    IndexingMode = IndexingMode.Consistent,
                    Automatic = true
                };

                // Define throughput and auto-scale
                if (container.Autoscale && (!container.Throughput.HasValue || container.Throughput.Value < minAutoScale.Value || container.Throughput.Value > maxAutoScale.Value))
                {
                    container.Throughput = minAutoScale;
                }
                else if (!container.Autoscale && (!container.Throughput.HasValue || container.Throughput.Value < minManualScale))
                {
                    container.Throughput = minManualScale;
                }

                ThroughputProperties throughputProperties = container.Autoscale
                    ? ThroughputProperties.CreateAutoscaleThroughput(container.Throughput.Value)
                    : ThroughputProperties.CreateManualThroughput(container.Throughput.Value);

                ContainerResponse response;

                // Create container with unique keys
                if (container.UniqueKeys != null && container.UniqueKeys.Count > 0)
                {
                    Console.WriteLine($"Create container {container.Name} with UniqueKeys.");

                    ContainerBuilder containerBuilder = new ContainerBuilder(database, container.Name, $"/{container.PartitionKey}");
                    containerBuilder
                        .WithIndexingPolicy()
                        .WithIndexingMode(indexingPolicy.IndexingMode)
                        .WithAutomaticIndexing(indexingPolicy.Automatic)
                        .Attach();

                    foreach (string uniqueKey in container.UniqueKeys)
                    {
                        UniqueKeyDefinition keyDefinition = containerBuilder.WithUniqueKey();

                        string[] keys = uniqueKey.Split(',', StringSplitOptions.RemoveEmptyEntries);
                        foreach (string key in keys)
                        {
                            keyDefinition.Path($"/{key}");
                        }

                        keyDefinition.Attach();
                    }

                    response = await containerBuilder.CreateIfNotExistsAsync();
                    if (response.StatusCode == HttpStatusCode.Created)
                    {
                        await database.GetContainer(container.Name).ReplaceThroughputAsync(throughputProperties);
                    }
                }
                // Create container without unique keys
                else
                {
                    Console.WriteLine($"Create container {container.Name} without UniqueKeys.");

                    response = await database.CreateContainerIfNotExistsAsync(
                        new ContainerProperties(container.Name, $"/{container.PartitionKey}")
                        {
                            IndexingPolicy = indexingPolicy
                        },
                        throughputProperties);
                }

                if (response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.Created)
                {
                    // Create user defined functions
                    if (container.UserDefinedFunctions != null && container.UserDefinedFunctions.Count > 0)
                    {
                        foreach (ScriptInfo userDefinedFunction in container.UserDefinedFunctions)
                        {
                            Console.WriteLine($"Create or update user defined function {userDefinedFunction.Id} in container {container.Name}.");

                            if (!string.IsNullOrWhiteSpace(userDefinedFunction.Id))
                            {
                                try
                                {
                                    Console.WriteLine($"Try to delete user defined function {userDefinedFunction.Id}");
                                    await response.Container.Scripts.DeleteUserDefinedFunctionAsync(userDefinedFunction.Id);
                                }
                                catch (Exception deleteError)
                                {
                                    Console.ForegroundColor = ConsoleColor.Red;
                                    Console.WriteLine($"User defined function {userDefinedFunction.Id} not deleted: {deleteError.Message}");
                                    Console.ForegroundColor = ConsoleColor.White;
                                }

                                Assembly assembly = Assembly.GetExecutingAssembly();
                                string userDefinedFunctionFileName = $"{assembly.GetName().Name}.{userDefinedFunction.Path}";

                                using (Stream stream = assembly.GetManifestResourceStream(userDefinedFunctionFileName))
                                {
                                    using (StreamReader reader = new StreamReader(stream))
                                    {
                                        string userDefinedFunctionContent = await reader.ReadToEndAsync();

                                        if (!string.IsNullOrWhiteSpace(userDefinedFunctionContent))
                                        {
                                            UserDefinedFunctionResponse udfResponse = await response.Container.Scripts.CreateUserDefinedFunctionAsync(new UserDefinedFunctionProperties()
                                            {
                                                Id = userDefinedFunction.Id,
                                                Body = userDefinedFunctionContent
                                            });

                                            Console.WriteLine($"User defined function {userDefinedFunction.Id} status code: {udfResponse.StatusCode}");
                                        }
                                        else
                                        {
                                            Console.ForegroundColor = ConsoleColor.Red;
                                            Console.WriteLine("Could not get user defined function content.");
                                            Console.ForegroundColor = ConsoleColor.White;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Create stored procedures
                    if (container.StoredProcedures != null && container.StoredProcedures.Count > 0)
                    {
                        foreach (ScriptInfo storedProcedure in container.StoredProcedures)
                        {
                            Console.WriteLine($"Create or update stored procedure {storedProcedure.Id} in container {container.Name}.");

                            if (!string.IsNullOrWhiteSpace(storedProcedure.Id))
                            {
                                try
                                {
                                    Console.WriteLine($"Try to delete stored procedure {storedProcedure.Id}");
                                    await response.Container.Scripts.DeleteStoredProcedureAsync(storedProcedure.Id);
                                }
                                catch (Exception deleteError)
                                {
                                    Console.ForegroundColor = ConsoleColor.Red;
                                    Console.WriteLine($"Stored procedure {storedProcedure.Id} not deleted: {deleteError.Message}");
                                    Console.ForegroundColor = ConsoleColor.White;
                                }

                                Assembly assembly = Assembly.GetExecutingAssembly();
                                string storedProcedureFileName = $"{assembly.GetName().Name}.{storedProcedure.Path}";

                                using (Stream stream = assembly.GetManifestResourceStream(storedProcedureFileName))
                                {
                                    using (StreamReader reader = new StreamReader(stream))
                                    {
                                        string storedProcedureContent = await reader.ReadToEndAsync();

                                        if (!string.IsNullOrWhiteSpace(storedProcedureContent))
                                        {
                                            StoredProcedureResponse spResponse = await response.Container.Scripts.CreateStoredProcedureAsync(new StoredProcedureProperties()
                                            {
                                                Id = storedProcedure.Id,
                                                Body = storedProcedureContent
                                            });

                                            Console.WriteLine($"Stored procedure {storedProcedure.Id} status code: {spResponse.StatusCode}");
                                        }
                                        else
                                        {
                                            Console.ForegroundColor = ConsoleColor.Red;
                                            Console.WriteLine("Could not get stored procedure content.");
                                            Console.ForegroundColor = ConsoleColor.White;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"Response status for container {container.Name} was NOT OK.");
                }

                Console.WriteLine($"Finished container {container.Name}!.");
                Console.WriteLine("");
            }
        }

        /// <summary>
        /// Reads the settings.
        /// </summary>
        /// <returns>The Cosmos settings.</returns>
        private static CosmosSettings ReadSettings()
        {
            Console.WriteLine("Reading Cosmos settings from appsettings.json file...");

            IConfigurationRoot configuration = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", false)
                .Build();

            IConfigurationSection cosmosSettingsSection = configuration.GetSection(nameof(CosmosSettings));
            if (cosmosSettingsSection == null)
            {
                Console.WriteLine("No CosmosSettings section has been found");
                return null;
            }

            return cosmosSettingsSection.Get<CosmosSettings>();
        }
    }
}
