﻿using System;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Update task DTO class
    /// </summary>
    public class UpdateTaskDto
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the task status.
        /// </summary>
        /// <value>
        /// The task status.
        /// </value>
        public string TaskStatus { get; set; }

        /// <summary>
        /// Represents an event that is raised when a task either successfully or unsuccessfully completes.
        /// </summary>
        /// <value>
        /// The task completed.
        /// </value>
        public bool? TaskCompleted { get; set; }

        /// <summary>
        /// Gets or sets the comments.
        /// </summary>
        /// <value>
        /// The comments.
        /// </value>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the workflow email.
        /// </summary>
        /// <value>
        /// The workflow email.
        /// </value>
        public WorkflowEmailDto EmailRequest { get; set; }
    }
}