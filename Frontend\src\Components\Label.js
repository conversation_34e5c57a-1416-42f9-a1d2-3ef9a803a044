import PropTypes from 'prop-types';
import { useTheme } from '@emotion/react';
import { Box } from '@mui/system';

const LabelRoot = ({ theme, styleProps }) => {
  const { color, customColor } = styleProps;
  const backgroundColor = customColor ? customColor.main : theme.palette[color]?.main || theme.palette.primary.main;
  const fontColor = customColor ? customColor.contrastText : theme.palette[styleProps.color]?.contrastText || theme.palette.primary.contrastText;

  return <Box
    sx={{
      alignItems: 'center',
      backgroundColor,
      borderRadius: theme.shape.borderRadius,
      color: fontColor,
      cursor: 'default',
      display: 'inline-flex',
      flexGrow: 0,
      flexShrink: 0,
      fontFamily: theme.typography.fontFamily,
      fontSize: theme.typography.pxToRem(11),
      fontWeight: theme.typography.fontWeightMedium,
      justifyContent: 'center',
      letterSpacing: 0.5,
      minWidth: 20,
      paddingBottom: theme.spacing(0.5),
      paddingLeft: theme.spacing(1),
      paddingRight: theme.spacing(1),
      paddingTop: theme.spacing(0.5),
      textTransform: 'uppercase',
      whiteSpace: 'nowrap'
    }}
  />
};
LabelRoot.propTypes = {
  theme: PropTypes.object,
  styleProps: PropTypes.object
}

const Label = ({ color = 'grey', customColor, children, ...other }) => {
  const styleProps = { color, customColor };
  const theme = useTheme();

  return (
    <LabelRoot
      styleProps={styleProps}
      theme={theme}
      {...other}
    >
      {children}
    </LabelRoot>
  );
};

Label.propTypes = {
  children: PropTypes.node,
  color: PropTypes.string,
  customColor: PropTypes.object
};

export default Label;
