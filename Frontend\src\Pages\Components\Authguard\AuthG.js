import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>con<PERSON>utton,
  Typography,
} from "@mui/material";
import { PropTypes } from "prop-types";
import { useTheme } from '@emotion/react';
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import { useState } from "react";
import {
  ExpandMore as ExpandMoreIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from "@mui/icons-material";
import getIntro from "./mocks/introAuthG";
import getCode from "./mocks/codeAuthG";
import getAuthenticatedCode from "./mocks/authenticated";
import getRouteProtectionCode from "./mocks/routeProtection";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import { styled } from '@mui/system';

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh",
})

const linkStyle = {
  marginRight: 20,
  alignItems: "center",
};

const AuthG = ({ open: openProp }) => {
  const theme = useTheme();
  const [intro, setIntro] = useState(getIntro);
  const [code, setCode] = useState(getCode);
  const [authenticated, setAuthenticated] = useState(getAuthenticatedCode);
  const [routeProt, setRouteProt] = useState(getRouteProtectionCode);

  const [open, setOpen] = useState(openProp);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box sx={{
        m: "auto",
        textAlign: "center"
      }}
      >
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h2>AuthGuard</h2>
          <p>
            The following code snippet demonstrates how AuthGuard component is
            used.
            <br />
            Whenever you develop an Application there will be the need to
            restrict unauthenticated users to access certain pages/content.
            <br />
            This is the Guard that handles the permissions against each route,
            based on user roles. Users with different roles will have different
            privileges over the resources of the application.
            <br />
            You can copy the full source code to your src folder:
          </p>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={open ? code : intro} />
                <Tooltip
                  title={
                    !open ? "Show full source Code" : "Hide full Source Code"
                  }
                >
                  <IconButton
                    onClick={handleToggle}
                    sx={{
                      color: `${theme.palette.primary.main}`,
                      "&:hover": {
                        backgroundColor: `${theme.palette.secondary.main}`,
                        color: "primary.main",
                      },
                      height: 35,
                      width: 35,
                      mt: 1,
                    }}
                  >
                    {!open ? <ExpandMoreIcon /> : <KeyboardArrowUpIcon />}
                  </IconButton>
                </Tooltip>
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={open ? code : intro}
              onValueChange={
                open ? (_code) => setCode(_code) : (_intro) => setIntro(_intro)
              }
              highlight={
                open
                  ? (_code) => highlight(_code, languages.js)
                  : (_intro) => highlight(_intro, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Typography
          color="textPrimary"
          sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
        >
          First we need to check if a user is registered or already
          authenticated (ie: Azure Authentication).
          <br />
          If not, and if the user does not have permission to see a certain
          content, he will be redirected to another page, it can be a login
          page, error page, etc.
          <br />
          We can define a page/content as accessible to any user, regardless of
          the type of role they have. For this to be possible, we just need to
          pass the <b>componentPermission</b> prop.
        </Typography>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={authenticated} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={authenticated}
              onValueChange={
                (_authenticated) =>
                  setAuthenticated(_authenticated)
              }
              highlight={
                (_authenticated) =>
                  highlight(_authenticated, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <p>Then we have to protect our Routes:</p>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={routeProt} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={routeProt}
              onValueChange={(_routeProt) => setRouteProt(_routeProt)}
              highlight={(_routeProt) => highlight(_routeProt, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          color="textPrimary"
          sx={{ m: "auto", textAlign: "justify", maxWidth: "80%" }}
        >
          <p>AuthGuard receives the following props:</p>
          <ul>
            <li>
              <b>children</b> - Content to be displayed (Protected content);
            </li>
            <li>
              <b>roles</b> - Codes that define the type of permissions. Each
              user is assigned one or more roles;
            </li>
            <li>
              <b>componentPermission</b> - Even if a user has a role that
              restricts him from viewing a certain piece of content, we can make
              that same content accessible by just passing it the
              componentPermission prop. In this way, we manage to continue to
              protect our routes, and at the same time define that a given route
              can be accessed by any user, regardless of the role;
            </li>
            <li>
              <b>moduleID</b>
            </li>
          </ul>
        </Box>
      </Box>
      <Box sx={{ textAlign: "center", mt: 5 }}>
        <p>
          This component also uses a useContext.
          <br />
          Please see the page below for more information:
          <br />
        </p>
        <Link
          className={linkStyle}
          underline="none"
          href="/Components/useAuthContext"
        >
          useAuth Context
        </Link>
      </Box>
      <ChangeLogEntry
        version="V1.0.0"
        changeLog={ChangeLog()}
      />
    </Box>
  );
};
AuthG.propTypes = {
  open: PropTypes.bool,
};
export default AuthG;

const ChangeLog = () => {
  const theme = useTheme();
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content: "Imported AuthGuard Component.",
      className: chips.green,
    },
  ];

  return changeLog;
};
