﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hangfire.Console.Extensions;
using Hangfire.Console.Progress;
using Microsoft.Extensions.Logging;
using RnD.BackEnd.Domain.Entities.Weather;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.UnitsOfWork;
using RnD.BackEnd.Domain.Interfaces.Services;

namespace RnD.BackEnd.Service.Services
{
    /// <summary>
    /// Jobs service class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Services.IJobsService" />
    public class JobsService : IJobsService
    {
        private readonly ILogger<JobsService> _logger;
        private readonly IProgressBarFactory _progressBarFactory;
        private readonly IDatabaseUnitOfWork _databaseUnitOfWork;

        /// <summary>
        /// Initializes a new instance of the <see cref="JobsService" /> class.
        /// </summary>
        /// <param name="databaseUnitOfWork">The database unit of work.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="progressBarFactory">The progress bar factory.</param>
        public JobsService(IDatabaseUnitOfWork databaseUnitOfWork, ILogger<JobsService> logger, IProgressBarFactory progressBarFactory)
        {
            _databaseUnitOfWork = databaseUnitOfWork;
            _logger = logger;
            _progressBarFactory = progressBarFactory;
        }

        /// <summary>
        /// Gets the weather forecast.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The weather forecast.
        /// </returns>
        public async Task OutputWeatherForecast()
        {
            _logger.LogInformation("Getting all weather forecasts...");

            IList<Weather> forecasts = (await _databaseUnitOfWork.WeatherRepository.ListAsync(
                id: Guid.Empty,
                location: null,
                itemsPerPage: null,
                page: null,
                cancellationToken: CancellationToken.None))?.Value;

            if (forecasts == null)
            {
                _logger.LogWarning("No weather forecasts found in database.");
            }
            else
            {
                _logger.LogInformation($"Found {forecasts.Count} forecasts.");

                IProgressBar progressBar = _progressBarFactory.Create("Progress");

                for (int i = 0; i < forecasts.Count; i++)
                {
                    double progress = i == 0 ? (0.1 * 100) : (double)i / forecasts.Count * 100;
                    progressBar.SetValue(progress);

                    // This is just to simulate and view progress bar movement
                    Thread.Sleep(1000);

                    var forecast = forecasts[i];

                    _logger.LogInformation("");
                    _logger.LogInformation($"Id: {forecast.Id}");
                    _logger.LogInformation($"Location: {forecast.Location}");
                    _logger.LogInformation($"Temperature: {forecast.Temperature}");
                }

                progressBar.SetValue(100);
            }

            _logger.LogInformation("");
            _logger.LogInformation("Finished job execution!");
        }
    }
}
