import { useState } from "react";
import {
  <PERSON>,
  Divider,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>H<PERSON><PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  IconButton,
} from "@mui/material";
import { useTheme } from '@emotion/react';
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import CustomAutocompleteTree from "../../../Components/CustomAutocompleteTree";
import { useTranslation } from "react-i18next";
import {
  ExpandMore as ExpandMoreIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from "@mui/icons-material";
import { PropTypes } from "prop-types";
import getIntro from "./mocks/intro";
import getExample from "./mocks/example";
import getCode from "./mocks/code";
import getApply from "./mocks/application";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import { styled } from "@mui/system";

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh",
})

const options = [
  {
    text: "Parent 1",
    id: "1",
    parentid: null,
  },
  {
    text: "Child 1.1",
    id: "1.1",
    parentid: "1",
  },
  {
    text: "Child *******",
    id: "*******",
    parentid: "3.2.1",
  },
  {
    text: "Parent 2",
    id: "2",
    parentid: null,
  },
  {
    text: "Parent 3",
    id: "3",
    parentid: null,
  },
  {
    text: "Child 3.1",
    id: "3.1",
    parentid: "3",
  },
  {
    text: "Child 3.2",
    id: "3.2",
    parentid: "3",
  },
  {
    text: "Child 3.2.1",
    id: "3.2.1",
    parentid: "3.2",
  },
];

export default function AutocompleteTreeComponent({ open: openProp }) {
  const theme = useTheme();
  const { t } = useTranslation();
  const [intro, setIntro] = useState(getIntro);
  const [example, setExample] = useState(getExample);
  const [code, setCode] = useState(getCode);
  const [apply, setApply] = useState(getApply);

  const [open, setOpen] = useState(openProp);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box
        sx={{
          m: "auto",
          textAlign: "center"
        }}
      >
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h2>Autocomplete Tree</h2>
          <p>
            This component allows us to have a Select with categories and
            subcategories.
            <br />
            The following code snippet demonstrates how Autocomplete Tree
            component is displayed:
          </p>
        </Box>
        <Box sx={{ maxWidth: "50%", m: "auto", mb: 2, marginTop: 2 }}>
          <CustomAutocompleteTree
            label={t("common:tableActions.search")}
            placeholder={t("common:tableActions.option")}
            text="text"
            id="id"
            data={options}
            multiple
            parentid="parentid"
          />
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={open ? example : intro} />
                <Tooltip
                  title={
                    !open ? "Show full source Code" : "Hide full Source Code"
                  }
                >
                  <IconButton
                    name="index"
                    onClick={handleToggle}
                    sx={{
                      color: `${theme.palette.primary.main}`,
                      "&:hover": {
                        backgroundColor: `${theme.palette.secondary.main}`,
                        color: "primary.main",
                      },
                      height: 35,
                      width: 35,
                      mt: 1,
                    }}
                  >
                    {!open ? <ExpandMoreIcon /> : <KeyboardArrowUpIcon />}
                  </IconButton>
                </Tooltip>
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={open ? example : intro}
              onValueChange={
                open
                  ? (_example) => setExample(_example)
                  : (_intro) => setIntro(_intro)
              }
              highlight={
                open
                  ? (_example) => highlight(_example, languages.js)
                  : (_intro) => highlight(_intro, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Typography
          color="textPrimary"
          sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
        >
          To get started copy the following code to your src folder:
        </Typography>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={code} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={code}
              onValueChange={(_code) => setCode(_code)}
              highlight={(_code) => highlight(_code, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Typography
          color="textPrimary"
          sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
        >
          Then, you will have to import the component as below:
        </Typography>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={apply} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={apply}
              onValueChange={(_apply) => setApply(_apply)}
              highlight={(_apply) => highlight(_apply, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>

        <Box
          sx={{
            m: "auto",
            textAlign: "justify",
            maxWidth: "80%",
            color: "textPrimary",
          }}
        >
          <p>Custom Autocomplete Tree receives the following props:</p>
          <ul>
            <li>
              <b>data</b> - Data that we want to be displayed;;
            </li>
            <li>
              <b>defaultSelection;</b> - In case we want to set a default value;
            </li>
            <li>
              <b>label</b> - Autocomplete Title;
            </li>
            <li>
              <b>parentid</b> - When there are children;{" "}
            </li>
            <li>
              <b>multiple</b> - Select more than one option;
            </li>
            <li>
              <b>text</b> - Displayed options;
            </li>
            <li>
              <b>placeholder</b> - Short hint that is displayed in the input
              field before the user enters a value.
            </li>
            <li>
              <b>onAppliedValues</b> - Returns the result of the selection;
            </li>
            <li>
              <b>maxChips</b> - Maximum number of chips;
            </li>
            <li>
              <b>treeSelection</b> - When selecting the parent, we automatically
              select its children.
            </li>
          </ul>
        </Box>
      </Box>
      <ChangeLogEntry
        version="V1.0.0"
        changeLog={ChangeLog()}
      />
    </Box>
  );
}
AutocompleteTreeComponent.propTypes = {
  open: PropTypes.func,
};
const ChangeLog = () => {
  const theme = useTheme();
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content: "Imported Autocomplete Tree Component.",
      className: chips.green,
    },
  ];

  return changeLog;
};
