﻿using System.Collections.Generic;
using RnD.BackEnd.API.Model.Vehicle;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.VehicleController
{
    /// <summary>
    /// Vehicle create request example
    /// </summary>
    public class VehicleCreate : IMultipleExamplesProvider<CreateVehicleDto>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>The create request example</returns>
        public IEnumerable<SwaggerExample<CreateVehicleDto>> GetExamples()
        {
            yield return SwaggerExample.Create("Ford", new CreateVehicleDto
            {
                Brand = "Ford",
                BrandId = "0001",
                FuelType = "DIESEL",
                ModelName = "Fiesta",
                Version = "567",
                Year = 2022
            });

            yield return SwaggerExample.Create("Honda", new CreateVehicleDto
            {
                Brand = "Honda",
                BrandId = "0004",
                FuelType = "PETROL",
                ModelName = "Civic",
                Version = "123",
                Year = 2022
            });
        }
    }
}
