{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj", "projectName": "RnD.BackEnd.Domain", "projectPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\RnD.BackEnd.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\PortalCloudServices\\Backend\\RnD.BackEnd.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Azure.Identity": {"target": "Package", "version": "[1.13.2, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.AspNetCore.JsonPatch": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Graph": {"target": "Package", "version": "[5.75.0, )"}, "Microsoft.Graph.Auth": {"target": "Package", "version": "[1.0.0-preview.7, )"}, "Microsoft.Graph.Core": {"target": "Package", "version": "[3.2.4, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.70.0, )"}, "Microsoft.Kiota.Authentication.Azure": {"target": "Package", "version": "[1.17.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}