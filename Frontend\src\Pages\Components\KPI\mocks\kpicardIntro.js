const getKPICardIntro = () => {
    return `    
const CardKPI = ({
    title,
    value,
    unit = "",
    description,
    extraContent
}) => {
};
CardKPI.propTypes = {
    title: PropTypes.string,
    value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number
    ]),
    unit: PropTypes.string,
    description: PropTypes.string,
    extraContent: PropTypes.object
}

export default CardKPI;
    `;
}

export default getKPICardIntro;
