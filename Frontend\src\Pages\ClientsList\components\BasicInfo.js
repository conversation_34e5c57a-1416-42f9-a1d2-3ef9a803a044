import React from 'react';
import { Box, Grid, Typography, TextField, Button, CircularProgress } from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import { useTranslation } from 'react-i18next';

const BasicInfo = ({ 
  client, 
  editMode, 
  editedClient, 
  handleChange, 
  handleSave, 
  saving, 
  hasChanges, 
  error 
}) => {
  const { t } = useTranslation();

  return (
    <Box sx={{ mt: 2 }}>
      {editMode ? (
        <Box component="form" sx={{ mt: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label={t('common:form.name')}
                name="Name"
                value={editedClient.Name}
                onChange={handleChange}
                variant="outlined"
                error={!editedClient.Name && !!error}
                helperText={!editedClient.Name && error ? "Nome é obrigatório" : ""}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="Contato"
                name="Contact"
                value={editedClient.Contact}
                onChange={handleChange}
                variant="outlined"
                helperText="Este campo é obrigatório. Se não houver contato, será preenchido com '0'."
              />
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSave}
              disabled={saving || !hasChanges()}
              startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
              size="large"
            >
              {saving ? 'A Salvar...' : 'Salvar Alterações'}
            </Button>
          </Box>
        </Box>
      ) : (
        <Box sx={{ mt: 2 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle1" fontWeight="bold">
                {t('common:form.name')}
              </Typography>
              <Typography variant="body1" paragraph>
                {client.Name}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle1" fontWeight="bold">
                {t('common:tableHeaders.basicInfo')}
              </Typography>
              <Typography variant="body1" paragraph>
                {client.Contact}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default BasicInfo; 