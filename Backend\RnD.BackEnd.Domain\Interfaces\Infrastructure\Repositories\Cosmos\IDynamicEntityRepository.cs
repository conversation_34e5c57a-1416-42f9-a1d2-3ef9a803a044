﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using RnD.BackEnd.Domain.DataModels;

namespace RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos
{
    /// <summary>
    /// Dynamic entity repository interface
    /// </summary>
    public interface IDynamicEntityRepository
    {
        /// <summary>
        /// The name of the partition key field.
        /// </summary>
        string PartitionKeyFieldName { get; }

        /// <summary>
        /// The name of the identifier field.
        /// </value>
        string IdFieldName { get; }

        /// <summary>
        /// Adds the specified entity.
        /// </summary>
        /// <param name="entity">The entity.</param>
        /// <returns>
        /// The entity added.
        /// </returns>
        Task<ResponseModel<Dictionary<string, object>>> AddAsync(Dictionary<string, object> entity);

        /// <summary>
        /// Updates the specified entity.
        /// </summary>
        /// <param name="entity">The entity.</param>
        /// <param name="etag">The etag.</param>
        /// <returns>
        /// The entity updated.
        /// </returns>
        Task<ResponseModel<Dictionary<string, object>>> UpdateAsync(Dictionary<string, object> entity, string etag);

        /// <summary>
        /// Deletes the entity.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// True if operation succeeded, False otherwise.
        /// </returns>
        Task<bool> DeleteAsync(string id);

        /// <summary>
        /// Gets the specified entity.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The entity
        /// </returns>
        Task<ResponseModel<Dictionary<string, object>>> GetAsync(string id);

        /// <summary>
        /// Lists the entities.
        /// </summary>
        /// <param name="pageNumber">The page number.</param>
        /// <param name="maxItems">The maximum items.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// The list of entities.
        /// </returns>
        Task<ResponseModel<List<Dictionary<string, object>>>> ListAsync(
            int? pageNumber,
            int? maxItems,
            string sortField,
            bool sortAscending,
            string continuationToken,
            CancellationToken cancellationToken);
    }
}
