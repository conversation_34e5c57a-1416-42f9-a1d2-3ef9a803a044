import { styled } from '@mui/system';
import { Typography, Checkbox } from '@mui/material';
import PropTypes from 'prop-types';

const CustomInlineCheckbox = styled("div")({
    display: "flex",
    alignItems: "center",
    marginRight: "1em"
});

const CustomCheckbox = ({ text, checked, onChange, disabled }) => {
    return (
        <CustomInlineCheckbox>
            <Typography>{text}</Typography>
            <Checkbox
                checked={checked}
                onChange={onChange}
                disabled={disabled}
            />

        </CustomInlineCheckbox>
    );
}

CustomCheckbox.propTypes = {
    text: PropTypes.string,
    checked: PropTypes.bool,
    onChange: PropTypes.func,
    disabled: PropTypes.bool
};

export default CustomCheckbox;
