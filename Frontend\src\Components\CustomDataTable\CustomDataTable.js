import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { DataGrid } from '@mui/x-data-grid';
import {
  Typography,
  IconButton,
  Autocomplete,
  TextField,
  Tooltip,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { styled } from '@mui/system';

const StyledContainer = styled('div')({
  '& .MuiDataGrid-columnsContainer': {
    background: "linear-gradient(0deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9)), #C2C9D1"
  },
  flexGrow: 1
})

const dateFormatted = (params) => {
  const valueFormatted = params && params.value ? new Date(params.value) : new Date();
  return `${format(valueFormatted, 'dd/MM/yyyy')}`;
};

const autocompleteFormatted = (params) => {
  let valueFormatted = '';

  if (params && params.value && !params.colDef.multiple) {
    valueFormatted = params && params.value ? params.value.text : '';
  } else if (params && params.value && params.colDef.multiple) {
    valueFormatted = params.value.map((x) => x.text).join(',');
  }

  return `${valueFormatted}`;
};

const AutocompleteEditInputCell = (props) => {
  const { id, value, api, field, colDef } = props;
  const handleChange = (event, data) => {
    api.setEditCellValue({ id, field, value: data }, event);
  };

  return (
    <Autocomplete
      value={value}
      onChange={handleChange}
      options={colDef.collection}
      fullWidth
      multiple={colDef.multiple}
      getOptionLabel={(option) => option.text}
      renderInput={(parms) => (
        <TextField
          fullWidth
          name={id}
          variant="outlined"
          onChange={(e, v) => handleChange(e, v)}
          {...parms}
        />
      )}
    />
  );
};

AutocompleteEditInputCell.propTypes = {
  api: PropTypes.any.isRequired,
  field: PropTypes.string.isRequired,
  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  value: PropTypes.oneOfType([
    PropTypes.instanceOf(Date),
    PropTypes.number,
    PropTypes.object,
    PropTypes.string,
    PropTypes.bool,
  ]),
  colDef: PropTypes.object.isRequired
};

const renderAutocompleteEditInputCell = (params) => <AutocompleteEditInputCell {...params} />;

const CustomDataTable = React.memo((props) => {
  const { title, columns, setFieldValue, ElementID, initialData, dynamicCollection, hidePagination, singleRow, action, viewOnly, onUpdateData, allowSelection, onSelectedData } = props;
  const [data, setData] = useState(initialData);
  const [selectedRows, handleSelectedRows] = useState([]);
  useEffect(() => {
    setData(initialData);
  }, [initialData]);
  const handleUpdateData = (model) => {
    if (Object.entries(model).length > 0) {
      const clonedData = [...data];

      const cellIndex = Object.keys(model)[0];

      Object.keys(model[cellIndex]).forEach(cell => {
        clonedData[Number(cellIndex) - 1][cell] = model[cellIndex][cell].value;
      });

      setData(clonedData);

      if (onUpdateData) {
        onUpdateData(clonedData);
      }

      if (setFieldValue) {
        setFieldValue(ElementID, clonedData);
      }
    }
  };

  function handleFileUpload(event, field, cell, column) {
    const file = event.target.files[0];
    const fileName = file.name;
    const reader = new FileReader();

    reader.readAsBinaryString(file);

    reader.onload = () => {
      const fileB64 = btoa(reader.result);
      cell.value = {
        document: fileB64,
        documentName: fileName
      };
      handleUpdateData(cell);
      if (setFieldValue) {
        setFieldValue(`stepsList[${column.stepIndex}].moduleList[${column.moduleIndex}].templateFilename`, fileName);
        setFieldValue(`stepsList[${column.stepIndex}].moduleList[${column.moduleIndex}].fileB64`, fileB64);
        setFieldValue(`stepsList[${column.stepIndex}].moduleList[${column.moduleIndex}].updatedDocument`, true);
      }
      //  setFieldValue(ElementID + `[${field}]`, btoa(reader.result));
    };

    reader.onerror = () => {
      console.log('error reading file');
    };
  }

  const getHeaderData = () => {
    const headerData = [];
    columns.forEach((column) => {
      switch (column.ElementType) {
        case 'NumberField':
          headerData.push({
            field: column.ElementID,
            headerName: column.ElementLabel,
            type: 'number',
            flex: 1,
            minWidth: column.Width,
            editable: column.editable === false ? column.editable : !viewOnly,
          });
          break;
        case 'CheckBoxField':
          headerData.push({
            field: column.ElementID,
            headerName: column.ElementLabel,
            type: 'boolean',
            flex: 1,
            minWidth: column.Width,
            editable: !viewOnly,
          });
          break;
        case 'DateField':
          headerData.push({
            field: column.ElementID,
            headerName: column.ElementLabel,
            type: 'date',
            valueFormatter: dateFormatted,
            flex: 1,
            minWidth: column.Width,
            editable: !viewOnly,
          });
          break;
        case 'DynamicCollection':
          headerData.push({
            field: column.ElementID,
            headerName: column.ElementLabel,
            collection: column.Collection || dynamicCollection[column.ElementDynamicCode].Collection,
            renderEditCell: renderAutocompleteEditInputCell,
            multiple: column.Multiple,
            valueFormatter: autocompleteFormatted,
            type: 'object',
            flex: 1,
            minWidth: column.Width,
            group: column.Grouped,
            editable: !viewOnly,
          });
          break;
        case 'FileInput':

          headerData.push({
            field: column.ElementID,
            headerName: column.ElementLabel,
            renderCell: (cell) => (
              <div>
                {column.fileName === undefined || column.fileName === null ?
                  <input
                    type="file"
                    accept=".doc, .docx"
                    onChange={(event) => handleFileUpload(event, "document", cell, column)}
                  />

                  : <div> <span onClick={() => action(column.filePath, column.fileName)}> {column.fileName}</span> <DeleteIcon onClick={() => setFieldValue(`stepsList[${column.stepIndex}].moduleList[${column.moduleIndex}].templateFilename`, undefined)} /> </div>}
              </div>),
            type: 'object',
            flex: 1,
            minWidth: column.Width,
            group: column.Grouped,
            editable: !viewOnly,
          });
          break;
        default:
          headerData.push({
            field: column.ElementID,
            headerName: column.ElementLabel,
            type: 'string',
            flex: 1,
            minWidth: column.Width,
            editable: column.editable === false ? column.editable : !viewOnly,
          });
          break;
      }
    });
    return headerData;
  };

  const headers = getHeaderData();

  const handleAddLine = () => {
    const newObj = {
      id: (data.length + 1).toString()
    };

    headers.forEach((column) => {
      newObj[column.ElementID] = column.type === 'DynamicCollection' ? null : '';
    });

    const clonedData = [...data];

    clonedData.push(newObj);

    setData(clonedData);
  };

  const handleRemoveSelectedLines = () => {
    const clonedData = [...data];

    selectedRows.forEach((index) => {
      clonedData.splice(clonedData.findIndex(x => x.id === index), 1);
    });

    setData(clonedData);
    if (onUpdateData) {
      onUpdateData(clonedData);
    }
    if (setFieldValue) {
      setFieldValue(ElementID, clonedData);
    }
  };

  return (
    <>
      {title && (
        <Typography
          color="textPrimary"
          gutterBottom
          variant="subtitle2"
        >
          {title}
        </Typography>
      )}
      {!viewOnly && (
        !singleRow && (
          <>
            <Tooltip title="Adicionar">
              <IconButton
                onClick={handleAddLine}
              >
                <AddIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Remover">
              <IconButton
                onClick={handleRemoveSelectedLines}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </>
        )
      )}
      <StyledContainer>
        <DataGrid
          experimentalFeatures={{ newEditingApi: true }}
          autoHeight
          disableColumnMenu
          checkboxSelection={(!singleRow && !viewOnly) || allowSelection}
          disableSelectionOnClick
          hideFooterPagination={hidePagination}
          rows={data}
          columns={headers}
          // editMode="row"
          isRowSelectable={(params) => params.row.rowSelectable === undefined || params.row.rowSelectable}
          onSelectionModelChange={(newSelection) => {
            if (onSelectedData) {
              const dataSelected = [];
              newSelection.forEach((index) => {
                dataSelected.push(data[data.findIndex(x => x.id === index)]);
              });
              onSelectedData(dataSelected);
            }
            handleSelectedRows(newSelection);
          }}
          onEditRowsModelChange={(model) => { handleUpdateData(model); }}
        />
      </StyledContainer>
    </>
  );
});

CustomDataTable.propTypes = {
  title: PropTypes.string,
  columns: PropTypes.array.isRequired,
  setFieldValue: PropTypes.func,
  ElementID: PropTypes.string,
  initialData: PropTypes.array,
  dynamicCollection: PropTypes.object.isRequired,
  hidePagination: PropTypes.bool,
  singleRow: PropTypes.bool,
  action: PropTypes.func,
  viewOnly: PropTypes.bool,
  onUpdateData: PropTypes.func,
  allowSelection: PropTypes.bool,
  onSelectedData: PropTypes.func
};

export default CustomDataTable;
