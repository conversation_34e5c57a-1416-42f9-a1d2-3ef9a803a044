﻿using System;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using RnD.BackEnd.Domain.BusinessMessages;
using RnD.BackEnd.Domain.Entities.DataEntry;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Extensions;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Managers;
using RnD.BackEnd.Domain.Interfaces.Services;
using RnD.BackEnd.Domain.Settings;
using RnD.BackEnd.Domain.Settings.APIConfiguration;

namespace RnD.BackEnd.Service.Services
{
    /// <summary>
    /// Class for Data Entry connector service example.
    /// </summary>
    /// <seealso cref="IDataEntryService" />
    public class DataEntryService : IDataEntryService
    {
        private readonly DataEntrySettings _settings;
        private readonly IApiConnectorManager _apiManager;

        private ServiceOutput<DataEntryAuthenticationResponse> _dataEntryToken;

        /// <summary>
        /// Initializes a new instance of the <see cref="DataEntryService" /> class.
        /// </summary>
        /// <param name="settings">The settings.</param>
        /// <param name="apiManager">The API manager.</param>
        public DataEntryService(IOptions<AppSettings> settings, IApiConnectorManager apiManager)
        {
            _settings = settings.Value.DataEntrySettings;
            _apiManager = apiManager;
        }

        /// <summary>
        /// Gets the data entry entities.
        /// </summary>
        /// <returns>
        /// The list of entities.
        /// </returns>
        public async Task<ServiceOutput<DataEntryEntitiesList>> GetDataEntryEntities()
        {
            ServiceOutput<DataEntryEntitiesList> output = new ServiceOutput<DataEntryEntitiesList>();

            // Validate and get token
            await GetAPIToken();

            // If token is not OK, return output with error HttpCode
            if (_dataEntryToken.Code != HttpStatusCode.OK)
            {
                output.CustomErrorCode(_dataEntryToken.Code, $"{CommonMessages.ErrorGettingToken}: {_dataEntryToken.Description}");
            }
            // If token is ok, get the entities
            else
            {
                output = await _apiManager.GetAsync<DataEntryEntitiesList>(_settings.BaseUrl, _settings.ListEntitiesEndpoint, _dataEntryToken.Value.AccessToken);
            }

            return output;
        }

        #region Private methods

        /// <summary>
        /// Gets the API token, checking if the current token is already valid.
        /// If current token is not valid, get a new token.
        /// </summary>
        private async Task GetAPIToken()
        {
            DateTimeOffset? tokenExpirationTime = null;

            if (_dataEntryToken != null && _dataEntryToken.Code == HttpStatusCode.OK)
            {
                tokenExpirationTime = DateTimeOffset.FromUnixTimeSeconds(_dataEntryToken.Value.AccessTokenExpiration);
            }

            if (_dataEntryToken == null
                || _dataEntryToken.Code == HttpStatusCode.Unauthorized
                || tokenExpirationTime.HasValue && DateTimeOffset.Now > tokenExpirationTime)
            {
                ApiUser user = new ApiUser()
                {
                    UserName = _settings.Username,
                    Password = _settings.Password
                };

                _dataEntryToken = await _apiManager.PostAsync<DataEntryAuthenticationResponse>(_settings.BaseUrl, _settings.AuthenticationEndpoint, user);
            }
        }

        #endregion
    }
}
