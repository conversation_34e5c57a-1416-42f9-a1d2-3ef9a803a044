﻿using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController
{
    /// <summary>
    /// Add Dynamic Entity response example for 200
    /// </summary>
    public class AddDynamicEntity200 : IExamplesProvider<ApiOutput<Dictionary<string, object>>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a Add~Dynamic Entity 200 response.
        /// </returns>
        public ApiOutput<Dictionary<string, object>> GetExamples()
        {
            return new ApiOutput<Dictionary<string, object>>
            {
                Code = HttpStatusCode.OK,
                Value = new Dictionary<string, object>
                {
                    {"entityId", "dive1" },
                    {"companyId", "diveCenterPhi2"},
                    {"name", "Dive Center Koh Phi Phi"},
                    {"type", "Dive Center"},
                    {"review", 4.5},
                    {"numberOfReviews", 104},
                    {"location", "Koh Phi Phi Island"},
                    {"country", "Thailand"},
                    {"id", "dive1:9e21138f-21d6-4c58-a518-2d0cb896b395"},
                    {"_rid", "DMV6ANEfcywFAAAAAAAAAA=="},
                    {"_self", "dbs/DMV6AA==/colls/DMV6ANEfcyw=/docs/DMV6ANEfcywFAAAAAAAAAA==/"},
                    {"_etag", "\"5100a39d-0000-0700-0000-63f8fa130000\""},
                    {"_attachments", "attachments/"},
                    { "_ts", 1677261331}
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}