﻿using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RnD.BackEnd.API.Model.DataEntry;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.SwashbuckleExamples;
using RnD.BackEnd.API.SwashbuckleExamples.DataEntryController;
using RnD.BackEnd.Domain.Entities.DataEntry;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Interfaces.Services;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.Controllers
{
    /// <summary>
    /// Data entry controller that interacts with DataEntry API
    /// </summary>
    /// <seealso cref="Microsoft.AspNetCore.Mvc.ControllerBase" />
    [Produces("application/json")]
    [Route("api/[controller]")]
    [ApiController]
    public class DataEntryController : ControllerBase
    {
        private readonly IDataEntryService _dataEntryService;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="DataEntryController"/> class.
        /// </summary>
        /// <param name="dataEntryService">The data entry service.</param>
        /// <param name="mapper">The mapper.</param>
        public DataEntryController(IDataEntryService dataEntryService, IMapper mapper)
        {
            _dataEntryService = dataEntryService;
            _mapper = mapper;
        }

        /// <summary>
        /// Gets the data entry entities.
        /// </summary>
        /// <returns>
        /// The list of all data entry entities.
        /// </returns>
        /// <response code="200">Returns a List of entities</response>
        /// <response code="204">No content response</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [ProducesResponseType(typeof(ApiOutput<EntityListDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(GetEntities200))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> GetDataEntryEntities()
        {
            ServiceOutput<DataEntryEntitiesList> response = await _dataEntryService.GetDataEntryEntities();

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<DataEntryEntitiesList>, ApiOutput<EntityListDto>>(response));
        }
    }
}