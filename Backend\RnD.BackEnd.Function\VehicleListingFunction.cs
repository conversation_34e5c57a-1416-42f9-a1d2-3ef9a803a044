using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Managers;
using RnD.BackEnd.Function.Model.Vehicle;
using RnD.BackEnd.Licensing.Interfaces;
using RnD.BackEnd.Licensing.Settings;

namespace RnD.BackEnd.Function
{
    /// <summary>
    /// Azure Function example using Vehicle listing
    /// </summary>
    public class VehicleListingFunction
    {
        private readonly ILicenseValidationService _licenseValidationService;
        private readonly IApiConnectorManager _apiManager;
        private ILogger _log;

        /// <summary>
        /// Initializes a new instance of the <see cref="VehicleListingFunction" /> class.
        /// </summary>
        /// <param name="licenseValidationService">The license validation service.</param>
        /// <param name="apiManager">The API manager.</param>
        public VehicleListingFunction(ILicenseValidationService licenseValidationService, IApiConnectorManager apiManager)
        {
            _apiManager = apiManager;
            _licenseValidationService = licenseValidationService;
        }

        /// <summary>
        /// Runs the Azure Function.
        /// </summary>
        /// <param name="req">The req.</param>
        /// <param name="log">The log.</param>
        /// <returns></returns>
        [FunctionName("VehicleListing")]
        public async Task<IActionResult> Run(
            [HttpTrigger(AuthorizationLevel.Function, "get", "post", Route = null)] HttpRequest req,
            ILogger log)
        {
            _log = log;
            _log.LogInformation($"VehicleListing - function executed at: {DateTime.Now}");

            Stopwatch timer = new Stopwatch();
            timer.Start();

            if (await _licenseValidationService.ValidateLicenseAsync(Environment.GetEnvironmentVariable("WEBSITE_HOSTNAME"), ValidationType.DOMAIN))
            {
                string apiUrl = Environment.GetEnvironmentVariable("API_URL");
                string vehicleListEndpoint = Environment.GetEnvironmentVariable("Endpoint_ListAsync");

                ServiceOutput<IList<VehicleDto>> output = await _apiManager.GetAsync<IList<VehicleDto>>(apiUrl, vehicleListEndpoint);

                if (output != null && output.Code == HttpStatusCode.OK)
                {
                    _log.LogInformation($"VehicleListing - Number of Results : |{output.Value.Count}|");

                    if (output.Value.Count > 0)
                    {
                        foreach (VehicleDto vehicle in output.Value)
                        {
                            _log.LogInformation($"VehicleListing | DEBUG - Vehicles Info " +
                                $"\n > id : |{vehicle.Id}|" +
                                $"\n > brand : {vehicle.Brand}" +
                                $"\n > model : {vehicle.ModelName}" +
                                $"\n > version : {vehicle.Version}" +
                                $"\n > fuel type : {vehicle.FuelType}" +
                                $"\n > year : {vehicle.Year}");
                        }
                    }

                    timer.Stop();
                    _log.LogInformation($"VehicleListing - END, Duration : |{timer.Elapsed}| >>> {timer.ElapsedMilliseconds} ms");

                    return new OkObjectResult("Success");
                }
                else
                {
                    _log.LogError("Response results is NULL");

                    if (output.Code != HttpStatusCode.OK)
                    {
                        _log.LogError($"Code: {output.Code}");
                        _log.LogError($"Description: {output.Description}");

                        if (output.ExceptionMessages.HasMessages)
                        {
                            foreach (string message in output.ExceptionMessages.Messages)
                            {
                                _log.LogError(message);
                            }
                        }

                    }

                    timer.Stop();
                    _log.LogInformation($"VehicleListing - END, Duration : |{timer.Elapsed}| >>> {timer.ElapsedMilliseconds} ms");
                    return new OkObjectResult("fail");
                }
            }
            else
            {
                timer.Stop();
                _log.LogError("Invalid license");
                _log.LogInformation($"VehicleListing - END, Duration : |{timer.Elapsed}| >>> {timer.ElapsedMilliseconds} ms");
                return new ObjectResult("Fail");
            }
        }
    }
}
