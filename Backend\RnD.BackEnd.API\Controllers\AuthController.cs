using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.Graph;
using Microsoft.Graph.Authentication;
using Microsoft.Graph.Models;
using Microsoft.Identity.Client;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Authentication;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using RnD.BackEnd.Domain.Interfaces.Services;
using RnD.BackEnd.Domain.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace RnD.BackEnd.API.Controllers
{
    [Route("api/auth")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly AzureAd _azureAdOptions;
        private readonly IConfidentialClientApplication _app;
        private readonly IUserService _userService;

        public AuthController(
            ApplicationDbContext context,
            IOptions<AzureAd> azureAdOptions,
            IUserService userService)
        {
            _context = context;
            _azureAdOptions = azureAdOptions.Value;
            _userService = userService;
            
            // Configurar a aplicação cliente confidencial para chamar o Microsoft Graph
            _app = ConfidentialClientApplicationBuilder
                .Create(_azureAdOptions.ClientId)
                .WithClientSecret(_azureAdOptions.ClientSecret)
                .WithAuthority(new Uri($"{_azureAdOptions.Instance}{_azureAdOptions.TenantId}"))
                .Build();
        }

        [HttpGet("me")]
        [Authorize(Policy = "AzureAD")]
        public async Task<ActionResult<Models.User>> GetCurrentUser()
        {
            try
            {
                // Obter o ID do Azure AD do token
                var azureAdId = User.FindFirstValue("http://schemas.microsoft.com/identity/claims/objectidentifier") ?? 
                                User.FindFirstValue("oid");

                if (string.IsNullOrEmpty(azureAdId))
                {
                    return BadRequest("Não foi possível obter o ID do Azure AD do token.");
                }

                // Verificar se o user já existe no banco de dados
                var existingUser = await _userService.GetUserByAzureIdAsync(azureAdId) as Models.User;
                
                if (existingUser != null)
                {
                    return existingUser;
                }

                // Se não existir, obter informações detalhadas do user do Microsoft Graph
                var tokenResult = await _app.AcquireTokenForClient(
                    new[] { _azureAdOptions.GraphDefaultScope })
                    .ExecuteAsync();

                // Criar o cliente Graph com o token de acesso
                var graphClient = new GraphServiceClient(
                    new BaseBearerTokenAuthenticationProvider(
                        new StaticAccessTokenProvider(tokenResult.AccessToken)));

                // Obter dados do user do Graph API
                var graphUser = await graphClient.Users[azureAdId].GetAsync();

                // Criar novo user no banco de dados
                var newUser = await _userService.CreateUserAsync(
                    azureAdId,
                    graphUser.DisplayName,
                    graphUser.Mail ?? graphUser.UserPrincipalName
                ) as Models.User;
                
                // Atribuir permissões padrão ao user
                await _userService.AssignDefaultPermissionsAsync(newUser);

                return newUser;
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao obter ou criar user: {ex.Message}");
            }
        }
        
        [HttpGet("permissions")]
        [Authorize(Policy = "AzureAD")]
        public async Task<ActionResult<object>> GetUserPermissions()
        {
            try
            {
                // Obter o ID do Azure AD do token
                var azureAdId = User.FindFirstValue("http://schemas.microsoft.com/identity/claims/objectidentifier") ?? 
                                User.FindFirstValue("oid");

                if (string.IsNullOrEmpty(azureAdId))
                {
                    return BadRequest("Não foi possível obter o ID do Azure AD do token.");
                }

                // Verificar se o user existe no banco de dados
                var user = await _userService.GetUserByAzureIdAsync(azureAdId) as Models.User;
                
                if (user == null)
                {
                    return NotFound("user não encontrado.");
                }

                // Obter permissões do user
                var permissions = await _userService.GetUserPermissionsAsync(user.UserID);

                return new { UserId = user.UserID, Username = user.Username, Permissions = permissions };
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao obter permissões do user: {ex.Message}");
            }
        }

        [HttpPost("register")]
        [Authorize(Policy = "AzureAD")]
        public async Task<ActionResult<object>> RegisterUser()
        {
            try
            {
                // Obter o ID do Azure AD do token
                var azureAdId = User.FindFirstValue("http://schemas.microsoft.com/identity/claims/objectidentifier") ?? 
                                User.FindFirstValue("oid");

                if (string.IsNullOrEmpty(azureAdId))
                {
                    return BadRequest("Não foi possível obter o ID do Azure AD do token.");
                }

                // Verificar se o user já existe no banco de dados
                var existingUser = await _userService.GetUserByAzureIdAsync(azureAdId) as Models.User;
                
                if (existingUser != null)
                {
                    return Ok(new { 
                        Message = "user já existe no sistema", 
                        User = existingUser
                    });
                }

                // Se não existir, obter informações das claims do token
                var name = User.FindFirstValue("name") ?? 
                           User.FindFirstValue(ClaimTypes.Name) ?? 
                           User.Identity.Name ?? 
                           "user";
                
                var email = User.FindFirstValue("preferred_username") ?? 
                            User.FindFirstValue("upn") ?? 
                            User.FindFirstValue(ClaimTypes.Email) ?? 
                            "<EMAIL>";

                // Criar novo user no banco de dados
                var newUser = await _userService.CreateUserAsync(
                    azureAdId,
                    name,
                    email
                ) as Models.User;
                
                // Atribuir permissões padrão ao user
                await _userService.AssignDefaultPermissionsAsync(newUser);

                return Ok(new { 
                    Message = "user registrado com sucesso", 
                    User = newUser 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao registar user: {ex.Message}");
            }
        }

        [HttpGet("azure-users-debug")]
        [Authorize(Policy = "AzureAD")]
        public async Task<ActionResult<object>> GetAzureUsersDebug()
        {
            try
            {
                // Obter token para o Microsoft Graph API
                var tokenResult = await _app.AcquireTokenForClient(
                    new[] { _azureAdOptions.GraphDefaultScope })
                    .ExecuteAsync();

                var diagnosticInfo = new
                {
                    TokenInfo = new 
                    { 
                        Acquired = !string.IsNullOrEmpty(tokenResult?.AccessToken),
                        TokenFirstChars = tokenResult?.AccessToken != null ? 
                            tokenResult.AccessToken.Substring(0, Math.Min(20, tokenResult.AccessToken.Length)) + "..." : null,
                        TokenLength = tokenResult?.AccessToken?.Length ?? 0,
                        Scope = _azureAdOptions.GraphDefaultScope,
                        Expires = tokenResult?.ExpiresOn.ToString()
                    },
                    GraphConfig = new
                    {
                        ClientId = _azureAdOptions.ClientId,
                        TenantId = _azureAdOptions.TenantId,
                        Authority = $"{_azureAdOptions.Instance}{_azureAdOptions.TenantId}",
                        GraphEndpoint = _azureAdOptions.Graph
                    }
                };

                if (tokenResult == null || string.IsNullOrEmpty(tokenResult.AccessToken))
                {
                    return Ok(new
                    {
                        Error = "Não foi possível obter o token de acesso",
                        DiagnosticInfo = diagnosticInfo
                    });
                }

                // Criar o cliente Graph com o token de acesso
                var graphClient = new GraphServiceClient(
                    new BaseBearerTokenAuthenticationProvider(
                        new StaticAccessTokenProvider(tokenResult.AccessToken)));

                try
                {
                    // Primeiro tentar buscar apenas um usuário para verificar se a conexão funciona
                    var testUser = await graphClient.Users
                        .GetAsync(options => {
                            options.QueryParameters.Top = 1;
                        });

                    if (testUser?.Value == null || !testUser.Value.Any())
                    {
                        return Ok(new
                        {
                            Status = "Erro: Não foi possível buscar nenhum usuário",
                            DiagnosticInfo = diagnosticInfo
                        });
                    }

                    // Se conseguiu buscar um usuário, tentar com o filtro
                    var users = await graphClient.Users
                        .GetAsync(options => {
                            options.QueryParameters.Filter = "endsWith(mail, '@bi4all.pt') or endsWith(userPrincipalName, '@bi4all.pt')";
                            options.QueryParameters.Select = new[] { "id", "displayName", "mail", "userPrincipalName" };
                            options.QueryParameters.Top = 10; // Limitar a 10 usuários para teste
                        });

                    var totalUsers = testUser.Value.Count;
                    var filteredUsers = users?.Value?.Count ?? 0;

                    return Ok(new
                    {
                        Status = "Sucesso",
                        TotalUsersInDirectory = totalUsers,
                        FilteredUsers = filteredUsers,
                        FirstUserTest = testUser.Value.Select(u => new {
                            id = u.Id,
                            displayName = u.DisplayName,
                            mail = u.Mail,
                            userPrincipalName = u.UserPrincipalName
                        }).FirstOrDefault(),
                        FilteredUsersData = users?.Value?.Select(u => new {
                            id = u.Id,
                            displayName = u.DisplayName,
                            mail = u.Mail,
                            userPrincipalName = u.UserPrincipalName
                        }),
                        Filter = "endsWith(mail, '@bi4all.pt') or endsWith(userPrincipalName, '@bi4all.pt')",
                        DiagnosticInfo = diagnosticInfo
                    });
                }
                catch (Exception ex)
                {
                    return Ok(new
                    {
                        Status = "Erro ao chamar Microsoft Graph API",
                        Error = ex.Message,
                        InnerError = ex.InnerException?.Message,
                        DiagnosticInfo = diagnosticInfo
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao adquirir token: {ex.Message}");
            }
        }

        [HttpGet("azure-users")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<object>>> GetAzureUsers()
        {
            try
            {
                // Obter token para o Microsoft Graph API
                var tokenResult = await _app.AcquireTokenForClient(
                    new[] { _azureAdOptions.GraphDefaultScope })
                    .ExecuteAsync();

                if (tokenResult == null || string.IsNullOrEmpty(tokenResult.AccessToken))
                {
                    return BadRequest("Não foi possível obter o token de acesso para o Microsoft Graph");
                }

                // Criar o cliente Graph com o token de acesso
                var graphClient = new GraphServiceClient(
                    new BaseBearerTokenAuthenticationProvider(
                        new StaticAccessTokenProvider(tokenResult.AccessToken)));

                // Buscar usuários do Azure AD com domínio bi4all.pt
                var users = await graphClient.Users
                    .GetAsync(options => {
                        // Tentar primeiro sem filtro se houver problemas com o domínio
                        options.QueryParameters.Filter = "endsWith(mail, '@bi4all.pt') or endsWith(userPrincipalName, '@bi4all.pt')";
                        options.QueryParameters.Select = new[] { "id", "displayName", "mail", "userPrincipalName" };
                        options.QueryParameters.Top = 100; // Limitar a 100 usuários por vez
                    });

                if (users?.Value == null)
                {
                    return Ok(new List<object>());
                }

                // Filtrar no código os usuários que pertencem ao domínio bi4all.pt
                var result = users.Value
                    .Select(u => new {
                        id = u.Id,
                        displayName = u.DisplayName,
                        mail = u.Mail,
                        userPrincipalName = u.UserPrincipalName
                    });

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erro ao buscar usuários do Azure AD: {ex.Message}");
            }
        }

        // Endpoint sem autenticação para testes com dados reais
        [HttpGet("azure-users-public")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<object>>> GetAzureUsersPublic([FromQuery] string searchText = null, [FromQuery] string email = null, [FromQuery] int page = 0, [FromQuery] int pageSize = 100)
        {
            try
            {
                // Limitar o tamanho máximo para evitar sobrecarga
                if (pageSize > 100) pageSize = 100;
                
                // Obter token para o Microsoft Graph API
                var tokenResult = await _app.AcquireTokenForClient(
                    new[] { _azureAdOptions.GraphDefaultScope })
                    .ExecuteAsync();

                if (tokenResult == null || string.IsNullOrEmpty(tokenResult.AccessToken))
                {
                    return BadRequest("Não foi possível obter o token de acesso para o Microsoft Graph");
                }

                // Criar o cliente Graph com o token de acesso
                var graphClient = new GraphServiceClient(
                    new BaseBearerTokenAuthenticationProvider(
                        new StaticAccessTokenProvider(tokenResult.AccessToken)));

                // Filtro base: usuários da BI4ALL
                string filter = "endsWith(mail, '@bi4all.pt') or endsWith(userPrincipalName, '@bi4all.pt')";
                
                // Pesquisa por email específico (prioridade mais alta)
                if (!string.IsNullOrWhiteSpace(email))
                {
                    filter = $"mail eq '{email}' or userPrincipalName eq '{email}'";
                    Console.WriteLine($"Buscando usuário com email específico: {email}");
                }
                // Caso contrário, usa a pesquisa por texto normal
                else if (!string.IsNullOrWhiteSpace(searchText))
                {
                    // Adicionar condição de busca por nome ou email
                    filter = $"(startsWith(displayName, '{searchText}') or startsWith(mail, '{searchText}') or startsWith(userPrincipalName, '{searchText}')) and ({filter})";
                    Console.WriteLine($"Buscando usuários com texto: {searchText}");
                }
                else
                {
                    Console.WriteLine("Buscando todos os usuários BI4ALL");
                }
                
                // Determinar quantos itens precisamos buscar
                int fetchCount = (page + 1) * pageSize;
                
                // Buscar usuários do Azure AD com o filtro definido
                var users = await graphClient.Users
                    .GetAsync(options => {
                        options.QueryParameters.Filter = filter;
                        options.QueryParameters.Select = new[] { "id", "displayName", "mail", "userPrincipalName" };
                        options.QueryParameters.Top = fetchCount;
                        options.QueryParameters.Count = true;
                        options.Headers.Add("ConsistencyLevel", "eventual");
                    });

                if (users?.Value == null)
                {
                    // Retorna um objeto com informações de paginação e array vazio
                    return Ok(new { 
                        users = new List<object>(),
                        currentPage = page,
                        pageSize = pageSize,
                        totalPages = 0,
                        totalUsers = 0,
                        hasNextPage = false,
                        hasPreviousPage = page > 0
                    });
                }

                var allUsers = users.Value.ToList();
                Console.WriteLine($"Encontrados {allUsers.Count} usuários");
                
                // Obter total de usuários (para paginação)
                int totalUsers = allUsers.Count;
                if (users.AdditionalData != null && users.AdditionalData.ContainsKey("@odata.count"))
                {
                    if (users.AdditionalData["@odata.count"] is int countValue)
                    {
                        totalUsers = countValue;
                    }
                    else if (users.AdditionalData["@odata.count"] is long longCountValue)
                    {
                        totalUsers = (int)longCountValue;
                    }
                    else if (users.AdditionalData["@odata.count"] is string countString && int.TryParse(countString, out int parsedValue))
                    {
                        totalUsers = parsedValue;
                    }
                }
                
                // Aplicar a paginação na coleção de usuários
                var startIndex = Math.Min(page * pageSize, allUsers.Count);
                var count = Math.Min(pageSize, allUsers.Count - startIndex);
                
                if (startIndex >= allUsers.Count)
                {
                    // Retorna um objeto com informações de paginação e array vazio
                    return Ok(new { 
                        users = new List<object>(),
                        currentPage = page,
                        pageSize = pageSize,
                        totalPages = (int)Math.Ceiling(totalUsers / (double)pageSize),
                        totalUsers = totalUsers,
                        hasNextPage = false,
                        hasPreviousPage = page > 0
                    });
                }
                
                // Extrair os resultados da página atual
                var pagedUsers = allUsers
                    .Skip(startIndex)
                    .Take(count)
                    .Select(u => new {
                        id = u.Id,
                        displayName = u.DisplayName,
                        mail = u.Mail,
                        userPrincipalName = u.UserPrincipalName
                    })
                    .ToList();

                // Calcular páginas
                int totalPages = (int)Math.Ceiling(totalUsers / (double)pageSize);
                bool hasNextPage = (page + 1) < totalPages;
                
                // Retornar objeto com usuários e informações de paginação
                // O frontend detectará a propriedade "users" e usará o array contido nela
                return Ok(new { 
                    users = pagedUsers,
                    currentPage = page,
                    pageSize = pageSize,
                    totalPages = totalPages,
                    totalUsers = totalUsers,
                    hasNextPage = hasNextPage,
                    hasPreviousPage = page > 0
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erro ao buscar usuários: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Erro interno: {ex.InnerException.Message}");
                }
                return StatusCode(500, $"Erro ao buscar usuários do Azure AD: {ex.Message}");
            }
        }

        // Novo endpoint que usa a autenticação em nome do usuário (On-Behalf-Of flow)
        [HttpGet("azure-users-delegated")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<object>>> GetAzureUsersDelegated()
        {
            try
            {
                // Usa o secret normal com escopo para MS Graph sem adicionar validações extras
                Console.WriteLine("Iniciando busca de usuários da BI4ALL usando fluxo delegado");
                Console.WriteLine($"ClientId: {_azureAdOptions.ClientId}");
                Console.WriteLine($"TenantId: {_azureAdOptions.TenantId}");
                
                // Configura uma aplicação cliente mais simples
                var appBuilder = ConfidentialClientApplicationBuilder
                    .Create(_azureAdOptions.ClientId)
                    .WithClientSecret(_azureAdOptions.ClientSecret)
                    .WithAuthority(new Uri($"{_azureAdOptions.Instance}{_azureAdOptions.TenantId}"))
                    .Build();
                
                try
                {
                    // Tenta adquirir um token cliente para aplicação
                    var tokenResult = await appBuilder.AcquireTokenForClient(
                        new[] { "https://graph.microsoft.com/.default" })
                        .ExecuteAsync();

                    Console.WriteLine($"Token adquirido com sucesso: {tokenResult.AccessToken.Substring(0, 20)}...");
                    
                    // Cria o cliente Graph
                    var authProvider = new BaseBearerTokenAuthenticationProvider(
                        new StaticAccessTokenProvider(tokenResult.AccessToken));
                    
                    var graphClient = new GraphServiceClient(authProvider);
                    
                    // Buscar todos os usuários (sem filtro inicialmente para debug)
                    var users = await graphClient.Users
                        .GetAsync(options => {
                            // Sem filtro inicialmente para ver se consegue buscar qualquer usuário
                            options.QueryParameters.Select = new[] { "id", "displayName", "mail", "userPrincipalName" };
                            options.QueryParameters.Top = 10; // Limitar a 10 inicialmente
                        });
                    
                    if (users?.Value == null || !users.Value.Any())
                    {
                        Console.WriteLine("Nenhum usuário encontrado no Azure AD");
                        return Ok(new List<object>());
                    }
                    
                    Console.WriteLine($"Encontrados {users.Value.Count} usuários no Azure AD");
                    
                    // Agora filtra manualmente para usuários da BI4ALL
                    var bi4allUsers = users.Value
                        .Where(u => 
                            (u.Mail != null && u.Mail.EndsWith("@bi4all.pt", StringComparison.OrdinalIgnoreCase)) || 
                            (u.UserPrincipalName != null && u.UserPrincipalName.EndsWith("@bi4all.pt", StringComparison.OrdinalIgnoreCase)))
                        .Select(u => new {
                            id = u.Id,
                            displayName = u.DisplayName,
                            mail = u.Mail,
                            userPrincipalName = u.UserPrincipalName
                        })
                        .ToList();
                    
                    Console.WriteLine($"Filtrados {bi4allUsers.Count} usuários da BI4ALL");
                    
                    return Ok(bi4allUsers);
                }
                catch (Exception graphEx)
                {
                    Console.WriteLine($"Erro ao acessar o Graph API: {graphEx.Message}");
                    Console.WriteLine($"Stack trace: {graphEx.StackTrace}");
                    if (graphEx.InnerException != null)
                    {
                        Console.WriteLine($"Inner exception: {graphEx.InnerException.Message}");
                    }
                    
                    // Retornar o erro detalhado para diagnóstico
                    return StatusCode(500, $"Erro ao acessar o Microsoft Graph API: {graphEx.Message} - {graphEx.InnerException?.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erro geral: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return StatusCode(500, $"Erro ao buscar usuários do Azure AD: {ex.Message}");
            }
        }
    }
    
    // Classe simples para prover o token de acesso
    public class StaticAccessTokenProvider : IAccessTokenProvider
    {
        private readonly string _accessToken;

        public StaticAccessTokenProvider(string accessToken)
        {
            _accessToken = accessToken;
        }

        public Task<string> GetAuthorizationTokenAsync(Uri uri, Dictionary<string, object> additionalAuthenticationContext = null, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(_accessToken);
        }

        public AllowedHostsValidator AllowedHostsValidator { get; } = new AllowedHostsValidator();
    }
} 