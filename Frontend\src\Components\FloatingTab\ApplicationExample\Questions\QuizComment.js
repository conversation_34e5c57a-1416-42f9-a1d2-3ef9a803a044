import React, { useState, useRef, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>,
    Collapse
} from '@mui/material';
import clsx from 'clsx';
import {
    KeyboardArrowUp as KeyboardArrowUpIcon,
    KeyboardArrowDown as KeyboardArrowDownIcon,
    Edit as EditIcon,
    Close as CloseIcon,
    Save as SaveIcon,
    AccountCircle as AccountCircleIcon
} from '@mui/icons-material';
import PropTypes from 'prop-types';
import parse from 'html-react-parser';
import { useTranslation } from 'react-i18next';
import { styled } from '@mui/system';

const CustomFlexWrapper = styled("div")({
    display: "flex",
    flex: 1,
    alignItems: "center"
});

const StyledAvatar = styled(AccountCircleIcon)(({ theme }) => ({
    margin: "10px",
    fontSize: "2.5rem",
    "&.currentUser": {
        color: theme.palette.primary.main
    }
}))

const CustomTextArea = styled("textarea")({
    border: "solid 1px lightgray",
    width: "100%",
    resize: "vertical",
    minHeight: "24px",
    marginLeft: "10px",
    marginRight: "20px",
    outline: "none",
    padding: "10px",
    "&:focus, &:focus-visible": {
        border: "solid 1px gray !important",
    }
});

const QuizComment = ({ commentsdata, owncomment, saveQuizComment, canedit = false }) => {
    const [commentsToggled, setCommentsToggled] = useState(false);
    const [editingComment, setEditingComment] = useState(false);
    const [editedComment, setEditedComment] = useState(false);
    const [_commentsdata, setCommentsData] = useState([]);
    const [discarding, setDiscarding] = useState(false);
    const textareaRef = useRef(null);
    const { t } = useTranslation();

    function openCommentTextArea() {
        if (canedit || _commentsdata.length > 0 || editedComment) return true;
        return false;
    }
    const openCommentSection = openCommentTextArea();
    const editedCommentCondition = editedComment && parse(editedComment) !== "";

    const getIcon = () => {
        return commentsToggled ? <KeyboardArrowDownIcon /> : <KeyboardArrowUpIcon />;
    }

    const toggleEditComment = (value) => {
        setEditingComment(value !== undefined ? value : !editingComment);
    };

    const discardComment = () => {
        toggleEditComment(false);
    };

    const toggleComments = () => {
        if (!canedit && _commentsdata.length <= 0 && editedComment === "") {
            return;
        }
        if (commentsToggled) {
            discardComment();
        }

        setCommentsToggled(!commentsToggled);
    };

    const handleChangeComment = (e) => {
        setEditedComment(e.target.value);
    };

    const commentsCount = () => {
        const validComments = _commentsdata.filter(x => x !== "");
        const commentCount = editedComment && editedComment !== null && editedComment !== "" ? validComments.length + 1 : validComments.length;
        return commentCount;
    };

    const saveComment = () => {
        if (discarding) {
            return;
        }
        saveQuizComment(textareaRef.current.value);
        toggleEditComment();
    };

    const handleTextareaKeydown = (event) => {
        /* UNSYNCED WITH REACT STATES */
        if (event.keyCode === 13) {
            if (event.altKey === false) {
                saveComment();
                return false;
            }
            const cursorPos = textareaRef.current.selectionStart;
            const beforeCursor = textareaRef.current.value.slice(0, cursorPos);
            const afterCursor = textareaRef.current.value.slice(cursorPos, textareaRef.current.value.length);
            setEditedComment(beforeCursor + "\r\n" + afterCursor);
        }
        if (event.keyCode === 27) {
            discardComment();
            return false;
        }
        return undefined;
    };

    useEffect(() => {
        let temp_owncomment = editedComment;
        if (owncomment && owncomment !== null) {
            temp_owncomment = owncomment === "" ? editedComment : owncomment;
        }
        if (temp_owncomment) {
            setEditedComment(temp_owncomment.replace(/\n\r?/g, '<br />'));
        }
    }, [owncomment]);

    useEffect(() => {
        if (commentsdata === null || commentsdata.length === 0) {
            setCommentsData([""]);
        }

        const treated_commentsdata = [];
        commentsdata.forEach(com => {
            const _comment = com !== null ? (com === "" ? com : parse(com.replace(/\n\r?/g, '<br />'))) : com;
            if (_comment !== null) {
                treated_commentsdata.push(com);
            }
        });
        setCommentsData(treated_commentsdata);
    }, [commentsdata]);

    useEffect(() => {
        if (textareaRef && textareaRef.current !== null) {
            textareaRef.current.addEventListener('keydown', handleTextareaKeydown);
            textareaRef.current.focus();
            textareaRef.current.selectionStart = textareaRef.current.value.length;
            textareaRef.current.selectionEnd = textareaRef.current.value.length;
        }
    }, [editingComment]);

    return (
        <>
            <Button
                variant="outlined"
                color="primary"
                onClick={toggleComments}
                style={{
                    marginBottom: 15
                }}
            >
                {t('common:buttons.observations')} ({commentsCount()})
                {
                    openCommentSection ?
                        <CustomFlexWrapper
                            style={{ marginLeft: "10px" }}
                        >
                            {getIcon()}
                        </CustomFlexWrapper>
                        :
                        null
                }
            </Button>
            <Collapse in={commentsToggled}>
                {
                    canedit ?
                        <Card
                            style={{
                                marginBottom: "10px"
                            }}
                        >
                            <CustomFlexWrapper
                                style={{
                                    padding: "10px"
                                }}
                            >
                                <StyledAvatar
                                    style={{ margin: 0 }}
                                    className={clsx('currentUser')}
                                />
                                {
                                    editingComment ?
                                        <CustomTextArea
                                            onBlur={saveComment}
                                            ref={textareaRef}
                                            value={editedComment ? editedComment.replace(/<br\s*?>/gi, "\n") : null}
                                            onChange={handleChangeComment}
                                            placeholder={t('common:labels.leaveComment')}
                                        />
                                        :
                                        <div
                                            style={{ margin: "0 10px" }}
                                            onClick={toggleEditComment}
                                        >
                                            {editedComment ? parse(editedComment) : t('common:labels.leaveComment')}
                                        </div>
                                }
                                <div
                                    style={{
                                        marginLeft: "auto",
                                        textAlign: "right",
                                        display: "flex"
                                    }}
                                >
                                    {
                                        editingComment ?
                                            <>
                                                <Button
                                                    variant="contained"
                                                    color="secondary"
                                                    onMouseDown={() => setDiscarding(true)}
                                                    onMouseUp={() => setDiscarding(false)}
                                                    onClick={discardComment}
                                                >
                                                    <CloseIcon />
                                                </Button>
                                                <Button
                                                    variant="contained"
                                                    color="primary"
                                                    onClick={saveComment}
                                                    sx={{
                                                        ml: "5px"
                                                    }}
                                                >
                                                    <SaveIcon />
                                                </Button>
                                            </>
                                            :
                                            <Button
                                                variant="contained"
                                                color="primary"
                                                onClick={toggleEditComment}
                                            >
                                                <EditIcon />
                                            </Button>
                                    }
                                </div>
                            </CustomFlexWrapper>
                        </Card>
                        :
                        editedCommentCondition ?
                            <Card
                                style={{
                                    boxShadow: "none"
                                }}
                            >
                                <CustomFlexWrapper>
                                    <StyledAvatar className={clsx('currentUser')} />
                                    <div>
                                        {parse(editedComment.replace(/\n\r?/g, '<br />'))}
                                    </div>
                                </CustomFlexWrapper>
                            </Card>
                            :
                            null
                }
                {
                    _commentsdata.map((q, index) => {
                        if (q === null || q === "") {
                            return;
                        }
                        return (<Card
                            key={q + "_" + index}
                            style={{
                                boxShadow: "none"
                            }}
                        >
                            <CustomFlexWrapper>
                                <StyledAvatar />
                                <div>
                                    {parse(q.replace(/\n\r?/g, '<br />'))}
                                </div>
                            </CustomFlexWrapper>
                        </Card>);
                    })
                }
            </Collapse>
        </>
    );
};

QuizComment.propTypes = {
    commentsdata: PropTypes.array.isRequired,
    owncomment: PropTypes.string,
    saveQuizComment: PropTypes.func,
    canedit: PropTypes.bool,
};

export default QuizComment;
