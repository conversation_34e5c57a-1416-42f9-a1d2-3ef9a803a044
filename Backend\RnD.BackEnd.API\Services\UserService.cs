using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using RnD.BackEnd.Domain.Interfaces.Services;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RnD.BackEnd.API.Services
{
    public class UserService : IUserService
    {
        private readonly ApplicationDbContext _dbContext;

        public UserService(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// Verifica se um user existe no banco de dados pelo AzureId
        /// </summary>
        public async Task<object> GetUserByAzureIdAsync(string azureId)
        {
            return await _dbContext.User
                .Include(u => u.UserPermissions)
                .ThenInclude(up => up.Permission)
                .FirstOrDefaultAsync(u => u.AzureId == azureId);
        }

        /// <summary>
        /// Cria um novo user no banco de dados
        /// </summary>
        public async Task<object> CreateUserAsync(string azureId, string username, string email)
        {
            var user = new User
            {
                AzureId = azureId,
                Username = username,
                Email = email
            };

            _dbContext.User.Add(user);
            await _dbContext.SaveChangesAsync();

            // Adicionar permissão padrão (903001)
            var defaultPermission = await _dbContext.Permission
                .FirstOrDefaultAsync(p => p.Id == 1);

            if (defaultPermission != null)
            {
                var userPermission = new UserPermission
                {
                    UserID = user.UserID,
                    PermissionID = defaultPermission.Id
                };

                _dbContext.UserPermission.Add(userPermission);
                await _dbContext.SaveChangesAsync();
            }

            return user;
        }

        /// <summary>
        /// Atribui permissões básicas a um novo user
        /// </summary>
        public async Task AssignDefaultPermissionsAsync(object userObj)
        {
            var user = userObj as User;
            if (user == null)
                return;

            // Neste exemplo, vamos atribuir a permissão "Usuario" para todos os novos users
            // Você pode modificar isso conforme necessário

            // Primeiro, verificamos se a permissão padrão existe
            var defaultPermission = await _dbContext.Permission
                .FirstOrDefaultAsync(p => p.Name == "Usuario");

            // Se não existir, criamos ela
            if (defaultPermission == null)
            {
                defaultPermission = new Permission { Name = "Usuario" };
                _dbContext.Permission.Add(defaultPermission);
                await _dbContext.SaveChangesAsync();
            }

            // Verificamos se o user já tem essa permissão
            var hasPermission = user.UserPermissions
                .Any(up => up.PermissionID == defaultPermission.Id);

            // Se não tiver, adicionamos
            if (!hasPermission)
            {
                var userPermission = new UserPermission
                {
                    UserID = user.UserID,
                    PermissionID = defaultPermission.Id
                };

                _dbContext.UserPermission.Add(userPermission);
                await _dbContext.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Obtém uma lista de todas as permissões que um user possui
        /// </summary>
        public async Task<List<string>> GetUserPermissionsAsync(int userId)
        {
            var permissions = await _dbContext.UserPermission
                .Where(up => up.UserID == userId)
                .Select(up => up.Permission.Name)
                .ToListAsync();

            return permissions;
        }
    }
}