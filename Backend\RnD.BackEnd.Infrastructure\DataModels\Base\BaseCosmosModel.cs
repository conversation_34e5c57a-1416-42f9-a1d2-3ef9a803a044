﻿using Newtonsoft.Json;

namespace RnD.BackEnd.Infrastructure.DataModels.Base
{
    public class BaseCosmosModel
    {
        [JsonProperty("id")]
        public string Id { get; set; }
        [JsonProperty("partitionKey")]
        public string PartitionKey => GetPartitionKey();
        [JsonProperty("_ts")]
        public long? Timestamp { get; set; }
        [JsonProperty("_etag")]
        public string ETag { get; set; }

        public virtual string GetPartitionKey()
        {
            return Id?.ToString()?.Substring(0, 4);
        }
    }
}
