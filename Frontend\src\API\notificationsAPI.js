import handleMethod from '../Utils/APIutils/handleMethod';

class NotificationsAPI {
  async getNotifications({ limit }) {
    const Input = {
      type: 'GET',
      method: `getNotifications?limit=${limit}`
    };
    const response = await handleMethod(Input);
    return response;
  }

  async saveNotificationRead({ notificationIDs }) {
    const Input = {
      type: 'POST',
      method: `saveNotificationRead`,
      params: notificationIDs
    };
    const response = await handleMethod(Input);
    return response;
  }
}
export const notificationsAPI = new NotificationsAPI();
