using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;

namespace RnD.BackEnd.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ConsumoSubscricaoController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public ConsumoSubscricaoController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<ConsumoSubscricao>>> GetConsumos()
        {
            return await _context.ConsumoSubscricao
                .Include(c => c.Subscription)
                .OrderByDescending(c => c.StartDate)
                .ToListAsync();
        }

        [HttpGet("subscription/{subscriptionId}")]
        public async Task<ActionResult<IEnumerable<ConsumoSubscricao>>> GetConsumosBySubscription(string subscriptionId)
        {
            var consumos = await _context.ConsumoSubscricao
                .Include(c => c.Subscription)
                .Where(c => c.SubscriptionID == subscriptionId)
                .OrderByDescending(c => c.StartDate)
                .ToListAsync();

            return consumos; // Retorna lista vazia se não houver consumos
        }

        [HttpGet("period")]
        public async Task<ActionResult<IEnumerable<ConsumoSubscricao>>> GetConsumosByPeriod(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            var consumos = await _context.ConsumoSubscricao
                .Include(c => c.Subscription)
                .Where(c => c.StartDate >= startDate && c.EndDate <= endDate)
                .OrderByDescending(c => c.StartDate)
                .ToListAsync();

            return consumos; // Retorna lista vazia se não houver consumos
        }

        [HttpGet("summary")]
        public async Task<ActionResult<object>> GetConsumosSummary()
        {
            var consumos = await _context.ConsumoSubscricao.ToListAsync();
            
            if (!consumos.Any())
            {
                return new
                {
                    TotalCost = 0m,
                    TotalCostUSD = 0m,
                    ResourceCount = 0,
                    Currency = "EUR",
                    ResourceGroups = new List<object>(),
                    Locations = new List<object>()
                };
            }

            var summary = await _context.ConsumoSubscricao
                .GroupBy(c => c.SubscriptionID)
                .Select(g => new
                {
                    SubscriptionId = g.Key,
                    TotalCost = g.Sum(c => c.Cost),
                    TotalCostUSD = g.Sum(c => c.CostUSD),
                    ResourceCount = g.Count(),
                    LastUpdate = g.Max(c => c.EndDate)
                })
                .ToListAsync();

            return summary;
        }

        // GET: api/ConsumoSubscricao/cliente/5
        [HttpGet("cliente/{clienteId}")]
        public async Task<ActionResult<IEnumerable<ConsumoSubscricao>>> GetConsumosByCliente(int clienteId)
        {
            // Primeiro, obtemos todas as subscrições do cliente
            var subscricoes = await _context.ClienteSubscricao
                .Where(cs => cs.ClienteID == clienteId)
                .Include(cs => cs.Subscription)
                .Select(cs => cs.Subscription.SubscriptionID)
                .ToListAsync();

            if (!subscricoes.Any())
            {
                return new List<ConsumoSubscricao>(); // Retorna lista vazia se não houver subscrições
            }

            // Depois, obtemos todos os consumos dessas subscrições
            var consumos = await _context.ConsumoSubscricao
                .Include(c => c.Subscription)
                .Where(c => subscricoes.Contains(c.SubscriptionID))
                .OrderByDescending(c => c.StartDate)
                .ToListAsync();

            return consumos; // Retorna lista vazia se não houver consumos
        }

        // GET: api/ConsumoSubscricao/cliente/5/summary
        [HttpGet("cliente/{clienteId}/summary")]
        public async Task<ActionResult<object>> GetConsumosSummaryByCliente(int clienteId, 
            [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            // Primeiro, obtemos todas as subscrições do cliente
            var subscricoes = await _context.ClienteSubscricao
                .Where(cs => cs.ClienteID == clienteId)
                .Include(cs => cs.Subscription)
                .Select(cs => cs.Subscription.SubscriptionID)
                .ToListAsync();

            if (!subscricoes.Any())
            {
                return new
                {
                    TotalCost = 0m,
                    TotalCostUSD = 0m,
                    ResourceCount = 0,
                    Currency = "EUR",
                    StartDate = startDate ?? DateTime.Now,
                    EndDate = endDate ?? DateTime.Now,
                    ResourceGroups = new List<object>(),
                    Locations = new List<object>()
                };
            }

            // Preparamos a query base
            var query = _context.ConsumoSubscricao
                .Include(c => c.Subscription)
                .Where(c => subscricoes.Contains(c.SubscriptionID));

            // Aplicamos filtros de data se fornecidos
            if (startDate.HasValue)
            {
                query = query.Where(c => c.StartDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(c => c.EndDate <= endDate.Value);
            }

            // Executamos a query
            var consumos = await query.ToListAsync();

            if (!consumos.Any())
            {
                return new
                {
                    TotalCost = 0m,
                    TotalCostUSD = 0m,
                    ResourceCount = 0,
                    Currency = "EUR",
                    StartDate = startDate ?? DateTime.Now,
                    EndDate = endDate ?? DateTime.Now,
                    ResourceGroups = new List<object>(),
                    Locations = new List<object>()
                };
            }

            // Calculamos o resumo por grupo de recursos
            var resourceGroupSummary = consumos
                .GroupBy(c => c.ResourceGroup)
                .Select(g => new
                {
                    ResourceGroup = g.Key,
                    TotalCost = g.Sum(c => c.Cost),
                    TotalCostUSD = g.Sum(c => c.CostUSD),
                    ResourceCount = g.Count(),
                    LastUpdate = g.Max(c => c.EndDate)
                })
                .OrderByDescending(x => x.TotalCost)
                .ToList();

            // Calculamos o resumo por localização
            var locationSummary = consumos
                .GroupBy(c => c.ResourceLocation)
                .Select(g => new
                {
                    Location = g.Key,
                    TotalCost = g.Sum(c => c.Cost),
                    TotalCostUSD = g.Sum(c => c.CostUSD),
                    ResourceCount = g.Count(),
                    LastUpdate = g.Max(c => c.EndDate)
                })
                .OrderByDescending(x => x.TotalCost)
                .ToList();

            // Criamos o resumo geral
            var summary = new
            {
                TotalCost = consumos.Sum(c => c.Cost),
                TotalCostUSD = consumos.Sum(c => c.CostUSD),
                ResourceCount = consumos.Count,
                Currency = consumos.First().Currency,
                StartDate = consumos.Min(c => c.StartDate),
                EndDate = consumos.Max(c => c.EndDate),
                ResourceGroups = resourceGroupSummary,
                Locations = locationSummary
            };

            return summary;
        }
    }
} 