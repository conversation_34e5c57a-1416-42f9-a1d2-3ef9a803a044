﻿using System.Collections.Generic;

namespace RnD.BackEnd.Domain.Settings
{
    public class EmailSettings
    {
        public bool AllowSendingEmails { get; set; }
        public List<string> SendAlwaysToAddresses { get; set; }
        public string ApiKey { get; set; }
        public string FromEmailAddress { get; set; }
        public string FromUserName { get; set; }
        public string DefaultTemplateId { get; set; }
        public string CodeAcademyTemplateId { get; set; }
    }
}