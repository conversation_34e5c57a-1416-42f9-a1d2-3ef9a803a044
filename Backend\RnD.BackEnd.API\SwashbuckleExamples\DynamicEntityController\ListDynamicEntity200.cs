﻿using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.DynamicEntityController
{
    /// <summary>
    /// The example of a 200 response when listing Dynamic Entities.
    /// </summary>
    public class ListDynamicEntity200 : IExamplesProvider<ApiOutput<List<Dictionary<string, object>>>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 for a list response.
        /// </returns>
        public ApiOutput<List<Dictionary<string, object>>> GetExamples()
        {
            return new ApiOutput<List<Dictionary<string, object>>>
            {
                Code = HttpStatusCode.OK,
                Value = new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object>
                    {
                        {"entityId", "dive1" },
                        {"companyId", "diveCenterPhi1"},
                        {"name", "Dive Center Koh Phi Phi"},
                        {"type", "Dive Center"},
                        {"review", 4.5},
                        {"numberOfReviews", 104},
                        {"location", "Koh Phi Phi Island"},
                        {"country", "Thailand"},
                        {"id", "dive1:bfd6b932-9e6a-43f2-b739-0b65213bc333"},
                        {"_rid", "DMV6ANEfcywBAAAAAAAAAA=="},
                        {"_self", "dbs/DMV6AA==/colls/DMV6ANEfcyw=/docs/DMV6ANEfcywBAAAAAAAAAA==/"},
                        {"_etag", "\"510015a5-0000-0700-0000-63f8fbe10000\""},
                        {"_attachments", "attachments/"},
                        { "_ts", 1677261793 }
                    },
                    new Dictionary<string, object>
                    {
                        {"entityId", "car1" },
                        {"brand", "BMW"},
                        {"brandId", "bmw"},
                        {"modelName", "Serie 3"},
                        {"fuelType", "Diesel"},
                        {"version", "M"},
                        {"year", 2022},
                        {"id", "car1:3446381a-27fd-45d9-98ba-7f4c6d155316"},
                        {"_rid", "DMV6ANEfcywCAAAAAAAAAA=="},
                        {"_self", "dbs/DMV6AA==/colls/DMV6ANEfcyw=/docs/DMV6ANEfcywCAAAAAAAAAA==/"},
                        {"_etag", "\"0000f160-0000-0700-0000-63daa6200000\""},
                        {"_attachments", "attachments/"},
                        { "_ts", 1675273760}
                    }
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
                {
                    {"continuation_token", "W3sidG9rZW4iOiItUklEOn5ETVY2QU5FZmN5d0ZBQUFBQUFBQUFBPT0jUlQ6MiNUUkM6MyNJU1Y6MiNJRU86NjU1NjcjUUNGOjgiLCJyYW5nZSI6eyJtaW4iOiIiLCJtYXgiOiJGRiJ9fV0" }
                }
            };
        }
    }
}