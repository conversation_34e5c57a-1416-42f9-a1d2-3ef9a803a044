import { useRef, useState } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Avatar,
  Box,
  Button,
  ButtonBase,
  Divider,
  ListItemIcon,
  ListItemText,
  MenuItem,
  Popover,
  Typography
} from '@mui/material';
import { Person as PersonIcon } from "@mui/icons-material";
import toast from 'react-hot-toast';
import { PropTypes } from 'prop-types';
import { useTranslation } from 'react-i18next';

const AccountPopover = ({ name, department }) => {
  const navigate = useNavigate();
  const anchorRef = useRef(null);
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleLogout = async () => {
    try {
      handleClose();
      //await logout();
      navigate('/');
    } catch (err) {
      console.error(err);
      toast.error('Unable to logout.');
    }
  };

  return (
    <>
      <Box
        component={ButtonBase}
        onClick={handleOpen}
        ref={anchorRef}
        sx={{
          alignItems: 'center',
          display: 'flex'
        }}
      >
        <Avatar
          src="/static/mock-images/avatars/avatar-image.jpg"
          sx={{
            height: 40,
            width: 40
          }}
        />
        {/*Name should be returned from the API*/}
        <Typography
          sx={{
            ml: 1
          }}
          color="textPrimary"
          variant="subtitle2"
        >
          {name}
        </Typography>
      </Box>
      {/*Menu*/}
      <Popover
        anchorEl={anchorRef.current}
        anchorOrigin={{
          horizontal: 'center',
          vertical: 'bottom'
        }}
        keepMounted
        onClose={handleClose}
        open={open}
        PaperProps={{
          sx: { width: 240 }
        }}
      >
        {/*Name & Department should be returned from the API*/}
        <Box sx={{ p: 2 }}>
          <Typography
            color="textPrimary"
            variant="subtitle2"
          >
            {name}
          </Typography>
          <Typography
            color="textSecondary"
            variant="subtitle2"
          >
            {department}
          </Typography>
        </Box>
        <Divider />
        <Box sx={{ mt: 2 }}>
          <MenuItem
            component={RouterLink}
            to="/"
          >
            <ListItemIcon>
              <PersonIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText
              primary={(
                <Typography
                  color="textPrimary"
                  variant="subtitle2"
                >
                  {t('profile:common.profile')}
                </Typography>
              )}
            />
          </MenuItem>
        </Box>
        {process.env.REACT_APP_CUSTOM_AUTH === 'LDAP' && (
          <Box sx={{ p: 2 }}>
            <Button
              color="primary"
              fullWidth
              onClick={handleLogout}
              variant="outlined"
            >
              {t('common:common.logout')}
            </Button>
          </Box>
        )}
      </Popover>
    </>
  );
};
AccountPopover.propTypes = {
  name: PropTypes.string,
  department: PropTypes.string,
};

export default AccountPopover;
