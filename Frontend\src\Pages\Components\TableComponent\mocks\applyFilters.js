const getApplyFilters = () => {
    return `
        export const applyFilters = (headCells, array, query, filters) => {
        return array.filter((element) => {
            let matches = true;
            if (query) {
            const properties = headCells.filter((x) => x.filter).map((x) => x.id);
            let containsQuery = false;

            properties.forEach((property) => {
                // case insensitive and accent insensitive
                if (element[property] && like(element[property], query)) {
                containsQuery = true;
                }
            });

            if (!containsQuery) {
                matches = false;
            }
            }

            if (filters) {
            Object.keys(filters).forEach((key) => {
                const value = filters[key];

                if (value) {
                if (Array.isArray(value)) {
                    let matchesArray = false;

                    value.forEach(obj => {
                    if (obj[Object.keys(obj)[0]] === element[key]) {
                        matchesArray = true;
                    }
                    });
                    matches = matchesArray;
                } else if (element[key] && like(element[key], value)) {
                    if (matches) {
                    matches = true;
                    }
                } else {
                    matches = false;
                }
                }
            });
            }

            return matches;
        });
    `;
}

export default getApplyFilters;
