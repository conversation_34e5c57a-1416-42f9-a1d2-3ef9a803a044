/* eslint-disable react/no-unescaped-entities*/
import {
  <PERSON>,
  Divider,
  Typo<PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
} from "@mui/material";
import { useTheme } from '@emotion/react';
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import { useState } from "react";
import getHook from "./mocks/hook";
import getContext from "./mocks/context";
import getAuthProvider from "./mocks/authProvider";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import { styled } from "@mui/system";

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh",
})

const AuthContext = () => {
  const theme = useTheme();
  const [hook, setHook] = useState(getHook);
  const [context, setContext] = useState(getContext);
  const [authProvider, setAuthProvider] = useState(getAuthProvider);

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box
        sx={{
          m: "auto",
          textAlign: "center",
        }}
      >
        <Box
          color="textPrimary"
          sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
        >
          <h2>useAuth</h2>
          This hook calls a Context (AuthContext):
          <br />
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={hook} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={hook}
              onValueChange={(_hook) => setHook(_hook)}
              highlight={(_hook) => highlight(_hook, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Typography
          color="textPrimary"
          sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
        >
          The AuthContext has several methods that help manage user actions like
          login, authentication, logout, registration, etc.
          <br />
          Here's an example:
        </Typography>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={context} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={context}
              onValueChange={(_context) => setContext(_context)}
              highlight={(_context) => highlight(_context, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Typography
          color="textPrimary"
          sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
        >
          Import the BrowserRouter and AuthProvider into your App:
        </Typography>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={authProvider} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={authProvider}
              onValueChange={(_authProvider) => setAuthProvider(_authProvider)}
              highlight={
                (_authProvider) =>
                  highlight(_authProvider, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
      </Box>
      <ChangeLogEntry
        version="V1.0.0"
        changeLog={ChangeLog()}
      />
    </Box>
  );
};
export default AuthContext;

const ChangeLog = () => {
  const theme = useTheme();
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content: "Imported useCustomAuth.",
      className: chips.green,
    },
  ];

  return changeLog;
};
