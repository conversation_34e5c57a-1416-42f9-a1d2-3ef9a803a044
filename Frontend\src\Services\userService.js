export const getUsers = async () => {
    try {
        const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/users`);

        if (!response.ok) throw new Error("Erro ao buscar users");

        const data = await response.json();
        console.log("🔍 Dados da API:", data);

        if (!Array.isArray(data)) {
            console.error("❌ ERRO: API não retornou um array!", data);
            return [];
        }

        return data.map(user => ({
            userID: user.userID,
            username: user.username,
            email: user.email
        }));
    } catch (error) {
        console.error("❌ ERRO ao conectar com a API:", error);
        return [];
    }
};

export const getUserById = async (id) => {
    try {
        const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/users/${id}`);
        if (!response.ok) throw new Error(`Erro ao buscar user: ${response.status}`);

        const data = await response.json();
        console.log("🔍 Detalhes do user:", data);

        return {
            userID: data.userID,
            username: data.username,
            email: data.email,
            userPermissions: data.userPermissions || [],
            azureId: data.azureId
        };
    } catch (error) {
        console.error("❌ ERRO ao buscar detalhes do user:", error);
        throw error;
    }
};

export const updateUser = async (id, userData) => {
    try {
        const formData = new URLSearchParams();
        formData.append('Username', userData.username);
        formData.append('Email', userData.email);

        console.log(`Atualizando user ${id} com:`, {
            Username: userData.username,
            Email: userData.email
        });

        const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/users/update/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: formData
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Erro ao atualizar user:", errorText);
            throw new Error(`Erro ao atualizar user: ${response.status}`);
        }

        // Obter o user atualizado da resposta
        const updatedUser = await response.json();
        console.log("user atualizado com sucesso:", updatedUser);

        return { success: true, user: updatedUser };
    } catch (error) {
        console.error("❌ ERRO ao atualizar user:", error);
        throw error;
    }
};

export const updateUserAlternative = async (id, userData) => {
    try {
        const formData = new URLSearchParams();
        formData.append('Username', userData.username);
        formData.append('Email', userData.email);

        const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/users/update/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: formData
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Erro ao atualizar user:", errorText);
            throw new Error(`Erro ao atualizar user: ${response.status}`);
        }

        return { success: true };
    } catch (error) {
        console.error("❌ ERRO ao atualizar user:", error);
        throw error;
    }
};

export const deleteUser = async (id) => {
    try {
        const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/users/${id}`, {
            method: 'DELETE',
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Resposta da API (erro ao apagar):", errorText);
            throw new Error(`Erro ao apagar user: ${response.status}`);
        }

        return true;
    } catch (error) {
        console.error("❌ ERRO ao apagar user:", error);
        throw error;
    }
};
