const getApplySort = () => {
    return `
    export const applySort = (array, orderBy, orderDirection) => {
        const stabilizedThis = array.map((el, index) => [el, index]);
        stabilizedThis.sort((a, b) => {
            const order = getComparator(orderDirection, orderBy)(a[0], b[0]);
            if (order !== 0) return order;
            return a[1] - b[1];
        });

        return stabilizedThis.map((el) => el[0]);
    };
    `;
}

export default getApplySort;
