const getCode = () => {
    return `
        import CustomFilters from '../../Components/CustomFilters'; //Import Custom Filter Component from your src folder
        import { FilterList as FilterListIcon } from '@mui/icons-material'; //Import Filter Icon from Material UI

        //Example:
        export default function MyApp() {
        const { filters, saveFilters } = useFilters();
        const [customFiltersList, setCustomFiltersList] = useState([]); 
        const [customFiltersToggled, setCustomFiltersToggled] = useState(false);
        const [selectedCustomFilters, setSelectedCustomFilters] = useState(filters[customFiltersContext]);

        //API call to get Filters
        const getFilters = async () => {
            try {
                const data = await filtersAPI.getAvailableFilters({ context: "PROCESS" });
                if (!data?.error) {
                    setCustomFiltersList(data);
                } else if (data.exceptionMessages && data.exceptionMessages.hasMessages) {
                    data.exceptionMessages.messages.forEach(m => {
                        toast.error(m.description);
                    })
                } else {
                    toast.error("Ocorreu um erro inesperado")
                }
            } catch (err) {
                console.error(err);
            }
        };

        const handleResetCustomFilters = () => {
            if (customFiltersList > 0) {
                setSelectedCustomFilters([]);
            }
        };

        //Add Custom Filters
        const handleAddCustomFilter = (value) => {
            const _selectedCustomFilters = handleAddCustomFilterUtils(value, filters[customFiltersContext]);
            setSelectedCustomFilters(_selectedCustomFilters);
            saveFilters(customFiltersContext, _selectedCustomFilters);
        };

        //Delete Custom Filters
        const handleDeleteCustomFilter = (value) => {
            const _selectedCustomFilters = handleDeleteCustomFilterUtils(value, filters[customFiltersContext]);
            setSelectedCustomFilters(_selectedCustomFilters);
            saveFilters(customFiltersContext, _selectedCustomFilters);
        };

        //Edit Custom Filters
        const handleEditCustomFilter = (customFilterEdited) => {
            const _selectedCustomFilters = handleEditCustomFilterUtils(customFilterEdited, filters[customFiltersContext]);
            setSelectedCustomFilters(_selectedCustomFilters);
            saveFilters(customFiltersContext, _selectedCustomFilters);
        };

        useEffect(() => {
            getFilters();
        }, []);
        
        return (
            <Your Code Here...>
              <Tooltip
                    title="Visualizar filtros personalizados"
                >
                        <IconButton
                            color="primary"
                            onClick={() => setCustomFiltersToggled(!customFiltersToggled)}
                        >
                            <Badge
                                badgeContent={selectedCustomFilters.length}
                                color="primary"
                                sx={{
                                    ".MuiBadge-badge": {
                                        padding: "0 4px !important",
                                        minWidth: "17px !important",
                                        height: "17px !important",
                                        fontSize: "0.6rem !important",
                                        right: "-3px"
                                    }
                                }}
                            >
                                <FilterListIcon />
                            </Badge>
                        </IconButton>
                </Tooltip>
                <CustomFilters
                    customFiltersToggled={customFiltersToggled}
                    context="PROCESS"
                    customFiltersList={customFiltersList}
                    selectedCustomFilters={selectedCustomFilters}
                    resetCustomFilters={handleResetCustomFilters}
                    addCustomFilter={handleAddCustomFilter}
                    deleteCustomFilter={handleDeleteCustomFilter}
                    editCustomFilter={handleEditCustomFilter}
                />
            </Your Code Here...>
            );
    `;
}

export default getCode;
