import { createContext, useEffect, useReducer } from 'react';
import PropTypes from 'prop-types';
import { authApi } from '../mockAPI/authApi';
import { userAPI } from '../API/userAPI';
import { Authentication } from '../Routes/routes';

const initialState = {
  isAuthenticated: false,
  isInitialized: false,
  user: null
};

const handlers = {
  INITIALIZE: (state, action) => {
    const { isAuthenticated, user } = action.payload;

    return {
      ...state,
      isAuthenticated,
      isInitialized: true,
      user
    };
  },
  LOGIN: (state, action) => {
    const { user } = action.payload;

    return {
      ...state,
      isAuthenticated: true,
      user
    };
  },
  LOGOUT: (state) => ({
    ...state,
    isAuthenticated: false,
    user: null
  }),
  REGISTER: (state, action) => {
    const { user } = action.payload;

    return {
      ...state,
      isAuthenticated: true,
      user
    };
  },
  INITIALIZE_WITH_TOKEN: (state) => ({
    ...state,
    isAuthenticated: true,
    isInitialized: true,
    user: {
      name: "user Autenticado",
      roles: ["PORTAL"],
      userModules: []
    }
  })
};

const reducer = (state, action) => (handlers[action.type]
  ? handlers[action.type](state, action)
  : state);

const AuthContext = createContext({
  ...initialState,
  platform: 'JWT',
  login: () => Promise.resolve(),
  logout: () => Promise.resolve(),
  register: () => Promise.resolve()
});

export const AuthProvider = (props) => {
  const { children, instance } = props;
  const [state, dispatch] = useReducer(reducer, initialState);
  // const navigate = useNavigate();

  useEffect(() => {
    const initialize = async () => {
      try {
        console.log('AuthContext: Inicializando...');
        let data = null;
        let authSuccessful = false;

        const directToken = localStorage.getItem('azureToken');

        if (process.env.REACT_APP_CUSTOM_AUTH === 'AZUREAD') {
          console.log('AuthContext: Modo Azure AD');
          try {
            const adResponse = await Authentication(instance);

            if (adResponse) {
              console.log('AuthContext: Obteve resposta do Azure AD');
              localStorage.setItem('session', JSON.stringify({
                accessToken: adResponse
              }));
              data = await userAPI.AzureAuthenticate();
              authSuccessful = data && !data.error && data.response;
            } else {
              console.log('AuthContext: Sem resposta do Azure AD');
            }
          } catch (authError) {
            console.error('AuthContext: Erro na autenticação Azure AD:', authError);
          }
        } else {
          console.log('AuthContext: Modo autenticação padrão');
          const session = JSON.parse(window.localStorage.getItem('session'));
          if (session) {
            data = await userAPI.Authenticate();
            authSuccessful = data && !data.error && data.response;
          }
        }

        if (authSuccessful) {
          console.log('AuthContext: Autenticação bem-sucedida, configurando user');
          localStorage.setItem('session', JSON.stringify(data.response));
          dispatch({
            type: 'INITIALIZE',
            payload: {
              isAuthenticated: true,
              user: {
                avatar: `data:image/jpeg;base64, ${data.response.photo}`,
                name: data.response.name,
                jobRole: data.response.jobRole,
                roles: data.response.roles,
                defaultRoute: data.response.defaultRoute,
                userModules: data.response.userModules
              }
            }
          });
        } else if (directToken) {
          console.log('AuthContext: Usando token direto como fallback');
          dispatch({ type: 'INITIALIZE_WITH_TOKEN' });
        } else {
          console.log('AuthContext: Sem autenticação');
          dispatch({
            type: 'INITIALIZE',
            payload: {
              isAuthenticated: false,
              user: null
            }
          });
        }
      } catch (err) {
        console.error('AuthContext: Erro geral:', err);

        const directToken = localStorage.getItem('azureToken') ||
          localStorage.getItem('session') ||
          localStorage.getItem('accessToken');

        if (directToken) {
          console.log('AuthContext: Usando token como fallback após erro');
          dispatch({ type: 'INITIALIZE_WITH_TOKEN' });
        } else {
          dispatch({
            type: 'INITIALIZE',
            payload: {
              isAuthenticated: false,
              user: null
            }
          });
        }
      }
    };

    initialize();
  }, []);

  const login = async (email, password) => {
    const data = await userAPI.LdapLogin(email, password);
    if (!data.error && data.value) {
      localStorage.setItem('session', JSON.stringify(data.value));
      dispatch({
        type: 'LOGIN',
        payload: {
          isAuthenticated: true,
          // avatar: `data:image/jpeg;base64, ${data.value.photo}`,
          user: {
            name: data.value.name,
            jobRole: data.value.jobRole,
            roles: data.value.roles
          }
        }
      });
      // navigate('/');
    } else {
      throw new Error(data.description ? data.description : 'Erro: Utilizador ou password errada!');
    }
  };

  const logout = async () => {
    localStorage.removeItem('session');
    dispatch({ type: 'LOGOUT' });
  };

  const register = async (email, name, password) => {
    const accessToken = await authApi.register({ email, name, password });
    const user = await authApi.me(accessToken);

    localStorage.setItem('accessToken', accessToken);

    dispatch({
      type: 'REGISTER',
      payload: {
        user
      }
    });
  };
  return (
    <AuthContext.Provider
      value={{
        ...state,
        platform: 'JWT',
        login,
        logout,
        register
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

AuthProvider.propTypes = {
  children: PropTypes.node.isRequired,
  instance: PropTypes.any
};

export default AuthContext;
