﻿using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using RnD.BackEnd.API.Model.Weather;
using RnD.BackEnd.Domain.Entities.Weather;

namespace RnD.BackEnd.API.MappingProfiles
{
    /// <summary>
    /// Weather mapping profile
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class WeatherProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="WeatherProfile"/> class.
        /// </summary>
        public WeatherProfile()
        {
            CreateMap<Weather, WeatherDto>()
                .ReverseMap();

            CreateMap<CreateWeatherDto, Weather>();

            //Both are necessary for JsonPatch Service/Application isolation
            CreateMap<JsonPatchDocument<WeatherDto>, JsonPatchDocument<Weather>>();
            CreateMap<Operation<WeatherDto>, Operation<Weather>>();
        }
    }
}
