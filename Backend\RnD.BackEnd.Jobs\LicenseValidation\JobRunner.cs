﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting.Server;
using Microsoft.AspNetCore.Hosting.Server.Features;
using Microsoft.Extensions.Configuration;
using RnD.BackEnd.Domain.Interfaces.Services;
using RnD.BackEnd.Licensing.Interfaces;

namespace RnD.BackEnd.Jobs.LicenseValidation
{
    /// <summary>
    /// Validates license before executing each job
    /// </summary>
    /// <seealso cref="LicenseValidation.IJobRunner" />
    public class JobRunner : IJobRunner
    {
        private readonly ILicenseValidationService _licenseValidationService;
        private readonly IServer _server;
        private readonly IConfiguration _configuration;

        private readonly IJobsService _jobsService;
        private readonly IWorkflowService _workflowService;

        /// <summary>
        /// Initializes a new instance of the <see cref="JobRunner" /> class.
        /// </summary>
        /// <param name="licenseValidationService">The license validation service.</param>
        /// <param name="server">The server.</param>
        /// <param name="configuration">The configuration.</param>
        /// <param name="jobsService">The jobs service.</param>
        /// <param name="worflowService">The worflow service.</param>
        public JobRunner(
            ILicenseValidationService licenseValidationService,
            IServer server,
            IConfiguration configuration,
            IJobsService jobsService,
            IWorkflowService worflowService)
        {
            _licenseValidationService = licenseValidationService;
            _server = server;
            _configuration = configuration;

            _jobsService = jobsService;
            _workflowService = worflowService;
        }

        #region JobsService Jobs

        /// <summary>
        /// Executes <strong>OutputWeatherForecast</strong> job in <strong>JobsService</strong>.
        /// </summary>
        public async Task OutputWeatherForecast()
        {
            if (await IsLicenseValid())
            {
                await _jobsService.OutputWeatherForecast();
            }
        }

        #endregion

        #region Workflows Jobs

        /// <summary>
        /// Executes <strong>CheckOverdueTasks</strong> job in <strong>WorkflowService</strong>.
        /// </summary>
        public async Task CheckOverdueTasks()
        {
            if (await IsLicenseValid())
            {
                await _workflowService.CheckOverdueTasks();
            }
        }

        /// <summary>
        /// Executes <strong>CompleteBackgroundWorkflows</strong> job in <strong>WorkflowService</strong>.
        /// </summary>
        public async Task CompleteBackgroundWorkflows()
        {
            if (await IsLicenseValid())
            {
                await _workflowService.CompleteBackgroundWorkflows();
            }
        }

        #endregion

        #region Private methods

        /// <summary>
        /// Determines whether [is license valid].
        /// </summary>
        /// <returns>
        ///   <c>true</c> if [is license valid]; otherwise, <c>false</c>.
        /// </returns>
        private async Task<bool> IsLicenseValid()
        {
            string serverUrl = _server.Features.Get<IServerAddressesFeature>().Addresses?.FirstOrDefault(x => x.StartsWith("https"));
            if (string.IsNullOrWhiteSpace(serverUrl))
            {
                string appServiceHostname = _configuration["WEBSITE_HOSTNAME"];
                if (!string.IsNullOrWhiteSpace(appServiceHostname))
                {
                    serverUrl = $"https://{appServiceHostname}";
                }
            }

            if (!string.IsNullOrWhiteSpace(serverUrl))
            {
                Uri serverUri = new Uri(serverUrl);

                bool result = await _licenseValidationService.ValidateLicenseAsync(serverUri.Host);
                if (!result)
                {
                    throw new InvalidOperationException("License is invalid, contact administrator.");
                }

                return result;
            }
            else
            {
                throw new InvalidOperationException("Could not get server URL.");
            }
        }

        #endregion
    }
}
