﻿namespace RnD.BackEnd.Domain.Constants
{
    public static class WorkflowConstants
    {
        public const string ColumnNameUserId = "userId";
        public const string ColumnNameTaskStatus = "taskStatus";
        public const string ColumnNameTaskCompleted = "taskCompleted";
        public const string ColumnNameTaskStep = "s.displayName";
        public const string ColumnNameTaskWorkflow = "i.displayName";
        public const string ColumnNameWorkflowStatus = "workflowStatus";
        public const string ColumnNameWorkflowType = "workflowType";

        public const string ColumnNameSourceType = "sourceType";
        public const string ColumnNamePurpose = "purpose";
        public const string ColumnNameInstanceWorkflow = "displayName";

        #region Workflow Purpose

        public const string PURPOSE_APPROVAL = "APPROVAL";
        public const string PURPOSE_COLLECT_FEEDBACK = "COLLECT_FEEDBACK";
        public const string PURPOSE_INTEGRATION = "INTEGRATION";

        #endregion Workflow Purpose

        #region Workflow Status

        public const string WORKFLOW_STATUS_APPROVED = "APPROVED";
        public const string WORKFLOW_STATUS_REJECTED = "REJECTED";
        public const string WORKFLOW_STATUS_IN_PROGRESS = "IN_PROGRESS";
        public const string WORKFLOW_STATUS_CANCELLED = "CANCELLED";

        #endregion Workflow Status

        #region Workflow Approval Messages

        public const string WORKFLOW_APPROVED = "Workflow Approved successfully";
        public const string WORKFLOW_REJECTED = "Workflow Rejected successfully";
        public const string WORKFLOW_CANCELLED = "Workflow Cancelled successfully";
        public const string WORKFLOW_IN_PROGRESS = "Workflow in Progress";

        #endregion Workflow Approval Messages

        #region Workflow Email
        public const string WorkflowSubject = "Workflow Feedbacks";

        #endregion

        public const string WORKFLOW_API_USER = "WORKFLOWAPI";

        public const string WORKFLOW_API_USER_TYPE_API = "API_USER";
    }
}