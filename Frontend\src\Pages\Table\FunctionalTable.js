import { useState, useEffect, useCallback } from 'react';
import { LinearProgress } from '@mui/material';
import FunctionalListTable from '../../Components/FunctionalTable/FunctionalListTable';
import { processAPI } from '../../API/processAPI';
import toast from 'react-hot-toast';

const FunctionalTable = () => {
  const [process, setProcess] = useState([]);
  const [status, setStatus] = useState('Ativo');
  const [isLoading, setLoading] = useState(true);

  //API call to get processes
  const getProcess = useCallback(async (statusData) => {
    try {
      const data = await processAPI.getProcess(null, statusData);
      setLoading(false);
      if (!data?.error) {
        setProcess(data.value.processList);
      } else if (data.exceptionMessages && data.exceptionMessages.hasMessages) {
        data.exceptionMessages.messages.forEach(m => {
          toast.error(m.description);
        })
      } else {
        toast.error("Ocorreu um erro inesperado")
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  useEffect(() => {
    getProcess(status);
  }, [getProcess, status]);

  //Generic Layout for Functional Table
  return (
    <>
      {
        isLoading && <LinearProgress
          variant="indeterminate"
          // top must be header min height
          sx={{ width: '100%', position: 'absolute', top: '64px', left: 0 }}
        />
      }
      <FunctionalListTable
        processes={process}
        changeStatus={setStatus}
        getProcess={getProcess}
        isLoading={isLoading}
      />
    </>
  );
};
export default FunctionalTable;
