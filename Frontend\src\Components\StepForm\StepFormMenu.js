import { useRef, useState, memo } from 'react';
import PropTypes from 'prop-types';
import { ListItemIcon, ListItemText, Tooltip, IconButton, Menu, MenuItem } from '@mui/material';
import {
  DragIndicator as DragIndicatorIcon,
  TextFields as TextFieldsIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';

const StepFormMenu = (props) => {
  const { handleRenaming, handleRemoveStep } = props;
  const anchorRef = useRef(null);
  const [openMenu, setOpenMenu] = useState(false);

  const handleMenuOpen = () => {
    setOpenMenu(true);
  };

  const handleMenuClose = () => {
    setOpenMenu(false);
  };

  return (
    <>
      <Tooltip title="Opções">
        <IconButton
          onClick={handleMenuOpen}
          ref={anchorRef}
        >
          <DragIndicatorIcon fontSize="small" />
        </IconButton>
      </Tooltip>
      <Menu
        anchorEl={anchorRef.current}
        anchorOrigin={{
          horizontal: 'left',
          vertical: 'top'
        }}
        onClose={handleMenuClose}
        open={openMenu}
        PaperProps={{
          sx: {
            maxWidth: '100%',
            width: 256
          }
        }}
        transformOrigin={{
          horizontal: 'left',
          vertical: 'top'
        }}
      >
        <MenuItem onClick={handleRenaming}>
          <ListItemIcon>
            <TextFieldsIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Renomear" />
        </MenuItem>
        <MenuItem onClick={handleRemoveStep}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Remover" />
        </MenuItem>
      </Menu>
    </>
  );
};

StepFormMenu.propTypes = {
  handleRenaming: PropTypes.func,
  handleRemoveStep: PropTypes.func
};

export default memo(StepFormMenu);
