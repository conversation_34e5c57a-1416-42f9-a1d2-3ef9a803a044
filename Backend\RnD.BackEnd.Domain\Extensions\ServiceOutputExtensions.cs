﻿using System.Net;
using RnD.BackEnd.Domain.BusinessMessages;
using RnD.BackEnd.Domain.Entities.Exception;
using RnD.BackEnd.Domain.Entities.Generic;

namespace RnD.BackEnd.Domain.Extensions
{
    /// <summary>
    /// ServiceOutput extensions class.
    /// </summary>
    public static class ServiceOutputExtensions
    {
        #region 200

        /// <summary>
        /// Builds an Ok service output.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="output">The output.</param>
        /// <param name="value">The value.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        public static ServiceOutput<T> Ok<T>(this ServiceOutput<T> output, T value)
        {
            output.Error = false;
            output.Code = HttpStatusCode.OK;
            output.Value = value;
            output.ExceptionMessages.Messages.Clear();
            return output;
        }

        /// <summary>
        /// Builds a Created service output.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="output">The output.</param>
        /// <param name="value">The value.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        public static ServiceOutput<T> Created<T>(this ServiceOutput<T> output, T value)
        {
            output.Error = false;
            output.Code = HttpStatusCode.Created;
            output.Value = value;
            output.ExceptionMessages.Messages.Clear();
            return output;
        }


        /// <summary>
        /// Builds a NoContent service output.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="output">The output.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        public static ServiceOutput<T> NoContent<T>(this ServiceOutput<T> output)
        {
            output.Error = false;
            output.Code = HttpStatusCode.NoContent;
            output.ExceptionMessages.Messages.Clear();
            return output;
        }

        #endregion

        #region 400

        /// <summary>
        /// Builds a BadRequest service output.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="output">The output.</param>
        /// <param name="validationErrors">The validation errors.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        public static ServiceOutput<T> BadRequest<T>(this ServiceOutput<T> output, ModelException validationErrors)
        {
            output.Error = true;
            output.Code = HttpStatusCode.BadRequest;
            output.ExceptionMessages = validationErrors;
            output.Description = CommonMessages.WithErrors;

            return output;
        }

        /// <summary>
        /// Builds a BadRequest service output.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="output">The output.</param>
        /// <param name="message">The message.</param>
        /// <param name="description">The description.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        public static ServiceOutput<T> BadRequest<T>(this ServiceOutput<T> output, string message, string description = null)
        {
            output.Error = true;
            output.Description = !string.IsNullOrWhiteSpace(description) ? description : message;
            output.Code = HttpStatusCode.BadRequest;
            output.ExceptionMessages.Messages.Add(message);

            return output;
        }

        /// <summary>
        /// Builds an Unauthorized service output.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="output">The output.</param>
        /// <param name="message">The message.</param>
        /// <param name="description">The description.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        public static ServiceOutput<T> Unauthorized<T>(this ServiceOutput<T> output, string message, string description = null)
        {
            output.Error = true;
            output.Description = !string.IsNullOrWhiteSpace(description) ? description : message;
            output.Code = HttpStatusCode.Unauthorized;
            output.ExceptionMessages.Messages.Add(message);

            return output;
        }

        /// <summary>
        /// Builds an NotFound service output.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="output">The output.</param>
        /// <param name="message">The message.</param>
        /// <param name="description">The description.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        public static ServiceOutput<T> NotFound<T>(this ServiceOutput<T> output, string message, string description = null)
        {
            output.Error = true;
            output.Description = !string.IsNullOrWhiteSpace(description) ? description : message;
            output.Code = HttpStatusCode.NotFound;
            output.ExceptionMessages.Messages.Add(message);

            return output;
        }

        /// <summary>
        /// Builds a Conflict service output.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="output">The output.</param>
        /// <param name="message">The message.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        public static ServiceOutput<T> Conflict<T>(this ServiceOutput<T> output, string message)
        {
            output.Error = true;
            output.Description = message;
            output.Code = HttpStatusCode.Conflict;
            output.ExceptionMessages.Messages.Add(message);

            return output;
        }

        /// <summary>
        /// Builds an UnprocessableEntity service output.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="output">The output.</param>
        /// <param name="message">The message.</param>
        /// <param name="description">The description.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        public static ServiceOutput<T> UnprocessableEntity<T>(this ServiceOutput<T> output, string message, string description = null)
        {
            output.Error = true;
            output.Description = !string.IsNullOrWhiteSpace(description) ? description : message;
            output.Code = HttpStatusCode.UnprocessableEntity;
            output.ExceptionMessages.Messages.Add(message);

            return output;
        }

        #endregion

        #region 500

        /// <summary>
        /// Builds an InternalServerError service output.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="output">The output.</param>
        /// <param name="message">The message.</param>
        /// <param name="description">The description.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        public static ServiceOutput<T> InternalServerError<T>(this ServiceOutput<T> output, string message, string description = null)
        {
            output.Error = true;
            output.Description = !string.IsNullOrWhiteSpace(description) ? description : message;
            output.Code = HttpStatusCode.InternalServerError;
            output.ExceptionMessages.Messages.Add(message);

            return output;
        }

        #endregion

        #region Custom

        /// <summary>
        /// Builds the output with a custom error code.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="output">The output.</param>
        /// <param name="code">The code.</param>
        /// <param name="message">The message.</param>
        /// <param name="description">The description.</param>
        /// <param name="exceptionMessages">The exception messages.</param>
        /// <returns>
        /// The service output.
        /// </returns>
        public static ServiceOutput<T> CustomErrorCode<T>(this ServiceOutput<T> output, HttpStatusCode code, string message, string description = null, ModelException exceptionMessages = null)
        {
            output.Code = code;
            output.Error = true;
            output.Description = !string.IsNullOrWhiteSpace(description) ? description : message;

            if (exceptionMessages != null && exceptionMessages.HasMessages)
            {
                output.ExceptionMessages = exceptionMessages;
            }
            else
            {
                output.ExceptionMessages.Messages.Add($"{code} - {message}");
            }

            return output;
        }

        #endregion
    }
}
