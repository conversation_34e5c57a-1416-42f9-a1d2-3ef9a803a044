﻿using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Vehicle;
using RnD.BackEnd.API.SwashbuckleExamples;
using RnD.BackEnd.API.SwashbuckleExamples.VehicleController;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Entities.Vehicle;
using RnD.BackEnd.Domain.Interfaces.Services;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.Controllers
{
    /// <summary>
    /// Vehicle controller
    /// </summary>
    /// <seealso cref="Microsoft.AspNetCore.Mvc.ControllerBase" />
    [Produces("application/json")]
    [Route("api/[controller]")]
    [ApiController]
    public class VehicleController : ControllerBase
    {
        private readonly IVehiclesService _vehiclesService;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="VehicleController"/> class.
        /// </summary>
        /// <param name="vehiclesService">The vehicles service.</param>
        /// <param name="mapper">The mapper.</param>
        public VehicleController(IVehiclesService vehiclesService, IMapper mapper)
        {
            _vehiclesService = vehiclesService;
            _mapper = mapper;
        }

        /// <summary>
        /// Creates a new vehicle
        /// </summary>
        /// <param name="request">The new vehicle object</param>
        /// <returns>
        /// The newly created vehicle
        /// </returns>
        /// <response code="201">Returns the newly created vehicle</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpPost]
        [ProducesResponseType(typeof(ApiOutput<VehicleDto>), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ApiOutput<VehicleDto>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(CreateVehicleDto), typeof(VehicleCreate))]
        [SwaggerResponseExample((int)HttpStatusCode.Created, typeof(Vehicle201))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> AddAsync([FromBody] CreateVehicleDto request)
        {
            Vehicle vehicle = _mapper.Map<CreateVehicleDto, Vehicle>(request);

            ServiceOutput<Vehicle> response = await _vehiclesService.AddAsync(vehicle);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Vehicle>, ApiOutput<VehicleDto>>(response));
        }

        /// <summary>
        /// Creates many new vehicles
        /// Caution: Only objects within the same partition may be created
        /// IF UNSURE DO NOT USE
        /// </summary>
        /// <param name="request">The list of new vehicle objects</param>
        /// <returns>The newly created vehicles</returns>
        /// <response code="201">Returns the newly created vehicles</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpPost]
        [Route("many")]
        [ProducesResponseType(typeof(ApiOutput<List<VehicleDto>>), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(List<CreateVehicleDto>), typeof(VehicleMultipleCreate))]
        [SwaggerResponseExample((int)HttpStatusCode.Created, typeof(VehicleList201))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> AddManyAsync([FromBody] List<CreateVehicleDto> request)
        {
            List<Vehicle> listVehicles = _mapper.Map<List<CreateVehicleDto>, List<Vehicle>>(request);

            ServiceOutput<List<Vehicle>> response = await _vehiclesService.AddAsync(listVehicles);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<List<Vehicle>>, ApiOutput<List<VehicleDto>>>(response));
        }

        /// <summary>
        /// Adds or updates a vehicle
        /// Caution: If the ID is not found, it will create a new object
        /// It is safer and more canonical to use separate endpoints
        /// </summary>
        /// <param name="request">The vehicle</param>
        /// <returns>The vehicle</returns>
        /// <response code="200">Returns the newly created vehicles</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpPost]
        [Route("addorupdate")]
        [ProducesResponseType(typeof(ApiOutput<VehicleDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(CreateVehicleDto), typeof(VehicleAddOrUpdate))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(Vehicle200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> AddOrUpdateAsync([FromBody] VehicleDto request)
        {
            Vehicle vehicle = _mapper.Map<VehicleDto, Vehicle>(request);
            ServiceOutput<Vehicle> response = await _vehiclesService.AddOrUpdateAsync(vehicle);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Vehicle>, ApiOutput<VehicleDto>>(response));
        }

        /// <summary>
        /// Updates a vehicle
        /// </summary>
        /// <param name="request">The vehicle to update</param>
        /// <param name="id">The vehicle identifier</param>
        /// <param name="etag">The latest write tag (in concurrency cases, to avoid overwriting the existing document)</param>
        /// <returns>
        /// The updated vehicle
        /// </returns>
        /// <response code="200">Returns the newly created vehicles</response>
        /// <response code="400">Malformed request</response>
        /// <response code="404">Vehicle to update not found</response>
        /// <response code="409">Vehicle to update changed in another context</response>
        /// <response code="500">Internal error</response>
        [HttpPut]
        [Route("{id}")]
        [ProducesResponseType(typeof(ApiOutput<VehicleDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status409Conflict)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(VehicleDto), typeof(VehicleAddOrUpdate))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(Vehicle200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.NotFound, typeof(ResponseExample404))]
        [SwaggerResponseExample((int)HttpStatusCode.Conflict, typeof(ResponseExample409))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> UpdateAsync([FromBody] VehicleDto request, [FromRoute] string id, [FromQuery] string etag)
        {
            Vehicle vehicle = _mapper.Map<VehicleDto, Vehicle>(request);
            ServiceOutput<Vehicle> response = await _vehiclesService.UpdateAsync(vehicle, id, etag);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Vehicle>, ApiOutput<VehicleDto>>(response));
        }


        /// <summary>
        /// Updates a vehicle
        /// </summary>
        /// <param name="id">The vehicle identifier</param>
        /// <param name="request">The changes</param>
        /// <returns>
        /// The updated vehicle
        /// </returns>
        /// <response code="200">Returns the updated vehicle</response>
        /// <response code="400">Malformed request</response>
        /// <response code="404">Vehicle to patch not found</response>
        /// <response code="500">Internal error</response>
        [HttpPatch]
        [Route("{id}")]
        [ProducesResponseType(typeof(ApiOutput<VehicleDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(JsonPatchDocument<VehicleDto>), typeof(VehiclePatch))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(Vehicle200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.NotFound, typeof(ResponseExample404))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> PatchAsync([FromRoute] string id, [FromBody] JsonPatchDocument<VehicleDto> request)
        {
            JsonPatchDocument<Vehicle> changes = _mapper.Map<JsonPatchDocument<VehicleDto>, JsonPatchDocument<Vehicle>>(request);

            ServiceOutput<Vehicle> response = await _vehiclesService.UpdateAsync(changes, id);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Vehicle>, ApiOutput<VehicleDto>>(response));
        }

        /// <summary>
        /// Deletes a vehicle
        /// </summary>
        /// <param name="id">The vehicle identifier</param>
        /// <returns></returns>
        /// <response code="204">Vehicle deleted</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpDelete]
        [Route("{id}")]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status204NoContent)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> DeleteAsync([FromRoute] string id)
        {
            ServiceOutput<object> response = await _vehiclesService.DeleteAsync(id);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<object>, ApiOutput<object>>(response));
        }



        /// <summary>
        /// Gets a vehicle by ID
        /// </summary>
        /// <param name="id">The vehicle identifier</param>
        /// <returns>The vehicle</returns>
        /// <response code="200">Returns a vehicle</response>
        /// <response code="400">Malformed request</response>
        /// <response code="404">Vehicle not found</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [Route("{id}")]
        [ProducesResponseType(typeof(ApiOutput<VehicleDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(Vehicle200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.NotFound, typeof(ResponseExample404))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> GetAsync([FromRoute] string id)
        {
            ServiceOutput<Vehicle> response = await _vehiclesService.GetByIdAsync(id);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Vehicle>, ApiOutput<VehicleDto>>(response));
        }


        /// <summary>
        /// Gets all vehicles with custom filters
        /// </summary>
        /// <param name="id">Vehicle identifiers</param>
        /// <param name="brandId">Vehicle brands</param>
        /// <param name="modelName">Vehicle models</param>
        /// <param name="fuelType">Vehicle fuel types</param>
        /// <param name="version">Vehicle versions</param>
        /// <param name="year">Vehicle production year</param>
        /// <param name="pageNumber">The page number.</param>
        /// <param name="maxItems">The maximum number of items to be returned per request (may return a token to get the next page of results)</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="continuationToken">The token to present the next page of results (URLEncoded)</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// A list of vehicles
        /// </returns>
        /// <response code="200">Returns a list of vehicles</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [ProducesResponseType(typeof(ApiOutput<List<VehicleDto>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(VehicleList200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> ListAsync(
            [FromQuery] List<string> id,
            [FromQuery] List<string> brandId,
            [FromQuery] List<string> modelName,
            [FromQuery] List<string> fuelType,
            [FromQuery] List<string> version,
            [FromQuery] List<int> year,
            [FromQuery] int? pageNumber,
            [FromQuery] int? maxItems,
            [FromQuery] string sortField,
            [FromQuery] bool sortAscending,
            [FromQuery] string continuationToken,
            CancellationToken cancellationToken)
        {
            ServiceOutput<List<Vehicle>> response = await _vehiclesService.ListAsync(
                id,
                brandId,
                modelName,
                fuelType,
                version,
                year,
                pageNumber,
                maxItems,
                sortField,
                sortAscending,
                continuationToken,
                cancellationToken);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<List<Vehicle>>, ApiOutput<List<VehicleDto>>>(response));
        }
    }
}
