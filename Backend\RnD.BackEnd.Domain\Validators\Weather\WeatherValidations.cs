﻿using System;
using System.Collections.Generic;
using RnD.BackEnd.Domain.BusinessMessages;
using RnD.BackEnd.Domain.Entities.Exception;

namespace RnD.BackEnd.Domain.Validators.Weather
{
    /// <summary>
    /// Weather validations class
    /// </summary>
    public static class WeatherValidations
    {
        /// <summary>
        /// Returns true if the model is valid.
        /// </summary>
        /// <param name="weather">The weather</param>
        /// <returns>
        /// ModelException object containing error messages in case they exist.
        /// </returns>
        public static ModelException IsValid(Entities.Weather.Weather weather)
        {
            ModelException modelException = new ModelException()
            {
                Messages = new List<string>()
            };

            if (string.IsNullOrEmpty(weather.Location))
            {
                modelException.Messages.Add(WeatherMessages.Location_Mandatory);
            }

            return modelException;
        }


        /// <summary>
        /// Determines whether [is create request valid] [the specified weather].
        /// </summary>
        /// <param name="weather">The weather.</param>
        /// <returns>
        /// ModelException object containing error messages in case they exist.
        /// </returns>
        public static ModelException IsCreateRequestValid(Entities.Weather.Weather weather)
        {
            ModelException modelException = new ModelException()
            {
                Messages = new List<string>()
            };

            if (string.IsNullOrWhiteSpace(weather.Location))
            {
                modelException.Messages.Add(WeatherMessages.Location_Mandatory);
            }

            return modelException;
        }

        /// <summary>
        /// Determines whether [is update request valid] [the specified vehicle].
        /// </summary>
        /// <param name="weather">The vehicle.</param>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// ModelException object containing error messages in case they exist.
        /// </returns>
        public static ModelException IsUpdateRequestValid(Entities.Weather.Weather weather, Guid id)
        {
            ModelException modelException = IsCreateRequestValid(weather);

            if (weather.Id == Guid.Empty)
            {
                modelException.Messages.Add(WeatherMessages.Id_Mandatory);
            }

            if (weather.Id != id)
            {
                modelException.Messages.Add(WeatherMessages.Id_Mismatch);
            }

            return modelException;
        }

        /// <summary>
        /// Determines if the identifier is valid.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// ModelException object containing error messages in case they exist.
        /// </returns>
        public static ModelException IsIdValid(Guid id)
        {
            ModelException modelException = new ModelException()
            {
                Messages = new List<string>()
            };

            if (id == Guid.Empty)
            {
                modelException.Messages.Add(WeatherMessages.Id_Mandatory);
            }

            return modelException;
        }

    }
}
