﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.JsonPatch;
using Polly;
using RnD.BackEnd.Domain.BusinessMessages;
using RnD.BackEnd.Domain.Constants;
using RnD.BackEnd.Domain.DataModels;
using RnD.BackEnd.Domain.Entities.Exception;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Entities.Weather;
using RnD.BackEnd.Domain.Extensions;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.UnitsOfWork;
using RnD.BackEnd.Domain.Interfaces.Services;
using RnD.BackEnd.Domain.Validators.Weather;

namespace RnD.BackEnd.Service.Services
{
    /// <summary>
    /// Weather service class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Services.IWeatherService" />
    public class WeatherService : IWeatherService
    {
        private readonly IDatabaseUnitOfWork _databaseUnitOfWork;
        private readonly IAsyncPolicy<ServiceOutput<Weather>> _sqlRowversionRetryPolicy;

        /// <summary>
        /// Initializes a new instance of the <see cref="WeatherService" /> class.
        /// </summary>
        /// <param name="databaseUnitOfWork">The database unit of work.</param>
        /// <param name="sqlRowversionRetryPolicy">The SQL rowversion retry policy.</param>
        public WeatherService(IDatabaseUnitOfWork databaseUnitOfWork, IAsyncPolicy<ServiceOutput<Weather>> sqlRowversionRetryPolicy)
        {
            _databaseUnitOfWork = databaseUnitOfWork;
            _sqlRowversionRetryPolicy = sqlRowversionRetryPolicy;
        }

        /// <summary>
        /// Adds a weather
        /// </summary>
        /// <param name="weather">The weather.</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// The weather added
        /// </returns>
        public async Task<ServiceOutput<Weather>> AddAsync(Weather weather, string userId)
        {
            ServiceOutput<Weather> output = new ServiceOutput<Weather>();

            ModelException validationErrors = WeatherValidations.IsCreateRequestValid(weather);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                ResponseModel<Weather> response = await _databaseUnitOfWork.WeatherRepository.AddAsync(weather, userId);

                if (response.Code != HttpStatusCode.Created)
                {
                    output.CustomErrorCode(response.Code, WeatherMessages.Error_Create);
                }
                else
                {
                    output.Created(response.Value);
                }
            }

            return output;
        }

        /// <summary>
        /// Add two weather records for scoped transaction sample.
        /// </summary>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// The list of added weather records.
        /// </returns>
        public async Task<ServiceOutput<List<Weather>>> AddWithTransactionSample(string userId)
        {
            ServiceOutput<List<Weather>> output = new ServiceOutput<List<Weather>>()
            {
                Value = new List<Weather>()
            };

            Weather weatherLisboa = new Weather
            {
                Temperature = 20,
                Location = "Lisboa PT"
            };

            Weather weatherPorto = new Weather
            {
                Temperature = 15,
                Location = "Porto PT"
            };

            try
            {
                _databaseUnitOfWork.Begin();

                ResponseModel<Weather> response = await _databaseUnitOfWork.WeatherRepository.AddAsync(weatherLisboa, userId);
                if (response.Code != HttpStatusCode.Created)
                {
                    output.InternalServerError($"Error {response.Code}", WeatherMessages.Error_Create);
                }
                else
                {
                    output.Value.Add(response.Value);

                    response = await _databaseUnitOfWork.WeatherRepository.AddAsync(weatherPorto, userId);
                    if (response.Code != HttpStatusCode.Created)
                    {
                        output.InternalServerError($"Error {response.Code}", WeatherMessages.Error_Create);
                    }
                    else
                    {
                        output.Value.Add(response.Value);
                    }
                }

                if (output.Error)
                {
                    _databaseUnitOfWork.Rollback();
                }
                else
                {
                    _databaseUnitOfWork.Commit();
                }
            }
            catch (Exception error)
            {
                _databaseUnitOfWork.Rollback();
                output.InternalServerError(error.Message, WeatherMessages.Error_Create);
            }

            return output;
        }

        /// <summary>
        /// Updates the object using put (replace object)
        /// </summary>
        /// <param name="weather">The weather.</param>
        /// <param name="id">The object id</param>
        /// <param name="userId">The user identifier.</param>
        /// <param name="rowversion">The object rowversion</param>
        /// <returns>
        /// The vehicle updated.
        /// </returns>
        public async Task<ServiceOutput<Weather>> UpdateAsync(Weather weather, Guid id, string userId, string rowversion)
        {
            ServiceOutput<Weather> output = new ServiceOutput<Weather>();

            ModelException validationErrors = WeatherValidations.IsUpdateRequestValid(weather, id);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                ResponseModel<Weather> response = await _databaseUnitOfWork.WeatherRepository.UpdateAsync(weather, userId, rowversion);

                output = response.Code switch
                {
                    HttpStatusCode.OK => output.Ok(response.Value),
                    HttpStatusCode.PreconditionFailed => output.Conflict(WeatherMessages.Rowversion_Mismatch),
                    _ => output.CustomErrorCode(response.Code, WeatherMessages.Error_Update)
                };
            }

            return output;
        }

        /// <summary>
        /// Updates the vehicle using patch (discrete properties)
        /// </summary>
        /// <param name="changes">The changes</param>
        /// <param name="id">The id of the vehicle to be updated</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// The vehicle updated.
        /// </returns>
        public async Task<ServiceOutput<Weather>> UpdateAsync(JsonPatchDocument<Weather> changes, Guid id, string userId)
        {
            ServiceOutput<Weather> output = new ServiceOutput<Weather>();

            return await _sqlRowversionRetryPolicy.ExecuteAsync(async () =>
            {
                ResponseModel<Weather> response = await _databaseUnitOfWork.WeatherRepository.GetAsync(id);
                if (response.Code != HttpStatusCode.OK)
                {
                    output.CustomErrorCode(response.Code, WeatherMessages.Error_Get);
                }
                else
                {
                    changes.ApplyTo(response.Value);
                    response = await _databaseUnitOfWork.WeatherRepository.UpdateAsync(response.Value, userId, response.RowVersion);

                    output = response.Code switch
                    {
                        HttpStatusCode.OK => output.Ok(response.Value),
                        HttpStatusCode.PreconditionFailed => output.Conflict(WeatherMessages.Rowversion_Mismatch),
                        _ => output.CustomErrorCode(response.Code, WeatherMessages.Error_Update)
                    };
                }
                return output;
            });
        }

        /// <summary>
        /// Delete a weather record.
        /// </summary>
        /// <param name="id">identifier</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// No content if weather was delete, error otherwise.
        /// </returns>
        public async Task<ServiceOutput<object>> DeleteAsync(Guid id, string userId)
        {
            ServiceOutput<object> output = new ServiceOutput<object>();

            ModelException validationErrors = WeatherValidations.IsIdValid(id);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                if (!(await _databaseUnitOfWork.WeatherRepository.DeleteAsync(id, userId))?.Value ?? false)
                {
                    output.InternalServerError(WeatherMessages.Error_Delete);
                }
                else
                {
                    output.NoContent();
                }
            }

            return output;
        }

        /// <summary>
        /// Gets a weather record by identifier.
        /// </summary>
        /// <param name="id">iudentifier</param>
        /// <returns>
        /// The weather record.
        /// </returns>
        public async Task<ServiceOutput<Weather>> GetByIdAsync(Guid id)
        {
            ServiceOutput<Weather> output = new ServiceOutput<Weather>();

            ModelException validationErrors = WeatherValidations.IsIdValid(id);

            if (validationErrors.HasMessages)
            {
                output.BadRequest(validationErrors);
            }
            else
            {
                ResponseModel<Weather> response = await _databaseUnitOfWork.WeatherRepository.GetAsync(id);

                if (response.Code == HttpStatusCode.NotFound)
                {
                    output.NotFound(WeatherMessages.Not_Found);
                }
                else if (response.Code == HttpStatusCode.OK)
                {
                    output.Value = response.Value;
                    output.Properties.Add(SystemConstants.ROWVERSION, response.RowVersion);
                }
                else
                {
                    output.CustomErrorCode(response.Code, string.Format(WeatherMessages.Error_Get, id));
                }
            }

            return output;
        }

        /// <summary>
        /// Get a list of objects based on filters and paging
        /// </summary>
        /// <param name="id">identifiers</param>
        /// <param name="location">locations</param>
        /// <param name="itemsPerPage">The number of items per page.</param>
        /// <param name="page">page number (starts at 1)</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// List of objects
        /// </returns>
        public async Task<ServiceOutput<IList<Weather>>> ListAsync(
            Guid id,
            string location,
            int? itemsPerPage,
            int? page,
            string sortField,
            bool sortAscending,
            CancellationToken? cancellationToken = null)
        {
            ServiceOutput<IList<Weather>> output = new ServiceOutput<IList<Weather>>();

            ResponseModel<IList<Weather>> response = await _databaseUnitOfWork.WeatherRepository.ListAsync(
                id,
                location,
                itemsPerPage,
                page,
                sortField,
                sortAscending,
                cancellationToken);

            if (response.Code != HttpStatusCode.OK)
            {
                output.CustomErrorCode(response.Code, WeatherMessages.Error_List);
            }
            else
            {
                output.Value = response.Value;
            }

            return output;
        }
    }
}
