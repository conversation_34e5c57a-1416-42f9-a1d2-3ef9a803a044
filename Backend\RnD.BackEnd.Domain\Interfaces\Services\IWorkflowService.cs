﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Entities.Workflow;

namespace RnD.BackEnd.Domain.Interfaces.Services
{
    /// <summary>
    /// Workflows service interface
    /// </summary>
    public interface IWorkflowService
    {
        #region Workflow Definitions

        /// <summary>
        /// Gets the workflow definitions.
        /// </summary>
        /// <param name="sourceType">Type of the source.</param>
        /// <param name="workflowType">Type of the workflow.</param>
        /// <param name="fetchInactive">if set to <c>true</c> [fetch inactive].</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The Service response: The list of workflows.
        /// </returns>
        Task<ServiceOutput<IList<WorkflowDefinition>>> GetDefinitions(string sourceType, string workflowType, bool fetchInactive = false, string userToken = null);

        #endregion Workflow Definitions

        #region Workflow Instances

        /// <summary>
        /// Gets the Workflow instances by sourceId.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The Source Workflow instances.
        /// </returns>
        Task<ServiceOutput<IList<WorkflowInstance>>> GetInstances(WorkflowInstanceBySourceIds request, string userToken);

        /// <summary>
        /// Gets a list of instances of the user.
        /// </summary>
        /// <param name="workflowStatus">The workflow status.</param>
        /// <param name="workflowType">Type of the workflow.</param>
        /// <param name="workflowName">Name of the workflow.</param>
        /// <param name="sourceType">Type of the source.</param>
        /// <param name="purpose">The purpose.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortDirection">The sort direction.</param>
        /// <param name="itemsPerPage">The items per page.</param>
        /// <param name="currentPage">The current page.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The list with the user instances
        /// </returns>
        Task<ServiceOutput<WorkflowInstancesList>> GetUserInstances(
            string workflowStatus,
            string workflowType,
            string workflowName,
            string sourceType,
            string purpose,
            string sortField,
            string sortDirection,
            int? itemsPerPage,
            int? currentPage,
            string userToken);

        /// <summary>
        /// Gets the workflow instance.
        /// </summary>
        /// <param name="instanceId">The instance identifier.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The workflow instance.
        /// </returns>
        Task<ServiceOutput<WorkflowInstance>> GetInstance(string instanceId, string userToken);

        /// <summary>
        /// Cancels the workflow.
        /// </summary>
        /// <param name="instanceId">The instance identifier.</param>
        /// <param name="userId">The user identifier.</param>
        /// <param name="userToken">The user token.</param>
        /// <param name="emailRequest">The email request.</param>
        /// <returns>
        /// true if it work, false otherwise
        /// </returns>
        Task<ServiceOutput<bool>> CancelWorkflow(Guid instanceId, string userId, string userToken, WorkflowEmail emailRequest);

        /// <summary>
        /// Deletes the or cancel workflows by source identifier.
        /// </summary>
        /// <param name="sourceId">The source identifier.</param>
        /// <param name="isDeleteOperation">if set to <c>true</c> [is delete operation].</param>
        /// <param name="userToken">The user token.</param>
        /// <param name="emailRequest">The email request.</param>
        /// <returns>
        /// True if success False otherwise
        /// </returns>
        Task<ServiceOutput<bool>> DeleteOrCancelWorkflowsBySourceId(string sourceId, bool isDeleteOperation, string userToken, WorkflowEmail emailRequest);

        #endregion Workflow Instances

        #region Workflow Tasks

        /// <summary>
        /// Gets the workflow task.
        /// </summary>
        /// <param name="taskId">The task identifier.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The workflow task.
        /// </returns>
        Task<ServiceOutput<WorkflowTask>> GetTask(string taskId, string userToken);

        /// <summary>
        /// Gets a list of tasks.
        /// </summary>
        /// <param name="userId">The user identifier.</param>
        /// <param name="taskStatus">The task status.</param>
        /// <param name="taskCompleted">if set to <c>true</c> [task completed].</param>
        /// <param name="stepName">Name of the step.</param>
        /// <param name="workflowName">Name of the workflow.</param>
        /// <param name="workflowStatus">The workflow status.</param>
        /// <param name="sourceType">Type of the source.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortDirection">The sort direction.</param>
        /// <param name="itemsPerPage">The items per page.</param>
        /// <param name="currentPage">The current page.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The list of tasks.
        /// </returns>
        Task<ServiceOutput<WorkflowTasksList>> GetTasks(
            string userId,
            string taskStatus,
            bool taskCompleted,
            string stepName,
            string workflowName,
            string workflowStatus,
            string sourceType,
            string sortField,
            string sortDirection,
            int? itemsPerPage,
            int? currentPage,
            string userToken);

        /// <summary>
        /// Updates the task.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// True if success False otherwise
        /// </returns>
        Task<ServiceOutput<bool>> UpdateTask(UpdateTask request, string userToken);

        /// <summary>
        /// Updates the multiple task.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// True if success False otherwise
        /// </returns>
        Task<ServiceOutput<List<UpdateTaskOutput>>> UpdateTasks(List<UpdateTask> request, string userToken);

        /// <summary>
        /// Reassigns the task.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// True if success False otherwise
        /// </returns>
        Task<ServiceOutput<bool>> ReassignTask(ReassignTask request, string userToken);

        #endregion Workflow Tasks

        #region Start/trigger Workflow

        /// <summary>
        /// Starts the workflow.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The Instance that was started
        /// </returns>
        Task<ServiceOutput<WorkflowInstance>> StartWorkflow(List<StartWorkflow> request, string userToken);

        /// <summary>
        /// Triggers the automatic workflow.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The Workflow Instance that was Triggered
        /// </returns>
        Task<ServiceOutput<WorkflowInstance>> TriggerAutomaticWorkflow(StartAutomaticWorkflow request, string userToken);

        #endregion Start/trigger Workflow

        #region Jobs

        /// <summary>
        /// Checks the overdue tasks.
        /// </summary>
        Task CheckOverdueTasks();

        /// <summary>
        /// Completes the background workflows.
        /// </summary>
        Task CompleteBackgroundWorkflows();

        #endregion Jobs
    }
}