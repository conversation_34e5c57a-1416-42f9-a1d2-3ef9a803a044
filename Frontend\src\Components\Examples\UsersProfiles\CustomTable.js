import { React, useState, useEffect, isValidElement } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Avatar, Box, Card, Divider, IconButton, Table, TableBody, TableCell, TablePagination, TableRow, Link, Typography } from '@mui/material';
import Scrollbar from '../../Scrollbar';
import TableHeader from '../../TableUtils/TableHeader';
import TableLoading from '../../TableUtils/TableLoading';
import { applyPagination, applySort, applyFilters } from '../../TableUtils/TableUtils';
import { useTranslation } from 'react-i18next';

const CustomTable = ({ data = [], dataHeaders = [], query, isLoading, orderByString = 'displayName', style, modelid, pagination, handleSort, sortOrder, sortOrderBy }) => {
    const [page, setPage] = useState(0);
    const [limit, setLimit] = useState(10);
    const [orderBy, setOrderBy] = useState(orderByString);
    const [order, setOrder] = useState('asc');
    const { t } = useTranslation();

    console.log(data)
    const addPropertiesToLink = (link, item) => {
        const regex = /\$\w*/;
        const propertyName = regex.exec(link)[0].replace('$', '');
        link = link.replace(regex, item[propertyName]);
        link = link.replace('$modelid', modelid)
        return link;
    }

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handleLimitChange = (event) => {
        setLimit(parseInt(event.target.value, 10));
    };

    const handleRequestSort = (property) => {
        const isAsc = orderBy === property && order === 'asc';
        setOrder(isAsc ? 'desc' : 'asc');
        setOrderBy(property);
    };

    const filteredList = applyFilters(dataHeaders, data, query);
    const sortedList = applySort(filteredList, orderBy, order);
    const paginatedList = applyPagination(sortedList, page, limit);

    useEffect(() => {
        // update limit if pagination data comes from parent component
        if (pagination && data.length > 0) {
            setLimit(data.length)
        }
    }, [pagination])

    return (
        <Card>
            <Divider
                sx={{
                    ml: 2,
                    mr: 2,
                    mt: 0
                }}
            />
            <Scrollbar>
                <Box sx={{ minWidth: 700 }}>
                    <Table
                        stickyHeader
                        size="small"
                        style={style}
                    >
                        <TableHeader
                            hasCheckbox={false}
                            headCells={dataHeaders}
                            onSortClick={handleSort ?? handleRequestSort}
                            orderBy={sortOrderBy ?? orderBy}
                            order={sortOrder ?? order}
                        />
                        <TableBody>
                            {
                                isLoading ?
                                    <TableLoading
                                        isLoading={isLoading}
                                        headCells={dataHeaders}
                                        numRows={limit}
                                    />
                                    : <>
                                        {
                                            paginatedList.length <= 0 && query !== "" && (
                                                <TableRow>
                                                    <TableCell
                                                        colSpan={100}
                                                    >
                                                        <Typography>
                                                            {t('common:labels.noResultsFound')}
                                                        </Typography>
                                                    </TableCell>
                                                </TableRow>
                                            )
                                        }
                                        {paginatedList.map((row, index) => (
                                            <TableRow
                                                hover
                                                key={`row_${index}`}
                                            >
                                                {
                                                    dataHeaders.map((cell, cell_id) => {
                                                        let cellContent = null;
                                                        if (cell.link) {
                                                            cellContent = <Box
                                                                sx={{ display: 'flex' }}
                                                            >
                                                                <Link
                                                                    component={RouterLink}
                                                                    to={addPropertiesToLink(cell.link, row)}
                                                                    color="primary"
                                                                    sx={{
                                                                        fontWeight: 'bold',
                                                                        alignSelf: 'center',
                                                                        marginLeft: 1
                                                                    }}
                                                                >
                                                                    {row[cell.id]}
                                                                </Link>
                                                            </Box>
                                                            cellContent = <Box
                                                                sx={{ display: 'flex', alignItems: 'center' }}
                                                            >
                                                                <Avatar
                                                                    mx={2}
                                                                    alt={row[cell.id]}
                                                                    src=""
                                                                    sx={{
                                                                        marginRight: 1
                                                                    }}
                                                                />
                                                                {row[cell.id]}
                                                            </Box>
                                                        } else if (cell.action) {
                                                            const label = isValidElement(cell.label) ? cell.label : cell.label(row);
                                                            cellContent = label ? (
                                                                <Box
                                                                    sx={{ display: 'flex', justifyContent: 'center' }}
                                                                >
                                                                    <IconButton
                                                                        aria-controls="simple-menu"
                                                                        aria-haspopup="true"
                                                                        onClick={(e) => cell.action(e, row)}
                                                                    >
                                                                        {label}
                                                                    </IconButton>
                                                                </Box>
                                                            ) : undefined;
                                                        } else {
                                                            cellContent = row[cell.id]
                                                        }
                                                        return <TableCell
                                                            key={`cell_${cell_id}`}
                                                            align={cell.align}
                                                        >
                                                            {cellContent}
                                                        </TableCell>
                                                    })
                                                }
                                            </TableRow>
                                        ))}
                                    </>
                            }
                        </TableBody>
                    </Table>
                </Box>
            </Scrollbar>
            {pagination ? pagination : <TablePagination
                component="div"
                count={filteredList.length}
                onPageChange={handlePageChange}
                onRowsPerPageChange={handleLimitChange}
                page={page}
                rowsPerPage={limit}
                rowsPerPageOptions={[5, 10, 25]}
                labelRowsPerPage={t('common:table.rowsPerPage')}
                labelDisplayedRows={
                    ({ from, to, count }) => {
                        return '' + from + '-' + to + ` ${t('common:table.of')} ` + count
                    }
                }
            />}
        </Card>
    );
};

CustomTable.propTypes = {
    data: PropTypes.array.isRequired,
    dataHeaders: PropTypes.array,
    query: PropTypes.string,
    isLoading: PropTypes.bool,
    orderByString: PropTypes.string,
    style: PropTypes.object,
    modelid: PropTypes.string,
    pagination: PropTypes.element,
    handleSort: PropTypes.func,
    sortOrder: PropTypes.string,
    sortOrderBy: PropTypes.string
};

export default CustomTable;
