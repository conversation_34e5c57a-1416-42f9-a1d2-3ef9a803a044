export const getUnique = (collection, key) => {
    const uniqueCollection = [];
    if (collection && collection.length > 0) {
        collection.forEach(element => {
            const match = uniqueCollection.find(x => x[key] === element[key]);
            if (match === undefined) {
                uniqueCollection.push(element)
            }
        });
    }
    return uniqueCollection;
}

export const getAllIndexes = (arr, lookingIn, what) => {
    const indexes = [];
    for (let i = 0; i < arr.length; i++) {
        if (arr[i][lookingIn] === what) {
            indexes.push(i);
        }
    }
    return indexes;
}

export const groupByKey = (collection, key, labelGroups) => {
    const _collection = [...collection];
    const groups = [];
    if (!collection || collection.length <= 0) {
        return;
    }

    const dataCollection = _collection.reduce((rcollection, curr) => {
        (rcollection[curr[key]] = rcollection[curr[key]] || []).push(curr);
        return rcollection;
    }, {});

    for (const t of Object.keys(dataCollection)) {
        let data;
        if (labelGroups) {
            data = {
                group: dataCollection[t][0][key],
                data: dataCollection[t]
            }
        } else {
            data = dataCollection[t];
        }
        groups.push(data);
    }

    return groups;
}
