using Microsoft.Extensions.Logging;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace RnD.BackEnd.API.Services
{
    public class LogProcessamentoService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<LogProcessamentoService> _logger;

        public LogProcessamentoService(
            ApplicationDbContext context,
            ILogger<LogProcessamentoService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task RegistrarLog(int fileId, string mensagem, TipoLog tipoLog = TipoLog.Info)
        {
            try
            {
                // Limitar o tamanho da mensagem para evitar truncamento na base de dados
                var mensagemLimitada = mensagem?.Length > 500 ? mensagem.Substring(0, 500) + "..." : mensagem;

                var log = new LogProcessamento
                {
                    FileID = fileId,
                    Mensagem = mensagemLimitada,
                    TipoLog = tipoLog.ToString(),
                    DataRegistro = DateTime.Now
                };

                _context.LogsProcessamento.Add(log);
                await _context.SaveChangesAsync();

                // Também registra no logger do sistema para aparecer no console/Application Insights
                switch (tipoLog)
                {
                    case TipoLog.Info:
                        _logger.LogInformation($"File {fileId}: {mensagem}");
                        break;
                    case TipoLog.Aviso:
                        _logger.LogWarning($"File {fileId}: {mensagem}");
                        break;
                    case TipoLog.Erro:
                        _logger.LogError($"File {fileId}: {mensagem}");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erro ao registar log para arquivo {fileId}: {mensagem}");
            }
        }

        public async Task<List<LogProcessamento>> ObterLogsPorArquivo(int fileId)
        {
            return await _context.LogsProcessamento
                .Where(l => l.FileID == fileId)
                .OrderByDescending(l => l.DataRegistro)
                .ToListAsync();
        }

        public async Task<List<LogProcessamento>> ObterUltimosLogs(int quantidade = 100)
        {
            return await _context.LogsProcessamento
                .OrderByDescending(l => l.DataRegistro)
                .Take(quantidade)
                .ToListAsync();
        }

        public async Task<List<LogProcessamento>> ObterLogsPorTipo(TipoLog tipoLog, int quantidade = 100)
        {
            var tipoString = tipoLog.ToString();
            return await _context.LogsProcessamento
                .Where(l => l.TipoLog == tipoString)
                .OrderByDescending(l => l.DataRegistro)
                .Take(quantidade)
                .ToListAsync();
        }
    }
}