import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON>,
} from "@mui/material";
import { useTheme } from '@emotion/react';
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import getCustomMuiSelect from "./mocks/customMuiSelect";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import { styled } from "@mui/system";

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh",
})

export default function CustomMuiSelect() {
  const theme = useTheme();
  const [code, setCode] = useState(getCustomMuiSelect);

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box
        sx={{
          m: "auto",
          textAlign: "center"
        }}
      >
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h2>Custom Mui Select</h2>
          <p>
            The following code snippet demonstrates how the Custom Mui Select
            component is written:
          </p>
        </Box>
        {/*Custom Filters Example Code*/}
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={code} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={code}
              onValueChange={(_code) => setCode(_code)}
              highlight={(_code) => highlight(_code, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>

        <Box
          sx={{
            m: "auto",
            textAlign: "justify",
            maxWidth: "80%",
            color: "textPrimary",
          }}
        >
          <p>
            Custom Mui Select receives the following props and is used in
            conjunction with
            <Link
              href="https://mui.com/material-ui/react-table/"
              underline="none"
            >
              {" "}
              Material UI{" "}
            </Link>
            components.
          </p>
          <ul>
            <li>
              <b>data</b> - Options that we want to be displayed;
            </li>
            <li>
              <b>name</b> - Input Title;
            </li>
            <li>
              <b>label</b> - Input PlaceHolder;
            </li>
            <li>
              <b>selected</b> - Selected option;
            </li>
            <li>
              <b>handleSelect</b> - When clicking the Save button, an event will
              be launched.
            </li>
            <li>
              <b>width</b> - Input width;
            </li>
            <li>
              <b>itemText</b> - Equivalent to the text we want to be displayed
              (i.e.: Label);
            </li>
            <li>
              <b>itemValue</b> - Equivalent to the value we want to pass (i.e.:
              id);
            </li>
            <li>
              <b>hasEmpty</b> - Boolean value
            </li>
          </ul>
        </Box>
      </Box>
      <ChangeLogEntry
        version="V1.0.0"
        changeLog={ChangeLog()}
      />
    </Box>
  );
}
const ChangeLog = () => {
  const theme = useTheme()
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content:
        "Imported Custom Filters Component. Two Filtering options: CheckBox and TextField",
      className: chips.green,
    },
  ];

  return changeLog;
};
