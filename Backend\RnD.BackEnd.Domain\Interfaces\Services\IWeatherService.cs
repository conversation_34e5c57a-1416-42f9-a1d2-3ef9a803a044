﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.JsonPatch;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Entities.Weather;

namespace RnD.BackEnd.Domain.Interfaces.Services
{
    /// <summary>
    /// Weather service interface
    /// </summary>
    public interface IWeatherService
    {
        /// <summary>
        /// Adds a weather
        /// </summary>
        /// <param name="weather">The weather.</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// The weather added
        /// </returns>
        Task<ServiceOutput<Weather>> AddAsync(Weather weather, string userId);

        /// <summary>
        /// Add two weather records for scoped transaction sample.
        /// </summary>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// The list of added weather records.
        /// </returns>
        Task<ServiceOutput<List<Weather>>> AddWithTransactionSample(string userId);

        /// <summary>
        /// Updates the object using put (replace object)
        /// </summary>
        /// <param name="weather">The weather.</param>
        /// <param name="id">The object id</param>
        /// <param name="userId">The user identifier.</param>
        /// <param name="rowversion">The object rowversion</param>
        /// <returns>
        /// The vehicle updated.
        /// </returns>
        Task<ServiceOutput<Weather>> UpdateAsync(Weather weather, Guid id, string userId, string rowversion);

        /// <summary>
        /// Updates the vehicle using patch (discrete properties)
        /// </summary>
        /// <param name="changes">The changes</param>
        /// <param name="id">The id of the vehicle to be updated</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// The vehicle updated.
        /// </returns>
        Task<ServiceOutput<Weather>> UpdateAsync(JsonPatchDocument<Weather> changes, Guid id, string userId);

        /// <summary>
        /// Delete a weather record.
        /// </summary>
        /// <param name="id">identifier</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// No content if weather was delete, error otherwise.
        /// </returns>
        Task<ServiceOutput<object>> DeleteAsync(Guid id, string userId);

        /// <summary>
        /// Gets a weather record by identifier.
        /// </summary>
        /// <param name="id">iudentifier</param>
        /// <returns>
        /// The weather record.
        /// </returns>
        Task<ServiceOutput<Weather>> GetByIdAsync(Guid id);

        /// <summary>
        /// Get a list of objects based on filters and paging
        /// </summary>
        /// <param name="id">identifiers</param>
        /// <param name="location">locations</param>
        /// <param name="itemsPerPage">The number of items per page.</param>
        /// <param name="page">page number (starts at 1)</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// List of objects
        /// </returns>
        Task<ServiceOutput<IList<Weather>>> ListAsync(
            Guid id,
            string location,
            int? itemsPerPage,
            int? page,
            string sortField,
            bool sortAscending,
            CancellationToken? cancellationToken = null);
    }
}
