{"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"location": {"defaultValue": "[resourceGroup().location]", "type": "string", "metadata": {"description": "Provide the location for resources."}}, "sql-ServerName": {"type": "string", "minLength": 1}, "sql-AdminLogin": {"type": "string", "minLength": 1}, "sql-AdminLoginPassword": {"type": "securestring"}, "database-Name": {"type": "string", "minLength": 1}, "database-Collation": {"type": "string", "minLength": 1, "defaultValue": "SQL_Latin1_General_CP1_CI_AS"}, "database-Edition": {"type": "string", "defaultValue": "Basic", "allowedValues": ["Basic", "Standard", "Premium"]}, "database-RequestedServiceObjectiveName": {"type": "string", "defaultValue": "Basic", "allowedValues": ["Basic", "S0", "S1", "S2", "P1", "P2", "P3"], "metadata": {"description": "Describes the performance level for Edition"}}}, "variables": {}, "resources": [{"name": "[parameters('sql-ServerName')]", "type": "Microsoft.Sql/servers", "location": "[parameters('location')]", "apiVersion": "2014-04-01-preview", "dependsOn": [], "tags": {"displayName": "km-sql-server"}, "properties": {"administratorLogin": "[parameters('sql-AdminLogin')]", "administratorLoginPassword": "[parameters('sql-AdminLoginPassword')]"}, "resources": [{"name": "AllowAllWindowsAzureIps", "type": "firewallrules", "location": "[parameters('location')]", "apiVersion": "2014-04-01-preview", "dependsOn": ["[resourceId('Microsoft.Sql/servers', parameters('sql-ServerName'))]"], "properties": {"startIpAddress": "0.0.0.0", "endIpAddress": "0.0.0.0"}}, {"name": "[parameters('database-Name')]", "type": "databases", "location": "[parameters('location')]", "apiVersion": "2014-04-01-preview", "dependsOn": ["[resourceId('Microsoft.Sql/servers', parameters('sql-ServerName'))]"], "tags": {"displayName": "sql-database"}, "properties": {"collation": "[parameters('database-Collation')]", "edition": "[parameters('database-Edition')]", "maxSizeBytes": "1073741824", "requestedServiceObjectiveName": "[parameters('database-RequestedServiceObjectiveName')]"}}]}], "outputs": {"Database-ConnectionString": {"type": "string", "value": "[concat('Server=tcp:',reference(parameters('sql-ServerName')).fullyQualifiedDomainName,',1433;Initial Catalog=',parameters('database-Name'),';Persist Security Info=False;User ID=',parameters('sql-AdminLogin'),';Password=',parameters('sql-AdminLoginPassword'),';MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;')]"}}}