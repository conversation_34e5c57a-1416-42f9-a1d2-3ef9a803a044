import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert
} from "@mui/material";
import ErrorIcon from '@mui/icons-material/Error';
import { format } from 'date-fns';
import ptBR from 'date-fns/locale/pt-BR';
import axios from 'axios';
import { useTranslation } from "react-i18next";

const ErrorList = () => {
  const [erros, setErros] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { t } = useTranslation();
  const formatarData = (dataString) => {
    try {
      const data = new Date(dataString);
      return format(data, 'dd/MM/yyyy HH:mm:ss', { locale: ptBR });
    } catch (err) {
      return dataString;
    }
  };

  useEffect(() => {
    document.title = `${t('titles:importErrors')} | Portal Cloud Services`;
    buscarErros();
  }, []);

  const buscarErros = async () => {
    setLoading(true);
    try {
      // Buscar apenas logs de erro
      const response = await axios.get(`${process.env.REACT_APP_WEBAPI_URL}/api/LogProcessamento/tipo/Erro`);
      setErros(response.data);
    } catch (err) {
      console.error("Erro ao conectar à API:", err);
      setError("Não foi possível carregar os erros. Por favor, tente novamente mais tarde.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Box sx={{ padding: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ErrorIcon color="error" /> {t('titles:importErrors')}
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer component={Paper} variant="outlined">
            <Table sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow sx={{ bgcolor: 'background.paper' }}>
                  <TableCell>{t('common:tableHeaders.hourDate')} </TableCell>
                  <TableCell>{t('common:tableHeaders.fileID')} </TableCell>
                  <TableCell>{t('common:tableHeaders.errorMessage')} </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {erros.length > 0 ? (
                  erros.map((erro) => (
                    <TableRow key={erro.id} sx={{ '&:hover': { bgcolor: '#fff8f8' } }}>
                      <TableCell>{formatarData(erro.dataRegistro)}</TableCell>
                      <TableCell>
                        <Chip
                          label={`${t('common:tableHeaders.file')} #${erro.fileID}`}
                          size="small"
                          color="default"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell sx={{ color: 'error.main' }}>{erro.mensagem}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={3} align="center">
                      <Typography variant="body1" color="success.main">
                        Não há erros de importação registados no sistema.
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>
    </div>
  );
};

export default ErrorList;
