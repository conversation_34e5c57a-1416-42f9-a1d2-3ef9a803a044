import axios from "axios";

// URL da API
const API_BASE_URL = `${process.env.REACT_APP_WEBAPI_URL}/api`;

// Obter todos os consumos de subscrições
export const getAllConsumoSubscricoes = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/ConsumoSubscricao`);
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar consumos de subscrições:", error);
    throw error;
  }
};

// Obter consumos por ID de subscrição
export const getConsumoBySubscriptionId = async (subscriptionId) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/ConsumoSubscricao/subscription/${subscriptionId}`);
    return response.data;
  } catch (error) {
    console.error(`Erro ao buscar consumos da subscrição ${subscriptionId}:`, error);
    throw error;
  }
};

// Obter consumos por período
export const getConsumoByPeriod = async (startDate, endDate) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/ConsumoSubscricao/period?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
    );
    return response.data;
  } catch (error) {
    console.error("Erro ao buscar consumos por período:", error);
    throw error;
  }
};

// Obter consumos por cliente
export const getConsumoByClienteId = async (clienteId) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/ConsumoSubscricao/cliente/${clienteId}`);
    return response.data;
  } catch (error) {
    console.error(`Erro ao buscar consumos do cliente ${clienteId}:`, error);
    
    if (error.response && error.response.status === 404) {
      console.log("Nenhum consumo encontrado para o cliente", clienteId);
    }
    
    throw error;
  }
};

// Obter resumo de consumos por cliente
export const getConsumoSummaryByCliente = async (clienteId, startDate = null, endDate = null) => {
  try {
    let url = `${API_BASE_URL}/ConsumoSubscricao/cliente/${clienteId}/summary`;
    
    const params = [];
    if (startDate) {
      params.push(`startDate=${startDate.toISOString()}`);
    }
    if (endDate) {
      params.push(`endDate=${endDate.toISOString()}`);
    }
    
    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }
    
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error(`Erro ao buscar resumo de consumos do cliente ${clienteId}:`, error);
    
    if (error.response && error.response.status === 404) {
      console.log("Nenhum resumo de consumo encontrado para o cliente", clienteId, "no período especificado");
    }
    
    throw error;
  }
};

export default {
  getAllConsumoSubscricoes,
  getConsumoBySubscriptionId,
  getConsumoByPeriod,
  getConsumoByClienteId,
  getConsumoSummaryByCliente
}; 