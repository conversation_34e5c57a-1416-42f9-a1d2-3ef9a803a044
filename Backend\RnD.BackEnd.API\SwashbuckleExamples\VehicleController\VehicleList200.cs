﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Vehicle;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.VehicleController
{
    /// <summary>
    /// The example of a 200 response when listing vehicles.
    /// </summary>
    public class VehicleList200 : IExamplesProvider<ApiOutput<List<VehicleDto>>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 for a list response.
        /// </returns>
        public ApiOutput<List<VehicleDto>> GetExamples()
        {
            return new ApiOutput<List<VehicleDto>>
            {
                Code = HttpStatusCode.OK,
                Value = new List<VehicleDto>
                {
                    new VehicleDto
                    {
                        Id = Guid.NewGuid().ToString(),
                        Brand = "Honda",
                        BrandId = "0004",
                        FuelType = "PETROL",
                        ModelName = "Civic",
                        Version = "123",
                        Year = 2022
                    },
                    new VehicleDto
                    {
                        Id = Guid.NewGuid().ToString(),
                        Brand = "Honda",
                        BrandId = "0004",
                        FuelType = "PETROL",
                        ModelName = "Accord",
                        Version = "322",
                        Year = 1998
                    }
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}
