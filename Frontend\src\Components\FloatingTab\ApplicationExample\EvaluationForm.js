import { React, useState } from 'react';
import PropTypes from 'prop-types';
import { Box, Divider, Tabs, Tab, CircularProgress, Grid } from '@mui/material';
import TabPanel from './TabPanel';
import FloatingTab from '../index';
import Metrics from './Metrics';

const EvaluationForm = ({
  loadingData,
  canEdit,
  sectionTabs,
  sections,
  metrics,
  handleUpdateQuizAnswer,
  handleUpdateQuizYesOrNo,
  handleSaveQuizComment,
  handleUpdateFiles,
  setFloatingTabOpen = () => null,
  requestCalculationQuizSurvey,
  requestCalculationQuizSurvey_timeout,
  errors
}) => {
  const [activeTab, setActiveTab] = useState(0);

  const handleChangeActiveTab = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <>
      <Grid
        container
      >
        <Grid
          item
          xs={12}
        >
          {
            loadingData ?
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  my: 2,
                  marginLeft: '50%',
                  width: '80px',
                  height: '80px'
                }}
              >
                <CircularProgress />
              </Box>
              :
              <Grid
                container
              >
                <Grid
                  item
                  xs={12}
                >
                  <Tabs
                    TabIndicatorProps={{ sx: { background: "primary", height: 2, width: "120px" } }}
                    onChange={handleChangeActiveTab}
                    scrollButtons="auto"
                    textColor="primary"
                    value={activeTab}
                    variant="fullWidth"
                    sx={{ background: 'none !important' }}
                  >
                    {
                      sectionTabs.map((section, index) => {
                        return section ? <Tab
                          key={section}
                          label={section}
                          value={index}
                        />
                          :
                          null
                      })
                    }
                  </Tabs>
                  {
                    sections.map((section, index) => (
                      <TabPanel
                        value={activeTab}
                        index={index}
                        key={`section_${index}`}
                        section={section}
                        updateQuizAnswer={handleUpdateQuizAnswer}
                        updateQuizYesOrNo={handleUpdateQuizYesOrNo}
                        saveQuizComment={handleSaveQuizComment}
                        updateFiles={handleUpdateFiles}
                        canEdit={canEdit}
                        errors={errors}
                      >
                        {section.ID}
                      </TabPanel>
                    ))
                  }
                </Grid>
              </Grid>
          }
        </Grid>
      </Grid>
      {
        metrics && metrics.length > 0 && <FloatingTab
          isLoading={requestCalculationQuizSurvey_timeout}
          onOpen={() => {
            setFloatingTabOpen(true);
            requestCalculationQuizSurvey();
          }}
          onClose={() => setFloatingTabOpen(false)}
          data={
            <Box>
              {
                metrics.map((t, i) => {
                  return <Box
                    key={i}
                    sx={{
                      mb: i !== metrics.length - 1 ? 2 : 0
                    }}
                  >
                    <Metrics
                      data={t}
                      isLoading={requestCalculationQuizSurvey_timeout}
                    />
                    {
                      i !== (metrics.length - 1) && <Divider />
                    }
                  </Box>;
                })
              }
            </Box>
          }
        />
      }
    </>
  );
};

EvaluationForm.propTypes = {
  loadingData: PropTypes.bool,
  canEdit: PropTypes.bool,
  sectionTabs: PropTypes.array,
  sections: PropTypes.array,
  metrics: PropTypes.array,
  handleUpdateQuizAnswer: PropTypes.func,
  handleUpdateQuizYesOrNo: PropTypes.func,
  handleSaveQuizComment: PropTypes.func,
  handleUpdateFiles: PropTypes.func,
  setFloatingTabOpen: PropTypes.func,
  requestCalculationQuizSurvey: PropTypes.func,
  requestCalculationQuizSurvey_timeout: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.number
  ]),
  errors: PropTypes.object
}

export default EvaluationForm;
