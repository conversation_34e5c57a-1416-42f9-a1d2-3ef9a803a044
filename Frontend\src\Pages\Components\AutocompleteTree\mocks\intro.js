const getIntro = () => {
    return `
    //Example:
    <Box
        sx={{ maxWidth: "80%", m: 'auto', mb: 2 }}
    >
        <CustomAutocompleteTree
            label={t('common:tableActions.search')}                    
            placeholder={t('common:tableActions.option')}
            text="text"
            id="id"
            data={options}
            multiple
            parentid="parentid"
        />
    </Box>
    `;
}

export default getIntro;
