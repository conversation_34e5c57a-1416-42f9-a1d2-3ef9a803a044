﻿using RnD.BackEnd.API.Model.Vehicle;
using System.Collections.Generic;
using RnD.BackEnd.API.Model.Workflow;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// Workflow Start request example
    /// </summary>
    public class StartWorkflowRequest : IMultipleExamplesProvider<List<StartWorkflowDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to Start Workflow.
        /// </returns>
        public IEnumerable<SwaggerExample<List<StartWorkflowDto>>> GetExamples()
        {
            yield return SwaggerExample.Create("Vacation - With Custom Email", new List<StartWorkflowDto>()
            {
                new StartWorkflowDto
                {
                     DisplayName = "Vacation Approval",
                    SourceId = "Vacation/2023",
                    SourceUrl = "www.framework_vacation_2023.pt",
                    SourceType = "VACATION",
                    WorkflowType = "MANUAL",
                    Purpose = "APPROVAL",
                    UserId = "<EMAIL>",
                    AutoComplete = false,
                    Steps = new List<CustomStepDto>()
                    {
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>",
                                "<EMAIL>"
                            },
                            RequireAllToApprove = true,
                            SkipOverOtherTasks = false,
                            StepName = "Phase 1",
                            Order = 1
                        },
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>"
                            },
                            RequireAllToApprove = false,
                            SkipOverOtherTasks = true,
                            StepName = "Phase 2",
                            Order = 2
                        }
                    },
                    EmailRequest = new WorkflowEmailDto()
                    {
                        TemplateId = "d-c43961e085e04f1c8e1f0192db1c69d8",
                        Recipients = new List<string>() { "<EMAIL>", "<EMAIL>" },
                        TemplateData = new Dictionary<string, string>()
                          {
                              { "Message", "This is an example of Email" }
                          },
                        Subject = "FrameWork Example of Email",
                        SendEmail = true
                    }
                }
            });

            yield return SwaggerExample.Create("Project - Not Sending Email - With AutoComplete APPROVED", new List<StartWorkflowDto>()
            {
                new StartWorkflowDto()
                {
                    DisplayName = "Project Approval",
                    SourceId = "Project/2023",
                    SourceUrl = "www.framework_Project_2023.pt",
                    SourceType = "PROJECT",
                    WorkflowType = "MANUAL",
                    Purpose = "APPROVAL",
                    UserId = "<EMAIL>",
                    AutoComplete = true,
                    WorkflowStatus = "ACCEPTED",
                    Steps = new List<CustomStepDto>()
                    {
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>",
                                "<EMAIL>"
                            },
                            RequireAllToApprove = true,
                            SkipOverOtherTasks = false,
                            StepName = "Phase 1",
                            Order = 1
                        },
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>"
                            },
                            RequireAllToApprove = false,
                            SkipOverOtherTasks = true,
                            StepName = "Phase 2",
                            Order = 2
                        }
                    },
                    EmailRequest = new WorkflowEmailDto()
                    {
                        TemplateId = "d-c43961e085e04f1c8e1f0192db1c69d8",
                        Recipients = new List<string>() { "<EMAIL>", "<EMAIL>" },
                        TemplateData = new Dictionary<string, string>()
                          {
                              { "Message", "This is an example of Email" }
                          },
                        Subject = "FrameWork Example of Email",
                        SendEmail = false
                    }
                }
            });

            yield return SwaggerExample.Create("Document - With Default Email - With AutoComplete REJECTED", new List<StartWorkflowDto>()
            {
                new StartWorkflowDto()
                {
                    DisplayName = "Document Approval",
                    SourceId = "0037117",
                    SourceUrl = "www.framework_Document_2023.pt",
                    SourceType = "DOCUMENT",
                    WorkflowType = "MANUAL",
                    Purpose = "APPROVAL",
                    UserId = "<EMAIL>",
                    AutoComplete = true,
                    WorkflowStatus = "REJECTED",
                    Steps = new List<CustomStepDto>()
                    {
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>",
                                "<EMAIL>"
                            },
                            RequireAllToApprove = true,
                            SkipOverOtherTasks = false,
                            StepName = "Phase 1",
                            Order = 1
                        },
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>"
                            },
                            RequireAllToApprove = false,
                            SkipOverOtherTasks = true,
                            StepName = "Phase 2",
                            Order = 2
                        }
                    },
                    EmailRequest = null
                }
            });

            yield return SwaggerExample.Create("Timesheet - With Default Email", new List<StartWorkflowDto>()
            {
                new StartWorkflowDto()
                {
                    DisplayName = "Timesheet Approval",
                    SourceId = "jmagalhaes/09-02-2023",
                    SourceUrl = "www.framework_Timesheet_09-02-2023.pt",
                    SourceType = "TIMESHEET",
                    WorkflowType = "MANUAL",
                    Purpose = "APPROVAL",
                    UserId = "<EMAIL>",
                    AutoComplete = false,
                    Steps = new List<CustomStepDto>()
                    {
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>",
                                "<EMAIL>"
                            },
                            RequireAllToApprove = true,
                            SkipOverOtherTasks = false,
                            StepName = "Phase 1",
                            Order = 1
                        },
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>"
                            },
                            RequireAllToApprove = false,
                            SkipOverOtherTasks = true,
                            StepName = "Phase 2",
                            Order = 2
                        }
                    }
                }
            });

            yield return SwaggerExample.Create("Multiple Workflows - Vacation, Timesheet", new List<StartWorkflowDto>()
            {
                new StartWorkflowDto
                {
                    DisplayName = "Vacation Approval",
                    SourceId = "Vacation/2023",
                    SourceUrl = "www.framework_vacation_2023.pt",
                    SourceType = "VACATION",
                    WorkflowType = "MANUAL",
                    Purpose = "APPROVAL",
                    UserId = "<EMAIL>",
                    AutoComplete = false,
                    Steps = new List<CustomStepDto>()
                    {
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>",
                                "<EMAIL>"
                            },
                            RequireAllToApprove = true,
                            SkipOverOtherTasks = false,
                            StepName = "Phase 1",
                            Order = 1
                        },
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>"
                            },
                            RequireAllToApprove = false,
                            SkipOverOtherTasks = true,
                            StepName = "Phase 2",
                            Order = 2
                        }
                    },
                    EmailRequest = new WorkflowEmailDto()
                    {
                        TemplateId = "d-c43961e085e04f1c8e1f0192db1c69d8",
                        Recipients = new List<string>() { "<EMAIL>", "<EMAIL>" },
                        TemplateData = new Dictionary<string, string>()
                          {
                              { "Message", "This is an example of Email" }
                          },
                        Subject = "FrameWork Example of Email",
                        SendEmail = true
                    }
                },
                new StartWorkflowDto()
                {
                    DisplayName = "Timesheet Approval",
                    SourceId = "jmagalhaes/09-02-2023",
                    SourceUrl = "www.framework_Timesheet_09-02-2023.pt",
                    SourceType = "TIMESHEET",
                    WorkflowType = "MANUAL",
                    Purpose = "APPROVAL",
                    UserId = "<EMAIL>",
                    AutoComplete = false,
                    Steps = new List<CustomStepDto>()
                    {
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>",
                                "<EMAIL>"
                            },
                            RequireAllToApprove = true,
                            SkipOverOtherTasks = false,
                            StepName = "Phase 1",
                            Order = 1
                        },
                        new CustomStepDto()
                        {
                            StepUsers = new List<string>()
                            {
                                "<EMAIL>"
                            },
                            RequireAllToApprove = false,
                            SkipOverOtherTasks = true,
                            StepName = "Phase 2",
                            Order = 2
                        }
                    }
                }
            });
        }
    }
}
