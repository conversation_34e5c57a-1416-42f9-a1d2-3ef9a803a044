import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  LinearProgress,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Typography
} from '@mui/material';
import {
  AssignmentInd as AssignmentIndIcon,
  Work as WorkIcon,
  Home as HomeIcon,
  ContactPhone as ContactPhoneIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const ProfileAbout = (props) => {
  const {
    currentCity,
    currentJobTitle,
    departmentTitle,
    email,
    telephone,
    profileProgress,
    no,
    ...other
  } = props;
  const { t } = useTranslation();

  return (
    <div {...other}>
      <Box>
        <Card>
          <CardHeader
            sx={{ height: "56px" }}
            title={t('profile:common.about')}
          />
          <Divider />
          <CardContent>
            <LinearProgress
              value={profileProgress}
              variant="determinate"
            />
            <Box sx={{ mt: 2 }}>
              <Typography
                color="textSecondary"
                variant="subtitle2"
              >
                {profileProgress}% {t('profile:common.completedProfile')}

              </Typography>
            </Box>
            <List>
              <ListItem
                disableGutters
                divider
              >
                <ListItemAvatar>
                  <WorkIcon fontFamily="small" />
                </ListItemAvatar>
                <ListItemText
                  primary={(
                    <Typography
                      color="textPrimary"
                      variant="subtitle2"
                    >
                      {currentJobTitle}
                    </Typography>
                  )}
                  secondary={(
                    <Typography
                      color="textSecondary"
                      variant="caption"
                    >
                      {t('profile:common.empNo')}: {' '}
                      {no}
                      {' '}
                    </Typography>
                  )}
                />
              </ListItem>
              <ListItem
                disableGutters
                divider
              >
                <ListItemAvatar>
                  <AssignmentIndIcon fontSize="small" />
                </ListItemAvatar>
                <ListItemText
                  primary={(
                    <Typography
                      color="textPrimary"
                      variant="subtitle2"
                    >
                      {departmentTitle}
                    </Typography>
                  )}
                />
              </ListItem>
              <ListItem
                disableGutters
                divider
              >
                <ListItemAvatar>
                  <HomeIcon fontFamily="small" />
                </ListItemAvatar>
                <ListItemText
                  primary={(
                    <Typography
                      color="textPrimary"
                      variant="subtitle2"
                    >
                      {currentCity}
                    </Typography>
                  )}
                  secondary={(
                    <Typography
                      color="textSecondary"
                      variant="caption"
                    >
                      Portugal
                    </Typography>
                  )}
                />
              </ListItem>
              <ListItem
                disableGutters
                divider
              >
                <ListItemAvatar>
                  <ContactPhoneIcon fontFamily="small" />
                </ListItemAvatar>
                <ListItemText
                  primary={(
                    <Typography
                      color="textPrimary"
                      variant="subtitle2"
                    >
                      {email}
                    </Typography>
                  )}
                  secondary={(
                    <Typography
                      color="textSecondary"
                      variant="caption"
                    >
                      {telephone}
                    </Typography>
                  )}
                />
              </ListItem>
            </List>
          </CardContent>
        </Card>
      </Box>
    </div>
  );
};

ProfileAbout.propTypes = {
  currentCity: PropTypes.string,
  currentJobTitle: PropTypes.string,
  email: PropTypes.string,
  departmentTitle: PropTypes.string,
  telephone: PropTypes.string,
  profileProgress: PropTypes.number,
  no: PropTypes.string,
};

export default ProfileAbout;
