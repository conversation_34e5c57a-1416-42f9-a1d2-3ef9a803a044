import { useCallback, useState, useEffect } from 'react';
import { useParams } from 'react-router';
import { <PERSON><PERSON><PERSON>, HelmetProvider } from 'react-helmet-async';
import { Link as RouterLink } from 'react-router-dom';
import {
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { Box, Button, Container, Grid, Divider, Fab, Tooltip, LinearProgress } from '@mui/material';
import CreateForm from '../../Components/StepForm/CreateForm';
import { processAPI } from '../../API/processAPI';
import DocumentVariablesModal from '../../Components/Modal/Documents/DocumentVariablesCardModal';
import useSettings from '../../Context/Hooks/useSettings';
import toast from 'react-hot-toast';

const FunctionalTableDetail = () => {
  const { settings } = useSettings();
  const params = useParams();

  //getProcessTypes
  const [options, setOptions] = useState(false);
  const [process, setProcess] = useState(false);
  const [openProcessDocumentVariablesModal, setOpenProcessDocumentVariablesModal] = useState(false);
  const [isLoading, setLoading] = useState(true);

  //getOptions
  function handleTypeListToOptions(List) {
    const types = [];

    List.forEach((type) => {
      types.push({
        value: type.code,
        text: type.description,
        group: type.parentName ? type.parentName : type.description
      });
    });

    return types;
  }

  function handleChangeProcessType(type) {
    const _process = { ...process };
    _process.processTypeID = type;
    setProcess(_process);
  }

  //ERRA AQUI
  const getProcess = useCallback(async () => {
    try {
      const data = await processAPI.getProcess(params.pid, params['*'] === "/Form/new");
      if (!data.error) {
        setProcess(data.value.processList[0]);
      } else {
        toast.error(data.exceptionMessages.messages[0].description);
      }
      const resultOptions = await processAPI.getProcessTypes();
      if (!resultOptions.error) {
        const values = {
          moduleList: handleTypeListToOptions(resultOptions.moduleList),
          rolesList: handleTypeListToOptions(resultOptions.rolesList),
          statusList: handleTypeListToOptions(resultOptions.statusList),
        };
        setOptions(values);
      } else {
        toast.error(resultOptions.exceptionMessages.messages[0].description);
      }

      setLoading(false);
    } catch (err) {
      toast.error('Erro desconhecido ' + err);
    }
  }, [params.pid]);

  const handleOpenProcessDocumentVariablesModal = () => {
    setOpenProcessDocumentVariablesModal(true);
  };

  const handleCloseProcessDocumentVariablesModal = () => {
    setOpenProcessDocumentVariablesModal(false);
  };

  useEffect(() => {
    getProcess();
  }, [getProcess]);

  return (
    <>
      <HelmetProvider>
        <Helmet>
          <title>{window.platform_name + " |"} Processo de Recrutamento</title>
        </Helmet>
      </HelmetProvider>
      {
        isLoading && <LinearProgress
          variant="indeterminate"
        />
      }
      <Container
        maxWidth={settings.compact ? 'xl' : false}
      >
        <Grid
          container
          justifyContent="space-between"
        >
          <Grid
            item
            xs={12}
          >
            <div
              style={{
                display: "flex",
                flex: 1
              }}
            >
              <div
                style={{
                  display: "inline-flex",
                  alignItems: "flex-end",
                  marginLeft: "auto"
                }}
              >
                <Box sx={{ display: 'flex' }}>
                  {(params.pid) && (
                    process.statusDescription === "Ativo" && (
                      process.processTypeID === 942002 && (
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center"
                          }}
                        >
                          <Button
                            color="primary"
                            variant="contained"
                            onClick={handleOpenProcessDocumentVariablesModal}
                            sx={{ ml: 1 }}
                          >
                            Variáveis de Documentos
                          </Button>
                        </Box>))
                  )}
                </Box>
              </div>
            </div>
          </Grid>
          <Grid item>
            <Box sx={{ m: 1 }}>
              <Tooltip title="Back">
                <Fab
                  color="primary"
                  component={RouterLink}
                  type="submit"
                  to="/Examples/FunctionalTableExample"
                  sx={{
                    bottom: 0,
                    margin: (theme) => theme.spacing(4),
                    position: 'fixed',
                    left: 280,
                    zIndex: (theme) => theme.zIndex.speedDial
                  }}
                >
                  <ArrowBackIcon fontSize="large" />
                </Fab>
              </Tooltip>
            </Box>
          </Grid>
          <Grid
            item
            xs={12}
          >
            <Divider />
          </Grid>
          <Grid
            item
            xs={12}
          >
            <Box
              sx={{
                mb: "100px"
              }}
            >
              <CreateForm
                options={options}
                process={process}
                handleChangeProcessType={handleChangeProcessType}
              />
            </Box>
          </Grid>
        </Grid>
      </Container>
      <DocumentVariablesModal
        open={openProcessDocumentVariablesModal}
        onClose={handleCloseProcessDocumentVariablesModal}
        contextType="PROCESS"
        contextID={params.pid}
        readOnly={process.statusDescription !== "Ativo"}
      />
    </>
  );
};

export default FunctionalTableDetail;
