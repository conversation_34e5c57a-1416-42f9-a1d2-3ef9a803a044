/* eslint-disable no-constant-condition */
import React, { use<PERSON><PERSON>back, useEffect, useState } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { Box, Grid, Card, TextField, Alert, AlertTitle, Tooltip, Fab, Button, FormHelperText, FormControlLabel, RadioGroup, Radio, Divider, CardHeader, CardContent } from '@mui/material';
import {
  Save as SaveIcon,
  Add as AddIcon
} from '@mui/icons-material';
import toast from 'react-hot-toast';
import { StepForm } from '.';
import PropTypes from 'prop-types';
import { Formik } from 'formik';
import * as Yup from 'yup';
import StepRemoveModal from '../Modal/Step/StepRemoveCardModal';
import StepModuleRemoveModal from '../Modal/Step/StepModuleRemoveCardModal';
import DocumentVariablesModal from '../Modal/Documents/DocumentVariablesCardModal';
import { processAPI } from '../../API/processAPI'

const ErrorAlertMessage = ({ errors }) => {
  if (!errors.submit) return;

  return (
    <Box sx={{ mt: 2, mb: 2 }}>
      <Alert severity="error">
        <AlertTitle>Existem erros de validação</AlertTitle>
        {errors.submit.map((message) => <div key={message}>{message}</div>)}
      </Alert>
    </Box>
  )
}

ErrorAlertMessage.propTypes = {
  errors: PropTypes.object
}

const CardHead = ({ opportunityView, ...props }) => {
  if (opportunityView) return null;
  return (
    <CardHeader
      title={
        <TextField
          {...props}
        />
      }
    />)
}

CardHead.propTypes = {
  opportunityView: PropTypes.object
}

const CreateForm = (props) => {
  const { options, process, opportunityView, disableAll, handleChangeProcessType, setWasModified, wasModified, setModalSave, modalSave, processTypeCode } = props;
  const [processEdited, setProcessEdited] = useState({
    processName: '',
    stepsList: []
  });
  const [openProcessRemoveModal, setOpenProcessRemoveModal] = useState(false);
  const [openProcessModuleRemoveModal, setOpenProcessModuleRemoveModal] = useState(false);
  const [stepSelected, setStepSelected] = useState();
  const [moduleSelected, setModuleSelected] = useState();
  const [openDocumentVariablesModal, setOpenDocumentVariablesModal] = useState(false);
  const [activeStepCard, setActiveStepCard] = useState()
  const [activeModuleCard, setActiveModuleCard] = useState()

  let refFormik = React.createRef();

  const handleIncludesCollections = (collection, values) => {
    const includesList = [];
    if (values) {
      values.forEach((element) => {
        const elementFind = collection.find((item) => item.value === element);
        if (elementFind) {
          includesList.push(elementFind);
        }
      });
    }
    return includesList;
  };

  const handleDragStart = async ({ source, destination, ...rest }) => {
    if (false) {
      console.log(source);
      console.log(destination);
      console.log(rest);
    }
  };

  const handleDragEnd = async ({ source, destination }) => {
    try {
      // Dropped outside the column
      if (!destination) {
        return;
      }

      // Card has not been moved
      if (source.droppableId === destination.droppableId
        && source.index === destination.index) {
        return;
      }

      const processCloned = { ...refFormik.values };
      const oldProcess = processCloned.stepsList[destination.index];
      const newProcess = processCloned.stepsList[source.index];
      const newID = oldProcess.id;
      oldProcess.id = newProcess.id;
      newProcess.id = newID;
      processCloned.stepsList.splice(source.index, 1);
      processCloned.stepsList.splice(destination.index, 0, newProcess);
      setProcessEdited(processCloned);
    } catch (err) {
      console.error(err);
    }
  };

  const handleModuleDragEnd = async ({ source, destination }, stepIndex) => {
    try {
      // Dropped outside the column
      if (!destination) {
        return;
      }

      // Card has not been moved
      if (source.droppableId === destination.droppableId
        && source.index === destination.index) {
        return;
      }

      const processCloned = { ...refFormik.values };//{ ...processEdited };
      const newProcess = processCloned.stepsList[stepIndex].moduleList[source.index];
      processCloned.stepsList[stepIndex].moduleList.splice(source.index, 1);
      processCloned.stepsList[stepIndex].moduleList.splice(destination.index, 0, newProcess);
      setProcessEdited(processCloned);
    } catch (err) {
      console.error(err);
    }
  };

  const handleAddNewStep = () => {
    const processCloned = { ...refFormik.values };
    const newObject = {
      stepName: '',
      id: processCloned.stepsList.length + 1,
      rolePermissions: [],
      statusList: [],
      moduleList: [],
      renaming: true
    };

    processCloned.processName = refFormik.values.processName;
    processCloned.stepsList.push(newObject);
    setProcessEdited(processCloned);
  };

  const handleAddNewModule = (index) => {
    const processCloned = { ...refFormik.values };
    const newObject = {
      stepName: '',
      stepPermissions: true,
      rolePermissions: [],
      statusList: [],
      id: processCloned.stepsList[index].moduleList.length + 1,
      sequential: false,
      formData: [{ id: 1, evaluaterID: [], templateTypeID: null, granularity: options.granularityList[0], eventDate: false, optional: false, test: false, keepStatus: false }],
      docData: [{ id: 1, requiresSignature: false, format: null, variableDefinition: 'MODULE' }],
      emailData: [{ id: 1, templateEmailID: null, attachment: null, sendToStatus: [], variableDefinition: 'MODULE' }],
      showContent: true
    };

    processCloned.processName = refFormik.values.processName;
    processCloned.stepsList[index].moduleList.push(newObject);
    setProcessEdited(processCloned);
  };

  const handleRemoveStep = useCallback(() => {
    const processCloned = { ...refFormik.values };

    processCloned.stepsList.splice(stepSelected, 1);

    setProcessEdited(processCloned);
    setStepSelected(undefined);
    setOpenProcessRemoveModal(false);
  }, [refFormik.values, stepSelected]);

  const handleRemoveStepModule = useCallback(() => {
    const processCloned = { ...refFormik.values };

    processCloned.stepsList[stepSelected].moduleList.splice(moduleSelected, 1);

    setProcessEdited(processCloned);
    setStepSelected(undefined);
    setModuleSelected(undefined);
    setOpenProcessModuleRemoveModal(false);
  }, [moduleSelected, refFormik.values, stepSelected]);

  const handleOpenProcessRemoveModal = (index) => {
    setOpenProcessRemoveModal(true);
    setStepSelected(index);
  };

  const handleCloseProcessRemoveModal = () => {
    setOpenProcessRemoveModal(false);
  };

  const handleOpenProcessModuleRemoveModal = (stepIndex, moduleIndex) => {
    setOpenProcessModuleRemoveModal(true);
    setStepSelected(stepIndex);
    setModuleSelected(moduleIndex);
  };

  const handleCloseProcessModuleRemoveModal = () => {
    setOpenProcessRemoveModal(false);
  };

  const getProcessStepDocument = async (processStepID, templateFilePath, templateFileName) => {
    const file = await processAPI.getProcessStepDocument(processStepID, templateFilePath);
    if (!file.error) {
      const a = document.createElement("a"); //Create <a>
      a.href = "data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64," + file.value.file; //Image Base64 Goes here
      a.download = templateFileName; //File name Here
      a.click();
    } else {
      toast.error("Erro ao obter o ficheiro.");
    }
  };

  // const handleOpenDocumentVariablesModal = () => {
  //   setOpenDocumentVariablesModal(true);
  // };

  const handleCloseDocumentVariablesModal = () => {
    setOpenDocumentVariablesModal(false);
  };

  const findOptionByValue = (list, value) => list.find(item => item.value === value) || null;
  const getOption = (optionList, value) => {
    return optionList ? findOptionByValue(optionList, value) : {};
  }

  const handleEmails = (emails, emailTemplateList, statusList) => {
    if (emails == null || emails.length === 0) return [{ id: 1 }];

    return emails.map((email, index) => {
      if (email.statusToEvaluate == null) {
        email.statusToEvaluate = "";
      }

      return {
        id: index + 1,
        templateEmailID: findOptionByValue(emailTemplateList, email.templateFilename),
        attachment: email.attachedDocument,
        sendToStatus: statusList.filter(x => email.statusToEvaluate.split(',').includes(x.value.toString())),
        processStepDocumentID: email.processStepDocumentID,
        processStepOfferDocumentID: email.processStepOfferDocumentID,
        emailByProcess: email.emailByProcess,
        emailPersonal: email.emailPersonal,
        emailAutomatic: email.emailAutomatic,
        variableDefinition: email.variableDefinition
      };
    })
  }

  const getError = (touched, errors, fieldName) => Boolean(touched?.[fieldName] && errors?.[fieldName]);

  const getStepErrors = (errors, index) => {
    return errors?.stepsList?.[index] || [];
  };

  const getStepTouched = (touched, index) => {
    return touched?.stepsList?.[index] || [];
  };

  useEffect(() => {
    if (!process || !options) return;

    const processCloned = { ...process };
    processCloned.stepsList.forEach((step, id_) => {
      step.id = (id_ + 1);
      step.rolePermissions = handleIncludesCollections(options.rolesList, step.rolePermissions);
      step.statusList = handleIncludesCollections(options.statusList, step.statusList);

      step.moduleList.forEach((module, idx) => {
        module.id = (idx + 1).toString();
        module.templateTypeID = findOptionByValue(options.moduleList, module.templateTypeID);
        module.rolePermissions = handleIncludesCollections(options.rolesList, module.rolePermissions);
        module.statusList = handleIncludesCollections(options.statusList, module.statusList);
        module.stepPermissions = module.inheritsPermissions;
        module.showContent = true;
        module.formData = [
          {
            id: 1,
            templateTypeID: module.templateTypeID,
            optional: module.optional,
            keepStatus: module.keepStatus,
            test: module.requiresTest,
            eventDate: module.showInterviewDate,
            granularity: getOption(options.granularityList, module.granularityID),
            evaluaterID: handleIncludesCollections(options.evaluaterList, module.evaluaterIDs)
          }
        ];
        module.docData = [
          {
            id: 1,
            ProcessStepDocumentID: module.ProcessStepDocumentID,
            mandatory: module.mandatory,
            requiresSignature: module.requiresSignature,
            templateFileName: module.templateFileName,
            document: null, // module.processStepDocumentID === undefined ? null : {}
            format: getOption(options.formatList, module.format),
            variableDefinition: module.variableDefinition,
          }
        ];

        module.emailData = handleEmails(module.emails, options.emailTemplateList, step.statusList);
      });
    });
    if (processCloned.stepsList.length <= 0) {
      const newObject = {
        stepName: '',
        id: 1,
        rolePermissions: [],
        statusList: [],
        moduleList: [],
        renaming: true
      };
      processCloned.stepsList.push(newObject);
    }
    setProcessEdited(processCloned);
  }, [options, process]);

  useEffect(() => {
    if (modalSave) {
      refFormik.submitForm()

      setModalSave(false);
    }
  }, [modalSave, refFormik, setModalSave]);

  const processTypeContinuous = "CONTINUOUS";

  return (
    <>
      <Formik
        innerRef={(p) => (refFormik = p)}
        initialValues={{
          submit: null,
          processID: processEdited.processID,
          processName: processEdited.processName,
          stepsList: processEdited.stepsList
        }}
        validateOnChange={false}
        enableReinitialize
        validationSchema={Yup
          .object()
          .shape({
            processName: Yup.string().required('O nome do processo é obrigatório'),
            stepsList: Yup.array().of(
              Yup.object().shape({
                stepName: Yup.string().required('O nome da fase é obrigatório'),
                rolePermissions: Yup.array().min(1, 'É necessário incluir pelo menos uma permissão'),
                statusList: Yup.array().min(1, 'É necessário incluir pelo menos um estado'),
                moduleList: Yup.array().of(
                  Yup.object().shape({
                    stepName: Yup.string().required('O nome do módulo é obrigatório').nullable(),
                    rolePermissions: Yup.array().when(
                      'stepPermissions',
                      {
                        is: false,
                        then: Yup.array().min(1, 'É necessário incluir pelo menos uma permissão'),
                        otherwise: Yup.array()
                      }
                    ),
                    statusList: Yup.array().when(
                      'sequential',
                      {
                        is: true,
                        then: Yup.array().min(1, 'É necessário incluir pelo menos um estado'),
                        otherwise: Yup.array().nullable()
                      }
                    ),
                    formData: Yup.array().of(
                      Yup.object().shape({
                        granularity: Yup.object().required('É necessário incluir a Granularidade').nullable(),
                        templateTypeID: Yup.object().required('É necessário incluir o Formulário').nullable(),
                        evaluaterID: Yup.array().when('granularity', (granularity) => {
                          if (granularity && granularity.value === 948001 && opportunityView) {
                            return Yup.array().min(1, 'Com este nível de granularidade é necessário pelo menos 1 avaliador.')
                          }
                          return Yup.array().nullable()
                        }),
                      })
                    ).min(1, 'É necessário incluir pelo menos um formulário')
                  })
                ).min(1, 'É necessário incluir pelo menos uma Etapa')
              })
            ).min(1, 'É necessário incluir pelo menos uma fase'),
          })}
        onSubmit={(values, actions) => {
          setTimeout(() => {
            alert(JSON.stringify(values, null, 2));
            actions.setSubmitting(false);
          }, 1000);
        }}
      >
        {({
          errors,
          handleBlur,
          handleChange,
          handleSubmit,
          isSubmitting,
          setFieldValue,
          setFieldTouched,
          touched,
          values
        }) => (
          <form
            autoComplete="off"
            onSubmit={handleSubmit}
          >
            <ErrorAlertMessage errors={errors} />
            <DragDropContext
              onDragEnd={handleDragEnd}
              onDragStart={handleDragStart}
            >
              <Box>
                <Grid
                  container
                  spacing={3}
                >
                  <Grid
                    item
                    lg={12}
                    md={12}
                    xs={12}
                  >
                    <Card
                      className="floatOnHover"
                      sx={{
                        backgroundColor: 'background.paper',
                        width: '100%'
                      }}
                    >
                      <CardHead
                        fullWidth
                        placeholder="Nome do Processo"
                        name="processName"
                        onBlur={handleBlur}
                        onChange={handleChange}
                        value={values.processName}
                        disabled={opportunityView}
                        variant="standard"
                        sx={{
                          background: 'white',
                          maxWidth: "300px"
                        }}
                        error={getError(touched, errors, 'processName')}
                        helperText={errors?.processName}
                      />
                      <Divider />
                      <CardContent>
                        <FormControlLabel
                          sx={{
                            ml: 1
                          }}
                          control={(
                            <RadioGroup
                              row
                              aria-label="Tipo de Processo"
                              name="processTypeID"
                              value={process?.processTypeID?.toString()}
                              onChange={(e, value) => handleChangeProcessType(value)}
                            >
                              {options?.processTypeList?.length > 0 && options.processTypeList.map((type) => (
                                <FormControlLabel
                                  key={`Option-${type?.value}`}
                                  value={type?.value?.toString()}
                                  control={<Radio />}
                                  label={type.text}
                                  disabled={opportunityView}
                                />
                              ))}
                            </RadioGroup>
                          )}
                          label=""
                        />
                      </CardContent>
                    </Card>
                    <Droppable
                      droppableId="droppableId-1"
                      key={1}
                      type="list"
                    >
                      {(provided) => (
                        <Box
                          ref={provided.innerRef}
                          sx={{
                            flexGrow: 1,
                            minHeight: 80,
                            overflowY: 'auto'
                          }}
                        >
                          {values.stepsList.map((p, index) => (
                            <Draggable
                              draggableId={`dragabble-${index}`}
                              index={index}
                              key={`dragabbleKey-${index.toString()}`}
                              isDragDisabled={opportunityView}
                            >
                              {(_provided, snapshot) => (
                                <StepForm
                                  dragging={snapshot.isDragging}
                                  key={`step-${p.id}`}
                                  options={options ? options : {}}
                                  processType={process.processTypeID}
                                  process={p}
                                  index={index.toString()}
                                  ref={_provided.innerRef}
                                  style={{ ..._provided.draggableProps.style }}
                                  setFieldValue={setFieldValue}
                                  setFieldTouched={setFieldTouched}
                                  {..._provided.draggableProps}
                                  errors={getStepErrors(errors, index)}
                                  touched={getStepTouched(touched, index)}
                                  handleRemoveStep={handleOpenProcessRemoveModal}
                                  handleRemoveModule={handleOpenProcessModuleRemoveModal}
                                  handleAddNewModule={handleAddNewModule}
                                  handleModuleDragEnd={handleModuleDragEnd}
                                  getProcessStepDocument={getProcessStepDocument}
                                  opportunityView={opportunityView}
                                  provided={_provided}
                                  disableAll={disableAll}
                                  handleFormikBlur={handleBlur}
                                  handleFormikChange={handleChange}
                                  activeStepCard={activeStepCard}
                                  setActiveStepCard={setActiveStepCard}
                                  activeModuleCard={activeModuleCard}
                                  setActiveModuleCard={setActiveModuleCard}
                                  wasModified={wasModified}
                                  setWasModified={setWasModified}
                                  processTypeCode={processTypeCode}
                                />
                              )}
                            </Draggable>
                          ))}
                          {provided.placeholder}
                        </Box>
                      )}

                    </Droppable>
                    {(errors?.stepsList) && (!Array.isArray(errors.stepsList)) && (
                      <Box sx={{ mt: 2 }}>
                        <FormHelperText error>
                          {errors.stepsList}
                        </FormHelperText>
                      </Box>
                    )}
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        mt: 3
                      }}
                    >
                      <Tooltip title="Guardar">
                        <Fab
                          color="primary"
                          disabled={isSubmitting || (disableAll && processTypeCode !== processTypeContinuous)}
                          type="submit"
                          sx={{
                            bottom: 0,
                            margin: (theme) => theme.spacing(4),
                            position: 'fixed',
                            right: 0,
                            zIndex: (theme) => theme.zIndex.speedDial
                          }}
                        >
                          <SaveIcon fontSize="large" />
                        </Fab>
                      </Tooltip>
                    </Box>
                  </Grid>
                  {
                    !opportunityView &&
                    <Grid
                      item
                      xs={12}
                    >
                      <Button
                        color="primary"
                        startIcon={<AddIcon fontSize="small" />}
                        sx={{ m: 1 }}
                        variant="contained"
                        onClick={handleAddNewStep}
                      >
                        Fase
                      </Button>
                    </Grid>
                  }
                </Grid>
              </Box>
            </DragDropContext>
          </form>
        )}
      </Formik>
      <StepRemoveModal
        open={openProcessRemoveModal}
        onClose={handleCloseProcessRemoveModal}
        onRemoveProcessStep={handleRemoveStep}
      />
      <StepModuleRemoveModal
        open={openProcessModuleRemoveModal}
        onClose={handleCloseProcessModuleRemoveModal}
        onRemoveProcessStep={handleRemoveStepModule}
      />
      <DocumentVariablesModal
        open={openDocumentVariablesModal}
        onClose={handleCloseDocumentVariablesModal}
        contextType="ALL"
      />
    </>
  );
};

CreateForm.propTypes = {
  options: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.bool
  ]),
  process: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.bool
  ]),
  opportunityView: PropTypes.bool,
  disableAll: PropTypes.bool,
  saveOpportunityConfigurations: PropTypes.func,
  handleChangeProcessType: PropTypes.func,
  setWasModified: PropTypes.func,
  wasModified: PropTypes.bool,
  setModalSave: PropTypes.func,
  modalSave: PropTypes.bool,
  formChangesModal: PropTypes.object,
  setFormChangesModal: PropTypes.func,
  handleTabsChange: PropTypes.func,
  processTypeCode: PropTypes.string
};

export default CreateForm;
