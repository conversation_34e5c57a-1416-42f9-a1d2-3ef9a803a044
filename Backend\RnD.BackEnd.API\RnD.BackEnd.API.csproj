﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
		<PackageReference Include="ExcelDataReader" Version="3.7.0" />
		<PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
		<PackageReference Include="LinqKit.Microsoft.EntityFrameworkCore" Version="8.1.8" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="8.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.2">
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		  <PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.2" />
		<PackageReference Include="Microsoft.Graph" Version="5.75.0" />
		<PackageReference Include="Microsoft.Identity.Client" Version="4.70.0" />
		<PackageReference Include="Microsoft.Kiota.Abstractions" Version="1.17.1" />
		<PackageReference Include="PdfPig" Version="0.1.10" />
		<PackageReference Include="Docnet.Core" Version="2.6.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.6" />
      <PackageReference Include="Serilog" Version="4.2.0" />
      <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.5.0" />
      <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
      <PackageReference Include="System.Net.Http.Json" Version="8.0.0" />
      <PackageReference Include="Verify.PdfPig" Version="2.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\RnD.BackEnd.Domain\RnD.BackEnd.Domain.csproj" />
		<ProjectReference Include="..\RnD.BackEnd.Infrastructure\RnD.BackEnd.Infrastructure.csproj" />
		<ProjectReference Include="..\RnD.BackEnd.Licensing\RnD.BackEnd.Licensing.csproj" />
		<ProjectReference Include="..\RnD.BackEnd.Logging\RnD.BackEnd.Logging.csproj" />
		<ProjectReference Include="..\RnD.BackEnd.Service\RnD.BackEnd.Service.csproj" />
      <ProjectReference Include="..\RnD.BackEnd.Email\RnD.BackEnd.Email.csproj" />
	</ItemGroup>

</Project>
