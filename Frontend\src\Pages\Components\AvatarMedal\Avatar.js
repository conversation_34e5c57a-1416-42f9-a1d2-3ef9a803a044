/* eslint-disable react/no-unescaped-entities */
import {
  <PERSON>,
  Divide<PERSON>,
  <PERSON>,
  <PERSON>H<PERSON><PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Typography,
} from "@mui/material";
import PropTypes from "prop-types";
import { useTheme } from '@emotion/react';
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import AvatarMedal from "../../../Components/Avatar/AvatarMedal";
import { useState } from "react";
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import {
  ExpandMore as ExpandMoreIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from "@mui/icons-material";
import getExample from "./mocks/example";
import getIntro from "./mocks/intro";
import getCode from "./mocks/code";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import { styled } from "@mui/system";

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh",
})

const Avatar = ({ open: openProp }) => {
  const theme = useTheme();
  const [example, setExample] = useState(getExample);
  const [intro, setIntro] = useState(getIntro);
  const [code, setCode] = useState(getCode);

  const [open, setOpen] = useState(openProp);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box
        sx={{
          m: "auto",
          textAlign: "center"
        }}
      >
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h2>Avatar Medal</h2>
          <p>
            This component creates a medal whose content is the user's avatar.
          </p>
        </Box>
        <Box
          sx={{
            width: "80%",
            margin: "auto",
            marginTop: 10,
            marginBottom: 10,
            maxHeight: "100vh"
          }}
        >
          <AvatarMedal
            image="/static/mock-images/avatars/avatar-image.jpg"
            label="Hello World"
            textPosition="right"
            sublabel="Example"
          />
        </Box>
        <Typography
          color="textPrimary"
          sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
        >
          The following code snippet demonstrates how Avatar Medal is displayed.
        </Typography>
      </Box>
      <StyledEditorBox>
        <CardHeader
          action={
            <>
              <CopyToClipboardButton value={example} />
            </>
          }
        />
        <Divider />
        <CardContent
          sx={{
            maxHeight: "50vh",
            overflowY: "scroll",
          }}
        >
          <Editor
            value={example}
            onValueChange={(_example) => setExample(_example)}
            highlight={(_example) => highlight(_example, languages.js)}
            padding={10}
            style={{
              fontFamily: '"Fira code", "Fira Mono", monospace',
              fontSize: 12,
              backgroundColor: theme.palette.background.paper,
              borderRadius: theme.shape.borderRadius,
            }}
          />
        </CardContent>
      </StyledEditorBox>
      <Typography
        color="textPrimary"
        sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
      >
        To get started copy the full source code to your scr folder.
      </Typography>
      <StyledEditorBox>
        <CardHeader
          action={
            <>
              <CopyToClipboardButton value={open ? code : intro} />
              <Tooltip
                title={
                  !open ? "Show full source Code" : "Hide full Source Code"
                }
              >
                <IconButton
                  onClick={handleToggle}
                  sx={{
                    color: `${theme.palette.primary.main}`,
                    "&:hover": {
                      backgroundColor: `${theme.palette.secondary.main}`,
                      color: "primary.main",
                    },
                    height: 35,
                    width: 35,
                    mt: 1,
                  }}
                >
                  {!open ? <ExpandMoreIcon /> : <KeyboardArrowUpIcon />}
                </IconButton>
              </Tooltip>
            </>
          }
        />
        <Divider />
        <CardContent
          sx={{
            maxHeight: "50vh",
            overflowY: "scroll",
          }}
        >
          <Editor
            value={open ? code : intro}
            onValueChange={
              open ? (_code) => setCode(_code) : (_intro) => setIntro(_intro)
            }
            highlight={
              open
                ? (_code) => highlight(_code, languages.js)
                : (_intro) => highlight(_intro, languages.js)
            }
            padding={10}
            style={{
              fontFamily: '"Fira code", "Fira Mono", monospace',
              fontSize: 12,
              backgroundColor: theme.palette.background.paper,
              borderRadius: theme.shape.borderRadius,
            }}
          />
        </CardContent>
      </StyledEditorBox>
      <Box
        sx={{
          m: "auto",
          textAlign: "justify",
          maxWidth: "80%",
          color: "textPrimary",
        }}
      >
        <p>Avatar Medal receives the following props:</p>
        <ul>
          <li>
            <b>image</b> - Here you can define the source of your image. String
            value.
          </li>
          <li>
            <b>label</b> - Place a label next to the avatar.
          </li>
          <li>
            <b>sublabel</b> - Place a sublabel next to the avata (Sublabel
            appears above the label).
          </li>
          <li>
            <b>textPosition</b> - By default, the text is positioned to the
            right;
          </li>
          <li>
            <b>avatarsize</b> - By default, the avatar size is 200.
          </li>
          <li>
            <b>wrapperColor</b>
          </li>
        </ul>
      </Box>
      <ChangeLogEntry
        version="V1.0.0"
        changeLog={ChangeLog()}
      />
    </Box>
  );
};

Avatar.propTypes = {
  open: PropTypes.bool,
};
export default Avatar;

const ChangeLog = () => {
  const theme = useTheme();
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content: "Imported Autocomplete Tree Component.",
      className: chips.green,
    },
  ];

  return changeLog;
};
