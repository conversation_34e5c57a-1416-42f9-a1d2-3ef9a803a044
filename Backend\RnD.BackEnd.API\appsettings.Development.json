{"LicenseKey": "-FKDhZ0ywBJ-GAHA-5bz2VMCBcoGnVOSkurgzT74AXAaAZvab502yZ7fYnCoWWZnAxE7LFS958FeEGOXznQ4kHbW-n1tm6utwpTB0FCBQh_MxS7nvcD_mR44suUrJtDpavmpqSJdi2MPqrNTNmYWbIfoq99wIoB_0avEaHkwSur1Hw6YeQp_blBKPTl4h1f6m9QYhON9bB4jRBBQhxYZR_anCV-tiWUNBPjdE_jWU4Lp-NE773MjO52o4OlbpVs7zvheSUTBoTWytjrSBP4rYg", "ApplicationInsights": {"InstrumentationKey": "b6918425-d7f4-4be8-ab80-fbe389c56219"}, "ConnectionStrings": {"DatabaseCS": "Server=tcp:portal-cloud-services.database.windows.net,1433;Initial Catalog=PortalCloudService;Persist Security Info=False;User ID=PortalCloudService;Password=PortalCloud2025#;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}, "AzureStorage": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=portalcloudservice;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ContainerName": "consumo-excel"}, "StorageExcelAzure": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=portalcloudservice;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ContainerName": "consumo-excel-azure"}, "AzureStoragePDF": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=portalcloudservice;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ContainerName": "consumo-pdf"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "bi4all.pt", "TenantId": "41bcb72d-c1e9-42e2-b451-eaded8635d70", "ClientId": "a50ae653-c7d8-4387-b2f1-486e164ebfe5", "ClientSecret": "****************************************", "Graph": "https://graph.microsoft.com", "GraphDefaultScope": "https://graph.microsoft.com/.default", "GraphScopes": "User.Read User.ReadBasic.All"}, "AppSettings": {"EmailSettings": {"AllowSendingEmails": true, "SendAlwaysToAddresses": [], "ApiKey": "*********************************************************************", "FromEmailAddress": "<EMAIL>", "FromUserName": "Knowledge & Innovation", "DefaultTemplateId": "d-c43961e085e04f1c8e1f0192db1c69d8", "CodeAcademyTemplateId": "d-8d3eac75edc84f6e87cdfe48a3e850b5"}, "CosmosSettings": {"EndpointUri": "https://rnd-cosmos-frmk.documents.azure.com:443/", "PrimaryKey": "****************************************************************************************", "DatabaseName": "FrameworkDB"}, "WorkflowSettings": {"BaseUrl": "https://wfe-app-webapi-dev.azurewebsites.net", "AllowWorkflowEngine": true, "CollectFeedbackTemplateId": "d-8b84916e892445fba4ffd048189aa581", "TokenEndpoint": "/api/Authorization/GetToken", "ListDefinitionsEndpoint": "/api/Definitions/List", "InstancesBySourceIDEndpoint": "/api/Instances/GetInstancesBySourceIds", "InstanceByIDEndpoint": "/api/Instances/GetInstanceById", "TaskByIDEndpoint": "/api/Tasks/GetWorkflowTask", "TasksEndpoint": "/api/Tasks/GetAllUsersTasks", "UserWorkflowInstancesEndpoint": "/api/Instances/GetUserWorkflowInstances", "UpdateTaskEndpoint": "/api/Tasks/UpdateTask", "UpdateTasksEndpoint": "/api/Tasks/UpdateTasks", "CancelWorkflowEndpoint": "/api/Instances/CancelWorkflow", "ReassignTaskEndpoint": "/api/Tasks/ReassignTask", "StartWorkflowEndpoint": "/api/Triggers/StartWorkflow", "AutomaticTriggerWorkflowEndpoint": "/api/Triggers/AutomaticTrigger", "DeleteOrCancelBySourceIdEndpoint": "/api/Instances/DeleteOrCancelWorkflowsBySourceId"}, "DataEntrySettings": {"BaseUrl": "https://mdm-external-api-dev.azurewebsites.net", "Username": "API_User", "Password": "apiuserpassword123", "AuthenticationEndpoint": "/api/Authorization/AuthenticateAPI", "ListEntitiesEndpoint": "/api/Entity/List"}}}