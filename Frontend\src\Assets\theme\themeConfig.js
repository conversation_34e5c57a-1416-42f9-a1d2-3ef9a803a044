const ThemeConfig = {
    spacing: 8,
    themeColor1: process?.env?.REACT_APP_THEME_PRIMARY_COLOR || "#00B098",
    themeColor2: process?.env?.REACT_APP_THEME_SECONDARY_COLOR || "#40BB73",
    themeColor3: process?.env?.REACT_APP_THEME_TERTIARY_COLOR || "#96C93D",
    commonWhite: "#FFFFFF",
    textPrimary: "#0B3B66",
    textSecondary: "#B3B3B3",
    paperWhite: "#F5F5F5",
    alertlevels: {},
    states: {
        success: {
            contrastText: "#FFF",
            main: "#0CB232",
            description: "conclusão positiva"
        },
        fail: {
            contrastText: "#FFF",
            main: "#EF5858",
            description: "conclusão negativa"
        },
        pending: {
            contrastText: "#FFF",
            main: "#FFA348",
            description: "Em espera de acção, pendente, admitido condicional, admitido automático"
        },
        submitted: {
            contrastText: "#FFF",
            main: "#0BC0E4",
            description: "Submissão"
        },
        draft: {
            contrastText: "#FFF",
            main: "#BECCD7",
            description: "Rascunho"
        },
        ongoing: {
            contrastText: "#FFF",
            main: "#07869F",
            description: "Em andamento"
        },
        archived: {
            contrastText: "#FFF",
            main: "#0B3B66",
            description: "Arquivado"
        }
    },
    labels: {
        no_go: {
            contrastText: "#fff",
            main: "#D97880"
        },
        fail: {
            contrastText: "#fff",
            main: "#E8AEB3"
        },
        go: {
            contrastText: "#fff",
            main: "#E1B090"
        },
        ongoing: {
            contrastText: "#fff",
            main: "#8EDADF"
        },
        draft: {
            contrastText: "#fff",
            main: "#707889"
        },
        standby: {
            contrastText: "#fff",
            main: "#B8BBC2"
        }
    }
};
export default ThemeConfig;
