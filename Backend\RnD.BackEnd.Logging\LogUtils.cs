﻿using System;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;

namespace RnD.BackEnd.Logging
{
    /// <summary>
    /// Logging utils class
    /// </summary>
    public class LogUtils
    {
        private const string _currentUserProperty = "CurrentUser";
        private const string _currentMethodProperty = "CurrentMethod";
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="LogUtils"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        public LogUtils(ILogger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Logs the information.
        /// </summary>
        /// <param name="currentMethod">The current method.</param>
        /// <param name="currentUser">The current user.</param>
        /// <param name="message">The message.</param>
        public void LogInfo(string currentMethod, string currentUser, string message)
        {
            if (_logger.IsEnabled(LogLevel.Information))
            {
                using (_logger.BeginScope(new Dictionary<string, object>
                {
                    [_currentMethodProperty] = currentMethod,
                    [_currentUserProperty] = currentUser
                }))
                {
                    _logger.LogInformation(message);
                }
            }
        }

        /// <summary>
        /// Logs the error.
        /// </summary>
        /// <param name="currentMethod">The current method.</param>
        /// <param name="currentUser">The current user.</param>
        /// <param name="message">The message.</param>
        /// <param name="ex">The ex.</param>
        public void LogError(string currentMethod, string currentUser, string message, Exception ex)
        {
            using (_logger.BeginScope(new Dictionary<string, object>
            {
                [_currentMethodProperty] = currentMethod,
                [_currentUserProperty] = currentUser
            }))
            {
                if (ex == null)
                {
                    _logger.LogError(message);
                }
                else
                {
                    _logger.LogError(ex, message, null);
                }
            }
        }
    }
}
