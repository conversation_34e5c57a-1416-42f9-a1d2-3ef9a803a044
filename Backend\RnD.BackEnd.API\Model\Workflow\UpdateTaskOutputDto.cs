﻿using System;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Update task output Dto
    /// </summary>
    public class UpdateTaskOutputDto
    {
        /// <summary>
        /// Gets or sets the workflow status.
        /// </summary>
        /// <value>
        /// The workflow status.
        /// </value>
        public string WorkflowStatus { get; set; }

        /// <summary>
        /// Gets or sets the workflow purpose.
        /// </summary>
        /// <value>
        /// The workflow purpose.
        /// </value>
        public string WorkflowPurpose { get; set; }

        /// <summary>
        /// Gets or sets the type of the source.
        /// </summary>
        /// <value>
        /// The type of the source.
        /// </value>
        public string SourceType { get; set; }

        /// <summary>
        /// Gets or sets the task identifier.
        /// </summary>
        /// <value>
        /// The task identifier.
        /// </value>
        public Guid? TaskId { get; set; }

        /// <summary>
        /// Gets or sets the workflow instance identifier.
        /// </summary>
        /// <value>
        /// The workflow instance identifier.
        /// </value>
        public Guid WorkflowInstanceId { get; set; }
    }
}
