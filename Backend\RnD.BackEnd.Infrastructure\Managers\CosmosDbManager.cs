﻿using System;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using RnD.BackEnd.Domain.Settings.Cosmos;
using RnD.BackEnd.Infrastructure.Interfaces;

namespace RnD.BackEnd.Infrastructure.Managers
{
    /// <summary>
    /// Cosmos DB manager class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Infrastructure.Interfaces.ICosmosDbManager" />
    public class CosmosDbManager : ICosmosDbManager
    {
        private readonly CosmosClient _cosmosClient;
        private bool disposed;

        /// <summary>
        /// Initializes a new instance of the <see cref="CosmosDbManager" /> class.
        /// </summary>
        /// <param name="cosmosSettings">The cosmos settings.</param>
        public CosmosDbManager(IOptions<CosmosSettings> cosmosSettings)
        {
            if (cosmosSettings.Value != null)
            {
                _cosmosClient = new CosmosClient(cosmosSettings.Value.EndpointUri, cosmosSettings.Value.PrimaryKey);
            }
        }

        /// <summary>
        /// Gets the container.
        /// </summary>
        /// <param name="database">The database name.</param>
        /// <param name="container">The container name.</param>
        /// <returns>
        /// The container.
        /// </returns>
        public Container GetContainer(string database, string container)
        {
            return _cosmosClient.GetContainer(database, container);
        }

        #region Dispose methods

        /// <summary>
        /// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Releases unmanaged and - optionally - managed resources.
        /// </summary>
        /// <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    _cosmosClient?.Dispose();
                }

                disposed = true;
            }
        }

        #endregion
    }
}
