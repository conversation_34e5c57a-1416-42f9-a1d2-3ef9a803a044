import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import menu_pt from './I18n/Translations/pt/menu.json';
import menu_en from './I18n/Translations/en/menu.json';
import common_pt from './I18n/Translations/pt/common.json';
import common_en from './I18n/Translations/en/common.json';
import profile_pt from './I18n/Translations/pt/profile.json';
import profile_en from './I18n/Translations/en/profile.json';
import sidebar_pt from './I18n/Translations/pt/sidebar.json';
import sidebar_en from './I18n/Translations/en/sidebar.json';
import titles_pt from './I18n/Translations/pt/titles.json';
import titles_en from './I18n/Translations/en/titles.json';

const resources = {
  en: {
    menu: menu_en,
    common: common_en,
    profile: profile_en,
    sidebar: sidebar_en,
    titles: titles_en,
  },
  pt: {
    menu: menu_pt,
    common: common_pt,
    profile: profile_pt,
    sidebar: sidebar_pt,
    titles: titles_pt,
  },
};

const availableLanguages = process.env.REACT_APP_AVAILABLE_LANGUAGES.split(';');

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: availableLanguages.length > 1 && localStorage.getItem('preferedLanguage') ? localStorage.getItem('preferedLanguage') : availableLanguages[0],
    fallbackLng: 'pt',
    interpolation: {
      escapeValue: false
    }
  });

export default i18n;
