/* eslint-disable no-useless-escape */
const getNameNormalize = () => {
    return `
    const nameNormalize = (name) => {
        if (name && name !== null) {
            const normalize = (typeof (name) === "boolean")
            ? Number(name)
            : name.toString().toLowerCase().normalize('NFD').replace(/\p{Diacritic}/gu, '')
            return normalize;
        }
    return null;
    };
    `;
}

export default getNameNormalize;
