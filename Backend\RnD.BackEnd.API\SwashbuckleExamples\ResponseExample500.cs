﻿using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples
{
    /// <summary>
    /// Response example of a 500
    /// </summary>
    public class ResponseExample500 : IExamplesProvider<Logging.ServiceOutput>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The Service Output.
        /// </returns>
        public Logging.ServiceOutput GetExamples()
        {
            return new Logging.ServiceOutput
            {
                Description = "Internal error description",
                Error = true,
            };
        }
    }
}
