import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Button, Dialog, Divider, Typography, DialogTitle, IconButton, DialogContent, DialogActions, Box } from '@mui/material';
import {
  Send as SendIcon,
  Close as CloseIcon,
  OpenInNew as OpenInNewIcon,
  CloseFullscreen as CloseFullscreenIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const ModalTemplate = ({ open, title, size = "sm", content, isSubmitting, onClose, onConfirm, canConfirm = true, onCancel, customActions, icon, canFullscreen, fullscreen = false }) => {
  const [isFullscreen, setIsFullscreen] = useState(fullscreen);
  const toggleFullscreen = () => setIsFullscreen(canFullscreen ? !isFullscreen : fullscreen);
  const { t } = useTranslation();

  return (
    <Dialog
      fullWidth
      maxWidth={size}
      onClose={onClose}
      open={Boolean(open)}
      fullScreen={isFullscreen}
    >
      {
        title && (
          <>
            <DialogTitle
              sx={{
                display: "flex"
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center"
                }}
              >
                <>
                  {
                    icon && (
                      <Box
                        sx={{ display: "flex", marginRight: "10px" }}
                      >
                        {icon}
                      </Box>
                    )
                  }
                  {
                    typeof title === "string" ? <Typography
                      variant="h1"
                    >
                      {title}
                    </Typography>
                      :
                      title
                  }
                </>
              </Box>
              <Box
                sx={{
                  ml: "auto"
                }}
              >
                {
                  canFullscreen && <IconButton
                    aria-label="close"
                    onClick={toggleFullscreen}
                    size="small"
                    sx={{
                      mr: "16px"
                    }}
                  >
                    {isFullscreen ? <CloseFullscreenIcon fontSize="medium" /> : <OpenInNewIcon fontSize="medium" />}
                  </IconButton>
                }
                <IconButton
                  aria-label="close"
                  onClick={onClose}
                  size="small"
                >
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <Divider />
          </>
        )
      }
      <DialogContent>
        {content}
      </DialogContent>
      <Divider />
      <DialogActions>
        <>
          {
            onCancel && (
              <Button
                color="primary"
                variant="outlined"
                onClick={onCancel || null}
                disabled={isSubmitting !== undefined ? isSubmitting : false}
                startIcon={<CloseIcon />}
              >
                {t('common:buttons.cancel')}
              </Button>
            )
          }
          {
            customActions ?
              customActions
              :
              onConfirm && (
                <Button
                  color="primary"
                  variant="contained"
                  style={{ marginLeft: 10 }}
                  type="submit"
                  onClick={onConfirm}
                  disabled={isSubmitting !== undefined || canConfirm !== undefined ? isSubmitting || !canConfirm : false}
                  startIcon={<SendIcon />}
                >
                  {t('common:buttons.confirm')}
                </Button>
              )
          }
        </>
      </DialogActions>
    </Dialog>
  );
};

ModalTemplate.propTypes = {
  open: PropTypes.bool,
  fullscreen: PropTypes.bool,
  canFullscreen: PropTypes.bool,
  title: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]),
  size: PropTypes.string,
  content: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]),
  isSubmitting: PropTypes.bool,
  onClose: PropTypes.func,
  onConfirm: PropTypes.func,
  canConfirm: PropTypes.bool,
  onCancel: PropTypes.func,
  customActions: PropTypes.object,
  icon: PropTypes.oneOfType([
    PropTypes.bool,
    PropTypes.object
  ])
};

export default ModalTemplate;
