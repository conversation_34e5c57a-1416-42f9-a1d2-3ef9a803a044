/* eslint-disable react/no-unescaped-entities */
import {
  <PERSON>,
  Di<PERSON><PERSON>,
  <PERSON>,
  <PERSON>H<PERSON><PERSON>,
  Card<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Typography,
} from "@mui/material";
import { useTheme } from '@emotion/react';
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import { useState } from "react";
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import {
  ExpandMore as ExpandMoreIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from "@mui/icons-material";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import getIntro from "./mocks/intro";
import getFullIntro from "./mocks/fullIntro";
import getKPICardIntro from "./mocks/kpicardIntro";
import getKPICardFullCode from "./mocks/kpicardfullcode";
import getKPIProgress from "./mocks/kpiProgress";
import KPIDetailComponent from "../../../Components/KPI/KPIDetailComponent";
import PropTypes from "prop-types";
import { styled } from '@mui/system';

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh"
})

const KPIComponent = ({ userView }) => {
  const theme = useTheme();
  const [intro, setIntro] = useState(getIntro);
  const [fullIntro, setFullIntro] = useState(getFullIntro);
  const [kpiCardIntro, setKpiCardIntro] = useState(getKPICardIntro);
  const [kpiCardFull, setKpiCardFull] = useState(getKPICardFullCode);
  const [kpiProgress, setKpiProgress] = useState(getKPIProgress);

  const [open, setOpen] = useState({
    index: false,
    kpiCard: false,
  });

  const handleToggle = (e) => {
    const name = e?.currentTarget?.name;
    setOpen({
      ...open,
      [name]: !open[name],
    });
  };

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box
        sx={{
          m: "auto",
          textAlign: "center"
        }}
      >
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h2>KPI Component</h2>
          <p>
            This component may contain statistical data, information, or any
            other content that may be relevant to the user.
          </p>
          <KPIDetailComponent userView={userView} />
        </Box>
        <Typography
          color="textPrimary"
          sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
        >
          <p>
            The following code snippet demonstrates how KPI Component is
            displayed.
          </p>
        </Typography>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton
                  value={open?.index ? fullIntro : intro}
                />
                <Tooltip
                  title={
                    !open?.index
                      ? "Show full source Code"
                      : "Hide full Source Code"
                  }
                >
                  <IconButton
                    name="index"
                    onClick={handleToggle}
                    sx={{
                      color: `${theme.palette.primary.main}`,
                      "&:hover": {
                        backgroundColor: `${theme.palette.secondary.main}`,
                        color: "primary.main",
                      },
                      height: 35,
                      width: 35,
                      mt: 1,
                    }}
                  >
                    {!open?.index ? (
                      <ExpandMoreIcon />
                    ) : (
                      <KeyboardArrowUpIcon />
                    )}
                  </IconButton>
                </Tooltip>
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={open?.index ? fullIntro : intro}
              onValueChange={
                open?.index
                  ? (_fullIntro) => setFullIntro(_fullIntro)
                  : (_intro) => setIntro(_intro)
              }
              highlight={
                open?.index
                  ? (_fullIntro) => highlight(_fullIntro, languages.js)
                  : (_intro) => highlight(_intro, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          sx={{
            m: "auto",
            textAlign: "justify",
            maxWidth: "80%",
            color: "textPrimary",
          }}
        >
          <p>KPIDetailComponent receives the following props:</p>
          <ul>
            <li>
              <b>userView</b> - Boolean value{" "}
            </li>
            <li>
              <b>username</b> - Check if a user exists;{" "}
            </li>
            <li>
              <b>periodicView</b> - Boolean value;{" "}
            </li>
            <li>
              <b>evaluationMomentID</b> - Data context, cycle to which the data
              refer.
            </li>
          </ul>
          <p>
            {" "}
            After making the API calls, we need to draw the cards. For this we
            resort to the following component. You can import this component
            into your src folder:
          </p>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton
                  value={open?.kpiCard ? kpiCardFull : kpiCardIntro}
                />
                <Tooltip
                  title={
                    !open?.kpiCard
                      ? "Show full source Code"
                      : "Hide full Source Code"
                  }
                >
                  <IconButton
                    name="kpiCard"
                    onClick={handleToggle}
                    sx={{
                      color: `${theme.palette.primary.main}`,
                      "&:hover": {
                        backgroundColor: `${theme.palette.secondary.main}`,
                        color: "primary.main",
                      },
                      height: 35,
                      width: 35,
                      mt: 1,
                    }}
                  >
                    {!open?.kpiCard ? (
                      <ExpandMoreIcon />
                    ) : (
                      <KeyboardArrowUpIcon />
                    )}
                  </IconButton>
                </Tooltip>
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={open?.kpiCard ? kpiCardFull : kpiCardIntro}
              onValueChange={
                open?.kpiCard
                  ? (_kpiCardFull) => setKpiCardFull(_kpiCardFull)
                  : (_kpiCardIntro) => setKpiCardIntro(_kpiCardIntro)
              }
              highlight={
                open?.kpiCard
                  ? (_kpiCardFull) => highlight(_kpiCardFull, languages.js)
                  : (_kpiCardIntro) => highlight(_kpiCardIntro, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
      </Box>
      <Box
        sx={{
          m: "auto",
          textAlign: "justify",
          maxWidth: "80%",
          color: "textPrimary",
        }}
      >
        <p>CardKPI Component receives the following props:</p>
        <ul>
          <li>
            <b>title</b> - The title of the card;{" "}
          </li>
          <li>
            <b>value</b> - The value of the card. (In this example: 20, 50 and
            30);{" "}
          </li>
          <li>
            <b>unit</b> - Value type, in this case, percentage (%);
          </li>
          <li>
            <b>description</b> - Subtitle;{" "}
          </li>
          <li>
            <b>extraContent</b> - We can add more content to the card, for
            example a progress line.
          </li>
        </ul>
        <p>
          {" "}
          Since in this example we also use a progress line, it requires
          importing one more component:
        </p>
      </Box>
      <StyledEditorBox>
        <CardHeader
          action={
            <>
              <CopyToClipboardButton value={kpiProgress} />
            </>
          }
        />
        <Divider />
        <CardContent
          sx={{
            maxHeight: "50vh",
            overflowY: "scroll",
          }}
        >
          <Editor
            value={kpiProgress}
            onValueChange={(_kpiProgress) => setKpiProgress(_kpiProgress)}
            highlight={(_kpiProgress) => highlight(_kpiProgress, languages.js)}
            padding={10}
            style={{
              fontFamily: '"Fira code", "Fira Mono", monospace',
              fontSize: 12,
              backgroundColor: theme.palette.background.paper,
              borderRadius: theme.shape.borderRadius,
            }}
          />
        </CardContent>
      </StyledEditorBox>
      <Box
        sx={{
          m: "auto",
          textAlign: "justify",
          maxWidth: "80%",
          color: "textPrimary",
        }}
      >
        <p>KPIProgress receives the following props:</p>
        <ul>
          <li>
            <b>description</b> - Subtitle;
          </li>
          <li>
            <b>tooltip</b> - Informative Text;
          </li>
          <li>
            <b>progress</b> - Progress value;
          </li>
          <li>
            <b>progressCap</b> - Progress max value;
          </li>
        </ul>
      </Box>
      <ChangeLogEntry
        version="V1.0.0"
        changeLog={ChangeLog()}
      />
    </Box>
  );
};
KPIComponent.propTypes = {
  userView: PropTypes.bool,
};
export default KPIComponent;

const ChangeLog = () => {
  const theme = useTheme();
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content:
        "Imported KPI Component. This component is formed by: KPI Detail Component, CardKPI and KPIProgress.",
      className: chips.green,
    },
  ];

  return changeLog;
};
