﻿using System;
using System.Collections.Generic;
using RnD.BackEnd.API.Model.Workflow;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// Reassign Task request example
    /// </summary>
    public class ReassignRequest : IMultipleExamplesProvider<ReassignTaskDto>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to Reassign a task.
        /// </returns>
        public IEnumerable<SwaggerExample<ReassignTaskDto>> GetExamples()
        {
            yield return SwaggerExample.Create("Reassign - With Custom Email", new ReassignTaskDto
            {
                Id = Guid.NewGuid(),
                Comments = "I don't have enough informations i will forwarded to my colleague",
                NewUserId = "<EMAIL>",
                EmailRequest = new WorkflowEmailDto()
                {
                    TemplateId = "d-c43961e085e04f1c8e1f0192db1c69d8",
                    Recipients = new List<string>() { "<EMAIL>", "<EMAIL>" },
                    TemplateData = new Dictionary<string, string>()
                      {
                          { "Message", "This is an example of Email" }
                      },
                    Subject = "FrameWork Example of Email",
                    SendEmail = true
                }
            });
            yield return SwaggerExample.Create("Reassign - With Default Email", new ReassignTaskDto
            {
                Id = Guid.NewGuid(),
                Comments = "I don't have enough informations i will forwarded to my colleague",
                NewUserId = "<EMAIL>",
                EmailRequest = new WorkflowEmailDto()
                {
                    SendEmail = true
                }
            });
            yield return SwaggerExample.Create("Reassign - Not Sending Email", new ReassignTaskDto
            {
                Id = Guid.NewGuid(),
                Comments = "I don't have enough informations i will forwarded to my colleague",
                NewUserId = "<EMAIL>",
                EmailRequest = new WorkflowEmailDto()
                {
                    SendEmail = false
                }
            });
        }
    }
}
