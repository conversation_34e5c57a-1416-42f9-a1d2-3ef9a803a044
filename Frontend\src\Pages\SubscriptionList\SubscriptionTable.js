import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
    Box,
    Card,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
    TablePagination,
    TextField,
    Typography,
    Link
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import Scrollbar from '../../Components/Scrollbar';
import TableLoading from '../../Components/TableUtils/TableLoading';
import { formatCurrency, formatDate } from '../../Utils/formatUtils';
import { useTranslation } from 'react-i18next';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ptBR from 'date-fns/locale/pt-BR';
import enUS from 'date-fns/locale/en-US';

const SubscriptionTable = ({ subscriptions, isLoading, selectedMonth, onMonthChange }) => {
    const navigate = useNavigate();
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [searchTerm, setSearchTerm] = useState('');
    const { t } = useTranslation('common');

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleSearchChange = (event) => {
        setSearchTerm(event.target.value);
        setPage(0);
    };

    const handleViewDetails = (subscriptionId) => {
        navigate(`/Subscriptions/${subscriptionId}`);
    };

    const filteredSubscriptions = subscriptions.filter(subscription =>
        subscription.subscriptionId.toLowerCase().includes(searchTerm.toLowerCase()));

    if (!subscriptions || subscriptions.length === 0) {
        return (
            <Box sx={{ p: "24px 32px 0px 32px" }}>
                <Card>
                    <Box sx={{ p: 2 }}>
                        <Typography>{t("subscription.noSubscriptionsFound")}</Typography>
                    </Box>
                </Card>
            </Box>
        );
    }

    return (
        <Box sx={{ p: "24px 32px 0px 32px" }}>
            <Card>
                <Box sx={{ alignItems: "center", display: "flex", m: 2, height: "88px" }}>
                    <TextField
                        sx={{ width: "458px", height: "56px" }}
                        InputProps={{
                            startAdornment: <SearchIcon fontSize="small" />,
                        }}
                        onChange={handleSearchChange}
                        placeholder={t("search.searchSubscription")}
                        value={searchTerm}
                        variant="outlined"
                    />
                    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={localStorage.getItem('preferedLanguage') === 'en' ? enUS : ptBR}>
                        <TextField
                            type="month"
                            value={selectedMonth}
                            onChange={(e) => onMonthChange(e.target.value)}
                            sx={{ ml: 2 }}
                            InputLabelProps={{
                                shrink: true,
                            }}
                        />
                    </LocalizationProvider>
                </Box>
                <Scrollbar>
                    <Box sx={{ p: 2, pt: 0, minWidth: 700 }}>
                        <Table stickyHeader>
                            <TableHead>
                                <TableRow>
                                    <TableCell>{t("subscription.id")}</TableCell>
                                    <TableCell align="right">{t("subscription.totalCostEUR")}</TableCell>
                                    <TableCell align="right">{t("subscription.totalCostUSD")}</TableCell>
                                    <TableCell align="right">{t("subscription.resourceCount")}</TableCell>
                                    <TableCell>{t("subscription.startDate")}</TableCell>
                                    <TableCell>{t("subscription.endDate")}</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                <TableLoading
                                    isLoading={isLoading}
                                    numRows={rowsPerPage}
                                    numColumns={6}
                                />
                                {filteredSubscriptions
                                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                    .map((subscription) => (
                                        <TableRow hover key={subscription.subscriptionId} sx={{ height: "77px" }}>
                                            <TableCell>
                                                <Link
                                                    component="button"
                                                    variant="body2"
                                                    onClick={() => handleViewDetails(subscription.subscriptionId)}
                                                    sx={{ textDecoration: 'none', color: 'primary.main' }}
                                                >
                                                    {subscription.subscriptionId}
                                                </Link>
                                            </TableCell>
                                            <TableCell align="right">
                                                {subscription.hasNoData ? t("tableActions.noValues") : formatCurrency(subscription.totalCost)}
                                            </TableCell>
                                            <TableCell align="right">
                                                {subscription.hasNoData ? t("tableActions.noValues") : formatCurrency(subscription.totalCostUSD, 'USD')}
                                            </TableCell>
                                            <TableCell align="right">
                                                {subscription.hasNoData ? t("tableActions.noValues") : subscription.resourceCount}
                                            </TableCell>
                                            <TableCell>
                                                {subscription.hasNoData ? t("tableActions.noValues") : formatDate(subscription.startDate)}
                                            </TableCell>
                                            <TableCell>
                                                {subscription.hasNoData ? t("tableActions.noValues") : formatDate(subscription.endDate)}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                            </TableBody>
                        </Table>
                    </Box>
                </Scrollbar>
                <TablePagination
                    component="div"
                    count={filteredSubscriptions.length}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    page={page}
                    rowsPerPage={rowsPerPage}
                    rowsPerPageOptions={[5, 10, 25]}
                    showFirstButton
                    showLastButton
                    labelRowsPerPage={t("common:tableActions.rowsPerPage")}
                />
            </Card>
        </Box>
    );
};

SubscriptionTable.propTypes = {
    subscriptions: PropTypes.arrayOf(
        PropTypes.shape({
            subscriptionId: PropTypes.string.isRequired,
            totalCost: PropTypes.number,
            totalCostUSD: PropTypes.number,
            resourceCount: PropTypes.number,
            startDate: PropTypes.string,
            endDate: PropTypes.string,
            hasNoData: PropTypes.bool
        })
    ).isRequired,
    isLoading: PropTypes.bool,
    selectedMonth: PropTypes.string.isRequired,
    onMonthChange: PropTypes.func.isRequired
};

export default SubscriptionTable;
