import axios from "axios";

//URL da API
const API_BASE_URL = `${process.env.REACT_APP_WEBAPI_URL}/api`;

// Procurar todos os consumos de um cliente específico
export const getConsumosByClienteId = async (clienteId) => {
    console.log('Iniciando getConsumosByClienteId:', { clienteId });
    try {
        console.log('Fazendo requisição para:', `${API_BASE_URL}/Consumo_Cliente/cliente/${clienteId}`);
        const response = await axios.get(`${API_BASE_URL}/Consumo_Cliente/cliente/${clienteId}`);
        console.log('Resposta recebida:', response.data);
        return response.data;
    } catch (error) {
        console.error("Erro ao buscar consumos do cliente:", error);
        console.log('Detalhes do erro:', {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
        });
        // Retorna array vazio em caso de erro
        return [];
    }
};

// Procurar todos os resource groups de um cliente
export const getResourceGroupsByClienteId = async (clienteId) => {
    console.log('Iniciando getResourceGroupsByClienteId:', { clienteId });
    try {
        const consumos = await getConsumosByClienteId(clienteId);
        console.log('Consumos recebidos:', consumos);

        // Extrair regiões únicas dos consumos
        const resourceGroups = [...new Set(consumos.map(item => item.consumo.regiao))];
        console.log('Resource groups extraídos:', resourceGroups);
        return ["Geral", ...resourceGroups];
    } catch (error) {
        console.error("Erro ao buscar resource groups do cliente:", error);
        console.log('Detalhes do erro:', {
            message: error.message,
            stack: error.stack
        });
        // Retorna apenas "Geral" em caso de erro
        return ["Geral"];
    }
};

// Calcular totais para um cliente por resource group
export const getConsumoSummaryByResourceGroup = async (clienteId, resourceGroup = null, dataInicio = null, dataFim = null) => {
    console.log('Iniciando getConsumoSummaryByResourceGroup:', { clienteId, resourceGroup, dataInicio, dataFim });
    try {
        const consumos = await getConsumosByClienteId(clienteId);
        console.log('Consumos recebidos:', consumos);

        // Se não houver consumos, retorna objeto vazio com estrutura esperada
        if (!consumos || consumos.length === 0) {
            console.log('Nenhum consumo encontrado, retornando objeto vazio');
            return {
                totalServicos: 0,
                countryListTotal: 0,
                countryResellerTotal: 0,
                countryCustomerTotal: 0,
                totalGeral: 0,
                servicosPorTipo: {},
                regioesResumo: {},
                periodoInicio: null,
                periodoFim: null,
                moeda: "EUR"
            };
        }

        // Filtrar por resource group e datas
        let filteredConsumos = resourceGroup && resourceGroup !== "Geral"
            ? consumos.filter(item => item.consumo.regiao === resourceGroup)
            : consumos;

        console.log('Consumos filtrados por resource group:', filteredConsumos);

        // Filtrar por datas se fornecidas
        if (dataInicio) {
            filteredConsumos = filteredConsumos.filter(item =>
                new Date(item.consumo.dataInicio) >= new Date(dataInicio));
        }
        if (dataFim) {
            filteredConsumos = filteredConsumos.filter(item =>
                new Date(item.consumo.dataFim) <= new Date(dataFim));
        }

        console.log('Consumos filtrados por data:', filteredConsumos);

        if (filteredConsumos.length === 0) {
            console.log('Nenhum consumo após filtros, retornando objeto vazio');
            return {
                totalServicos: 0,
                countryListTotal: 0,
                countryResellerTotal: 0,
                countryCustomerTotal: 0,
                totalGeral: 0,
                servicosPorTipo: {},
                regioesResumo: {},
                periodoInicio: null,
                periodoFim: null,
                moeda: "EUR"
            };
        }

        // Extrair a moeda do primeiro consumo, se existir
        const { consumo: { moeda = "EUR" } = {} } = filteredConsumos[0] || {};

        const summary = {
            totalServicos: filteredConsumos.length,
            countryListTotal: filteredConsumos.reduce((sum, item) => sum + item.consumo.countryListTotal, 0),
            countryResellerTotal: filteredConsumos.reduce((sum, item) => sum + item.consumo.countryResellerTotal, 0),
            countryCustomerTotal: filteredConsumos.reduce((sum, item) => sum + item.consumo.countryCustomerTotal, 0),
            totalGeral: 0,
            servicosPorTipo: {},
            regioesResumo: {},
            periodoInicio: null,
            periodoFim: null,
            moeda
        };

        console.log('Summary inicial calculado:', summary);

        // Calcular o total geral
        summary.totalGeral = summary.countryListTotal + summary.countryResellerTotal + summary.countryCustomerTotal;

        // Agrupar por tipo de serviço
        filteredConsumos.forEach(item => {
            const { consumo: { tipoServico } } = item;
            if (!summary.servicosPorTipo[tipoServico]) {
                summary.servicosPorTipo[tipoServico] = {
                    countryListTotal: 0,
                    countryResellerTotal: 0,
                    countryCustomerTotal: 0,
                    total: 0
                };
            }
            summary.servicosPorTipo[tipoServico].countryListTotal += item.consumo.countryListTotal;
            summary.servicosPorTipo[tipoServico].countryResellerTotal += item.consumo.countryResellerTotal;
            summary.servicosPorTipo[tipoServico].countryCustomerTotal += item.consumo.countryCustomerTotal;
            summary.servicosPorTipo[tipoServico].total =
                summary.servicosPorTipo[tipoServico].countryListTotal +
                summary.servicosPorTipo[tipoServico].countryResellerTotal +
                summary.servicosPorTipo[tipoServico].countryCustomerTotal;
        });

        console.log('Serviços por tipo calculados:', summary.servicosPorTipo);

        // Se estiver na visualização "Geral", adicionar resumo por região
        if (resourceGroup === "Geral") {
            // Agrupar por região
            const regioes = [...new Set(filteredConsumos.map(item => item.consumo.regiao))];
            console.log('Regiões encontradas:', regioes);

            regioes.forEach(regiao => {
                const consumosDaRegiao = filteredConsumos.filter(item => item.consumo.regiao === regiao);

                summary.regioesResumo[regiao] = {
                    countryListTotal: consumosDaRegiao.reduce((sum, item) => sum + item.consumo.countryListTotal, 0),
                    countryResellerTotal: consumosDaRegiao.reduce((sum, item) => sum + item.consumo.countryResellerTotal, 0),
                    countryCustomerTotal: consumosDaRegiao.reduce((sum, item) => sum + item.consumo.countryCustomerTotal, 0),
                    total: 0,
                    servicosCount: consumosDaRegiao.length
                };

                summary.regioesResumo[regiao].total =
                    summary.regioesResumo[regiao].countryListTotal +
                    summary.regioesResumo[regiao].countryResellerTotal +
                    summary.regioesResumo[regiao].countryCustomerTotal;
            });

            console.log('Resumo por região calculado:', summary.regioesResumo);
        }

        // período de datas
        if (filteredConsumos.length > 0) {
            summary.periodoInicio = new Date(Math.min(...filteredConsumos.map(item => new Date(item.consumo.dataInicio))));
            summary.periodoFim = new Date(Math.max(...filteredConsumos.map(item => new Date(item.consumo.dataFim))));
            console.log('Período calculado:', { inicio: summary.periodoInicio, fim: summary.periodoFim });
        }

        console.log('Summary final:', summary);
        return summary;
    } catch (error) {
        console.error("Erro ao calcular resumo de consumos:", error);
        console.log('Detalhes do erro:', {
            message: error.message,
            stack: error.stack
        });
        // Retorna objeto vazio com estrutura esperada em caso de erro
        return {
            totalServicos: 0,
            countryListTotal: 0,
            countryResellerTotal: 0,
            countryCustomerTotal: 0,
            totalGeral: 0,
            servicosPorTipo: {},
            regioesResumo: {},
            periodoInicio: null,
            periodoFim: null,
            moeda: "EUR"
        };
    }
};

export default {
    getConsumosByClienteId,
    getResourceGroupsByClienteId,
    getConsumoSummaryByResourceGroup
};
