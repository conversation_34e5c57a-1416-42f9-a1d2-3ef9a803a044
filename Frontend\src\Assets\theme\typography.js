import palette from './palette';

const Font = {
  fontFamily: '<PERSON><PERSON>ri',
  color: palette.text.primary,
  primary: {
    color: palette.primary.main
  },

  subtitle2: {
    fontSize: '16px',
    fontWeight: 400,
    lineHeight: '18px',
    letterSpacing: '0.1px'
  },

  //Header customization
  h1: {
    fontSize: '1.3rem',
    background: "none",
    WebkitTextFillColor: "initial"
  },

  //Page Title
  h2: {
    fontSize: '24px',
    fontWeight: 'bold'
  },

  h3: {
    fontSize: '1.08rem',
    marginBottom: '0 !important',
    fontWeight: 'bold'

  },
  h4: {
    fontSize: '1.05rem',
    fontWeight: 'bold'
  },
  h5: {
    fontSize: '1.03rem'
  },

  //Gradient Header customization
  h1g: {
    fontSize: '1.12rem',
    background: palette.text.primary,
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent"
  },

  h2g: {
    fontSize: '1.12rem',
    background: palette.primary.mainGradient,
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent"
  },

  button: {
    fontSize: '0.9rem',
    textTransform: 'capitalize',
  },

  //Body and Breadcrumbs
  subtitle1: {
    fontSize: "14px",
    letterSpacing: "0.1px"
  }
};
export default Font;
