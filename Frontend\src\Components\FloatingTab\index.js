import React, { useState, useEffect } from 'react';
import clsx from 'clsx';
import { Box, Card, IconButton } from '@mui/material';
import { DonutLarge as DonutLargeIcon } from '@mui/icons-material';
import PropTypes from 'prop-types';
import Scrollbar from '../Scrollbar';
import { styled } from '@mui/system';

const NavbarHeight = "64px";

const FloatingWindowWrapper = styled(Box)({
    position: 'fixed',
    zIndex: 1040,
    top: NavbarHeight,
    right: 0,
    height: `calc(100% - ${NavbarHeight})`,
    transition: "500ms",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    transform: "translateX(calc(100% - 39px))",
    pointerEvents: "none",
    "&.open": {
        transform: "translateX(1px)"
    },
    "& .floatingWindow": {
        display: "flex",
        alignItems: "center",
        height: "100%",
        "& .floatingTab": {
            pointerEvents: "initial",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1030,
            position: "relative",
            borderTopRightRadius: "0",
            borderBottomRightRadius: "0",
            overflow: "initial",
            background: "whitesmoke",
            "&.active": {
                zIndex: 1040,
                background: "white",
                "&:after": {
                    content: "''",
                    height: "100%",
                    width: "10px",
                    position: "absolute",
                    top: "0px",
                    left: "calc(100% - 5px)",
                    background: "white"
                },
                "& > .MuiBox-root": {
                    background: "white",
                }
            },
            "& > .MuiBox-root": {
                background: "lightgray",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
            }
        },
        "& .floatingContent": {
            pointerEvents: "initial",
            zIndex: 1030,
            minWidth: "200px",
            maxHeight: `76vh`,
            overflow: "auto",
            display: "flex",
            justifyContent: "center"
        }
    }
});

const StyledIcon = styled("div")({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    width: "24px"
});

const DefaultIcon = ({ children }) => {
    return (
        <StyledIcon>{children}</StyledIcon>
    )
};

DefaultIcon.propTypes = {
    children: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number
    ])
}

const FloatingTab = ({ data, onOpen, onClose }) => {
    const [toggled, setToggled] = useState(0);
    const [open, setOpen] = useState(true);
    const [isLoaded, setIsLoaded] = useState(false);

    const handleToggle = (index) => {
        if (data.length <= 1) {
            setOpen(!open);
        } else {
            setOpen(true);
            setToggled(index);
        }
    }

    useEffect(() => {
        if (!isLoaded) {
            return;
        }
        if (open && onOpen) {
            onOpen();
        }
        if (!open && onClose) {
            onClose();
        }
    }, [open]);

    useEffect(() => {
        setIsLoaded(true);
    }, []);

    return (
        <FloatingWindowWrapper className={clsx(open && 'open')}>
            <Box
                className="floatingWindow"
            >
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: Array.isArray(data) ? 'flex-start' : 'center'
                    }}
                >
                    <Card
                        sx={{
                            p: 1,
                            mb: Array.isArray(data) ? 2 : 0
                        }}
                        className="floatingTab active"
                    >
                        <IconButton
                            onClick={() => setOpen(!open)}
                            size="small"
                        >
                            {open ? <DonutLargeIcon /> : <DonutLargeIcon />}
                        </IconButton>
                    </Card>
                    {
                        data && Array.isArray(data) && data.map((d, d_index) =>
                            <Card
                                key={"floatingTab_" + d_index}
                                sx={{
                                    p: 1,
                                    mb: data.length > 1 ? 1 : 0
                                }}
                                onClick={() => handleToggle(d_index)}
                                className={clsx("floatingTab", data.length <= 1 || toggled === d_index ? 'active' : '')}
                            >
                                {d.icon ? d.icon : <DefaultIcon>{d_index}</DefaultIcon>}
                            </Card>)
                    }
                </Box>
                <Card className="floatingContent">
                    <Scrollbar>
                        {
                            data && <Box
                                sx={{
                                    p: 2,
                                    width: "100%",
                                    height: "100%"
                                }}
                            >
                                {

                                    Array.isArray(data) ? data.map((d, d_i) => {
                                        if (d_i !== toggled) {
                                            return null
                                        }
                                        return <Box
                                            key={d_i}
                                        >
                                            {d.content ? d.content : d}
                                        </Box>
                                    })
                                        :
                                        <Box>
                                            {data.content ? data.content : data}
                                        </Box>
                                }
                            </Box>
                        }
                    </Scrollbar>
                </Card>
            </Box>
        </FloatingWindowWrapper>
    )
};

FloatingTab.propTypes = {
    data: PropTypes.oneOfType([
        PropTypes.object,
        PropTypes.array
    ]),
    onOpen: PropTypes.func,
    onClose: PropTypes.func
}

export default FloatingTab;
