/*eslint-disable no-unused-expressions*/
import handleMethod from '../Utils/APIutils/handleMethod';

class UserAPI {
  async AzureAuthenticate() {
    const Input = {
      type: 'GET',
      method: `AzureAuthenticate`,
      params: {}
    };

    const response = await handleMethod(Input);

    return {
      error: false,
      response
    };
  }

  async Authenticate() {
    const Input = {
      type: 'GET',
      method: `Authenticate`,
      params: {}
    };

    const response = await handleMethod(Input);

    return response;
  }

  async GetUsersList(id, filters = []) {
    const request = {
      type: 'GET',
      method: `getUserList`,
      params: {
        roleIDs: id,
        filters: filters
      }
    }

    const response = await handleMethod(request);

    return response;
  }

  async GetUserDetails(username) {
    const searchParams = username && username !== null ? `?username=${username}` : '';
    const Input = {
      type: 'GET',
      method: `api/user/GetUserDetails${searchParams}`,
      params: {}
    };

    try {
      console.log('userAPI: Chamando endpoint GetUserDetails...');
      const response = await handleMethod(Input);

      if (response && response.error) {
        console.error('userAPI: Erro ao obter detalhes do usuário:', response);
        throw new Error(response.description || 'Erro desconhecido');
      }

      if (username === "new") {
        response[0].username = null;
      }

      return response;
    } catch (error) {
      console.error('userAPI: Falha ao obter detalhes do usuário:', error);

      // Tentar endpoint alternativo
      try {
        console.log('userAPI: Tentando endpoint alternativo GetCurrentUser...');
        const altInput = {
          type: 'GET',
          method: `api/user/GetCurrentUser`,
          params: {}
        };

        const altResponse = await handleMethod(altInput);
        if (altResponse && !altResponse.error) {
          console.log('userAPI: Detalhes obtidos via GetCurrentUser');
          return altResponse;
        }
      } catch (altError) {
        console.error('userAPI: Falha no endpoint alternativo:', altError);
      }

      return { error: true, description: error.message };
    }
  }

  async getProfiles(id, filters = []) {
    const Input = {
      type: 'GET',
      method: `getRoles`,
      params: {
        id: id,
        filters: filters
      }
    };

    const response = await handleMethod(Input);

    return response;
  }

  async GetRoleConfiguration(roleID) {
    const searchParams = roleID && roleID !== null ? `?roleIDs=${roleID}` : '';
    const Input = {
      type: 'GET',
      method: `GetRoleConfiguration${searchParams}`,
      params: {}
    };

    const response = await handleMethod(Input);

    if (roleID === "new") {
      return response[0];
    }
    return response;
  }

  async LdapLogin(email, password) {
    const Input = {
      type: 'POST',
      method: `${process.env.REACT_APP_WEBAPI_URL}/api/User/LdapLogin`,
      params: {
        Email: email,
        Password: password
      }
    };

    const response = await handleMethod(Input);

    return response;
  }

  // Novo método para salvar o token do Azure AD
  saveAzureToken(token) {
    // Salvar no localStorage
    if (token) {
      localStorage.setItem('azureToken', token);

      // Usar o mesmo token como sessão para o handleMethod
      localStorage.setItem('session', JSON.stringify(token));

      console.log('Token Azure AD salvo com sucesso!');
      return true;
    }
    return false;
  }

  // Verificar se o user já está registrado no backend
  async checkUserRegistration() {
    const Input = {
      type: 'GET',
      method: 'api/user/me',
      params: {}
    };

    try {
      const response = await handleMethod(Input);
      console.log('Verificação de registro:', response);

      // Se o user não estiver no banco de dados regist automaticamente
      if (response?.isAuthenticated && !response?.userInDatabase) {
        return this.registerUser();
      }

      return response;
    } catch (error) {
      console.error('Erro ao verificar registro:', error);
      return { error: true, message: 'Erro ao verificar registro do user' };
    }
  }

  // registar user no backend
  async registerUser() {
    console.log('userAPI: Iniciando registro de user...');

    const token = localStorage.getItem('azureToken');
    if (!token) {
      console.error('userAPI: Nenhum token disponível para registro');
      return { error: true, message: 'Nenhum token de autenticação disponível' };
    }

    console.log('userAPI: Token disponível, enviando requisição de registro');

    const apiBaseUrl = process.env.REACT_APP_WEBAPI_URL || 'http://localhost:44354';

    const Input = {
      type: 'POST',
      method: `${apiBaseUrl}/api/user/register`,
      params: {}
    };

    try {
      console.log('userAPI: Enviando requisição POST para:', Input.method);
      const response = await handleMethod(Input);
      console.log('userAPI: Resposta do registro:', response);

      // Verificar se a resposta contém erro
      if (response && response.error) {
        console.error('userAPI: Erro no registro retornado pela API:', response.error, response.description || response.message);
        return { error: true, message: response.description || response.message || 'Erro desconhecido no registro' };
      }

      // Verificar se tem a propriedade 'User'
      if (response && response.User) {
        console.log('userAPI: user registrado com sucesso:', response.User);
      } else if (response && response.Message && response.Message.includes('já existe')) {
        console.log('userAPI: user já existente:', response.Message);
      }

      return response;
    } catch (error) {
      console.error('userAPI: Erro ao registar user:', error);
      return {
        error: true,
        message: error.message || 'Erro ao registar user',
        originalError: error
      };
    }
  }

  // Função de inicialização - deve ser chamada no carregamento da aplicação
  async initializeUser() {
    console.log('Inicializando verificação de user...');

    // Verificar se temos um token
    const token = localStorage.getItem('azureToken');

    if (!token) {
      console.log('Nenhum token encontrado, pulando verificação de user');
      return { authenticated: false };
    }

    console.log('Token encontrado, verificando autenticação...');

    try {
      // Primeiro verificar se estamos autenticados
      const authInfo = await this.fetchAuthInfo();

      console.log('Informações de autenticação:', authInfo);

      if (authInfo.isAuthenticated) {
        console.log('user autenticado, verificando registro...');

        // Verificar se o user já existe no sistema
        const userInfo = await this.checkUserRegistration();

        return {
          authenticated: true,
          registered: userInfo?.userInDatabase || false,
          userInfo
        };
      }
    } catch (error) {
      console.error('Erro ao iniciar user:', error);
      return {
        authenticated: false,
        error: true,
        message: error.message || 'Erro ao verificar user'
      };
    }
  }

  // Buscar informações de autenticação
  async fetchAuthInfo() {
    const Input = {
      type: 'GET',
      method: 'api/user/auth-info',
      params: {}
    };

    try {
      const response = await handleMethod(Input);
      return response;
    } catch (error) {
      console.error('Erro ao buscar informações de autenticação:', error);
      throw error;
    }
  }
}

export const userAPI = new UserAPI();

// Exportar uma função para iniciar o user
export const initializeAuthentication = async () => {
  try {
    console.log('Iniciando processo de autenticação...');
    const result = await userAPI.initializeUser();
    console.log('Resultado da inicialização:', result);
    return result;
  } catch (error) {
    console.error('Erro na inicialização da autenticação:', error);
    return { authenticated: false, error: true };
  }
};
