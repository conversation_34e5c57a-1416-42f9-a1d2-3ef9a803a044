﻿namespace RnD.BackEnd.Email.BusinessMessages
{
    public static class EmailMessages
    {
        public const string Recipients_Required = "At least one recipient is required to send an e-mail.";
        public const string Template_Data_Required = "E-mail template data is required.";
        public const string Template_Id_Required = "E-mail template Id is required.";
        public const string From_Email_Address_Required = "From E-mail address is required.";
        public const string From_User_Name_Required = "From User Name is required.";
        public const string Invalid_Email = "The e-mail address {0} is invalid.";
        public const string Error_Sending_Email_With_Template = "An error has occurred while sending the e-mail with template Id {0}.";
        public const string Error_Sending_Email = "An error has occurred while sending the e-mail.";
    }
}
