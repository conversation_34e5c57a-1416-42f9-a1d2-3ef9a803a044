import PropTypes from 'prop-types';
import * as Yup from 'yup';
import { Formik } from 'formik';
import { useParams } from 'react-router-dom';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Grid,
  Typography,
  FormHelperText,
  TextField,
  Alert,
  AlertTitle,
  Switch
} from '@mui/material';
import {
  useEffect,
  useState,
  useCallback
} from 'react';
import ConfirmModal from '../../Modal/ConfirmModal';
import { useTranslation } from 'react-i18next';

const UserDetailsForm = (props) => {
  const { options } = props;
  const params = useParams();
  const { t } = useTranslation();

  const [allProfiles, setAllProfiles] = useState([]);
  const [userData, setUserData] = useState(options);
  const [selectedProfiles, setselectedProfiles] = useState([])
  const [auxselectedProfiles, setAuxSelectedProfiles] = useState([])
  const [profileIDs, setProfileIDs] = useState([]);
  const userDataName = userData?.name !== null || userData?.name !== undefined
  const userDataPassword = userData && (userData?.password !== null || userData?.password !== undefined);
  if (profileIDs === 123456) {
    console.log(profileIDs);
  }
  const [showModal, setShowModal] = useState(false);

  const handleArrayofIDs = (currentSelectedProfiles) => {
    const arrayofIDs = currentSelectedProfiles?.map(profile => profile?.id).sort((x, y) => { return x - y })
    setProfileIDs(arrayofIDs)
  }

  //The profiles will appear selected according to the information in database.
  const handleSelectedProfiles = () => {
    const userProfiles = options?.roleIDs?.split(",");
    let arrProfiles = [];
    let auxSelectedProfiles = [];
    if (userProfiles && userProfiles?.length > 0) {
      auxSelectedProfiles = options?.allRoles?.filter(role => {
        const arr = userProfiles?.filter(value => value === role?.id?.toString())
        return (arr?.length > 0)
      });

      arrProfiles = Object?.values(auxSelectedProfiles);
    }

    setselectedProfiles(arrProfiles)
  }

  const getUserDetailsForm = useCallback(() => {
    handleSelectedProfiles();
    setAuxSelectedProfiles(selectedProfiles);
    setUserData(options);
  }, [options]);

  const HandleAllRoles = () => {
    if (options?.allRoles?.length > 0) {
      const profileOptions = options?.allRoles ? [...options.allRoles] : [];
      const result = [];
      const parts = 3;
      for (let i = parts; i > 0; i--) {
        if (profileOptions.length) {
          result.push(profileOptions.splice(0, Math.ceil(profileOptions.length / i)));
        }
      }
      setAllProfiles(result);
    }
  };

  useEffect(() => {
    getUserDetailsForm();
    HandleAllRoles();
  }, [options]);

  const isChecked = (type) => {
    if (selectedProfiles && selectedProfiles?.length > 0) {
      return selectedProfiles?.some((element) => { return element?.id === type; });
    }
    return false;
  };

  //Handles checkbox selection
  const handleCheckRole = (role, setFieldValue) => {
    const clonedProfiles = [...selectedProfiles];
    const checkedElement = Object?.values(clonedProfiles).find(x => x?.id === role);

    if (checkedElement) {
      clonedProfiles?.splice(clonedProfiles?.indexOf(checkedElement), 1);
    } else {
      const newProfile = Object?.values(options?.allRoles).find(x => x?.id === role)
      clonedProfiles.push(newProfile);
    }

    handleArrayofIDs(clonedProfiles);
    setselectedProfiles(clonedProfiles);
    setFieldValue('roles', clonedProfiles);
  };

  //API user = true/false
  const handleIsLocal = (isLocalUser, setFieldValue) => {
    const isLocal = isLocalUser
    setFieldValue('isLocalUser', !isLocal)
  }

  const handleValidationSchema = Yup
    .object()
    .shape({
      isLocalUser: Yup.boolean(),
      roles: Yup.array().min(1),
      username: Yup.string().required(),
      name: Yup.string().required(),
      password: Yup.string().when("isLocalUser", { is: true, then: Yup.string().required() })
    });

  const handleInitialValues = {
    roles: auxselectedProfiles,
    allRoles: allProfiles,
    username: userData ? userData?.username : "",
    name: userData ? userData?.name : "",
    password: userData ? userData?.password : "",
    isLocalUser: false,
    submit: null
  }

  const getError = (touched, errors, fieldName) => Boolean(touched?.[fieldName] && errors?.[fieldName]);

  const getHelperText = (touched, errors, fieldName,) => {
    return touched?.[fieldName] && errors?.[fieldName] ? t(`profile:formik.${fieldName}`) : "";
  }

  return (
    <Formik
      enableReinitialize
      validateOnChange={false}
      initialValues={handleInitialValues}
      validationSchema={handleValidationSchema}
      onSubmit={(values, actions) => {
        setTimeout(() => {
          alert(JSON.stringify(values, null, 2));
          actions.setSubmitting(false);
        }, 1000);
      }}
    >
      {({ handleSubmit, setFieldValue, errors, isSubmitting, touched, handleBlur, handleChange, values }) => (
        <form
          onSubmit={handleSubmit}
          {...props}
        >
          {errors?.submit && (
            <Box sx={{ mt: 2, mb: 2 }}>
              <Alert severity="error">
                <AlertTitle>{t('profile:formik.error')}</AlertTitle>
                {errors?.submit.map((message) => <div key={message}>{message}</div>)}
              </Alert>
            </Box>
          )}
          <Card>
            <CardHeader
              title={t('common:labels.user')}
              sx={{ paddingBottom: 0, height: "56px" }}
            />
            <CardContent>
              {!userDataName ? <Grid
                container
                sx={{ paddingLeft: 1, paddingBottom: 1 }}
                spacing={3}
                wrap="wrap"
              >
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={values?.isLocalUser}
                        onChange={() => handleIsLocal(values?.isLocalUser, setFieldValue)}
                        name="isLocal"
                      />
                    }
                    label={t('common:labels.apiUser')}
                    labelPlacement="start"
                  />
                </FormGroup>
              </Grid> : null}
              <Grid
                container
                spacing={3}
                wrap="wrap"
              >
                <Grid
                  item
                  md={4}
                  xs={12}
                >
                  <TextField
                    error={getError(touched, errors, 'name')}
                    fullWidth
                    helperText={getHelperText(touched, errors, 'name')}
                    label={t('common:form.name')}
                    name="name"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values?.name || ""}
                    variant="outlined"
                  />
                </Grid>
                <Grid
                  item
                  md={4}
                >
                  <TextField
                    error={getError(touched, errors, 'username')}
                    fullWidth
                    helperText={getHelperText(touched, errors, 'username')}
                    label={t('common:form.username')}
                    name="username"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values?.username || ""}
                    variant="outlined"
                  />
                </Grid>
                {values?.isLocalUser || userDataPassword ? <Grid
                  item
                  md={4}
                >
                  <TextField
                    error={getError(touched, errors, 'password')}
                    fullWidth
                    helperText={getHelperText(touched, errors, 'password')}
                    label="Password"
                    name="password"
                    type="password"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values?.password || ""}
                    variant="outlined"
                  />
                </Grid> : null}
                <Grid
                  item
                  md={12}
                >
                  <Typography
                    color="textPrimary"
                    gutterBottom
                    variant="subtitle2"
                  >
                    {t('common:labels.profiles')}
                  </Typography>
                  {selectedProfiles?.length === 0 && touched?.roles && errors?.roles && (
                    <Box>
                      <FormHelperText error>
                        {t('profile:formik.profile')}
                      </FormHelperText>
                    </Box>
                  )}
                  {values?.allRoles && values?.allRoles?.length !== 0 && (
                    <FormGroup row>
                      {values?.allRoles?.map((p, index) => {
                        return (
                          <Grid
                            key={index}
                            item
                            md={4}
                          >
                            {
                              p.map((c, p_index) => {
                                return (
                                  <Box
                                    key={p_index.toString()}
                                  >
                                    <FormControlLabel
                                      key={p_index.toString()}
                                      control={(
                                        <Checkbox
                                          name="unlimitedCandidates"
                                          checked={isChecked(c?.id)}
                                          color="primary"
                                          value={parseInt(c?.id, 10)}
                                          onChange={(event) => handleCheckRole(parseInt(event.target.value, 10), setFieldValue)}
                                        />
                                      )}
                                      label={
                                        <Typography variant="subtitle">
                                          {c?.longDesc}
                                        </Typography>
                                      }
                                    />
                                  </Box>
                                );
                              })
                            }
                          </Grid>
                        );
                      })}
                    </FormGroup>
                  )}
                </Grid>
              </Grid>
            </CardContent>
          </Card>
          <Grid
            container
            spacing={1}
            sx={{
              display: 'flex',
              justifyContent: 'flex-end',
              p: 3
            }}
          >
            {params?.username ? <Grid item>
              <Button
                color="primary"
                variant="outlined"
                onClick={() => setShowModal(true)}
              >
                {t('common:buttons.deleteUser')}
              </Button>
            </Grid> : null}
            <Grid item>
              <Button
                color="primary"
                onChange={handleSubmit}
                disabled={isSubmitting}
                type="submit"
                variant="contained"
              >
                {t('common:buttons.saveChanges')}
              </Button>
            </Grid>
          </Grid>
          <ConfirmModal
            open={showModal}
            onClose={() => setShowModal(false)}
            title={t('common:labels.deleteUser')}
            message={t('common:warnings.deleteMessageUser')}
          />
        </form>
      )}
    </Formik>
  );
};

UserDetailsForm.propTypes = {
  options: PropTypes.object
};

export default UserDetailsForm;
