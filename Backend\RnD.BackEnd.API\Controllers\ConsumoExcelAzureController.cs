using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Azure.Storage.Blobs;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using RnD.BackEnd.API.Services;

namespace RnD.BackEnd.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ConsumoExcelAzureController : ControllerBase
    {
        private readonly ILogger<ConsumoExcelAzureController> _logger;
        private readonly IConfiguration _configuration;
        private readonly ApplicationDbContext _context;
        private readonly LogProcessamentoService _logService;

        public ConsumoExcelAzureController(
            ILogger<ConsumoExcelAzureController> logger,
            IConfiguration configuration,
            ApplicationDbContext context,
            LogProcessamentoService logService)
        {
            _logger = logger;
            _configuration = configuration;
            _context = context;
            _logService = logService;
        }

        /// <summary>
        /// Faz upload de um arquivo Excel para processamento no container Azure
        /// </summary>
        /// <param name="file">Arquivo Excel a ser processado</param>
        /// <returns>Nome do arquivo no Azure Storage</returns>
        [HttpPost("upload")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> UploadExcel(IFormFile file)
        {
            try
            {
                _logger.LogInformation($"Iniciando upload do arquivo Excel Azure: {file?.FileName}");
                
                if (file == null || file.Length == 0)
                    return BadRequest("Arquivo não fornecido");

                // Verificar se é um arquivo Excel
                string contentType = file.ContentType;
                if (!contentType.Contains("excel") && !contentType.Contains("spreadsheetml"))
                {
                    if (!file.FileName.EndsWith(".xlsx") && !file.FileName.EndsWith(".xls"))
                    {
                        return BadRequest("O arquivo deve ser um Excel (.xlsx ou .xls)");
                    }
                }

                var connectionString = _configuration["StorageExcelAzure:ConnectionString"];
                var containerName = _configuration["StorageExcelAzure:ContainerName"];
                
                if (string.IsNullOrEmpty(connectionString))
                {
                    return BadRequest("Connection string do Azure Storage não configurada.");
                }
                
                var blobServiceClient = new BlobServiceClient(connectionString);
                var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
                await containerClient.CreateIfNotExistsAsync();

                // Gera um nome único para o arquivo
                string fileName = $"{Guid.NewGuid()}-{file.FileName}";
                var blobClient = containerClient.GetBlobClient(fileName);

                using (var stream = file.OpenReadStream())
                {
                    await blobClient.UploadAsync(stream, true);
                }

                // Registra o arquivo no banco de dados para processamento posterior
                var arquivoProcessado = new ArquivoProcessado
                {
                    Nome = fileName,
                    BlobUrl = blobClient.Uri.ToString(),
                    DataUpload = DateTime.Now,
                    Processado = false,
                    RegistrosProcessados = 0,
                    RegistrosComErro = 0
                };

                _context.ArquivosProcessados.Add(arquivoProcessado);
                await _context.SaveChangesAsync();
                int fileId = arquivoProcessado.Id;

                await _logService.RegistrarLog(fileId, $"Arquivo Excel Azure {fileName} carregado com sucesso", TipoLog.Info);
                
                return Ok(new { 
                    message = $"Arquivo {fileName} carregado com sucesso",
                    fileName = fileName
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao fazer upload do arquivo Excel Azure");
                return BadRequest(new { 
                    message = "Erro ao fazer upload do arquivo Excel Azure",
                    error = ex.Message
                });
            }
        }
    }
} 