﻿using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using RnD.BackEnd.API.Model.Vehicle;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.VehicleController
{
    /// <summary>
    /// Vehicle patch request example
    /// </summary>
    public class VehiclePatch : IExamplesProvider<JsonPatchDocument<VehicleDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to patch a vehicle
        /// </returns>
        public JsonPatchDocument<VehicleDto> GetExamples()
        {
            JsonPatchDocument<VehicleDto> changes = new JsonPatchDocument<VehicleDto>();
            changes.Operations.Add(new Operation<VehicleDto>
            {
                op = "replace",
                path = "/brand",
                value = "Nissan"
            });
            changes.Operations.Add(new Operation<VehicleDto>
            {
                op = "remove",
                path = "/version"
            });
            changes.Operations.Add(new Operation<VehicleDto>
            {
                op = "add",
                path = "/version",
                value = "1234"
            });
            return changes;
        }
    }
}
