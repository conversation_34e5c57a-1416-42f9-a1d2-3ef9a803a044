﻿using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using RnD.BackEnd.Domain.BusinessMessages;
using RnD.BackEnd.Domain.Constants;
using RnD.BackEnd.Domain.Entities.Email;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Entities.Workflow;
using RnD.BackEnd.Domain.Extensions;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Managers;
using RnD.BackEnd.Domain.Interfaces.Services;
using RnD.BackEnd.Domain.Settings;
using RnD.BackEnd.Domain.Settings.APIConfiguration;
using RnD.BackEnd.Domain.Utilities;

namespace RnD.BackEnd.Service.Services
{
    /// <summary>
    /// Workflows service class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Services.IWorkflowService" />
    public class WorkflowService : IWorkflowService
    {
        private readonly ILogger<WorkflowService> _logger;
        private readonly WorkflowSettings _settings;
        private readonly IEmailService _emailService;
        private readonly IApiConnectorManager _apiManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkflowService" /> class.
        /// </summary>
        /// <param name="settings">The settings.</param>
        /// <param name="apiManager">The API manager.</param>
        /// <param name="emailService">The email service.</param>
        /// <param name="logger">The logger.</param>
        public WorkflowService(IOptions<AppSettings> settings, IApiConnectorManager apiManager, IEmailService emailService, ILogger<WorkflowService> logger)
        {
            _settings = settings.Value.WorkflowSettings;
            _apiManager = apiManager;
            _emailService = emailService;
            _logger = logger;
        }

        #region Workflow Definitions

        /// <summary>
        /// Gets the workflow definitions.
        /// </summary>
        /// <param name="sourceType">Type of the source.</param>
        /// <param name="workflowType">Type of the workflow.</param>
        /// <param name="fetchInactive">if set to <c>true</c> [fetch inactive].</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The Service response: The list of workflows.
        /// </returns>
        public async Task<ServiceOutput<IList<WorkflowDefinition>>> GetDefinitions(string sourceType, string workflowType, bool fetchInactive = false, string userToken = null)
        {
            (ServiceOutput<IList<WorkflowDefinition>> output, string token) = await InitServiceOutputAndGetToken<IList<WorkflowDefinition>>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                Dictionary<string, string> queryParameters = new Dictionary<string, string>()
                    {
                        { "sourceType", sourceType },
                        { "workflowType", workflowType },
                        { "fetchInactive", fetchInactive.ToString() }
                    };
                output = await _apiManager.GetAsync<IList<WorkflowDefinition>>(_settings.BaseUrl, _settings.ListDefinitionsEndpoint, token, queryParameters);
            }

            return output;
        }

        #endregion Workflow Definitions

        #region Workflow Instances

        /// <summary>
        /// Gets the Workflow instances by sourceId.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The workflow by source identifier.
        /// </returns>
        public async Task<ServiceOutput<IList<WorkflowInstance>>> GetInstances(WorkflowInstanceBySourceIds request, string userToken)
        {
            (ServiceOutput<IList<WorkflowInstance>> output, string token) = await InitServiceOutputAndGetToken<IList<WorkflowInstance>>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                output = await _apiManager.PostAsync<IList<WorkflowInstance>>(_settings.BaseUrl, _settings.InstancesBySourceIDEndpoint, request, token);
            }

            return output;
        }

        /// <summary>
        /// Gets a list of instances of the user.
        /// </summary>
        /// <param name="workflowStatus">The workflow status.</param>
        /// <param name="workflowType">Type of the workflow.</param>
        /// <param name="workflowName">Name of the workflow.</param>
        /// <param name="sourceType">Type of the source.</param>
        /// <param name="purpose">The purpose.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortDirection">The sort direction.</param>
        /// <param name="itemsPerPage">The items per page.</param>
        /// <param name="currentPage">The current page.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The list with the user instances
        /// </returns>
        public async Task<ServiceOutput<WorkflowInstancesList>> GetUserInstances(
            string workflowStatus,
            string workflowType,
            string workflowName,
            string sourceType,
            string purpose,
            string sortField,
            string sortDirection,
            int? itemsPerPage,
            int? currentPage,
            string userToken)
        {
            (ServiceOutput<WorkflowInstancesList> output, string token) = await InitServiceOutputAndGetToken<WorkflowInstancesList>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                EngineApiListRequest apiRequest = new EngineApiListRequest()
                {
                    Filters = new Dictionary<string, string>(),
                    SortField = sortField,
                    SortDirection = sortDirection,
                    ItemsPerPage = itemsPerPage,
                    CurrentPage = currentPage
                };

                if (!string.IsNullOrWhiteSpace(workflowStatus))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNameWorkflowStatus, workflowStatus);
                }

                if (!string.IsNullOrWhiteSpace(workflowType))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNameWorkflowType, workflowType);
                }

                if (!string.IsNullOrWhiteSpace(sourceType))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNameSourceType, sourceType);
                }

                if (!string.IsNullOrWhiteSpace(purpose))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNamePurpose, purpose);
                }

                if (!string.IsNullOrWhiteSpace(workflowName))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNameInstanceWorkflow, workflowName);
                }

                output = await _apiManager.PostAsync<WorkflowInstancesList>(_settings.BaseUrl, _settings.UserWorkflowInstancesEndpoint, apiRequest, token);
            }

            return output;
        }

        /// <summary>
        /// Gets the workflow instance.
        /// </summary>
        /// <param name="instanceId">The instance identifier.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The workflow instance.
        /// </returns>
        public async Task<ServiceOutput<WorkflowInstance>> GetInstance(string instanceId, string userToken)
        {
            (ServiceOutput<WorkflowInstance> output, string token) = await InitServiceOutputAndGetToken<WorkflowInstance>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                Dictionary<string, string> queryParameters = new Dictionary<string, string>() { { "instanceId", instanceId } };
                output = await _apiManager.GetAsync<WorkflowInstance>(_settings.BaseUrl, _settings.InstanceByIDEndpoint, token, queryParameters);
            }

            return output;
        }

        /// <summary>
        /// Cancels the workflow.
        /// </summary>
        /// <param name="instanceId">The instance identifier.</param>
        /// <param name="userId">The user identifier.</param>
        /// <param name="userToken">The user token.</param>
        /// <param name="emailRequest">The email request.</param>
        /// <returns>
        /// true if it work, false otherwise
        /// </returns>
        public async Task<ServiceOutput<bool>> CancelWorkflow(Guid instanceId, string userId, string userToken, WorkflowEmail emailRequest)
        {
            (ServiceOutput<bool> output, string token) = await InitServiceOutputAndGetToken<bool>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                output = await _apiManager.PostAsync<bool>(
                    _settings.BaseUrl,
                    _settings.CancelWorkflowEndpoint,
                    new { instanceId, userId, emailRequest },
                    token);
            }

            return output;
        }

        /// <summary>
        /// Deletes the or cancel workflows by source identifier.
        /// </summary>
        /// <param name="sourceId">The source identifier.</param>
        /// <param name="isDeleteOperation">if set to <c>true</c> [is delete operation].</param>
        /// <param name="userToken">The user token.</param>
        /// <param name="emailRequest">The email request.</param>
        /// <returns>
        /// True if success False otherwise
        /// </returns>
        public async Task<ServiceOutput<bool>> DeleteOrCancelWorkflowsBySourceId(string sourceId, bool isDeleteOperation, string userToken, WorkflowEmail emailRequest)
        {
            (ServiceOutput<bool> output, string token) = await InitServiceOutputAndGetToken<bool>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                output = await _apiManager.PostAsync<bool>(
                    _settings.BaseUrl,
                    _settings.DeleteOrCancelBySourceIdEndpoint,
                    new { sourceId, isDeleteOperation, emailRequest },
                    token);
            }

            return output;
        }

        #endregion Workflow Instances

        #region Workflow Tasks

        /// <summary>
        /// Gets the workflow task.
        /// </summary>
        /// <param name="taskId">The task identifier.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The workflow task.
        /// </returns>
        public async Task<ServiceOutput<WorkflowTask>> GetTask(string taskId, string userToken)
        {
            (ServiceOutput<WorkflowTask> output, string token) = await InitServiceOutputAndGetToken<WorkflowTask>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                Dictionary<string, string> queryParameters = new Dictionary<string, string>() { { "taskId", taskId } };
                output = await _apiManager.GetAsync<WorkflowTask>(_settings.BaseUrl, _settings.TaskByIDEndpoint, token, queryParameters);
            }

            return output;
        }

        /// <summary>
        /// Gets a list of tasks.
        /// </summary>
        /// <param name="userId">The user identifier.</param>
        /// <param name="taskStatus">The task status.</param>
        /// <param name="taskCompleted">if set to <c>true</c> [task completed].</param>
        /// <param name="stepName">Name of the step.</param>
        /// <param name="workflowName">Name of the workflow.</param>
        /// <param name="workflowStatus">The workflow status.</param>
        /// <param name="sourceType">Type of the source.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortDirection">The sort direction.</param>
        /// <param name="itemsPerPage">The items per page.</param>
        /// <param name="currentPage">The current page.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The list of tasks.
        /// </returns>
        public async Task<ServiceOutput<WorkflowTasksList>> GetTasks(
            string userId,
            string taskStatus,
            bool taskCompleted,
            string stepName,
            string workflowName,
            string workflowStatus,
            string sourceType,
            string sortField,
            string sortDirection,
            int? itemsPerPage,
            int? currentPage,
            string userToken)
        {
            (ServiceOutput<WorkflowTasksList> output, string token) = await InitServiceOutputAndGetToken<WorkflowTasksList>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                EngineApiListRequest apiRequest = new EngineApiListRequest()
                {
                    Filters = new Dictionary<string, string>(),
                    SortField = sortField,
                    SortDirection = sortDirection,
                    ItemsPerPage = itemsPerPage,
                    CurrentPage = currentPage
                };

                if (!string.IsNullOrWhiteSpace(userId))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNameUserId, userId);
                }

                if (!string.IsNullOrWhiteSpace(taskStatus))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNameTaskStatus, taskStatus);
                }

                if (!string.IsNullOrWhiteSpace(taskCompleted.ToString()))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNameTaskCompleted, taskCompleted.ToString());
                }

                if (!string.IsNullOrWhiteSpace(stepName))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNameTaskStep, stepName);
                }

                if (!string.IsNullOrWhiteSpace(workflowName))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNameTaskWorkflow, workflowName);
                }

                if (!string.IsNullOrWhiteSpace(workflowStatus))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNameWorkflowStatus, workflowStatus);
                }

                if (!string.IsNullOrWhiteSpace(sourceType))
                {
                    apiRequest.Filters.Add(WorkflowConstants.ColumnNameSourceType, sourceType);
                }

                output = await _apiManager.PostAsync<WorkflowTasksList>(_settings.BaseUrl, _settings.TasksEndpoint, apiRequest, token);
            }

            return output;
        }

        /// <summary>
        /// Updates the task.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// True if success False otherwise
        /// </returns>
        public async Task<ServiceOutput<bool>> UpdateTask(UpdateTask request, string userToken)
        {
            (ServiceOutput<bool> output, string token) = await InitServiceOutputAndGetToken<bool>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                ServiceOutput<UpdateTaskOutput> response = await _apiManager.PutAsync<UpdateTaskOutput>(_settings.BaseUrl, _settings.UpdateTaskEndpoint, request, token);
                if (response.Code == HttpStatusCode.OK && response.Value != null)
                {
                    output = await HandleWorkflowCompletion(token, response.Value);
                }
                else
                {
                    output.CustomErrorCode(response.Code, $"{CommonMessages.RequestError}: {response.Description}");
                }
            }

            return output;
        }

        /// <summary>
        /// Updates the multiple task.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// True if success False otherwise
        /// </returns>
        public async Task<ServiceOutput<List<UpdateTaskOutput>>> UpdateTasks(List<UpdateTask> request, string userToken)
        {
            (ServiceOutput<List<UpdateTaskOutput>> output, string token) = await InitServiceOutputAndGetToken<List<UpdateTaskOutput>>(userToken);
            output.Value = new List<UpdateTaskOutput>();

            if (output.Code == HttpStatusCode.OK)
            {
                ServiceOutput<List<UpdateTaskOutput>> response = await _apiManager.PutAsync<List<UpdateTaskOutput>>(_settings.BaseUrl, _settings.UpdateTasksEndpoint, request, token);
                if (response.Code == HttpStatusCode.OK && response.Value != null)
                {
                    foreach (UpdateTaskOutput updateTask in response.Value)
                    {
                        if ((await HandleWorkflowCompletion(token, updateTask)).Value)
                        {
                            output.Value.Add(updateTask);
                        }
                        else
                        {
                            output.ExceptionMessages.Messages.Add($"Task {updateTask.TaskId} & Workflow {updateTask.WorkflowInstanceId}: Not completed.");
                        }
                    }
                }
                else
                {
                    output.CustomErrorCode(response.Code, $"{CommonMessages.RequestError}: {response.Description}");
                }
            }

            return output;
        }

        /// <summary>
        /// Reassigns the task.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// True if success False otherwise
        /// </returns>
        public async Task<ServiceOutput<bool>> ReassignTask(ReassignTask request, string userToken)
        {
            (ServiceOutput<bool> output, string token) = await InitServiceOutputAndGetToken<bool>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                output = await _apiManager.PostAsync<bool>(_settings.BaseUrl, _settings.ReassignTaskEndpoint, request, token);
            }

            return output;
        }

        #endregion Workflow Tasks

        #region Start/trigger Workflow

        /// <summary>
        /// Starts the workflow.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The Instance that was started
        /// </returns>
        public async Task<ServiceOutput<WorkflowInstance>> StartWorkflow(List<StartWorkflow> request, string userToken)
        {
            (ServiceOutput<WorkflowInstance> output, string token) = await InitServiceOutputAndGetToken<WorkflowInstance>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                output = await _apiManager.PostAsync<WorkflowInstance>(_settings.BaseUrl, _settings.StartWorkflowEndpoint, request, token);
            }

            return output;
        }

        /// <summary>
        /// Triggers the automatic workflow.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The Workflow Instance that was Triggered
        /// </returns>
        public async Task<ServiceOutput<WorkflowInstance>> TriggerAutomaticWorkflow(StartAutomaticWorkflow request, string userToken)
        {
            (ServiceOutput<WorkflowInstance> output, string token) = await InitServiceOutputAndGetToken<WorkflowInstance>(userToken);

            if (output.Code == HttpStatusCode.OK)
            {
                output = await _apiManager.PostAsync<WorkflowInstance>(_settings.BaseUrl, _settings.AutomaticTriggerWorkflowEndpoint, request, token);
            }

            return output;
        }

        #endregion Start/trigger Workflow

        #region Jobs

        /// <summary>
        /// Checks the overdue tasks.
        /// </summary>
        public async Task CheckOverdueTasks()
        {
            _logger.LogInformation("Checking overdue tasks...");
            if (_settings.AllowWorkflowEngine)
            {
                string apiToken = GetAPIToken();

                foreach (string sourceType in _settings.OverdueTasksSourceTypes)
                {
                    CheckOverdueTasks request = new CheckOverdueTasks()
                    {
                        EmailRequest = null,
                        SourceType = sourceType,
                    };

                    ServiceOutput<WorkflowTasksList> output = await _apiManager.PostAsync<WorkflowTasksList>(
                        _settings.BaseUrl,
                        _settings.OverdueTasksEndpoint,
                        request,
                        apiToken);

                    if (output.Code == HttpStatusCode.OK && output.Value != null)
                    {
                        if (output.Value.Tasks == null)
                        {
                            _logger.LogError("Tasks list is null");
                        }
                        else
                        {
                            _logger.LogInformation($"Found {output.Value.Tasks.Count} tasks  with the source Type {sourceType} to send reminder e-mails.");

                            if (output.Value.Tasks.Count > 0)
                            {
                                foreach (WorkflowTask task in output.Value.Tasks)
                                {
                                    _logger.LogInformation($"Sent a reminder email to: {task.UserId} for the task {task.StepName}.");
                                }
                            }
                        }
                    }
                    else
                    {
                        _logger.LogError($"Error {output.Code} - {output.Description}: {string.Join(", ", output.ExceptionMessages.Messages)}");
                    }
                }
            }
            else
            {
                _logger.LogError(WorkflowsMessages.WorkflowEngineDisabled);
            }

            _logger.LogInformation("Job Finished!");
        }

        /// <summary>
        /// Completes the background workflows.
        /// </summary>
        public async Task CompleteBackgroundWorkflows()
        {
            _logger.LogInformation("Completing background workflows...");
            if (_settings.AllowWorkflowEngine)
            {
                string apiToken = GetAPIToken();
                foreach (string sourceType in _settings.CompleteBackgroundTasksSourceTypes)
                {
                    // First get all instances required for status processing for this source type
                    ServiceOutput<IList<WorkflowInstance>> instances = await _apiManager.GetAsync<IList<WorkflowInstance>>(
                        _settings.BaseUrl,
                        _settings.InstancesRequiredForProcessingEndpoint,
                        apiToken,
                        new Dictionary<string, string> { { "sourceType", sourceType } }
                    );

                    if (instances.Value != null && instances.Value.Count > 0)
                    {
                        _logger.LogInformation($"Found {instances.Value.Count} workflow instances with this source type: {sourceType}.");
                        ServiceOutput<bool> result;

                        foreach (WorkflowInstance instance in instances.Value)
                        {
                            string userId = instance.Steps?.LastOrDefault()?.Tasks?.OrderByDescending(x => x.SysModifyDate).First()?.UserId;
                            if (string.IsNullOrWhiteSpace(userId))
                            {
                                _logger.LogError(string.Format("Could not find User Id for workflow {0} with instance Id {1}", instance.DisplayName, instance.Id.Value.ToString()));
                                break;
                            }

                            // Handle workflow completion, here on WFE client, based on workflow status
                            _logger.LogInformation($"Completing workflow {instance.DisplayName} on source with id {instance.SourceId}.");

                            result = await HandleWorkflowCompletion(
                                apiToken,
                                new UpdateTaskOutput()
                                {
                                    WorkflowPurpose = instance.Purpose,
                                    WorkflowStatus = instance.WorkflowStatus,
                                    WorkflowInstanceId = instance.WorkflowId
                                });

                            // Update workflow instance to set Required Processing = FALSE
                            if (result.Value)
                            {
                                UpdateInstance instanceRequest = new UpdateInstance()
                                {
                                    Id = instance.Id.Value,
                                    StateProcessingRequired = false,
                                    WorkflowStatus = instance.WorkflowStatus
                                };

                                ServiceOutput<HttpResponseMessage> serviceResponse = await _apiManager.PutAsync<HttpResponseMessage>(
                                    _settings.BaseUrl,
                                    _settings.UpdateInstanceEndpoint,
                                    instanceRequest,
                                    apiToken);

                                if (serviceResponse.Code != HttpStatusCode.OK)
                                {
                                    _logger.LogError(string.Format("Error updating workflow {0} with instance Id {1}: {2} - {3}", instance.DisplayName, instance.Id.Value.ToString(), serviceResponse.Code.ToString(), serviceResponse.ExceptionMessages.Messages[0]));
                                    break;
                                }
                                _logger.LogInformation($"Workflow {instance.Id.Value} - {instance.DisplayName} on source with id {instance.SourceId}, was completed!");
                            }
                            else
                            {
                                _logger.LogError(string.Format("Error completing workflow {0} with instance Id {1}: {2} - {3}", instance.DisplayName, instance.Id.Value.ToString(), result.Code.ToString(), result.Description));
                                break;
                            }
                        }
                    }
                    else
                    {
                        _logger.LogInformation($"Instances with the source type {sourceType} not found for workflow completion.");
                    }
                }

                _logger.LogInformation("Job Finished!");
            }
            else
            {
                _logger.LogWarning(WorkflowsMessages.WorkflowEngineDisabled);
            }
        }

        #endregion Jobs

        #region Handle workflow completion

        /// <summary>
        /// Handles the workflow completion.
        /// </summary>
        /// <param name="accessToken">The access token.</param>
        /// <param name="taskOutcome">The task outcome.</param>
        /// <returns>
        /// True if operation succeeded, False otherwise.
        /// </returns>
        private async Task<ServiceOutput<bool>> HandleWorkflowCompletion(string accessToken, UpdateTaskOutput taskOutcome)
        {
            ServiceOutput<bool> output = new ServiceOutput<bool>();

            switch (taskOutcome.WorkflowPurpose)
            {
                case WorkflowConstants.PURPOSE_APPROVAL:
                    output = TerminateApprovalWorkflow(taskOutcome.WorkflowStatus);
                    break;

                case WorkflowConstants.PURPOSE_COLLECT_FEEDBACK:
                    output = await TerminateCollectFeedbackWorkflow(taskOutcome.WorkflowInstanceId.ToString(), accessToken, taskOutcome.WorkflowStatus);
                    break;

                case WorkflowConstants.PURPOSE_INTEGRATION:
                    output = TerminateIntegrationWorkflow();
                    break;

                default:
                    output.Value = true;
                    break;
            }

            return output;
        }

        /// <summary>
        /// Exemplifies how to treat a conclusion of a Approval workflow and should be implemented according to the needs
        /// </summary>
        /// <param name="workflowStatus">The workflow status.</param>
        /// <returns>
        /// True if operation succeeded, False otherwise.
        /// </returns>
        private static ServiceOutput<bool> TerminateApprovalWorkflow(string workflowStatus)
        {
            ServiceOutput<bool> output = new ServiceOutput<bool>();

            switch (workflowStatus)
            {
                case WorkflowConstants.WORKFLOW_STATUS_APPROVED:
                    output.Description = WorkflowConstants.WORKFLOW_APPROVED;
                    output.Value = true;
                    break;

                case WorkflowConstants.WORKFLOW_STATUS_REJECTED:
                    output.Description = WorkflowConstants.WORKFLOW_REJECTED;
                    output.Value = true;
                    break;

                case WorkflowConstants.WORKFLOW_STATUS_IN_PROGRESS:
                    output.Description = WorkflowConstants.WORKFLOW_IN_PROGRESS;
                    output.Value = true;
                    break;

                case WorkflowConstants.WORKFLOW_STATUS_CANCELLED:
                    output.Description = WorkflowConstants.WORKFLOW_CANCELLED;
                    output.Value = true;
                    break;

                default:
                    output.Value = true;
                    break;
            }

            return output;
        }

        /// <summary>
        /// Exemplifies how to treat a conclusion of a Integration workflow and should be implemented according to the needs
        /// </summary>
        /// <returns>
        /// True
        /// </returns>
        private static ServiceOutput<bool> TerminateIntegrationWorkflow()
        {
            return new ServiceOutput<bool>
            {
                Value = true
            };
        }

        /// <summary>
        /// Exemplifies how to treat a conclusion of a Collect workflow and should be implemented according to the needs
        /// </summary>
        /// <param name="instanceId">The instance identifier.</param>
        /// <param name="accessToken">The access token.</param>
        /// <param name="workflowStatus">The workflow status.</param>
        /// <returns>
        /// True if success, false otherwise
        /// </returns>
        private async Task<ServiceOutput<bool>> TerminateCollectFeedbackWorkflow(string instanceId, string accessToken, string workflowStatus)
        {
            ServiceOutput<bool> output = new ServiceOutput<bool>();
            if (workflowStatus == WorkflowConstants.WORKFLOW_STATUS_APPROVED || workflowStatus == WorkflowConstants.WORKFLOW_STATUS_REJECTED)
            {
                Dictionary<string, string> queryParameters = new Dictionary<string, string>()
                {
                    { "instanceId", instanceId }
                };

                ServiceOutput<WorkflowInstance> response = await _apiManager.GetAsync<WorkflowInstance>(_settings.BaseUrl, _settings.InstanceByIDEndpoint, accessToken, queryParameters);

                if (response.Code != HttpStatusCode.OK)
                {
                    response.CustomErrorCode(response.Code, $"{CommonMessages.RequestError}: {response.Description}");
                }
                else if (response.Value != null && response.Value.Steps != null)
                {
                    List<CollectFeedbackEmailContent> feedbackEmailContent = new List<CollectFeedbackEmailContent>();

                    foreach (WorkflowInstanceStep step in response.Value.Steps)
                    {
                        feedbackEmailContent.AddRange(step.Tasks.Select(task => new CollectFeedbackEmailContent()
                        {
                            Message = task.Comments,
                            Date = task.SysModifyDate.ToString("MMMM dd, yyyy, H:mm:ss"),
                            User = task.UserId
                        }));
                    }

                    if (feedbackEmailContent.Count > 0)
                    {
                        output = await _emailService.SendTemplatedEmail(new EmailMessage<object>()
                        {
                            TemplateData = new CollectFeedbackTemplateData()
                            {
                                EmailContent = feedbackEmailContent,
                                Subject = WorkflowConstants.WorkflowSubject
                            },
                            Recipients = new List<string> { response.Value.SysCreateUserId }
                        },
                        _settings.CollectFeedbackTemplateId);
                    }
                    else
                    {
                        output.NotFound(WorkflowsMessages.EmailWithoutContent);
                    }
                }
            }
            else
            {
                output.Description = workflowStatus == WorkflowConstants.WORKFLOW_IN_PROGRESS ? WorkflowConstants.WORKFLOW_IN_PROGRESS : WorkflowConstants.WORKFLOW_CANCELLED;
            }
            return output;
        }

        #endregion Handle workflow completion

        #region Tokens

        /// <summary>
        /// Initializes the ServiceOutput and gets the WFE access token.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="userToken">The user token.</param>
        /// <returns>
        /// The service output and the access token.
        /// </returns>
        private async Task<(ServiceOutput<T>, string)> InitServiceOutputAndGetToken<T>(string userToken)
        {
            ServiceOutput<T> output = new ServiceOutput<T>();

            ServiceOutput<string> token = await _apiManager.GetAsync<string>(_settings.BaseUrl, _settings.TokenEndpoint, userToken);

            if (token.Code != HttpStatusCode.OK)
            {
                output.CustomErrorCode(token.Code, $"{CommonMessages.ErrorGettingToken}: {token.Description}");
            }

            return (output, token.Value);
        }

        /// <summary>
        /// Gets the token for API calls.
        /// </summary>
        /// <returns>
        /// The access token.
        /// </returns>
        private string GetAPIToken()
        {
            JwtSecurityTokenHandler tokenHandler = new JwtSecurityTokenHandler();

            byte[] key = Encoding.ASCII.GetBytes(_settings.Secret);
            SecurityTokenDescriptor tokenDescriptor = new SecurityTokenDescriptor
            {
                Issuer = _settings.BaseUrl,
                Audience = _settings.BaseUrl,
                Subject = new ClaimsIdentity(new[] { new Claim(ClaimTypes.Name, WorkflowConstants.WORKFLOW_API_USER) }),
                Expires = DateTime.UtcNow.AddMinutes(60),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha512),
                Claims = DictionaryHelper.ToDictionary(new CustomClaims()
                {
                    UserType = WorkflowConstants.WORKFLOW_API_USER_TYPE_API,
                    Permissions = new List<string>() { WorkflowPermissions.WORKFLOWS.ToString() }
                })
            };

            SecurityToken token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        #endregion Tokens
    }
}