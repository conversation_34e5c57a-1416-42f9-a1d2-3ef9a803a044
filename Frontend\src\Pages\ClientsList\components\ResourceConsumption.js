import React from 'react';
import { Box, Grid, Typography, CircularProgress, Card, CardContent, FormControl, InputLabel, Select, MenuItem, List, ListItem, TextField } from '@mui/material';
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import ptBR from "date-fns/locale/pt-BR";
import { useTranslation } from 'react-i18next';

const ResourceConsumption = ({
  resourceGroups,
  selectedResourceGroup,
  handleResourceGroupChange,
  dataInicio,
  setDataInicio,
  dataFim,
  setDataFim,
  consumoLoading,
  consumoSummary,
  formatCurrency
}) => {
  const { t } = useTranslation();

  return (
    <Box sx={{ mt: 2 }}>
      {resourceGroups.length > 0 ? (
        <>
          <Grid container spacing={2} sx={{ mb: 4 }}>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel id="resource-group-label">Resource Group</InputLabel>
                <Select
                  labelId="resource-group-label"
                  id="resource-group-select"
                  value={selectedResourceGroup}
                  label="Resource Group"
                  onChange={handleResourceGroupChange}
                >
                  {resourceGroups.map((group) => (
                    <MenuItem key={group} value={group}>
                      {group}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
                <DatePicker
                  label={t('common:tableActions.initialdate')}
                  value={dataInicio}
                  onChange={(newValue) => setDataInicio(newValue)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} md={4}>
              <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
                <DatePicker
                  label={t('common:tableActions.finaldate')}
                  value={dataFim}
                  onChange={(newValue) => setDataFim(newValue)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </LocalizationProvider>
            </Grid>
          </Grid>

          {consumoLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            consumoSummary ? (
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Card
                    variant="outlined"
                    sx={{
                      borderColor: '#20c997',
                      borderWidth: 1,
                      borderRadius: "10px",
                      transition: "all 0.2s ease",
                      '&:hover': {
                        boxShadow: '0 4px 8px rgba(32, 201, 151, 0.2)'
                      }
                    }}
                  >
                    <CardContent sx={{ p: 2.5 }}>
                      <Typography variant="subtitle1" color="#087f5b" fontWeight={500} gutterBottom>
                        Country List Total
                      </Typography>
                      <Typography variant="h5" color="#0ca678" fontWeight={600}>
                        {formatCurrency(consumoSummary.countryListTotal, consumoSummary.moeda)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Card
                    variant="outlined"
                    sx={{
                      borderColor: '#fa5252',
                      borderWidth: 1,
                      borderRadius: "10px",
                      transition: "all 0.2s ease",
                      '&:hover': {
                        boxShadow: '0 4px 8px rgba(250, 82, 82, 0.2)'
                      }
                    }}
                  >
                    <CardContent sx={{ p: 2.5 }}>
                      <Typography variant="subtitle1" color="#e03131" fontWeight={500} gutterBottom>
                        Country Reseller Total
                      </Typography>
                      <Typography variant="h5" color="#c92a2a" fontWeight={600}>
                        {formatCurrency(consumoSummary.countryResellerTotal, consumoSummary.moeda)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Card
                    variant="outlined"
                    sx={{
                      borderColor: '#339af0',
                      borderWidth: 1,
                      borderRadius: "10px",
                      transition: "all 0.2s ease",
                      '&:hover': {
                        boxShadow: '0 4px 8px rgba(51, 154, 240, 0.2)'
                      }
                    }}
                  >
                    <CardContent sx={{ p: 2.5 }}>
                      <Typography variant="subtitle1" color="#1971c2" fontWeight={500} gutterBottom>
                        Country Customer Total
                      </Typography>
                      <Typography variant="h5" color="#1864ab" fontWeight={600}>
                        {formatCurrency(consumoSummary.countryCustomerTotal, consumoSummary.moeda)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                {selectedResourceGroup === "Geral" && consumoSummary.regioesResumo && (
                  <Grid item xs={12}>
                    <Card variant="outlined" sx={{ mt: 2 }}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Resumo por Resource Group
                        </Typography>
                        <List>
                          {Object.entries(consumoSummary.regioesResumo).map(([regiao, valores]) => (
                            <ListItem key={regiao} divider>
                              <Grid container spacing={2}>
                                <Grid item xs={12}>
                                  <Typography variant="subtitle1" fontWeight="bold">
                                    {regiao} ({valores.servicosCount} serviços)
                                  </Typography>
                                </Grid>
                                <Grid item xs={12} sm={3}>
                                  <Typography variant="body2" color="text.secondary">
                                    Country List:
                                  </Typography>
                                  <Typography variant="body1" color="primary.dark">
                                    {formatCurrency(valores.countryListTotal, consumoSummary.moeda)}
                                  </Typography>
                                </Grid>
                                <Grid item xs={12} sm={3}>
                                  <Typography variant="body2" color="text.secondary">
                                    Country Reseller:
                                  </Typography>
                                  <Typography variant="body1" color="error.dark">
                                    {formatCurrency(valores.countryResellerTotal, consumoSummary.moeda)}
                                  </Typography>
                                </Grid>
                                <Grid item xs={12} sm={3}>
                                  <Typography variant="body2" color="text.secondary">
                                    Country Customer:
                                  </Typography>
                                  <Typography variant="body1" color="info.dark">
                                    {formatCurrency(valores.countryCustomerTotal, consumoSummary.moeda)}
                                  </Typography>
                                </Grid>
                              </Grid>
                            </ListItem>
                          ))}
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Distribuição por Tipo de Serviço
                      </Typography>
                      <List>
                        {Object.entries(consumoSummary.servicosPorTipo).map(([tipo, valores]) => (
                          <ListItem key={tipo} divider>
                            <Grid container spacing={2}>
                              <Grid item xs={12}>
                                <Typography variant="subtitle1" fontWeight="bold">
                                  {tipo}
                                </Typography>
                              </Grid>
                              <Grid item xs={12} sm={3}>
                                <Typography variant="body2" color="text.secondary">
                                  Country List:
                                </Typography>
                                <Typography variant="body1" color="primary.dark">
                                  {formatCurrency(valores.countryListTotal, consumoSummary.moeda)}
                                </Typography>
                              </Grid>
                              <Grid item xs={12} sm={3}>
                                <Typography variant="body2" color="text.secondary">
                                  Country Reseller:
                                </Typography>
                                <Typography variant="body1" color="error.dark">
                                  {formatCurrency(valores.countryResellerTotal, consumoSummary.moeda)}
                                </Typography>
                              </Grid>
                              <Grid item xs={12} sm={3}>
                                <Typography variant="body2" color="text.secondary">
                                  Country Customer:
                                </Typography>
                                <Typography variant="body1" color="info.dark">
                                  {formatCurrency(valores.countryCustomerTotal, consumoSummary.moeda)}
                                </Typography>
                              </Grid>
                              <Grid item xs={12} sm={3}>
                                <Typography variant="body2" color="text.secondary">
                                  Total:
                                </Typography>
                                <Typography variant="body1" fontWeight="bold">
                                  {formatCurrency(valores.total, consumoSummary.moeda)}
                                </Typography>
                              </Grid>
                            </Grid>
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            ) : (
              <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', my: 4 }}>
                Nenhum dado de consumo disponível para este resource group.
              </Typography>
            )
          )}
        </>
      ) : (
        <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', my: 4 }}>
          Nenhum resource group encontrado para este cliente.
        </Typography>
      )}
    </Box>
  );
};

export default ResourceConsumption; 