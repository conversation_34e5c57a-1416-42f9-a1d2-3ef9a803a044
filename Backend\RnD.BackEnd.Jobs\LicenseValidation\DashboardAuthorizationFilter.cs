﻿using System;
using System.Collections;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Hangfire.Annotations;
using Hangfire.Dashboard;
using Microsoft.Extensions.Caching.Memory;
using RnD.BackEnd.Licensing.Interfaces;

namespace RnD.BackEnd.Jobs.LicenseValidation
{
    /// <summary>
    /// Dashboard authorization filter class
    /// </summary>
    /// <seealso cref="Hangfire.Dashboard.IDashboardAsyncAuthorizationFilter" />
    public class DashboardAuthorizationFilter : IDashboardAsyncAuthorizationFilter
    {
        private readonly ILicenseValidationService _validationService;
        private IMemoryCache _cache;

        /// <summary>
        /// Initializes a new instance of the <see cref="DashboardAuthorizationFilter" /> class.
        /// </summary>
        /// <param name="validationService">The validation service.</param>
        /// <param name="cache">The cache.</param>
        public DashboardAuthorizationFilter(ILicenseValidationService validationService, IMemoryCache cache)
        {
            _validationService = validationService;
            _cache = cache;
        }

        /// <summary>
        /// Validates the license when authorizing
        /// </summary>
        /// <param name="context">The context.</param>
        /// <returns>
        /// True if license is valid, False otherwise.
        /// </returns>
        public async Task<bool> AuthorizeAsync([NotNull] DashboardContext context)
        {
            bool isAuthorized = false;

            ClaimsPrincipal user = context.GetHttpContext().User;

            if (user != null && user.Identity != null && user.Identity.IsAuthenticated)
            {
                string username = user.Claims.FirstOrDefault(x => x.Type == "preferred_username")?.Value;

                bool isAdmin = await GetFromCacheAsync(username, async () =>
                {
                    // Implement here logic to check the user
                    // Role or profile: user should be ADMIN in the system to be able to access Hangfire Dashboard
                    // Change returned value according to implemented logic
                    return true;
                }, TimeSpan.FromHours(1));

                isAuthorized = isAdmin && await _validationService.ValidateLicenseAsync(context.GetHttpContext().Request.Host.Value);
            }

            return isAuthorized;
        }

        /// <summary>
        /// Gets from cache asynchronous.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key">The key.</param>
        /// <param name="acquire">The acquire.</param>
        /// <param name="expirationTime">The expiration time.</param>
        /// <returns>
        /// The cached item
        /// </returns>
        private async Task<T> GetFromCacheAsync<T>(string key, Func<Task<T>> acquire, TimeSpan expirationTime)
        {
            T cachedObject = _cache.Get<T>(key);

            if (cachedObject == null || cachedObject.Equals(default(T)))
            {
                cachedObject = await acquire();
                if (cachedObject != null || (cachedObject != null && cachedObject is ICollection list && list.Count > 0))
                {
                    _cache.Set<T>(key, cachedObject, expirationTime);
                }
            }
            return cachedObject;
        }
    }
}
