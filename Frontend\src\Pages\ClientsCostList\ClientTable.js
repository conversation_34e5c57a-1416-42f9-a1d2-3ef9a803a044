import { useState, useEffect } from "react";
import { Link as RouterLink } from "react-router-dom";
import PropTypes from "prop-types";
import {
  Avatar,
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TablePagination,
  TableRow,
  TextField,
  Link,
  Grid,
} from "@mui/material";
import { Search as SearchIcon } from "@mui/icons-material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import ptBR from "date-fns/locale/pt-BR";
import { startOfMonth, endOfMonth } from "date-fns";
import Scrollbar from "../../Components/Scrollbar";
import TableHeader from "../../Components/TableUtils/TableHeader";
import TableLoading from "../../Components/TableUtils/TableLoading";
import {
  applyPagination,
  applySort,
} from "../../Components/TableUtils/TableUtils";
import { useTranslation } from "react-i18next";
import { getConsumoSummaryByResourceGroup } from "../../Services/consumoService";
import { getConsumoPDFByClienteId } from "../../Services/consumoPDFService";
import {
  getConsumoSummaryByCliente,
  getConsumoResellerByCliente,
} from "../../Services/azureConsumptionService";

const ClientTable = ({ clientList, isLoading }) => {
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const [query, setQuery] = useState("");
  const [orderBy, setOrderBy] = useState("name");
  const [order, setOrder] = useState("asc");
  const { t } = useTranslation();
  const [clientData, setClientData] = useState({});
  const [dataInicio, setDataInicio] = useState(startOfMonth(new Date()));
  const [dataFim, setDataFim] = useState(endOfMonth(new Date()));

  // Função para carregar todos os dados para um cliente
  const loadClientData = async (clientId) => {
    try {
      // Buscar dados de reseller, PDF e consumo reseller em paralelo
      const [resellerData, pdfData, resellerConsumptionData] =
        await Promise.all([
          getConsumoSummaryByResourceGroup(
            clientId,
            "Geral",
            dataInicio,
            dataFim
          ).catch((error) => {
            console.error("Erro ao buscar dados do reseller:", error);
            return { countryCustomerTotal: 0, moeda: "EUR" };
          }),
          getConsumoPDFByClienteId(clientId).catch((error) => {
            console.error("Erro ao buscar dados de PDF:", error);
            return [];
          }),
          getConsumoResellerByCliente(clientId, dataInicio, dataFim).catch(
            (error) => {
              console.error("Erro ao buscar dados de consumo reseller:", error);
              return {
                countryCustomerTotal: 0,
                countryResellerTotal: 0,
                countryListTotal: 0,
              };
            }
          ),
        ]);

      // Buscar dados do Azure em uma chamada separada
      let azureData = null;
      try {
        azureData = await getConsumoSummaryByCliente(
          clientId,
          dataInicio,
          dataFim
        );
        console.log(
          `Dados do Azure para cliente ${clientId}:`,
          JSON.stringify(azureData)
        );

        // Verificar os valores
        if (azureData) {
          console.log(
            `TotalCost: ${azureData.TotalCost}, Currency: ${azureData.Currency}`
          );
        }
      } catch (azureError) {
        console.error("Erro ao obter dados do Azure:", azureError);
      }

      // Calcular o total da fatura do PDF, filtrando pelo período selecionado
      let totalFatura = 0;
      if (Array.isArray(pdfData) && pdfData.length > 0) {
        // Filtrar PDFs pelo período selecionado
        const filteredPDFs = pdfData.filter((item) => {
          const dataDoc = new Date(
            item.dataFatura ||
              item.dataInicio ||
              item.dataFim ||
              item.data ||
              item.DataFatura
          );
          let passes = true;
          if (dataInicio && dataDoc < new Date(dataInicio)) passes = false;
          if (dataFim && dataDoc > new Date(dataFim)) passes = false;
          return passes;
        });
        totalFatura = filteredPDFs.reduce((sum, item) => {
          const valor = item.valorTotal || item.totalFatura || item.valor || 0;
          return sum + (parseFloat(valor) || 0);
        }, 0);
      }

      // Priorizar dados do consumo reseller da API específica
      const countryCustomerTotal =
        resellerConsumptionData?.countryCustomerTotal ??
        resellerData?.countryCustomerTotal ??
        0;

      setClientData((prev) => ({
        ...prev,
        [clientId]: {
          resellerData,
          azureData,
          totalFatura,
          resellerConsumptionData: {
            countryCustomerTotal,
          },
        },
      }));
    } catch (error) {
      console.error(`Erro ao carregar dados para cliente ${clientId}:`, error);
      // Manter os dados anteriores se houver erro
      setClientData((prev) => ({
        ...prev,
        [clientId]: prev[clientId] || {
          resellerData: null,
          azureData: null,
          totalFatura: 0,
          resellerConsumptionData: { countryCustomerTotal: 0 },
        },
      }));
    }
  };

  // Efeito para carregar os dados quando a lista de clientes ou datas mudam
  useEffect(() => {
    if (clientList && clientList.length > 0) {
      clientList.forEach((client) => {
        loadClientData(client.id);
      });
    }
  }, [clientList, dataInicio, dataFim]);

  const formatCurrency = (value, currency = "EUR") => {
    if (value === null || value === undefined) return "N/A";
    return new Intl.NumberFormat("pt-PT", {
      style: "currency",
      currency: currency,
    }).format(value);
  };

  const headerCells = [
    {
      id: "name",
      label: t("common:tableHeaders.name"),
      sort: true,
    },
    {
      id: "consumoAzure",
      label: t("common:tableHeaders.consumoAzure") || "Consumo Azure",
      sort: true,
    },
    {
      id: "consumoReseller",
      label: t("common:tableHeaders.consumoReseller") || "Consumo Reseller",
      sort: true,
    },
    {
      id: "faturacaoReseller",
      label: t("common:tableHeaders.faturacaoReseller") || "Faturação Reseller",
      sort: true,
    },
  ];

  const handleQueryChange = (event) => {
    setQuery(event.target.value);
    setPage(0);
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleLimitChange = (event) => {
    setLimit(parseInt(event.target.value, 10));
  };

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  // Função para filtrar clientes
  const filterClients = (clients, searchQuery) => {
    if (!searchQuery) return clients;

    const searchTerm = searchQuery.toLowerCase();
    return clients.filter((client) =>
      client.name.toLowerCase().includes(searchTerm)
    );
  };

  const filteredClientList = filterClients(clientList, query);
  const sortedClientList = applySort(filteredClientList, orderBy, order);
  const paginatedClientList = applyPagination(sortedClientList, page, limit);

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Card>
        <Box
          sx={{ alignItems: "center", display: "flex", m: 2, height: "88px" }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                sx={{ width: "100%", height: "56px" }}
                InputProps={{
                  startAdornment: <SearchIcon fontSize="small" />,
                }}
                onChange={handleQueryChange}
                placeholder={t("common:search.searchClient")}
                value={query}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <LocalizationProvider
                dateAdapter={AdapterDateFns}
                adapterLocale={ptBR}
              >
                <DatePicker
                  label={t("common:tableActions.initialdate")}
                  value={dataInicio}
                  onChange={(newValue) => setDataInicio(newValue)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} md={4}>
              <LocalizationProvider
                dateAdapter={AdapterDateFns}
                adapterLocale={ptBR}
              >
                <DatePicker
                  label={t("common:tableActions.finaldate")}
                  value={dataFim}
                  onChange={(newValue) => setDataFim(newValue)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </LocalizationProvider>
            </Grid>
          </Grid>
        </Box>
        <Scrollbar>
          <Box sx={{ p: 2, pt: 0, minWidth: 700 }}>
            <Table stickyHeader>
              <TableHeader
                hasCheckbox={false}
                headCells={headerCells}
                onSortClick={handleRequestSort}
                orderBy={orderBy}
                order={order}
              />
              <TableBody>
                {isLoading ? (
                  <TableLoading
                    isLoading={isLoading}
                    headCells={headerCells}
                    numRows={limit}
                  />
                ) : (
                  paginatedClientList.map((client) => {
                    const data = clientData[client.id] || {};
                    return (
                      <TableRow hover key={client.id} sx={{ height: "77px" }}>
                        <TableCell>
                          <Box sx={{ display: "flex" }}>
                            <Avatar
                              mx={2}
                              key={`avatar-${client.id}}`}
                              alt={client.name}
                            />
                            <Link
                              color="inherit"
                              component={RouterLink}
                              to={`/ClientsList/${client.id}`}
                              sx={{
                                color: "primary.main",
                                textDecoration: "none",
                                fontStyle: "normal",
                                fontWeight: 700,
                                fontSize: "14px",
                                lineHeight: "20px",
                                alignSelf: "center",
                                marginLeft: 1,
                              }}
                            >
                              {client.name}
                            </Link>
                          </Box>
                        </TableCell>
                        <TableCell>
                          {data.azureData &&
                          typeof data.azureData === "object" ? (
                            data.azureData.TotalCost ? (
                              formatCurrency(
                                data.azureData.TotalCost,
                                data.azureData.Currency || "EUR"
                              )
                            ) : data.azureData.totalCost ? (
                              formatCurrency(
                                data.azureData.totalCost,
                                data.azureData.currency || "EUR"
                              )
                            ) : (
                              <Box sx={{ color: "text.secondary" }}>
                                {t("common:tableActions.noValues")}
                              </Box>
                            )
                          ) : (
                            <Box sx={{ color: "text.secondary" }}>
                              {t("common:tableActions.noValues")}
                            </Box>
                          )}
                        </TableCell>
                        <TableCell>
                          {data.resellerConsumptionData &&
                          data.resellerConsumptionData.countryCustomerTotal >
                            0 ? (
                            formatCurrency(
                              data.resellerConsumptionData.countryCustomerTotal,
                              "EUR"
                            )
                          ) : data.resellerData &&
                            data.resellerData.countryCustomerTotal > 0 ? (
                            formatCurrency(
                              data.resellerData.countryCustomerTotal,
                              data.resellerData.moeda
                            )
                          ) : (
                            <Box sx={{ color: "text.secondary" }}>
                              {t("common:tableActions.noValues")}
                            </Box>
                          )}
                        </TableCell>
                        <TableCell>
                          {data.totalFatura > 0 ? (
                            formatCurrency(data.totalFatura, "EUR")
                          ) : (
                            <Box sx={{ color: "text.secondary" }}>
                              {t("common:tableActions.noValues")}
                            </Box>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </Box>
        </Scrollbar>
        <TablePagination
          component="div"
          count={filteredClientList.length}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleLimitChange}
          page={page}
          rowsPerPage={limit}
          rowsPerPageOptions={[5, 10, 25]}
          showFirstButton
          showLastButton
          labelRowsPerPage={t("common:tableActions.rowsPerPage")}
        />
      </Card>
    </Box>
  );
};

ClientTable.propTypes = {
  clientList: PropTypes.array.isRequired,
  isLoading: PropTypes.bool,
};

export default ClientTable;
