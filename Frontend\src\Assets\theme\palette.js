import { shiftColor, changeOpacity } from '../../Utils/Theme/colorUtils';
import themeConfig from './themeConfig';

//Theme colors
const themeColors = {
  type: "light",
  contrastThreshold: 3,
  tonalOffset: 0.2,
  details: {
    main: themeConfig.details
  },
  common: {
    100: themeConfig.commonWhite,
    250: themeConfig.commonGray,
    500: themeConfig.textSecondary,
    1000: themeConfig.paperWhite,
    black: themeConfig.commonBlack,
    white: themeConfig.commonWhite,
    paper: themeConfig.paperWhite,
    mainBackGround: themeConfig.commonWhite
  },
  primary: {
    contrastText: themeConfig.commonWhite,
    main: themeConfig.themeColor1,
    transparent: {
      15: changeOpacity(themeConfig.themeColor1, 15),
      20: changeOpacity(themeConfig.themeColor1, 20),
      30: changeOpacity(themeConfig.themeColor1, 30),
      45: changeOpacity(themeConfig.themeColor1, 45),
      60: changeOpacity(themeConfig.themeColor1, 60),
    },
    grad1: themeConfig.themeColor1,
    grad2: themeConfig.themeColor2,
    light: shiftColor(themeConfig.themeColor1, 1, 1.3),
    dark: shiftColor(themeConfig.themeColor1, 1, 0.65),
    mainGradient: `linear-gradient(200.98deg, ${themeConfig.themeColor1} 20%, ${themeConfig.themeColor3} 105%)`,
    lightGradient: `linear-gradient(200.98deg, ${themeConfig.themeColor2} 20%, ${themeConfig.themeColor3} 105%)`,
    grayGradient: `linear-gradient(200.98deg, ${themeConfig.commonGray} 20%, ${themeConfig.commonWhite} 200%)`
  },
  secondary: {
    contrastText: themeConfig.paperWhite,
    main: themeConfig.commonWhite
  },
  error: {
    contrastText: themeConfig.commonWhite,
    main: "#c64244",
    light: "rgb(209, 103, 105)",
    dark: "rgb(138, 46, 47)"
  },
  warning: {
    light: "#ffb74d",
    main: "#ff9800",
    dark: "#f57c00",
    contrastText: themeConfig.commonWhite,
  },
  info: {
    light: "#64b5f6",
    main: "#2196f3",
    dark: "#1976d2",
    contrastText: themeConfig.commonWhite
  },
  success: {
    light: "#81c784",
    main: "#4caf50",
    dark: "#388e3c",
    contrastText: themeConfig.commonWhite,
  },
  text: {
    primary: themeConfig.textPrimary,
    primaryGradient: `linear-gradient(200.98deg, ${themeConfig.themeColor1} -16.92%, ${themeConfig.themeColor3} 208.13%)`,
    secondary: themeConfig.textSecondary,
    disabled: "rgba(0, 0, 0, 0.38)",
    hint: "rgba(0, 0, 0, 0.38)",
    link: "#00bcd4",
    themeBlue: "#0b3b66"
  },

  hover: "#E8F5F4",
  navBar: "#EEF1F4",
  divider: "#BECCD7",
  background: {
    paper: themeConfig.commonWhite,
    default: themeConfig.paperWhite
  },
  gradient: {
    primary: {
      '1': themeConfig.themeColor1,
      '05': themeConfig.themeColor2,
      '00': themeConfig.themeColor3
    },
    secondary: {
      '1': themeConfig.themeColor1,
      '05': themeConfig.themeColor2,
      '00': themeConfig.themeColor3
    }
  },
  states: {
    ...themeConfig.states
  },
};
export default themeColors;
