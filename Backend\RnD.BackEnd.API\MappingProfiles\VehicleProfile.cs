﻿using System;
using AutoMapper;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using RnD.BackEnd.API.Model.Vehicle;
using RnD.BackEnd.Domain.Entities.Vehicle;

namespace RnD.BackEnd.API.MappingProfiles
{
    /// <summary>
    /// Vehicle mapping profile
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class VehicleProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="VehicleProfile"/> class.
        /// </summary>
        public VehicleProfile()
        {
            CreateMap<Vehicle, VehicleDto>()
                .ReverseMap();

            CreateMap<CreateVehicleDto, Vehicle>()
                .ForMember(dst => dst.Id, map => map.MapFrom(src => Guid.NewGuid().ToString()));

            //Both are necessary for JsonPatch Service/Application isolation
            CreateMap<JsonPatchDocument<VehicleDto>, JsonPatchDocument<Vehicle>>();
            CreateMap<Operation<VehicleDto>, Operation<Vehicle>>();

        }
    }
}
