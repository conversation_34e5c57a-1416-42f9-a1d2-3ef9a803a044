﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Vehicle;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.VehicleController
{
    /// <summary>
    /// The example of a response 201 for a list of created vehicles.
    /// </summary>
    public class VehicleList201 : IExamplesProvider<ApiOutput<List<VehicleDto>>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a response 201 for a list of created vehicles
        /// </returns>
        public ApiOutput<List<VehicleDto>> GetExamples()
        {
            return new ApiOutput<List<VehicleDto>>
            {
                Code = HttpStatusCode.Created,
                Value = new List<VehicleDto>
                {
                    new VehicleDto
                    {
                        Id = Guid.NewGuid().ToString(),
                        Brand = "Honda",
                        BrandId = "0004",
                        FuelType = "PETROL",
                        ModelName = "Civic",
                        Version = "123",
                        Year = 2022
                    },
                    new VehicleDto
                    {
                        Id = Guid.NewGuid().ToString(),
                        Brand = "Honda",
                        BrandId = "0004",
                        FuelType = "PETROL",
                        ModelName = "Accord",
                        Version = "322",
                        Year = 1998
                    }
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}
