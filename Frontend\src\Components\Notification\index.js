import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Avatar,
  Badge,
  Box,
  Button,
  Divider,
  IconButton,
  Link,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Popover,
  Tooltip,
  Typography
} from '@mui/material';
import grey from '@mui/material/colors/grey';
import {
  AccountTree as AccountTreeIcon,
  Star as StarIcon,
  Notifications as NotificationsIcon,
  Email as EmailIcon,
  Drafts as DraftsIcon,
} from '@mui/icons-material';
import { notificationsAPI } from '../../API/notificationsAPI';
import toast from 'react-hot-toast';
import moment from 'moment';
import 'moment/locale/pt';

const Notification = () => {
  const anchorRef = useRef(null);
  const [open, setOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [newNotifications, setNewNotifications] = useState(0);

  const getNotifications = useCallback(async () => {
    try {
      const result = await notificationsAPI.getNotifications({ limit: 5 });
      if (!result.error) {
        setNotifications(result.value);
        setNewNotifications(result.value.length);
      } else {
        toast.error(result.exceptionMessages.messages[0].description);
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  const handleMarkAllAsRead = async () => {
    try {
      const notificationIDs = notifications
        .filter(notification => !notification.isRead)
        .map(notification => notification.notificationID);
      const result = await notificationsAPI.saveNotificationRead({ notificationIDs: notificationIDs });
      if (!result.error) {
        getNotifications();
        toast.success('All notifications marked as read');
      } else {
        toast.error(result.exceptionMessages.messages[0].description);
      }
    } catch (err) {
      console.error(err);
    }
  };

  useEffect(() => {
    getNotifications();
  }, []);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const renderNotificationType = (notificationType) => {
    switch (notificationType) {
      case 'Recrutamento':
        return <Avatar
          sx={{
            backgroundColor: 'primary.main',
            color: 'primary.contrastText'
          }}
        >
          <AccountTreeIcon />
        </Avatar>
      case 'Avaliação':
        return <Avatar
          sx={{
            backgroundColor: 'primary.main',
            color: 'primary.contrastText'
          }}
        >
          <StarIcon />
        </Avatar>
      default:
        return <Avatar
          sx={{
            backgroundColor: 'primary.main',
            color: 'primary.contrastText'
          }}
        />
    }
  };

  return (
    <>
      <Tooltip title="Notifications">
        <IconButton
          color="inherit"
          ref={anchorRef}
          onClick={handleOpen}
        >
          <Badge
            color="error"
            badgeContent={newNotifications}
          >
            <NotificationsIcon
              sx={{
                color: 'primary.main'
              }}
              fontSize="small"
            />
          </Badge>
        </IconButton>
      </Tooltip>
      <Popover
        anchorEl={anchorRef.current}
        anchorOrigin={{
          horizontal: 'center',
          vertical: 'bottom'
        }}
        onClose={handleClose}
        open={open}
        PaperProps={{
          sx: { width: 380 }
        }}
      >
        <Box
          sx={{
            p: 2,
            display: "flex",
            backgroundColor: "primary.main",
            justifyContent: "space-between",
            alignItems: "center"
          }}
        >
          <Tooltip title="Ver todas as notificações">
            <Button
              component={RouterLink}
              to="/dashboard/profile/myNotifications"
            >
              <Typography
                color="white"
                variant="h1"
                sx={{
                  cursor: "pointer"
                }}
              >
                Notificações
              </Typography>
            </Button>
          </Tooltip>
          {
            notifications.some(notification => !notification.isRead)
              ? <Tooltip title="Marcar todas como lidas">
                <IconButton
                  onClick={handleMarkAllAsRead}
                  variant="contained"
                >
                  <Badge
                    color="error"
                    badgeContent={newNotifications}
                  >
                    <EmailIcon fontSize="small" sx={{ color: "white" }} />
                  </Badge>
                </IconButton>
              </Tooltip>
              : <Tooltip title="Notificações lidas">
                <IconButton
                  color="white"
                  variant="contained"
                  disabled
                >
                  <DraftsIcon fontSize="small" sx={{ color: "white" }} />
                </IconButton>
              </Tooltip>
          }
        </Box>
        <Divider />
        {notifications.length === 0
          ? <Box sx={{ p: 2 }}>
            <Typography
              color="textPrimary"
              variant="subtitle2"
            >
              Não existem notificações
            </Typography>
          </Box>
          : <>
            <List
              disablePadding
              sx={{ overflow: 'auto' }}
            >
              {notifications.map((notification, notification_i) => {
                return (
                  <React.Fragment
                    key={"notification_" + notification_i}
                  >
                    {notification.notificationActionType === 'Link' && (
                      <ListItem
                        divider
                        sx={{
                          backgroundColor: notification.isRead ? 'white' : grey[100]
                        }}
                        component={RouterLink}
                        to={notification.notificationAction}
                        onClick={handleClose}
                      >
                        <ListItemAvatar>
                          {renderNotificationType(notification.notificationType)}
                        </ListItemAvatar>
                        <ListItemText
                          primary={(
                            <Link
                              color="textPrimary"
                              sx={{ cursor: 'pointer' }}
                              underline="none"
                              variant="subtitle2"
                              component={RouterLink}
                              to={notification.linkURL}
                            >
                              <div dangerouslySetInnerHTML={{ __html: notification.notificationText }} />
                            </Link>
                          )}
                          secondary={moment(notification.dateActive).fromNow()}
                        />
                      </ListItem>
                    )}
                    {notification.notificationActionType === 'Mensagem' && (
                      <ListItem
                        divider
                        sx={{
                          backgroundColor: notification.isRead ? 'white' : grey[100]
                        }}
                      >
                        <ListItemAvatar>
                          {renderNotificationType(notification.notificationType)}
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <div dangerouslySetInnerHTML={{ __html: notification.notificationText }} />
                          }
                          secondary={moment(notification.dateActive).fromNow()}
                        />
                      </ListItem>
                    )}
                  </React.Fragment>
                );
              })}
            </List>
          </>}
      </Popover>
    </>
  );
};

export default Notification;
