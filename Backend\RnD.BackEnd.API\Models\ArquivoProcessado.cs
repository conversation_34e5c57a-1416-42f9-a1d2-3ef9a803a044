using System;

namespace RnD.BackEnd.API.Models
{
    public class ArquivoProcessado
    {
        public int Id { get; set; }
        public string Nome { get; set; }
        public string BlobUrl { get; set; }
        public DateTime DataUpload { get; set; }
        public DateTime DataProcessamento { get; set; } = DateTime.Now;
        public bool Processado { get; set; }
        public int RegistrosProcessados { get; set; }
        public int RegistrosComErro { get; set; }
    }
} 