using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace RnD.BackEnd.API.Controllers
{
    [Route("api/user")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly ApplicationDbContext _dbContext;

        public UserController(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        [HttpGet("me")]
        public async Task<ActionResult> GetCurrentUser()
        {
            // Verificar se o user está autenticado
            if (!User.Identity.IsAuthenticated)
            {
                return Ok(new { IsAuthenticated = false });
            }

            try
            {
                // Obter o ID do Azure AD (ou outro identificador) do token
                var azureAdId = User.FindFirstValue("http://schemas.microsoft.com/identity/claims/objectidentifier") ?? 
                                User.FindFirstValue("oid") ??
                                User.FindFirstValue(ClaimTypes.NameIdentifier) ??
                                User.FindFirstValue("sub");

                // Se não encontrar nenhum ID, retornar não autenticado
                if (string.IsNullOrEmpty(azureAdId))
                {
                    return Ok(new { 
                        IsAuthenticated = true,
                        Message = "Autenticado, mas sem ID do Azure AD",
                        UserInDatabase = false,
                        Claims = User.Claims.Select(c => new { Type = c.Type, Value = c.Value }).ToList()
                    });
                }

                // Verificar se o user existe no banco de dados
                var existingUser = await _dbContext.User
                    .Include(u => u.UserPermissions)
                    .ThenInclude(up => up.Permission)
                    .FirstOrDefaultAsync(u => u.AzureId == azureAdId);

                if (existingUser != null)
                {
                    // user existe no banco de dados
                    return Ok(new { 
                        IsAuthenticated = true,
                        UserInDatabase = true,
                        User = existingUser,
                        Permissions = existingUser.UserPermissions.Select(up => up.Permission.Name).ToList()
                    });
                }
                else
                {
                    // user não existe no banco de dados
                    return Ok(new { 
                        IsAuthenticated = true,
                        UserInDatabase = false,
                        Message = "user não encontrado no banco de dados",
                        AzureId = azureAdId
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message, 
                    InnerError = ex.InnerException?.Message
                });
            }
        }

        [HttpGet("GetUserDetails")]
        public async Task<ActionResult> GetUserDetails(string username = null)
        {
            try
            {
                // Obter o ID do Azure AD do token atual
                var azureAdId = User.FindFirstValue("http://schemas.microsoft.com/identity/claims/objectidentifier") ?? 
                                User.FindFirstValue("oid") ??
                                User.FindFirstValue(ClaimTypes.NameIdentifier) ??
                                User.FindFirstValue("sub");

                // Se não encontrar nenhum ID, retornar erro
                if (string.IsNullOrEmpty(azureAdId))
                {
                    return BadRequest("Não foi possível identificar o usuário.");
                }

                // Buscar usuário no banco de dados
                var query = _dbContext.User
                    .Include(u => u.UserPermissions)
                    .ThenInclude(up => up.Permission)
                    .AsQueryable();

                // Se um username específico foi fornecido e não é "new", buscar por ele
                if (!string.IsNullOrEmpty(username) && username != "new")
                {
                    query = query.Where(u => u.Username == username);
                }
                else
                {
                    // Caso contrário, buscar pelo ID do Azure AD
                    query = query.Where(u => u.AzureId == azureAdId);
                }

                var user = await query.FirstOrDefaultAsync();

                if (user == null)
                {
                    // Se estamos criando um novo usuário
                    if (username == "new")
                    {
                        return Ok(new List<object> {
                            new { 
                                username = "new",
                                userPermissions = new List<object>()
                            }
                        });
                    }
                    
                    return NotFound("Usuário não encontrado");
                }

                // Mapear permissões para o formato esperado pelo frontend
                var userPermissions = user.UserPermissions
                    .Select(up => new {
                        id = up.PermissionID,
                        // Mapear IDs para os nomes correspondentes (903001 ou 903010)
                        name = up.PermissionID == 1 ? "903001" : 
                               up.PermissionID == 2 ? "903010" : 
                               up.Permission.Name
                    })
                    .ToList();

                // Criar objeto de resposta
                var userResponse = new {
                    userID = user.UserID,
                    username = user.Username,
                    email = user.Email,
                    azureId = user.AzureId,
                    userPermissions = userPermissions,
                    userModules = new List<object>() // Vazio por enquanto
                };

                return Ok(new List<object> { userResponse });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message, 
                    InnerError = ex.InnerException?.Message
                });
            }
        }

        [HttpPost("register")]
        public async Task<ActionResult> RegisterUser()
        {
            // Verificar se o user está autenticado
            if (!User.Identity.IsAuthenticated)
            {
                return Unauthorized(new { Message = "user não autenticado" });
            }

            try
            {
                // Obter o ID do Azure AD (ou outro identificador) do token
                var azureAdId = User.FindFirstValue("http://schemas.microsoft.com/identity/claims/objectidentifier") ?? 
                                User.FindFirstValue("oid") ??
                                User.FindFirstValue(ClaimTypes.NameIdentifier) ??
                                User.FindFirstValue("sub");

                // Se não encontrar nenhum ID, retornar erro
                if (string.IsNullOrEmpty(azureAdId))
                {
                    return BadRequest("Não foi possível obter o ID do usuário a partir do token.");
                }

                // Verificar se o user já existe no banco de dados
                var existingUser = await _dbContext.User.FirstOrDefaultAsync(u => u.AzureId == azureAdId);
                
                if (existingUser != null)
                {
                    return Ok(new { 
                        Message = "usuário já existe no sistema",
                        User = existingUser
                    });
                }

                // Obter dados do usuário a partir das claims
                var userName = User.FindFirstValue("name") ?? 
                               User.FindFirstValue(ClaimTypes.Name) ?? 
                               User.Identity.Name ?? 
                               "Usuário";
                
                var email = User.FindFirstValue("preferred_username") ?? 
                            User.FindFirstValue("upn") ?? 
                            User.FindFirstValue(ClaimTypes.Email) ?? 
                            "<EMAIL>";

                // Criar novo user
                var newUser = new User
                {
                    AzureId = azureAdId,
                    Username = userName,
                    Email = email
                };

                _dbContext.User.Add(newUser);
                await _dbContext.SaveChangesAsync();

                // Adicionar permissão padrão (903001)
                var defaultPermission = await _dbContext.Permission.FindAsync(1);
                
                if (defaultPermission == null)
                {
                    // Se não existir a permissão com ID 1, criar
                    defaultPermission = new Permission { Id = 1, Name = "903001" };
                    _dbContext.Permission.Add(defaultPermission);
                    await _dbContext.SaveChangesAsync();
                }
                
                var userPermission = new UserPermission
                {
                    UserID = newUser.UserID,
                    PermissionID = defaultPermission.Id
                };
                
                _dbContext.UserPermission.Add(userPermission);
                await _dbContext.SaveChangesAsync();

                return Ok(new { 
                    Message = "Usuário registrado com sucesso",
                    User = newUser,
                    Permission = defaultPermission.Name
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message, 
                    InnerError = ex.InnerException?.Message 
                });
            }
        }

        [HttpPost("force-register")]
        public async Task<ActionResult> ForceRegisterUser([FromBody] UserRegisterRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.Name) || string.IsNullOrEmpty(request.Email))
                {
                    return BadRequest(new { Message = "Nome e e-mail são obrigatórios" });
                }

                // Gerar um ID para o user
                string azureId = request.AzureId ?? $"manual-{Guid.NewGuid()}";

                // Verificar se o user já existe no banco de dados
                var existingUser = await _dbContext.User.FirstOrDefaultAsync(u => u.AzureId == azureId);
                
                if (existingUser != null)
                {
                    return Ok(new { 
                        Message = "user já existe", 
                        User = new { existingUser.UserID, existingUser.Username, existingUser.Email, existingUser.AzureId }
                    });
                }

                // Criar novo user
                var newUser = new User
                {
                    AzureId = azureId,
                    Username = request.Name,
                    Email = request.Email
                };

                _dbContext.User.Add(newUser);
                await _dbContext.SaveChangesAsync();

                // Adicionar permissão padrão
                var defaultPermission = await _dbContext.Permission.FirstOrDefaultAsync(p => p.Name == "Usuario");
                
                if (defaultPermission == null)
                {
                    defaultPermission = new Permission { Name = "Usuario" };
                    _dbContext.Permission.Add(defaultPermission);
                    await _dbContext.SaveChangesAsync();
                }
                
                var userPermission = new UserPermission
                {
                    UserID = newUser.UserID,
                    PermissionID = defaultPermission.Id
                };
                
                _dbContext.UserPermission.Add(userPermission);
                await _dbContext.SaveChangesAsync();

                return Ok(new { 
                    Message = "user registrado com sucesso",
                    User = new { newUser.UserID, newUser.Username, newUser.Email, newUser.AzureId },
                    Permission = defaultPermission.Name
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message, 
                    InnerError = ex.InnerException?.Message 
                });
            }
        }

        [HttpPost("fallback-register")]
        public async Task<ActionResult> FallbackRegister()
        {
            try
            {
                // Verificar se o user está autenticado
                if (!User.Identity.IsAuthenticated)
                {
                    return Unauthorized(new { Message = "user não autenticado" });
                }

                // Extrair informações do user do token
                var azureAdId = User.FindFirstValue("http://schemas.microsoft.com/identity/claims/objectidentifier") ?? 
                                User.FindFirstValue("oid") ??
                                User.FindFirstValue(ClaimTypes.NameIdentifier) ??
                                User.FindFirstValue("sub");

                // Verificar se temos o ID
                if (string.IsNullOrEmpty(azureAdId))
                {
                    return BadRequest(new { 
                        Message = "ID do Azure AD não encontrado",
                        Claims = User.Claims.Select(c => new { Type = c.Type, Value = c.Value }).ToList()
                    });
                }

                // Obter informações corretas do user das claims
                var name = User.FindFirstValue("name") ?? 
                           User.FindFirstValue(ClaimTypes.Name) ??
                           User.Identity.Name ?? 
                           "user " + azureAdId.Substring(0, Math.Min(azureAdId.Length, 8));
        
                var email = User.FindFirstValue("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/upn") ??
                           User.FindFirstValue("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name") ??
                           User.FindFirstValue("upn") ??
                           User.FindFirstValue("unique_name") ??
                           User.FindFirstValue("preferred_username") ?? 
                           User.FindFirstValue(ClaimTypes.Email) ??
                           $"user-{azureAdId.Substring(0, Math.Min(azureAdId.Length, 8))}@example.com";

                Console.WriteLine($">>> [Fallback] Informações do token:");
                Console.WriteLine($">>> [Fallback] AzureAdId: {azureAdId}");
                Console.WriteLine($">>> [Fallback] Nome: {name}");
                Console.WriteLine($">>> [Fallback] Email: {email}");
                
                // Todas as claims disponíveis para debug
                Console.WriteLine(">>> [Fallback] TODAS AS CLAIMS:");
                foreach (var claim in User.Claims)
                {
                    Console.WriteLine($">>> [Fallback] {claim.Type}: {claim.Value}");
                }

                // Verificar se já existe
                var existingUser = await _dbContext.User.FirstOrDefaultAsync(u => u.AzureId == azureAdId);
        
                if (existingUser != null)
                {
                    // Se já existe, atualizar as informações do user
                    bool userUpdated = false;
                    
                    if (existingUser.Email != email)
                    {
                        Console.WriteLine($">>> [Fallback] Atualizando email de '{existingUser.Email}' para '{email}'");
                        existingUser.Email = email;
                        userUpdated = true;
                    }
                    
                    if (existingUser.Username != name)
                    {
                        Console.WriteLine($">>> [Fallback] Atualizando nome de '{existingUser.Username}' para '{name}'");
                        existingUser.Username = name;
                        userUpdated = true;
                    }
                    
                    if (userUpdated)
                    {
                        await _dbContext.SaveChangesAsync();
                        return Ok(new { 
                            Message = "user atualizado com sucesso (fallback)", 
                            User = new { existingUser.UserID, existingUser.Username, existingUser.Email, existingUser.AzureId }
                        });
                    }
                    else
                    {
                        return Ok(new { 
                            Message = "user já existe e está atualizado (fallback)", 
                            User = new { existingUser.UserID, existingUser.Username, existingUser.Email, existingUser.AzureId }
                        });
                    }
                }

                // Criar novo user
                var newUser = new User
                {
                    AzureId = azureAdId,
                    Username = name,
                    Email = email
                };

                _dbContext.User.Add(newUser);
                await _dbContext.SaveChangesAsync();

                // Adicionar permissão padrão
                var defaultPermission = await _dbContext.Permission.FirstOrDefaultAsync(p => p.Name == "Usuario");
        
                if (defaultPermission == null)
                {
                    defaultPermission = new Permission { Name = "Usuario" };
                    _dbContext.Permission.Add(defaultPermission);
                    await _dbContext.SaveChangesAsync();
                }
        
                var userPermission = new UserPermission
                {
                    UserID = newUser.UserID,
                    PermissionID = defaultPermission.Id
                };
        
                _dbContext.UserPermission.Add(userPermission);
                await _dbContext.SaveChangesAsync();

                return Ok(new { 
                    Message = "user registrado com sucesso (fallback)",
                    User = new { newUser.UserID, newUser.Username, newUser.Email, newUser.AzureId },
                    Permission = defaultPermission.Name
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message, 
                    InnerError = ex.InnerException?.Message,
                    Stack = ex.StackTrace
                });
            }
        }

        [HttpGet("auth-info")]
        public ActionResult GetAuthInfo()
        {
            // Capturar o valor bruto do token para diagnóstico
            string authHeader = Request.Headers["Authorization"].ToString();
            
            // Extrair o token (sem o "Bearer ")
            string token = string.Empty;
            if (!string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                token = authHeader.Substring("Bearer ".Length).Trim();
            }
            
            var claims = new List<object>();
            
            if (User.Claims != null)
            {
                claims = User.Claims.Select(c => new { Type = c.Type, Value = c.Value }).ToList<object>();
            }
            
            return Ok(new
            {
                IsAuthenticated = User.Identity?.IsAuthenticated ?? false,
                AuthenticationType = User.Identity?.AuthenticationType,
                Name = User.Identity?.Name,
                Claims = claims,
                HeadersReceived = Request.Headers.Select(h => new { Key = h.Key, Value = h.Value.ToString() }).ToList(),
                TokenInfo = new
                {
                    AuthorizationHeader = authHeader.Substring(0, Math.Min(30, authHeader.Length)) + "...",
                    TokenFirstChars = !string.IsNullOrEmpty(token) ? token.Substring(0, Math.Min(30, token.Length)) + "..." : null,
                    TokenLength = token.Length
                }
            });
        }

        [HttpPost("add-azure-users")]
        [Authorize]
        public async Task<ActionResult> AddAzureUsers([FromBody] AddAzureUsersRequest request)
        {
            if (request?.Users == null || !request.Users.Any())
            {
                return BadRequest("Nenhum usuário enviado para adicionar");
            }

            try
            {
                var resultsAdded = new List<object>();
                var resultsExisting = new List<object>();
                var resultsError = new List<object>();

                foreach (var userRequest in request.Users)
                {
                    try
                    {
                        // Verificar se o usuário já existe
                        var existingUser = await _dbContext.User
                            .FirstOrDefaultAsync(u => u.AzureId == userRequest.AzureId);

                        if (existingUser != null)
                        {
                            // Usuário já existe, apenas adiciona à lista de existentes
                            resultsExisting.Add(new { 
                                AzureId = userRequest.AzureId, 
                                Username = existingUser.Username,
                                Email = existingUser.Email
                            });
                            continue;
                        }

                        // Criar novo usuário
                        var newUser = new User
                        {
                            AzureId = userRequest.AzureId,
                            Username = userRequest.DisplayName,
                            Email = userRequest.Email
                        };

                        _dbContext.User.Add(newUser);
                        await _dbContext.SaveChangesAsync();

                        // Adicionar permissão de admin se solicitado
                        if (request.IsAdmin)
                        {
                            // Adicionar permissão de Admin (ID 2)
                            var adminPermission = await _dbContext.Permission.FindAsync(2);
                            
                            if (adminPermission == null)
                            {
                                // Se não existir a permissão com ID 2 (Admin), criar
                                adminPermission = new Permission { Id = 2, Name = "903010" };
                                _dbContext.Permission.Add(adminPermission);
                                await _dbContext.SaveChangesAsync();
                            }
                            
                            var userPermission = new UserPermission
                            {
                                UserID = newUser.UserID,
                                PermissionID = adminPermission.Id
                            };
                            
                            _dbContext.UserPermission.Add(userPermission);
                        }

                        // Sempre adicionar também a permissão básica (ID 1)
                        var defaultPermission = await _dbContext.Permission.FindAsync(1);
                        
                        if (defaultPermission == null)
                        {
                            // Se não existir a permissão com ID 1, criar
                            defaultPermission = new Permission { Id = 1, Name = "903001" };
                            _dbContext.Permission.Add(defaultPermission);
                            await _dbContext.SaveChangesAsync();
                        }
                        
                        var userDefaultPermission = new UserPermission
                        {
                            UserID = newUser.UserID,
                            PermissionID = defaultPermission.Id
                        };
                        
                        _dbContext.UserPermission.Add(userDefaultPermission);
                        await _dbContext.SaveChangesAsync();

                        // Adicionar à lista de resultados
                        resultsAdded.Add(new { 
                            UserId = newUser.UserID,
                            AzureId = newUser.AzureId, 
                            Username = newUser.Username,
                            Email = newUser.Email
                        });
                    }
                    catch (Exception ex)
                    {
                        // Registrar o erro para este usuário específico
                        resultsError.Add(new { 
                            AzureId = userRequest.AzureId, 
                            DisplayName = userRequest.DisplayName,
                            Error = ex.Message
                        });
                    }
                }

                // Retornar resultado agrupado
                return Ok(new { 
                    Added = resultsAdded,
                    AlreadyExisting = resultsExisting,
                    Errors = resultsError,
                    TotalAdded = resultsAdded.Count,
                    TotalExisting = resultsExisting.Count,
                    TotalErrors = resultsError.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message, 
                    InnerError = ex.InnerException?.Message
                });
            }
        }

        // Endpoint para adicionar usuários sem autenticação (apenas para testes)
        [HttpPost("add-azure-users-test")]
        public async Task<ActionResult> AddAzureUsersTest([FromBody] AddAzureUsersRequest request)
        {
            if (request?.Users == null || !request.Users.Any())
            {
                return BadRequest("Nenhum usuário enviado para adicionar");
            }

            try
            {
                var resultsAdded = new List<object>();
                var resultsExisting = new List<object>();
                var resultsError = new List<object>();

                foreach (var userRequest in request.Users)
                {
                    try
                    {
                        // Verificar se o usuário já existe
                        var existingUser = await _dbContext.User
                            .FirstOrDefaultAsync(u => u.AzureId == userRequest.AzureId);

                        if (existingUser != null)
                        {
                            // Usuário já existe, apenas adiciona à lista de existentes
                            resultsExisting.Add(new { 
                                AzureId = userRequest.AzureId, 
                                Username = existingUser.Username,
                                Email = existingUser.Email
                            });
                            continue;
                        }

                        // Criar novo usuário
                        var newUser = new User
                        {
                            AzureId = userRequest.AzureId,
                            Username = userRequest.DisplayName,
                            Email = userRequest.Email
                        };

                        _dbContext.User.Add(newUser);
                        await _dbContext.SaveChangesAsync();

                        // Adicionar permissão de admin se solicitado
                        if (request.IsAdmin)
                        {
                            // Adicionar permissão de Admin (ID 2)
                            var adminPermission = await _dbContext.Permission.FindAsync(2);
                            
                            if (adminPermission == null)
                            {
                                // Se não existir a permissão com ID 2 (Admin), criar
                                adminPermission = new Permission { Id = 2, Name = "903010" };
                                _dbContext.Permission.Add(adminPermission);
                                await _dbContext.SaveChangesAsync();
                            }
                            
                            var userPermission = new UserPermission
                            {
                                UserID = newUser.UserID,
                                PermissionID = adminPermission.Id
                            };
                            
                            _dbContext.UserPermission.Add(userPermission);
                        }

                        // Sempre adicionar também a permissão básica (ID 1)
                        var defaultPermission = await _dbContext.Permission.FindAsync(1);
                        
                        if (defaultPermission == null)
                        {
                            // Se não existir a permissão com ID 1, criar
                            defaultPermission = new Permission { Id = 1, Name = "903001" };
                            _dbContext.Permission.Add(defaultPermission);
                            await _dbContext.SaveChangesAsync();
                        }
                        
                        var userDefaultPermission = new UserPermission
                        {
                            UserID = newUser.UserID,
                            PermissionID = defaultPermission.Id
                        };
                        
                        _dbContext.UserPermission.Add(userDefaultPermission);
                        await _dbContext.SaveChangesAsync();

                        // Adicionar à lista de resultados
                        resultsAdded.Add(new { 
                            UserId = newUser.UserID,
                            AzureId = newUser.AzureId, 
                            Username = newUser.Username,
                            Email = newUser.Email
                        });
                    }
                    catch (Exception ex)
                    {
                        // Registrar o erro para este usuário específico
                        resultsError.Add(new { 
                            AzureId = userRequest.AzureId, 
                            DisplayName = userRequest.DisplayName,
                            Error = ex.Message
                        });
                    }
                }

                // Retornar resultado agrupado
                return Ok(new { 
                    Added = resultsAdded,
                    AlreadyExisting = resultsExisting,
                    Errors = resultsError,
                    TotalAdded = resultsAdded.Count,
                    TotalExisting = resultsExisting.Count,
                    TotalErrors = resultsError.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message, 
                    InnerError = ex.InnerException?.Message
                });
            }
        }

        // Endpoint público para adicionar usuários sem autenticação (apenas para testes com dados reais)
        [HttpPost("add-azure-users-public")]
        [AllowAnonymous]
        public async Task<ActionResult> AddAzureUsersPublic([FromBody] AddAzureUsersRequest request)
        {
            if (request?.Users == null || !request.Users.Any())
            {
                return BadRequest("Nenhum usuário enviado para adicionar");
            }

            try
            {
                var resultsAdded = new List<object>();
                var resultsExisting = new List<object>();
                var resultsError = new List<object>();

                foreach (var userRequest in request.Users)
                {
                    try
                    {
                        // Verificar se o usuário já existe
                        var existingUser = await _dbContext.User
                            .FirstOrDefaultAsync(u => u.AzureId == userRequest.AzureId);

                        if (existingUser != null)
                        {
                            // Usuário já existe, apenas adiciona à lista de existentes
                            resultsExisting.Add(new { 
                                AzureId = userRequest.AzureId, 
                                Username = existingUser.Username,
                                Email = existingUser.Email
                            });
                            continue;
                        }

                        // Criar novo usuário
                        var newUser = new User
                        {
                            AzureId = userRequest.AzureId,
                            Username = userRequest.DisplayName,
                            Email = userRequest.Email
                        };

                        _dbContext.User.Add(newUser);
                        await _dbContext.SaveChangesAsync();

                        // Adicionar permissão de admin se solicitado
                        if (request.IsAdmin)
                        {
                            // Adicionar permissão de Admin (ID 2)
                            var adminPermission = await _dbContext.Permission.FindAsync(2);
                            
                            if (adminPermission == null)
                            {
                                // Se não existir a permissão com ID 2 (Admin), criar
                                adminPermission = new Permission { Id = 2, Name = "903010" };
                                _dbContext.Permission.Add(adminPermission);
                                await _dbContext.SaveChangesAsync();
                            }
                            
                            var userPermission = new UserPermission
                            {
                                UserID = newUser.UserID,
                                PermissionID = adminPermission.Id
                            };
                            
                            _dbContext.UserPermission.Add(userPermission);
                            await _dbContext.SaveChangesAsync();
                        }
                        else
                        {
                            // Se não for admin, adicionar a permissão básica (ID 1)
                            var defaultPermission = await _dbContext.Permission.FindAsync(1);
                            
                            if (defaultPermission == null)
                            {
                                // Se não existir a permissão com ID 1, criar
                                defaultPermission = new Permission { Id = 1, Name = "903001" };
                                _dbContext.Permission.Add(defaultPermission);
                                await _dbContext.SaveChangesAsync();
                            }
                            
                            var userDefaultPermission = new UserPermission
                            {
                                UserID = newUser.UserID,
                                PermissionID = defaultPermission.Id
                            };
                            
                            _dbContext.UserPermission.Add(userDefaultPermission);
                            await _dbContext.SaveChangesAsync();
                        }

                        // Adicionar à lista de resultados
                        resultsAdded.Add(new { 
                            UserId = newUser.UserID,
                            AzureId = newUser.AzureId, 
                            Username = newUser.Username,
                            Email = newUser.Email
                        });
                    }
                    catch (Exception ex)
                    {
                        // Registrar o erro para este usuário específico
                        resultsError.Add(new { 
                            AzureId = userRequest.AzureId, 
                            DisplayName = userRequest.DisplayName,
                            Error = ex.Message
                        });
                    }
                }

                // Retornar resultado agrupado
                return Ok(new { 
                    Added = resultsAdded,
                    AlreadyExisting = resultsExisting,
                    Errors = resultsError,
                    TotalAdded = resultsAdded.Count,
                    TotalExisting = resultsExisting.Count,
                    TotalErrors = resultsError.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message, 
                    InnerError = ex.InnerException?.Message
                });
            }
        }
    }

    public class UserRegisterRequest
    {
        public string Name { get; set; }
        public string Email { get; set; }
        public string AzureId { get; set; }
    }

    // Modelo para adicionar usuários do Azure
    public class AddAzureUsersRequest
    {
        public List<AzureUserInfo> Users { get; set; }
        public bool IsAdmin { get; set; } = false;
    }

    public class AzureUserInfo
    {
        public string AzureId { get; set; }
        public string DisplayName { get; set; }
        public string Email { get; set; }
    }
} 