﻿using System;
using System.Collections;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using RestSharp;
using RnD.BackEnd.Licensing.Entities;
using RnD.BackEnd.Licensing.Handlers;
using RnD.BackEnd.Licensing.Interfaces;
using RnD.BackEnd.Licensing.Settings;

namespace RnD.BackEnd.Licensing.Services
{
    /// <summary>
    /// License Validation Service
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Licensing.Interfaces.ILicenseValidationService" />
    public class LicenseValidationService : ILicenseValidationService
    {
        private readonly IMemoryCache _cache;
        private readonly ValidationHandler _validationHandler;

        private readonly string _validLicenseCacheKey = "LIC_VAL";
        private readonly string _encryptionCacheKey = "CLI_ENC_K";
        private readonly string _licenseKeyConfigurationKey = "LicenseKey";

        /// <summary>
        /// Initializes a new instance of the <see cref="LicenseValidationService"/> class.
        /// </summary>
        /// <param name="cache">The cache.</param>
        /// <param name="configuration">The configuration.</param>
        public LicenseValidationService(IMemoryCache cache, IConfiguration configuration)
        {
            _cache = cache;

            SettingsHandler _settingsHandler = new SettingsHandler(configuration[_licenseKeyConfigurationKey]);
            ValidationSettings validationSettings = _settingsHandler.GetDecryptedValidationSettings();

            _validationHandler = new ValidationHandler(validationSettings);
        }

        /// <summary>
        /// Validates the licence asynchronous.
        /// </summary>
        /// <param name="domainName">Name of the domain.</param>
        /// <param name="validationType">Type of the validation.</param>
        /// <returns>
        /// True if license is valid, False otherwise.
        /// </returns>
        public async Task<bool> ValidateLicenseAsync(string domainName, ValidationType? validationType = null)
        {
            EncryptionKeys encryptionKeys = await GetEncryptionAndSigningKeys();

            return await GetFromCacheAsync(_validLicenseCacheKey, async () =>
            {
                return await _validationHandler.ValidateLicenseAsync(domainName, encryptionKeys, validationType);
            }, TimeSpan.FromHours(24));
        }

        #region Private methods

        /// <summary>
        /// Gets the keys (Encryption and Signing) from cache.
        /// </summary>
        /// <returns>
        /// Encryption and Signing Keys
        /// </returns>
        private async Task<EncryptionKeys> GetEncryptionAndSigningKeys()
        {
            return await GetFromCacheAsync(_encryptionCacheKey, () =>
            {
                return Task.FromResult(_validationHandler.GetEncryptionAndSigningKeys());
            }, TimeSpan.FromDays(30));
        }

        /// <summary>
        /// Gets from cache asynchronous.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key">The key.</param>
        /// <param name="acquire">The acquire.</param>
        /// <param name="expirationTime">The expiration time.</param>
        /// <returns>
        /// The cached item
        /// </returns>
        private async Task<T> GetFromCacheAsync<T>(string key, Func<Task<T>> acquire, TimeSpan expirationTime)
        {
            T cachedObject = _cache.Get<T>(key);

            if (cachedObject == null || cachedObject.Equals(default(T)))
            {
                cachedObject = await acquire();
                if (cachedObject != null || (cachedObject != null && cachedObject is ICollection list && list.Count > 0))
                {
                    _cache.Set<T>(key, cachedObject, expirationTime);
                }
            }
            return cachedObject;
        }

        #endregion
    }
}
