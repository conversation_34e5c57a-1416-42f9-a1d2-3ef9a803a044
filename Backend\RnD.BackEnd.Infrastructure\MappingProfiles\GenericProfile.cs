﻿using AutoMapper;
using RnD.BackEnd.Domain.DataModels;

namespace RnD.BackEnd.Infrastructure.MappingProfiles
{
    /// <summary>
    /// Generic profile mapping
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class GenericProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GenericProfile"/> class.
        /// </summary>
        public GenericProfile()
        {
            CreateMap(typeof(ResponseModel<>), typeof(ResponseModel<>));
        }
    }
}
