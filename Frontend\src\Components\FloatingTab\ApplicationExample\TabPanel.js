import React from 'react';
import PropTypes from 'prop-types';
import QuestionCard from './Questions/QuestionCard';

export default function TabPanel(props) {
  const { value, index, section, updateQuizAnswer, updateQuizYesOrNo, saveQuizComment, canEdit, updateFiles, errors } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
    >
      {
        value === index && section.map((question, i) => (
          <QuestionCard
            key={`tab_${index}_question_${i}`}
            question={question}
            updateQuizAnswer={updateQuizAnswer}
            updateQuizYesOrNo={updateQuizYesOrNo}
            updateFiles={updateFiles}
            saveQuizComment={saveQuizComment}
            canEdit={canEdit}
            errors={errors}
          />
        ))
      }
    </div>
  );
}

TabPanel.propTypes = {
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired,
  section: PropTypes.array.isRequired,
  updateQuizAnswer: PropTypes.func.isRequired,
  updateQuizYesOrNo: PropTypes.func.isRequired,
  saveQuizComment: PropTypes.func,
  updateFiles: PropTypes.func,
  canEdit: PropTypes.bool.isRequired,
  errors: PropTypes.object
};
