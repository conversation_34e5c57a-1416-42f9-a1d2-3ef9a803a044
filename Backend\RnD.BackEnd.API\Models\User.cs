﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace RnD.BackEnd.API.Models
{
    public class User
    {
        [Key]
        public int UserID { get; set; }

        [Required, <PERSON><PERSON>ength(126)]
        public string Username { get; set; }

        [Required, <PERSON><PERSON><PERSON><PERSON>(126)]
        [EmailAddress]
        public string Email { get; set; }

        [Required, MaxLength(255)]
        public string AzureId { get; set; }

        public ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
    }
}