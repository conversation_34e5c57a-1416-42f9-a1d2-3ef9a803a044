import { format } from 'date-fns';
import { isArray } from 'lodash';
import * as yup from 'yup';

function getCondition(condition, trueValue, falseValue) {
  return condition ? trueValue : falseValue;
}

export const getValidationSchema = (fields) => {
  const schemaFields = fields.reduce((schema, field) => {
    const { ElementID, ElementRequired, ElementType } = field;
    let validationType;
    let validations = [];
    const validationTypeError = 'Este campo é obrigatório';
    const name = ElementID;

    const isObject = name.indexOf('.') >= 0;

    if (!ElementRequired) {
      return schema;
    }

    switch (ElementType) {
      case 'TextField':
        validationType = 'string';
        validations = [
          {
            type: 'required',
            params: ['Este campo é obrigatório']
          }
        ];
        break;

      case 'FileField':
        validationType = 'string';
        validations = [
          {
            type: 'required',
            params: ['Este ficheiro é obrigatório']
          }
        ];
        break;

      case 'TextAreaField':
        validationType = 'string';
        validations = [
          {
            type: 'required',
            params: ['Este campo é obrigatório']
          }
        ];
        break;

      case 'CheckBoxField':
        validationType = 'array';
        validations = [
          {
            type: 'min',
            params: [1, 'Este campo é obrigatório']
          }
        ];
        break;

      case 'DateField':
        validationType = 'date';
        validations = [
          {
            type: 'required',
            params: ['Este campo é obrigatório']
          }
        ];
        break;

      case 'NumberField':
        validationType = 'number';
        validations = [
          {
            type: 'required',
            params: ['Este campo é obrigatório']
          }
        ];
        break;

      case 'RadioField':
        validationType = 'string';
        validations = [
          {
            type: 'required',
            params: ['Este campo é obrigatório']
          }
        ];
        break;

      case 'TableField':
        validationType = 'array';
        validations = [
          {
            type: 'min',
            params: [1, 'A tabela deverá conter pelo menos um elemento']
          }
        ];
        break;

      case 'MultipleDynamicCollection':
        validationType = 'array';
        validations = [
          {
            type: 'min',
            params: [1, 'Este campo é obrigatório']
          }
        ];
        break;

      case 'Rating Description':
        validationType = 'string';
        validations = [
          {
            type: 'required',
            params: ['Este campo é obrigatório']
          }
        ];
        break;

      case 'Text':
        validationType = 'string';
        validations = [
          {
            type: 'required',
            params: ['Este campo é obrigatório']
          }
        ];
        break;

      case 'Rating':
        validationType = 'string';
        validations = [
          {
            type: 'required',
            params: ['Este campo é obrigatório']
          }
        ];
        break;

      case 'Single Choice':
        validationType = 'string';
        validations = [
          {
            type: 'required',
            params: ['Este campo é obrigatório']
          }
        ];
        break;

      default:
        break;
    }

    if (!validationType) {
      return schema;
    }

    let validator = yup[validationType]().typeError(validationTypeError || '');

    validations.forEach((validation) => {
      const { params, type } = validation;
      if (!validator[type]) {
        return;
      }
      validator = validator[type](...params);
    });

    if (!isObject) {
      return schema.concat(yup.object().shape({ [name]: validator }));
    }

    const reversePath = name.split('.').reverse();
    const currNestedObject = reversePath.slice(1).reduce((yupObj, path) => {
      if (!Number.isNaN(path)) {
        return { array: yup.array().of(yup.object().shape(yupObj)) };
      }
      if (yupObj.array) {
        return { [path]: yupObj.array };
      }
      return { [path]: yup.object().shape(yupObj) };
    }, { [reversePath[0]]: validator });

    const newSchema = yup.object().shape(currNestedObject);
    return schema.concat(newSchema);
  }, yup.object().shape({}));

  return schemaFields;
};

const parseDefaultData = (aiData) => {
  const parsedObject = {};

  if (aiData) {
    const parsedData = aiData; // JSON.parse(aiData);

    Object.keys(parsedData).forEach((x) => {
      if (Array.isArray(parsedData[x])) {
        parsedObject[x] = parsedData[x];
      } else {
        Object.keys(parsedData[x]).map((y) => (
          parsedObject[`${x}.${y}`] = parsedData[x][y]
        ));
      }
    });
  }
  return parsedObject;
};

const hasMatchWholeWord = (wordCollection, word) => {
  if (word && wordCollection) {
    const matched = wordCollection.filter((w) => w === word).length > 0;
    return matched;
  }
  return false;
};

const tryparse = (value) => {
  return Number.isNaN(parseFloat(value)) ? 0 : parseFloat(value);
};

const getObjectValue = (element, v, dataForm) => {
  const elementType = dataForm.surveyData.templateStructure.TabCollection[0].Tab.ElementCollection.Element.find(x => x.ElementID === element);

  if (elementType) {
    switch (elementType.ElementType) {
      case 'SelectList':
        return elementType.ElementSelectListType.find(x => x.ElementValue === v?.ElementValue)?.ElementValue;

      case 'CheckboxField':
        return elementType.ElementSelectListType.find(x => x.ElementLabel === v)?.ElementValue;

      default:
        return v;
    }
  } else {
    return element;
  }
};

const calculate = (formula, v, dataForm) => {
  formula.split(/[(+-/*)]/).forEach(element => {
    if (element !== '' && element !== 'Math' && element !== 'round') {
      formula = formula.replace(element, tryparse(getObjectValue(element, v[element], dataForm)));
    }
  });
  /*eslint-disable no-eval*/
  return eval(formula);
};

export const calculateValue = (dataForm, element, values) => {
  const elementType = dataForm.surveyData.templateStructure.TabCollection[0].Tab.ElementCollection.Element.find(x => x.ElementID === element.ElementID);
  let newValue;

  switch (elementType.ElementType) {
    case 'SelectList':
      newValue = elementType.ElementSelectListType.find(x => x.ElementValue === calculate(element.ElementFormula, values, dataForm).toString());
      return newValue ?? null;

    case 'NumberField':
      newValue = calculate(element.ElementFormula, values, dataForm).toString();
      return newValue ?? null;

    default:
      break;
  }
};

export const getFormInitialValues = (data, dynamicCollection, files) => {
  const InitialValues = {};

  const surveyData = data.templateStructure.TabCollection[0].Tab.ElementCollection.Element;
  const metadata = data.metaData;
  const aiData = data.cV_AI_PARSED_DATA;

  const defaultData = getCondition(!metadata, parseDefaultData(aiData), metadata);

  const values = [];

  surveyData.forEach((element) => {
    InitialValues[element.ElementID] = '';

    switch (element.ElementType) {
      case 'CheckBoxField':
        if (!metadata && element.ElementDefaultValue) {
          InitialValues[element.ElementID] = element.ElementDefaultValue;
        } else {
          InitialValues[element.ElementID] = defaultData[getCondition(!metadata, element.ElementParsedObject, element.ElementID)] || [];
        }

        if (element.ElementConditional && element.ElementConditional.length > 0) {
          element.ElementConditional.forEach((elementConditional) => {
            if (InitialValues[element.ElementID] !== null && InitialValues[element.ElementID].length > 0 && (elementConditional.Value === InitialValues[element.ElementID][0] || elementConditional.Value === InitialValues[element.ElementID])) {
              InitialValues[elementConditional.ElementID] = defaultData[getCondition(!metadata, elementConditional.ElementParsedObject, elementConditional.ElementID)] || null;
            } else {
              InitialValues[elementConditional.ElementID] = null;
            }
          });
        }

        break;

      case 'MultipleDynamicCollection':
        InitialValues[element.ElementID] = defaultData[getCondition(!metadata, element.ElementParsedObject, element.ElementID)] || [];

        if (!metadata && defaultData[element.ElementParsedObject]) {
          InitialValues[element.ElementID] = defaultData[element.ElementParsedObject].map((x) => x.Code);
        }

        InitialValues[element.ElementID].forEach((item) => {
          if (dynamicCollection[element.ElementDynamicCode].Collection.find((t) => t.key === item || t.text === item)) {
            values.push(dynamicCollection[element.ElementDynamicCode].Collection.find((t) => t.key === item || t.text === item));
          }
        });

        InitialValues[element.ElementID] = values;

        break;

      case 'SelectList':
        InitialValues[element.ElementID] = defaultData[getCondition(!metadata, element.ElementParsedObject, element.ElementID)] || [];

        if (!metadata && defaultData[element.ElementParsedObject]) {
          InitialValues[element.ElementID] = defaultData[element.ElementParsedObject].map((x) => x.Code);
        }

        InitialValues[element.ElementID] = element.ElementSelectListType.find((t) => t.ElementValue === InitialValues[element.ElementID]) ?? null;

        break;

      case 'DynamicCollection':
        InitialValues[element.ElementID] = defaultData[getCondition(!metadata, element.ElementParsedObject, element.ElementID)] || null;

        if (InitialValues[element.ElementID] !== null && dynamicCollection[element.ElementDynamicCode].Collection.find((t) => t.key === InitialValues[element.ElementID])) {
          InitialValues[element.ElementID] = dynamicCollection[element.ElementDynamicCode].Collection.find((t) => t.key === InitialValues[element.ElementID]);
        }

        if (element.ElementConditional && element.ElementConditional.length > 0) {
          element.ElementConditional.forEach((elementConditional) => {
            if (InitialValues[element.ElementID] !== null && elementConditional.Value === InitialValues[element.ElementID].key) {
              InitialValues[elementConditional.ElementID] = defaultData[getCondition(!metadata, elementConditional.ElementParsedObject, elementConditional.ElementID)] || null;
              InitialValues[elementConditional.ElementID] = getCondition(InitialValues[elementConditional.ElementID] != null, dynamicCollection[elementConditional.ElementDynamicCode].Collection.find((t) => t.key === InitialValues[elementConditional.ElementID]), null);
            } else {
              InitialValues[elementConditional.ElementID] = null;
            }
          });
        }

        break;

      case 'RadioField':
        InitialValues[element.ElementID] = defaultData[getCondition(!metadata, element.ElementParsedObject, element.ElementID)] || null;

        if (element.ElementConditional && element.ElementConditional.length > 0) {
          element.ElementConditional.forEach((elementConditional) => {
            if (InitialValues[element.ElementID] !== null && hasMatchWholeWord([InitialValues[element.ElementID]], elementConditional.Value)) {
              if (elementConditional.ElementType === 'DateField') {
                InitialValues[elementConditional.ElementID] = defaultData[getCondition(!metadata, elementConditional.ElementParsedObject, elementConditional.ElementID)] ? format(new Date(defaultData[getCondition(!metadata, elementConditional.ElementParsedObject, elementConditional.ElementID)]), 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd');
              } else {
                InitialValues[elementConditional.ElementID] = defaultData[getCondition(!metadata ? elementConditional.ElementParsedObject : elementConditional.ElementID)] || '';
              }
            } else {
              InitialValues[elementConditional.ElementID] = null;
            }
          });
        }

        break;

      case 'DateField':
        InitialValues[element.ElementID] = defaultData[getCondition(!metadata, element.ElementParsedObject, element.ElementID)] ? format(new Date(defaultData[getCondition(!metadata ? element.ElementParsedObject : element.ElementID)]), 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd');
        break;

      case 'NumberField':
        InitialValues[element.ElementID] = defaultData[getCondition(!metadata, element.ElementParsedObject, element.ElementID)] || 0;
        break;

      case 'FileField':
        InitialValues[element.ElementID] = getCondition(files && files.length > 0 && files.find((f) => f.id === element.ElementID), files.find((f) => f.id === element.ElementID).files[0].fileB64, '');
        break;

      case 'TableField':
        InitialValues[element.ElementID] = getCondition(metadata && defaultData[element.ElementID], defaultData[element.ElementID], []);

        if (!metadata && isArray(defaultData[element.ElementParsedObject])) {
          defaultData[element.ElementParsedObject].forEach((value) => {
            const obj = {};
            element.ElementList.forEach((column) => {
              obj[column.ElementID] = getCondition(column.ElementParsedObject, value[column.ElementParsedObject], value[column.ElementID]);

              if (column.ElementType === 'DynamicCollection') {
                obj[column.ElementID] = getCondition(dynamicCollection[column.ElementDynamicCode].Collection.find((t) => t.text === obj[column.ElementID]), dynamicCollection[column.ElementDynamicCode].Collection.find((t) => t.text === obj[column.ElementID]), null);
              }
            });
            InitialValues[element.ElementID].push(obj);
          });
        } else {
          element.ElementList.forEach((column) => {
            if (column.ElementType === 'DynamicCollection') {
              // old table data structure
              if (InitialValues[element.ElementID].tableData) {
                InitialValues[element.ElementID].tableData.forEach((item) => {
                  item[column.ElementID] = getCondition(dynamicCollection[column.ElementDynamicCode].Collection.find((t) => t.value === item[column.ElementID]), dynamicCollection[column.ElementDynamicCode].Collection.find((t) => t.value === item[column.ElementID]), null);
                });
              } else {
                // if it is an object then i dont need to update these object
                if (InitialValues[element.ElementID].length > 0 && typeof InitialValues[element.ElementID][0][column.ElementID] !== 'object') {
                  InitialValues[element.ElementID].forEach((item) => {
                    item[column.ElementID] = getCondition(dynamicCollection[column.ElementDynamicCode].Collection.find((t) => t.text === item[column.ElementID]), dynamicCollection[column.ElementDynamicCode].Collection.find((t) => t.text === item[column.ElementID]), null);
                  });
                }
              }
            } else {
              //old table data structure
              if (InitialValues[element.ElementID].tableData) {
                InitialValues[element.ElementID].tableData.forEach((item) => {
                  item[column.ElementID] = getCondition(item[column.ElementID] && typeof item[column.ElementID] === 'object', item[column.ElementID].ElementID, item[column.ElementID]);
                });
              }
            }
          });
        }

        if (InitialValues[element.ElementID].tableID) {
          InitialValues[element.ElementID] = InitialValues[element.ElementID].tableData;
        }

        InitialValues[element.ElementID].forEach((item, index) => {
          item.id = (index + 1).toString();
        });

        break;

      default:
        InitialValues[element.ElementID] = defaultData[getCondition(!metadata, element.ElementParsedObject, element.ElementID)] || '';
        break;
    }
  });

  // Calculated Fields
  surveyData.filter(item => item.ElementCalculatedField).forEach(item => {
    InitialValues[item.ElementID] = calculateValue({ surveyData: data }, item, InitialValues);
  });

  return InitialValues;
};

export const parseDataToSave = (surveyData, templateStructure, files) => {
  const values = { ...surveyData };

  templateStructure.forEach((element) => {
    switch (element.ElementType) {
      case 'MultipleDynamicCollection':
        values[element.ElementID] = [];

        surveyData[element.ElementID].forEach((item) => {
          values[element.ElementID].push(item.value);
        });

        break;

      case 'DynamicCollection':
        values[element.ElementID] = getCondition(values[element.ElementID] && values[element.ElementID] != null, values[element.ElementID].value, values[element.ElementID]);

        if (values[element.ElementID] && values[element.ElementID] != null && element.ElementConditional && element.ElementConditional.length > 0) {
          element.ElementConditional.forEach((elementConditional) => {
            if (surveyData[element.ElementID] !== null && elementConditional.Value === surveyData[element.ElementID].key) {
              values[elementConditional.ElementID] = values[elementConditional.ElementID]?.value;
            } else {
              values[elementConditional.ElementID] = null;
            }
          });
        }

        break;

      case 'FileField':
        values[element.ElementID] = '';
        break;

      case 'SelectList':
        values[element.ElementID] = values[element.ElementID]?.ElementValue;
        break;

      case 'TextField':
        values[element.ElementID] = values[element.ElementID]?.trimEnd();
        break;

      case 'TextAreaField':
        values[element.ElementID] = values[element.ElementID]?.trimEnd();
        break;

      case 'TableField':
        // Dynamic Collection get just value
        element.ElementList.forEach((column) => {
          if (column.ElementType === 'DynamicCollection') {
            values[element.ElementID].forEach((row) => {
              if (row[column.ElementID]) {
                row[column.ElementID] = row[column.ElementID].text;
              }
            });
          }
        });
        break;

      default:
        break;
    }
  });

  if (values.hiringOfferStatusForm) {
    values.hiringOfferStatusForm = values.hiringOfferStatusForm.value;
  }

  const interviewers = [];

  if (values.hiringOfferInterviewer && values.hiringOfferInterviewer.length > 0) {
    values.hiringOfferInterviewer.forEach(interviewer => {
      interviewers.push(interviewer.evaluaterID);
    });
  }

  const filesQueue = files.filter((f) => f.wasModified).map((f) => ({ id: f.id, file: surveyData[f.id].replace('data:application/pdf;base64,', '') }));

  return {
    Metadata: JSON.stringify(values),
    CandidateName: values.AbbreviatedName,
    HiringOfferInterviewer: interviewers,
    hiringOfferStatusForm: values.hiringOfferStatusForm,
    HiringOfferInterviewDate: values.hiringOfferInterviewDate,
    files: filesQueue
  };
};
