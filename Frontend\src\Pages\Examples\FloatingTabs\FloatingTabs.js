import React, { useState, useCallback, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Box, Grid, Alert, AlertTitle, LinearProgress } from "@mui/material";
import toast from "react-hot-toast";
import { Formik } from "formik";
import EvaluationForm from "../../../Components/FloatingTab/ApplicationExample/EvaluationForm";
import LinearProgressWithLabel from "../../../Components/FloatingTab/ApplicationExample/LinearProgressWithLabel";
import AvatarMedal from "../../../Components/Avatar/AvatarMedal";
import { useParams } from "react-router";
import { evaluationAPI } from "../../../API/evaluationAPI";
import palette from "../../../Assets/theme/palette";
import { groupByKey } from "../../../Utils/collectionUtils";
import { getValidationSchema } from "../../../Components/FloatingTab/ApplicationExample/getValidationSchema";
import CustomStepper from "../../../Components/FloatingTab/ApplicationExample/CustomStepper";

const FloatingTabs = () => {
  let refFormik = React.createRef();
  const params = useParams();
  const navigate = useNavigate();
  const [progressPercentage, setProgressPercentage] = useState(0);
  const [username, setUsername] = useState("");
  const [floatingTabInitValues, setFloatingTabInitValues] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [floatingTabOpen, setFloatingTabOpen] = useState(true);
  const [steps, setSteps] = useState([]);
  const [kpidata, setKpidata] = useState({});
  const [quizStatus, setQuizStatus] = useState();
  const [calculateMetrics, setCalculateMetrics] = useState(false);
  const [canEdit, setCanEdit] = useState();
  const [formData, setFormData] = useState([]);
  const [sectionTabs, setSectionTabs] = useState([]);
  const [sections, setSections] = useState([]);
  const [metrics, setMetrics] = useState([]);
  const [
    requestCalculationQuizSurvey_timeout,
    setRequestCalculationQuizSurvey_timeout,
  ] = useState(false);
  const [validationSchema, setValidationSchema] = useState({});
  const [initialValues, setInitialValues] = useState({});
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [isLoading, setLoading] = useState(true);

  if (React === 123456) {
    console.log(steps);
    console.log(unsavedChanges);
  }

  const statusOptions = [
    { text: "Pendente", key: "P", color: palette.states.pending },
    { text: "A Decorrer", key: "D", color: palette.states.ongoing },
    { text: "Por Assinar", key: "Z", color: palette.states.pending },
    { text: "Terminada", key: "T", color: palette.states.success },
    { text: "Submetida", key: "S", color: palette.states.submitted },
  ];

  //Handle previous status options
  const handleGetStatus = (status) => {
    return statusOptions.find((x) => x.key === status)
      ? statusOptions.find((x) => x.key === status)
      : statusOptions.find((x) => x.key === "P");
  };

  const canSaveForm = () => {
    return quizStatus?.key === "P" || quizStatus?.key === "D";
  };

  if (React === 123456) {
    console.log(canSaveForm);
  }

  const getFormInitialValues = (form) => {
    const InitialValues = {};
    form.forEach((question) => {
      InitialValues[question.quizSurveyID] = question.currentValue
        ? question.currentValue.toString()
        : "";
    });
    InitialValues.note =
      metrics && metrics.length > 0
        ? metrics.find((x) => x.title === "Resultado Global")?.note
        : "";
    return InitialValues;
  };

  const handleUpdateQuizAnswer = (question, value) => {
    const newFormData = [...formData];
    const findQuestion = newFormData.filter(
      (x) =>
        x.quizQuestionID === question.quizQuestionID &&
        x.quizSurveyID === question.quizSurveyID
    )[0];
    findQuestion.currentValue = value?.toString()?.toUpperCase();
    setFormData(newFormData);
    refFormik.setFieldValue(
      question.quizSurveyID,
      value ? value.toString() : ""
    );
    setUnsavedChanges(true);
  };

  const handleUpdateQuizYesOrNo = (question, value) => {
    const newFormData = [...formData];
    const findQuestion = newFormData.filter(
      (x) =>
        x.quizQuestionID === question.quizQuestionID &&
        x.quizSurveyID === question.quizSurveyID
    )[0];
    findQuestion.currentValue = value;
    const sectionsGroup = groupByKey(newFormData, "questionGroupTitle");
    setSectionTabs(sectionsGroup?.map((s) => s[0]?.questionGroupTitle));
    setSections(sectionsGroup);
    setFormData(newFormData);
    refFormik.setFieldValue(
      question.quizSurveyID,
      value ? value.toString() : ""
    );
    setUnsavedChanges(true);
  };

  const getEvaluationDetails = useCallback(async () => {
    try {
      const data = await evaluationAPI.getQuizSurvey({
        ratingQuizID: params.id,
        type: "TYPE_PERFORMANCE",
      });
      setLoading(false);
      if (!data.error) {
        setKpidata(data.value.detailedKPIs);
        setFormData(data.value.quizSurveyResumeList);
        const sectionsGroup = groupByKey(
          data.value.quizSurveyResumeList,
          "questionGroupTitle"
        );
        setSectionTabs(sectionsGroup?.map((s) => s[0]?.questionGroupTitle));
        setSections(sectionsGroup);
        setSteps(data.value.steps);
        setCalculateMetrics(data.value.calculateMetrics);
        if (data.value.quizSurveyResumeList.length > 0) {
          setQuizStatus(
            handleGetStatus(
              data.value.quizSurveyResumeList[0].formCompleted
                ? "S"
                : data.value.quizSurveyResumeList[0].quizStatus
            )
          );
          setCanEdit(
            data.value.quizSurveyResumeList[0].quizStatus === "P" ||
            data.value.quizSurveyResumeList[0].quizStatus === "D"
          );
          setUsername(data.value.quizSurveyResumeList[0].toEntity);
          let _validationSchema = data.value.quizSurveyResumeList.map(
            (question) => {
              return {
                ElementID: question.quizSurveyID,
                ElementRequired: question.mandatory_Answer,
                ElementType: question.questionType,
              };
            }
          );
          _validationSchema = getValidationSchema(_validationSchema);
          setValidationSchema(_validationSchema);
          setInitialValues(
            getFormInitialValues(data.value.quizSurveyResumeList)
          );
        }
        setLoadingData(false);
      } else {
        toast.error(
          <>
            {`Formulário inexistente, desconhecido ou interdito`}
            {params.user && (
              <>
                :<br />
                {params.user}
              </>
            )}
          </>
        );
        navigate(-1);
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  const getAnswers = (_save = false) => {
    const answers = [];
    for (const question of formData) {
      const defaultValue = null;
      const value = question.currentValue || defaultValue;
      if ((value == null || value) && !question.readOnly) {
        answers.push({
          QuizSurveyID: question.quizSurveyID,
          RatingQuizID: params.id,
          Value: value === null ? 0 : value,
          Comments: question.currentComments,
          files: _save ? question.files : [],
        });
      }
    }
    return answers;
  };

  const handleCalculateQuizSurvey = async (_refFormik) => {
    const answers = getAnswers();
    const data = {
      RatingEntityQuizID: params.id,
      Type: "TYPE_PERFORMANCE",
      QuizSurveyAnswers: answers,
      Status: 0,
    };
    const result = await evaluationAPI.calculateQuizSurvey(data);
    if (result && !result.error) {
      setMetrics(result.value);
      if (_refFormik) {
        _refFormik.setFieldValue(
          "note",
          result.value.find((x) => x.title === "Resultado Global")?.note
        );
      }
    }
  };

  const requestCalculationQuizSurvey = () => {
    if (!calculateMetrics) {
      return;
    }
    setFloatingTabInitValues(true);
    if (requestCalculationQuizSurvey_timeout) {
      clearTimeout(requestCalculationQuizSurvey_timeout);
    }
    const requestTimeout = setTimeout(
      (_refFormik) => {
        handleCalculateQuizSurvey(_refFormik);
        setRequestCalculationQuizSurvey_timeout(false);
      },
      1000,
      refFormik
    );
    setRequestCalculationQuizSurvey_timeout(requestTimeout);
  };

  useEffect(() => {
    const _progressPerc =
      (formData.filter((x) => x.mandatory_Answer && x.currentValue !== null)
        .length /
        formData.filter((x) => x.mandatory_Answer).length) *
      100;
    setProgressPercentage(_progressPerc);
    if (
      formData &&
      formData.length > 0 &&
      (floatingTabOpen || !floatingTabInitValues)
    ) {
      requestCalculationQuizSurvey();
    }
  }, [formData]);

  useEffect(() => {
    handleCalculateQuizSurvey();
  }, [calculateMetrics]);

  useEffect(() => {
    getEvaluationDetails();
  }, []);

  return (
    <>
      {isLoading && (
        <LinearProgress
          variant="indeterminate"
          // top must be header min height
          sx={{ width: "100%", position: "absolute", top: "64px", left: 0 }}
        />
      )}
      <Box sx={{ p: "24px 32px 0px 32px" }}>
        {" "}
        <Grid
          item
          xs={12}
        >
          <AvatarMedal
            image=""
            label={username}
            alt={username}
            textPosition="bottom"
          />
          {canEdit && <LinearProgressWithLabel progress={progressPercentage} />}
          {kpidata && kpidata.length > 0 ? (
            <Box sx={{ mt: 2, mb: 2 }}>
              <CustomStepper
                steps={kpidata}
                showQualitative
              />
            </Box>
          ) : null}
          <Formik
            innerRef={(p) => (refFormik = p)}
            validateOnChange={false}
            initialValues={initialValues}
            enableReinitialize
            validationSchema={validationSchema}
            onSubmit={async (values) => {
              formData
                .filter(
                  (x) =>
                    x.mandatoryConditional && x.mandatoryConditional.length > 0
                )
                .forEach((question) => {
                  question.mandatoryConditional.forEach((q) => {
                    if (
                      values[q.elementID] === "" &&
                      values[q.when] &&
                      q.is.includes(values[q.when])
                    ) {
                      refFormik.setFieldError(
                        q.elementID,
                        "Este campo é obrigatório"
                      );
                    }
                  });
                });
            }}
          >
            {({ errors }) => (
              <form autoComplete="nope">
                {errors && Object.keys(errors).length > 0 && (
                  <Box sx={{ mt: 2, mb: 2 }}>
                    <Alert severity="error">
                      <AlertTitle>
                        <b>Existem erros de validação</b>
                      </AlertTitle>
                      Por favor corriga antes de avançar
                    </Alert>
                  </Box>
                )}
                <EvaluationForm
                  loadingData={loadingData}
                  canEdit={canEdit}
                  sectionTabs={sectionTabs}
                  sections={sections}
                  metrics={metrics}
                  handleUpdateQuizAnswer={handleUpdateQuizAnswer}
                  handleUpdateQuizYesOrNo={handleUpdateQuizYesOrNo}
                  setFloatingTabOpen={setFloatingTabOpen}
                  requestCalculationQuizSurvey={requestCalculationQuizSurvey}
                  requestCalculationQuizSurvey_timeout={
                    requestCalculationQuizSurvey_timeout
                  }
                  errors={errors}
                />
                {errors && Object.keys(errors).length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Alert severity="error">
                      <AlertTitle>
                        <b>Existem erros de validação</b>
                      </AlertTitle>
                      Por favor corriga antes de avançar
                    </Alert>
                  </Box>
                )}
              </form>
            )}
          </Formik>
        </Grid>
      </Box>
    </>
  );
};

export default FloatingTabs;
