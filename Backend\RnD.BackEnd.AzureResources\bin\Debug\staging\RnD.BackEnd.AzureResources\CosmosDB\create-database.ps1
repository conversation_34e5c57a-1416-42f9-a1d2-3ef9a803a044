#az account set --subscription ""

$resourceGroupName = "rg-bi4all-framework"
$cosmosAccountName = "rnd-cosmos-frmk"
$databaseName = "FrameworkDB"
$modelsContainerName = "Models"
$modelsPartitionKeyPath = "/modelName"

# Create Cosmos DB
az cosmosdb sql database create `
    --account-name $cosmosAccountName `
    --name $databaseName `
    --resource-group $resourceGroupName

# Create Models Container
az cosmosdb sql container create `
    --account-name $cosmosAccountName `
    --database-name $databaseName `
    --name $modelsContainerName `
    --partition-key-path $modelsPartitionKeyPath `
    --throughput 1000 `
    --resource-group $resourceGroupName
