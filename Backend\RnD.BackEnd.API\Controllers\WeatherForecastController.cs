﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Weather;
using RnD.BackEnd.API.SwashbuckleExamples;
using RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController;
using RnD.BackEnd.Domain.Constants;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Entities.Weather;
using RnD.BackEnd.Domain.Interfaces.Services;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.Controllers
{
    /// <summary>
    /// Weather forecast controller
    /// </summary>
    /// <seealso cref="Microsoft.AspNetCore.Mvc.ControllerBase" />
    [Produces("application/json")]
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Policy = AuthenticationPolicy.AZURE_AD)]
    public class WeatherForecastController : ControllerBase
    {
        private readonly IWeatherService _weatherService;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="WeatherForecastController"/> class.
        /// </summary>
        /// <param name="weatherService">The weather service.</param>
        /// <param name="mapper">The mapper.</param>
        public WeatherForecastController(IWeatherService weatherService, IMapper mapper)
        {
            _weatherService = weatherService;
            _mapper = mapper;
        }

        /// <summary>
        /// Creates a new weather
        /// </summary>
        /// <param name="request">The new weather object</param>
        /// <returns>The newly created weather</returns>
        /// <response code="201">Returns the newly created weather</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpPost]
        [ProducesResponseType(typeof(ApiOutput<WeatherDto>), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(CreateWeatherDto), typeof(CreateWeather))]
        [SwaggerResponseExample((int)HttpStatusCode.Created, typeof(Weather201))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> AddAsync([FromBody] CreateWeatherDto request)
        {
            Weather weather = _mapper.Map<CreateWeatherDto, Weather>(request);

            ServiceOutput<Weather> response = await _weatherService.AddAsync(weather, HttpContext.User.Identity.Name);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Weather>, ApiOutput<WeatherDto>>(response));
        }

        /// <summary>
        /// Test UoW transaction
        /// </summary>
        /// <returns>
        /// The newly created weathers
        /// </returns>
        /// <response code="201">Returns the newly created weathers</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpPost]
        [Route("transaction")]
        [ProducesResponseType(typeof(ApiOutput<List<WeatherDto>>), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.Created, typeof(WeatherList201))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> AddWithTransactionSample()
        {
            ServiceOutput<List<Weather>> response = await _weatherService.AddWithTransactionSample(HttpContext.User.Identity.Name);

            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<List<Weather>>, ApiOutput<List<WeatherDto>>>(response));
        }

        /// <summary>
        /// Updates a weather
        /// </summary>
        /// <param name="request">The weather to update</param>
        /// <param name="id">The weather identifier</param>
        /// <param name="rowversion">The latest write tag (in concurrency cases, to avoid overwriting the existing document)</param>
        /// <returns>The updated weather</returns>
        /// <response code="200">Returns the newly created weathers</response>
        /// <response code="400">Malformed request</response>
        /// <response code="404">Weather not found</response>
        /// <response code="409">Conflict on the request</response>
        /// <response code="500">Internal error</response>
        [HttpPut]
        [Route("{id}")]
        [ProducesResponseType(typeof(ApiOutput<WeatherDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status409Conflict)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(WeatherDto), typeof(UpdateWeather))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(Weather200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.NotFound, typeof(ResponseExample404))]
        [SwaggerResponseExample((int)HttpStatusCode.Conflict, typeof(ResponseExample409))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> UpdateAsync([FromBody] WeatherDto request, [FromRoute] Guid id, [FromQuery] string rowversion)
        {
            Weather weather = _mapper.Map<WeatherDto, Weather>(request);
            ServiceOutput<Weather> response = await _weatherService.UpdateAsync(weather, id, HttpContext.User.Identity.Name, rowversion);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Weather>, ApiOutput<WeatherDto>>(response));
        }

        /// <summary>
        /// Updates a weather
        /// </summary>
        /// <param name="id">The weather identifier</param>
        /// <param name="request">The changes</param>
        /// <returns>
        /// The updated weather
        /// </returns>
        /// <response code="200">Returns the updated weather</response>
        /// <response code="400">Malformed request</response>
        /// <response code="404">Weather not found</response>
        /// <response code="500">Internal error</response>
        [HttpPatch]
        [Route("{id}")]
        [ProducesResponseType(typeof(ApiOutput<WeatherDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerRequestExample(typeof(JsonPatchDocument<WeatherDto>), typeof(PatchWeather))]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(Weather200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.NotFound, typeof(ResponseExample404))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> PatchAsync([FromRoute] Guid id, [FromBody] JsonPatchDocument<WeatherDto> request)
        {
            JsonPatchDocument<Weather> changes = _mapper.Map<JsonPatchDocument<WeatherDto>, JsonPatchDocument<Weather>>(request);

            ServiceOutput<Weather> response = await _weatherService.UpdateAsync(changes, id, HttpContext.User.Identity.Name);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Weather>, ApiOutput<WeatherDto>>(response));
        }

        /// <summary>
        /// Deletes a weather
        /// </summary>
        /// <param name="id">The weather identifier</param>
        /// <returns></returns>
        /// <response code="204">The weather was deleted</response>
        /// <response code="400">Malformed request</response>
        /// <response code="500">Internal error</response>
        [HttpDelete]
        [Route("{id}")]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> DeleteAsync([FromRoute] Guid id)
        {
            ServiceOutput<object> response = await _weatherService.DeleteAsync(id, HttpContext.User.Identity.Name);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<object>, ApiOutput<WeatherDto>>(response));
        }

        /// <summary>
        /// Gets a weather by ID
        /// </summary>
        /// <param name="id">The weather identifier</param>
        /// <returns>The weather</returns>
        /// <response code="200">Returns a weather</response>
        /// <response code="400">Malformed request</response>
        /// <response code="404">Weather not found</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [Route("{id}")]
        [ProducesResponseType(typeof(ApiOutput<WeatherDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(Weather200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.NotFound, typeof(ResponseExample404))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> GetAsync([FromRoute] Guid id)
        {
            ServiceOutput<Weather> response = await _weatherService.GetByIdAsync(id);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<Weather>, ApiOutput<WeatherDto>>(response));
        }

        /// <summary>
        /// Gets all weathers with custom filters
        /// </summary>
        /// <param name="id">Weather identifier</param>
        /// <param name="location">Location</param>
        /// <param name="itemsPerPage">The number of items per page.</param>
        /// <param name="page">The page.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// A list of weather forecasts
        /// </returns>
        /// <response code="200">Returns a list of weathers</response>
        /// <response code="400">Malformed request</response>
        /// <response code="404">Weather not found</response>
        /// <response code="500">Internal error</response>
        [HttpGet]
        [ProducesResponseType(typeof(IList<WeatherDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiOutput<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(Logging.ServiceOutput), StatusCodes.Status500InternalServerError)]
        [SwaggerResponseExample((int)HttpStatusCode.OK, typeof(WeatherList200))]
        [SwaggerResponseExample((int)HttpStatusCode.BadRequest, typeof(ResponseExample400))]
        [SwaggerResponseExample((int)HttpStatusCode.NotFound, typeof(ResponseExample404))]
        [SwaggerResponseExample((int)HttpStatusCode.InternalServerError, typeof(ResponseExample500))]
        public async Task<IActionResult> ListAsync(
            [FromQuery] Guid id,
            [FromQuery] string location,
            [FromQuery] int? itemsPerPage,
            [FromQuery] int? page,
            [FromQuery] string sortField,
            [FromQuery] bool sortAscending,
            CancellationToken cancellationToken)
        {
            ServiceOutput<IList<Weather>> response = await _weatherService.ListAsync(id, location, itemsPerPage, page, sortField, sortAscending, cancellationToken);
            return StatusCode((int)response.Code, _mapper.Map<ServiceOutput<IList<Weather>>, ApiOutput<IList<WeatherDto>>>(response));
        }
    }
}