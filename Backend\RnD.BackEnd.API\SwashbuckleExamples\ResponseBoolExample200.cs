﻿using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples
{
    /// <summary>
    /// Bool response example 200
    /// </summary>
    public class ResponseBoolExample200 : IExamplesProvider<ApiOutput<bool>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 for a bool response.
        /// </returns>
        public ApiOutput<bool> GetExamples()
        {
            return new ApiOutput<bool>
            {
                Code = HttpStatusCode.OK,
                Description = null,
                Value = true,
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}
