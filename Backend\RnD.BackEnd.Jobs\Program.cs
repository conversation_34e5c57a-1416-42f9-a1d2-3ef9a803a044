using System;
using System.Collections.Generic;
using Hangfire;
using Hangfire.Dashboard;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using RnD.BackEnd.Domain.Extensions;
using RnD.BackEnd.Domain.Settings;
using RnD.BackEnd.Jobs.Extensions;
using RnD.BackEnd.Jobs.LicenseValidation;
using RnD.BackEnd.Licensing.Interfaces;

const string JobsEnginePolicyName = "RnDBackendTemplateJobsEngine";

var builder = WebApplication.CreateBuilder();

// Configure settings
AppSettings appSettings = builder.Services.ConfigureAppSettings(builder.Configuration);
ConnectionStrings connectionStrings = builder.Services.ConfigureConnectionStrings(builder.Configuration);

// Configure data access layer
builder.Services.ConfigureInfrastructure(appSettings);

// Configure business services
builder.Services.ConfigureBusinessServices();

// Configure license validation
builder.Services.ConfigureJobsLicenseValidation();

// Configure Hangfire
builder.Services.ConfigureHangfire(builder.Configuration, connectionStrings, JobsEnginePolicyName);

// Automapper
builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseAuthentication();
app.UseRouting();
app.UseAuthorization();
app.UseHangfireDashboard();

app.UseEndpoints(endpoints =>
{
    endpoints.MapHangfireDashboard("", new DashboardOptions()
    {
        DashboardTitle = "Hangfire - Backend Framework",
        //Authorization = new List<IDashboardAuthorizationFilter> { }
        AsyncAuthorization = new List<IDashboardAsyncAuthorizationFilter>()
        {
            new DashboardAuthorizationFilter(
                app.Services.GetService<ILicenseValidationService>(),
                app.Services.GetService<IMemoryCache>())
        }
    })
    .RequireAuthorization(JobsEnginePolicyName);
});

// Configure recurring jobs
app.ConfigureRecurringJobs();

app.Run();
