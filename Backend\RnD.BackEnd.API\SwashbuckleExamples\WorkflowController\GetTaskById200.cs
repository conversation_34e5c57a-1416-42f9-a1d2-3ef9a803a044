﻿using System;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Workflow;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// Get Task by id response example for 200
    /// </summary>
    public class GetTaskById200 : IExamplesProvider<ApiOutput<WorkflowTaskDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 for a task by id response.
        /// </returns>
        public ApiOutput<WorkflowTaskDto> GetExamples()
        {
            return new ApiOutput<WorkflowTaskDto>
            {
                Code = HttpStatusCode.OK,
                Description = null,
                Value = new WorkflowTaskDto()
                {
                    Id = Guid.Parse("4f352192-8c29-456b-ab27-d6523c6fb3cd"),
                    WorkflowInstanceId = Guid.Parse("8ccf46e1-9305-4017-80ad-217c15785947"),
                    StepId = Guid.Parse("9c5cdeb0-60a7-4eff-a312-21f4075434b2"),
                    UserId = "<EMAIL>",
                    TaskStatus = "FORWARDED",
                    TaskCompleted = true,
                    TaskActivated = true,
                    Comments = "Forwarded using framework",
                    SourceId = "Project/2023",
                    SourceUrl = null,
                    WorkflowStatus = "IN_PROGRESS",
                    WorkflowName = "Not Billable Project Approval",
                    StepName = "Approval",
                    StepOrder = 1,
                    SysModifyDate = Convert.ToDateTime("2023-02-06T12:05:48.617+00:00"),
                    TotalRows = 0,
                    UserAllowedToEdit = false
                }
            };
        }
    }
}
