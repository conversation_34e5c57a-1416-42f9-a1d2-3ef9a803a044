import handleMethod from "../Utils/APIutils/handleMethod";

class OpportunityAPI {
  async getOpportunities(oid, pid, status) {
    oid = oid ? `?HiringOfferID=${oid}` : '';
    pid = pid ? `?ProcessID=${pid}` : '';
    status = status ? `HiringOfferStatus=${status}` : '';

    const Input = {
      type: 'GET',
      method: `getOpportunities${oid}${pid}${status}`,
      params: {}
    };

    const response = await handleMethod(Input);

    return response;
  }

  async getAllOpoturnitiesTypes() {
    const Input = {
      type: 'GET',
      method: `getAllOpoturnitiesTypes`,
      params: {}
    };

    const response = await handleMethod(Input);

    return response;
  }
}

export const opportunityAPI = new OpportunityAPI();
