﻿using System;
using System.ComponentModel.DataAnnotations;

namespace RnD.BackEnd.API.Models
{
    public class ConsumoSubscricao
    {
        [Key]
        public int ID { get; set; }

        [Required]
        [MaxLength(255)]
        public string SubscriptionID { get; set; }

        [Required]
        [MaxLength(255)]
        public string ResourceGroup { get; set; }

        [Required]
        [MaxLength(255)]
        public string ResourceName { get; set; }

        [Required]
        [MaxLength(255)]
        public string ResourceLocation { get; set; }

        [Required]
        public decimal Cost { get; set; }

        [Required]
        public decimal CostUSD { get; set; }

        [Required]
        [MaxLength(3)]
        public string Currency { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        public virtual Subscription Subscription { get; set; }
    }
}
