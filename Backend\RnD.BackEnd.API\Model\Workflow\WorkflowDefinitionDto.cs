﻿using System;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Workflow definition DTO class
    /// </summary>
    public class WorkflowDefinitionDto
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public Guid? Id { get; set; }

        /// <summary>
        /// Gets or sets the display name.
        /// </summary>
        /// <value>
        /// The display name.
        /// </value>
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the type of the rules condition.
        /// </summary>
        /// <value>
        /// The type of the rules condition.
        /// </value>
        public string RulesConditionType { get; set; }

        /// <summary>
        /// Gets or sets the type of the source.
        /// </summary>
        /// <value>
        /// The type of the source.
        /// </value>
        public string SourceType { get; set; }

        /// <summary>
        /// Gets or sets the type of the workflow.
        /// </summary>
        /// <value>
        /// The type of the workflow.
        /// </value>
        public string WorkflowType { get; set; }

        /// <summary>
        /// Gets or sets the purpose.
        /// </summary>
        /// <value>
        /// The purpose.
        /// </value>
        public string Purpose { get; set; }

        /// <summary>
        /// Gets or sets the number of steps.
        /// </summary>
        /// <value>
        /// The number of steps.
        /// </value>
        public int NumberOfSteps { get; set; }

        /// <summary>
        /// Gets or sets the number of rules.
        /// </summary>
        /// <value>
        /// The number of rules.
        /// </value>
        public int NumberOfRules { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this instance is active.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is active; otherwise, <c>false</c>.
        /// </value>
        public bool IsActive { get; set; }
    }
}
