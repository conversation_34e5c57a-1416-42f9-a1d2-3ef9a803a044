import { useState } from 'react';
import PropTypes from 'prop-types';
import { Box, TextField, Typography } from '@mui/material';
import Modal from '../ModalTemplate';

const TableRemoveModal = (props) => {
  const { selectedProcess, onClose, open } = props;
  const [deleteReason, setReason] = useState('');
  const [isSubmitting] = useState(false);

  const handleChange = (event) => {
    setReason(event.target.value);
  };

  return (
    <Modal
      onClose={onClose}
      open={open}
      title={`Arquivar processo${(selectedProcess.length > 1 ? 's' : '')}`}
      isSubmitting={isSubmitting}
      onCancel={onClose}
      content={
        <Box>
          <Typography
            align="center"
            color="textSecondary"
            variant="subtitle2"
          >
            {`Tem a certeza que deseja arquivar o${(selectedProcess.length > 1 ? 's' : '')} processo${(selectedProcess.length > 1 ? 's' : '')} seleccionado${(selectedProcess.length > 1 ? 's' : '')} ?`}
          </Typography>
          <Box sx={{ mt: 3 }}>
            <TextField
              autoFocus
              FormHelperTextProps={{
                sx: {
                  textAlign: 'right',
                  mr: 0
                }
              }}
              fullWidth
              helperText={`${200 - deleteReason.length} characters left`}
              label="Motivo"
              multiline
              onChange={handleChange}
              placeholder="Por questões de auditoria poderá especificar o motivo da arquivação do processo..."
              rows={5}
              value={deleteReason}
              variant="outlined"
            />
          </Box>
        </Box>
      }
    />
  );
};

TableRemoveModal.propTypes = {
  selectedProcess: PropTypes.array,
  onClose: PropTypes.func,
  open: PropTypes.bool.isRequired,
};

export default TableRemoveModal;
