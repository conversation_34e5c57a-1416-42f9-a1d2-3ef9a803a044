﻿using Microsoft.Extensions.DependencyInjection;
using RnD.BackEnd.Licensing.Interfaces;
using RnD.BackEnd.Licensing.Services;

namespace RnD.BackEnd.Licensing.Extensions
{
    /// <summary>
    /// Startup extensions for licensing setup
    /// <para>Careful when referencing this project due to <strong>System.IdentityModel.Tokens.Jwt (6.27.0)</strong></para>
    /// </summary>
    public static class StartupExtensions
    {
        /// <summary>
        /// Configures the license validation.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <returns>
        /// The service collection.
        /// </returns>
        public static IServiceCollection ConfigureLicenseValidation(this IServiceCollection services)
        {
            services.AddTransient<ILicenseValidationService, LicenseValidationService>();
            return services;
        }
    }
}
