﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire.Console.Extensions" Version="1.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.0" />
    <PackageReference Include="Polly" Version="8.2.0" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.6.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RnD.BackEnd.Domain\RnD.BackEnd.Domain.csproj" />
    <ProjectReference Include="..\RnD.BackEnd.Email\RnD.BackEnd.Email.csproj" />
  </ItemGroup>

</Project>
