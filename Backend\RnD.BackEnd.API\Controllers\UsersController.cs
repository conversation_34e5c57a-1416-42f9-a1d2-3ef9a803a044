﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RnD.BackEnd.API.Controllers
{
    [Route("api/users")]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public UsersController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/users
        [HttpGet]
        public async Task<ActionResult<IEnumerable<User>>> GetUsers()
        {
            return await _context.User.ToListAsync();
        }

        // GET: api/users/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<User>> GetUser(int id)
        {
            var user = await _context.User.FindAsync(id);
            if (user == null)
                return NotFound();
            return user;
        }

        // POST: api/users
        [HttpPost]
        public async Task<ActionResult<User>> <PERSON><PERSON><PERSON><PERSON>(User user)
        {
            _context.User.Add(user);
            await _context.SaveChangesAsync();
            return CreatedAtAction(nameof(GetUser), new { id = user.UserID }, user);
        }

        // PUT: api/users/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(int id, User user)
        {
            if (id != user.UserID)
                return BadRequest();

            // Verifica se o user existe antes de tentar atualizar
            var existingUser = await _context.User.FindAsync(id);
            if (existingUser == null)
                return NotFound();

            // Atualiza apenas as propriedades permitidas
            existingUser.Username = user.Username;
            existingUser.Email = user.Email;
            
            // Se tiver outros campos que podem ser atualizados, adicione aqui
            // Exemplo: existingUser.Role = user.Role;
            
            // Marca a entidade como modificada
            _context.Entry(existingUser).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!UserExists(id))
                    return NotFound();
                else
                    throw;
            }

            return NoContent();
        }

        // DELETE: api/users/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            var user = await _context.User.FindAsync(id);
            if (user == null)
                return NotFound();

            _context.User.Remove(user);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        // POST: api/users/update/{id}
        [HttpPost("update/{id}")]
        public async Task<ActionResult<User>> UpdateUserAlternative(int id, [FromForm] string Username, [FromForm] string Email)
        {
            // Verifica se o user existe antes de tentar atualizar
            var user = await _context.User.FindAsync(id);
            if (user == null)
                return NotFound();
            
            // Atualiza apenas as propriedades permitidas
            user.Username = Username;
            user.Email = Email;
            // AzureId permanece o mesmo
            
            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!UserExists(id))
                    return NotFound();
                else
                    throw;
            }

            // Retorna o user atualizado com o mesmo ID
            return user;
        }
        
        private bool UserExists(int id)
        {
            return _context.User.Any(e => e.UserID == id);
        }
    }
}
