﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseName>PortalCloudService</TargetDatabaseName>
    <DeployScriptFileName>RnD.BackEnd.Database.sql</DeployScriptFileName>
    <TargetConnectionString>Data Source=portal-cloud-services.database.windows.net;Persist Security Info=False;User ID=PortalCloudService;Pooling=False;Multiple Active Result Sets=False;Connect Timeout=60;Encrypt=True;Trust Server Certificate=False;Command Timeout=0</TargetConnectionString>
    <ProfileVersionNumber>1</ProfileVersionNumber>
  </PropertyGroup>
</Project>