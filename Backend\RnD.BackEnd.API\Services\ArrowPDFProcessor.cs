using Microsoft.Extensions.Logging;
using RnD.BackEnd.API.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace RnD.BackEnd.API.Services
{
    public class ArrowPDFProcessor : IPDFProcessor
    {
        private readonly ILogger<ArrowPDFProcessor> _logger;
        private readonly LogProcessamentoService _logService;
        private readonly IClienteService _clienteService;

        public string TipoProcessador => "Arrow";

        public ArrowPDFProcessor(ILogger<ArrowPDFProcessor> logger, LogProcessamentoService logService, IClienteService clienteService)
        {
            _logger = logger;
            _logService = logService;
            _clienteService = clienteService;
        }

        public bool PodeProcessar(string textoCompleto)
        {
            // Verificar se é PDF Arrow com múltiplos indicadores
            var indicadoresArrow = new[]
            {
                "ARROW",
                "ARROWECS PORTUGAL",
                "ARROWECS",
                "Arrow ECS"
            };
            
            var padroesArrow = new[]
            {
                new Regex(@"De\s+\d{2}/\d{2}/\d{2,4}\s+a\s+\d{2}/\d{2}/\d{2,4}", RegexOptions.IgnoreCase),
                new Regex(@"Período\s+de\s+faturação", RegexOptions.IgnoreCase),
                new Regex(@"Total\s+neto", RegexOptions.IgnoreCase),
                new Regex(@"Envio\s+a\s+cliente", RegexOptions.IgnoreCase)
            };
            
            bool temIndicadorArrow = indicadoresArrow.Any(indicador => 
                textoCompleto.Contains(indicador, StringComparison.OrdinalIgnoreCase));
            
            bool temPadraoArrow = padroesArrow.Any(padrao => padrao.IsMatch(textoCompleto));
            
            return temIndicadorArrow || temPadraoArrow;
        }

        public async Task<List<ConsumoPDFModel>> ProcessarPDF(string textoCompleto, int fileId)
        {
            var consumos = new List<ConsumoPDFModel>();
            
            // Extrair nome do cliente
            string nomeCliente = ExtrairNomeCliente(textoCompleto);
            if (string.IsNullOrEmpty(nomeCliente))
            {
                await _logService.RegistrarLog(fileId, "Nome do cliente não encontrado no PDF Arrow", TipoLog.Erro);
                throw new InvalidOperationException("Nome do cliente não encontrado no PDF Arrow");
            }

            // Buscar ou criar o cliente
            int clienteId = await _clienteService.GetClienteIdByName(nomeCliente);

            // Extrair período de faturação
            var (dataInicio, dataFim) = ExtrairPeriodo(textoCompleto);
            if (dataInicio == DateTime.MinValue || dataFim == DateTime.MinValue)
            {
                await _logService.RegistrarLog(fileId, "Período de faturação não encontrado no PDF Arrow", TipoLog.Erro);
                throw new InvalidOperationException("Período de faturação não encontrado no PDF Arrow");
            }

            var consumo = new ConsumoPDFModel
            {
                ClienteID = clienteId,
                NomeCliente = nomeCliente,
                DataInicio = dataInicio,
                DataFim = dataFim,
                Identificador = "Arrow"
            };

            // Extrair valores monetários
            ExtrairValoresMonetarios(textoCompleto, consumo);
            
            // Extrair descrição do serviço
            ExtrairDescricaoServico(textoCompleto, consumo);

            consumos.Add(consumo);
            return consumos;
        }

        private void ExtrairValoresMonetarios(string textoCompleto, ConsumoPDFModel consumo)
        {
            // Usar a lógica específica do arquivo de logs para extrair valores monetários
            // Padrão específico para "Total neto" seguido de valores
            var valoresMatch = Regex.Match(textoCompleto, @"Total\s+neto.*?(?:\r?\n|\s)*23\s*(\d+\.?\d*(?:[.,]\d+)?)\s*(?:\r?\n|\s)*.*?(?:\r?\n|\s)*(\d+\.?\d*(?:[.,]\d+)?)\s*(?:\r?\n|\s)*(\d+\.?\d*(?:[.,]\d+)?)", RegexOptions.Singleline);
            if (valoresMatch.Success && valoresMatch.Groups.Count >= 4)
            {
                // Processar valor base (Total)
                var baseStr = valoresMatch.Groups[1].Value.Replace(".", "").Replace(",", ".");
                if (decimal.TryParse(baseStr, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal valorBase))
                {
                    _logger.LogInformation($"Valor base encontrado: {valorBase:F2}");
                    consumo.Total = valorBase;
                }

                // Processar IVA (valor do meio)
                var ivaStr = valoresMatch.Groups[2].Value.Replace(".", "").Replace(",", ".");
                if (decimal.TryParse(ivaStr, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal valorIVA))
                {
                    consumo.Iva = valorIVA;
                    _logger.LogInformation($"IVA encontrado: {valorIVA:F2}");
                }

                // Processar Total da Fatura (último valor)
                var totalFaturaStr = valoresMatch.Groups[3].Value.Replace(".", "").Replace(",", ".");
                if (decimal.TryParse(totalFaturaStr, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal valorTotalFatura))
                {
                    consumo.TotalFatura = valorTotalFatura;
                    _logger.LogInformation($"Total da Fatura encontrado: {valorTotalFatura:F2}");
                }
            }
            else
            {
                // Fallback para padrões alternativos se o padrão principal não funcionar
                ExtrairValoresFallback(textoCompleto, consumo);
            }
            
            // Se não encontrou o total da fatura mas tem total e IVA, calcular
            if (consumo.TotalFatura == 0 && consumo.Total > 0)
            {
                consumo.TotalFatura = consumo.Total + consumo.Iva;
            }
        }

        private void ExtrairValoresFallback(string textoCompleto, ConsumoPDFModel consumo)
        {
            // Padrão para Total neto
            var padroesTotalNeto = new[]
            {
                new Regex(@"Total\s+neto[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"Total\s+líquido[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"Subtotal[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"Total\s+excl\.?\s*IVA[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase)
            };
            
            foreach (var padrao in padroesTotalNeto)
            {
                var match = padrao.Match(textoCompleto);
                if (match.Success)
                {
                    var valorStr = match.Groups[1].Value.Replace(",", "").Replace(" ", "");
                    if (decimal.TryParse(valorStr, NumberStyles.Currency, CultureInfo.InvariantCulture, out decimal totalNeto))
                    {
                        consumo.Total = totalNeto;
                        break;
                    }
                }
            }

            // Padrão para IVA
            var padroesIVA = new[]
            {
                new Regex(@"IVA[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"VAT[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"Imposto[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"Tax[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase)
            };
            
            foreach (var padrao in padroesIVA)
            {
                var match = padrao.Match(textoCompleto);
                if (match.Success)
                {
                    var valorStr = match.Groups[1].Value.Replace(",", "").Replace(" ", "");
                    if (decimal.TryParse(valorStr, NumberStyles.Currency, CultureInfo.InvariantCulture, out decimal iva))
                    {
                        consumo.Iva = iva;
                        break;
                    }
                }
            }

            // Padrão para Total da Fatura
            var padroesTotalFatura = new[]
            {
                new Regex(@"Total\s+da\s+Fatura[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"Total\s+geral[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"Total\s+final[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"Total\s+incl\.?\s*IVA[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"Total\s+Amount[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase),
                new Regex(@"Grand\s+Total[:\s]*€?\s*([\d,]+\.\d{2})", RegexOptions.IgnoreCase)
            };
            
            foreach (var padrao in padroesTotalFatura)
            {
                var match = padrao.Match(textoCompleto);
                if (match.Success)
                {
                    var valorStr = match.Groups[1].Value.Replace(",", "").Replace(" ", "");
                    if (decimal.TryParse(valorStr, NumberStyles.Currency, CultureInfo.InvariantCulture, out decimal totalFatura))
                    {
                        consumo.TotalFatura = totalFatura;
                        break;
                    }
                }
            }
        }

        private void ExtrairDescricaoServico(string textoCompleto, ConsumoPDFModel consumo)
        {
            string descricaoServico = null;
            
            // Tentar uma abordagem genérica para encontrar descrições de serviço
            var linhasPagina = textoCompleto.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var linha in linhasPagina)
            {
                _logger.LogInformation($"Analisando linha para descrição: {linha}");
                if (linha.Contains("Descrição") && linha.Contains("Código") && linha.Contains("Valor"))
                {
                    // Encontramos o cabeçalho da tabela de serviços
                    _logger.LogInformation("Encontrado cabeçalho da tabela de serviços");
                    continue;
                }
                
                if (!string.IsNullOrEmpty(descricaoServico))
                    break;
                    
                // Ignorar linhas de cabeçalho/rodapé
                if (linha.StartsWith("_") || linha.Contains("Total") || linha.Contains("Página") || linha.Trim().Length < 5)
                    continue;
                    
                // Procurar por padrão: descrição + valor + código + quantidade + preço
                var itemMatch = Regex.Match(linha, @"^([^0-9]+?)\s+(\d{1,3}(?:\.\d{3})*,\d{2})\s+(\S+)\s+(\d+)\s+(\d{1,3}(?:\.\d{3})*,\d{2})");
                if (itemMatch.Success)
                {
                    var descricaoBruta = itemMatch.Groups[1].Value.Trim();
                    descricaoBruta = Regex.Replace(descricaoBruta, @"^\bValor\b\s*", "", RegexOptions.IgnoreCase); // remove "Valor" no início
                    descricaoServico = descricaoBruta;
                    _logger.LogInformation($"Descrição do serviço encontrada por regex: {descricaoServico}");
                    break;
                }
            }
            
            // Se não encontrou descrição específica, tentar encontrar no texto completo
            if (string.IsNullOrEmpty(descricaoServico))
            {
                _logger.LogInformation("Tentando encontrar descrição no texto completo...");
                
                foreach (var linha in linhasPagina)
                {
                    // Procurar por linhas que contenham um padrão de produto/serviço
                    var itemMatch = Regex.Match(linha, @"([a-zA-Z]+(?:\s+[a-zA-Z]+){1,3})\s+(\d{1,3}(?:\.\d{3})*,\d{2})");
                    if (itemMatch.Success)
                    {
                        var descricaoBruta = itemMatch.Groups[1].Value.Trim();
                        descricaoBruta = Regex.Replace(descricaoBruta, @"^\bValor\b\s*", "", RegexOptions.IgnoreCase); // remove "Valor" no início
                        descricaoServico = descricaoBruta;
                        _logger.LogInformation($"Descrição encontrada no texto completo: {descricaoServico}");
                        break;
                    }
                }
            }
            
            // Atribuir a descrição encontrada ou usar padrão
            if (!string.IsNullOrEmpty(descricaoServico))
            {
                consumo.Codigo = descricaoServico.Length > 100 ? descricaoServico.Substring(0, 100) : descricaoServico;
                _logger.LogInformation($"Descrição final do serviço: {consumo.Codigo}");
            }
            else
            {
                // Se ainda não encontrou, usar uma descrição genérica baseada no valor
                if (consumo.Total > 0)
                {
                    consumo.Codigo = "Serviço Cloud";
                    _logger.LogInformation("Usando descrição genérica: Serviço Cloud");
                }
                else
                {
                    consumo.Codigo = "Não especificado";
                    _logger.LogInformation("Usando código padrão 'Não especificado' para o consumo");
                }
            }
        }

        private string ExtrairNomeCliente(string textoCompleto)
        {
            // Padrão principal para extrair nome do cliente em PDFs Arrow
            var padraoCliente = new Regex(@"Envio\s+a\s+cliente\s+(\w+)", RegexOptions.IgnoreCase);
            var match = padraoCliente.Match(textoCompleto);
            if (match.Success)
            {
                return match.Groups[1].Value.Trim();
            }

            // Padrão alternativo 1: Cliente seguido de nome
            var padraoAlternativo1 = new Regex(@"Cliente[:\s]+([^\n\r]+)", RegexOptions.IgnoreCase);
            var matchAlternativo1 = padraoAlternativo1.Match(textoCompleto);
            if (matchAlternativo1.Success)
            {
                return matchAlternativo1.Groups[1].Value.Trim();
            }

            // Padrão alternativo 2: Buscar por "Faturar a" ou "Bill to"
            var padraoAlternativo2 = new Regex(@"(?:Faturar\s+a|Bill\s+to)[:\s]*([^\n\r]+)", RegexOptions.IgnoreCase);
            var matchAlternativo2 = padraoAlternativo2.Match(textoCompleto);
            if (matchAlternativo2.Success)
            {
                return matchAlternativo2.Groups[1].Value.Trim();
            }

            // Padrão alternativo 3: Buscar por nome após "ARROWECS PORTUGAL"
            var padraoAlternativo3 = new Regex(@"ARROWECS\s+PORTUGAL[^\n\r]*\n\s*([^\n\r]+)", RegexOptions.IgnoreCase);
            var matchAlternativo3 = padraoAlternativo3.Match(textoCompleto);
            if (matchAlternativo3.Success)
            {
                var nomeExtraido = matchAlternativo3.Groups[1].Value.Trim();
                // Verificar se não é uma linha de endereço ou data
                if (!Regex.IsMatch(nomeExtraido, @"\d{2}/\d{2}/\d{2,4}|Rua|Avenida|\d{4}-\d{3}", RegexOptions.IgnoreCase))
                {
                    return nomeExtraido;
                }
            }

            return string.Empty;
        }

        private (DateTime dataInicio, DateTime dataFim) ExtrairPeriodo(string textoCompleto)
        {
            // Padrão principal para extrair período em PDFs Arrow (formato DD/MM/YY)
            var padraoData = new Regex(@"De\s+(\d{2}/\d{2}/\d{2})\s+a\s+(\d{2}/\d{2}/\d{2})", RegexOptions.IgnoreCase);
            var match = padraoData.Match(textoCompleto);
            if (match.Success)
            {
                var dataInicioStr = match.Groups[1].Value;
                var dataFimStr = match.Groups[2].Value;

                // Converter datas no formato DD/MM/YY para DD/MM/YYYY
                if (DateTime.TryParseExact(dataInicioStr, "dd/MM/yy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dataInicio) &&
                    DateTime.TryParseExact(dataFimStr, "dd/MM/yy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dataFim))
                {
                    return (dataInicio, dataFim);
                }
            }

            // Padrão alternativo para formato DD/MM/YYYY
            var padraoDataCompleta = new Regex(@"De\s+(\d{2}/\d{2}/\d{4})\s+a\s+(\d{2}/\d{2}/\d{4})", RegexOptions.IgnoreCase);
            var matchCompleta = padraoDataCompleta.Match(textoCompleto);
            if (matchCompleta.Success)
            {
                var dataInicioStr = matchCompleta.Groups[1].Value;
                var dataFimStr = matchCompleta.Groups[2].Value;

                if (DateTime.TryParseExact(dataInicioStr, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dataInicio) &&
                    DateTime.TryParseExact(dataFimStr, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dataFim))
                {
                    return (dataInicio, dataFim);
                }
            }

            // Padrão alternativo para "Período de faturação"
            var padraoPeriodo = new Regex(@"Período\s+de\s+faturação[:\s]*(\d{2}/\d{2}/\d{2,4})\s*[-–]\s*(\d{2}/\d{2}/\d{2,4})", RegexOptions.IgnoreCase);
            var matchPeriodo = padraoPeriodo.Match(textoCompleto);
            if (matchPeriodo.Success)
            {
                var dataInicioStr = matchPeriodo.Groups[1].Value;
                var dataFimStr = matchPeriodo.Groups[2].Value;

                // Tentar ambos os formatos
                var formatos = new[] { "dd/MM/yy", "dd/MM/yyyy" };
                foreach (var formato in formatos)
                {
                    if (DateTime.TryParseExact(dataInicioStr, formato, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dataInicio) &&
                        DateTime.TryParseExact(dataFimStr, formato, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime dataFim))
                    {
                        return (dataInicio, dataFim);
                    }
                }
            }

            return (DateTime.MinValue, DateTime.MinValue);
        }
    }
}