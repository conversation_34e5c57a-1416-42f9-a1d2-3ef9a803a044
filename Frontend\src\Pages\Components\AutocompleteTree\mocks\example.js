const getExample = () => {
    return `
    import CustomAutocompleteTree from '../../../Components/CustomAutocompleteTree'; //Import CustomAutocompleteTree component from your src folder
    import { useTranslation } from 'react-i18next';

    const options = [
        {
            text: "Parent 1",
            id: "1",
            parentid: null
        },
        {
            text: "Child 1.1",
            id: "1.1",
            parentid: "1"
        },
        {
            text: "Child 3.2.1.1",
            id: "3.2.1.1",
            parentid: "3.2.1"
        },
        {
            text: "Parent 2",
            id: "2",
            parentid: null
        },
        {
            text: "Parent 3",
            id: "3",
            parentid: null
        },
        {
            text: "Child 3.1",
            id: "3.1",
            parentid: "3"
        },
        {
            text: "Child 3.2",
            id: "3.2",
            parentid: "3"
        },
        {
            text: "Child 3.2.1",
            id: "3.2.1",
            parentid: "3.2"
        }
    ];

    export default function MyApp() {
    const { t } = useTranslation();

    return(
        <Your code...>
            <Box
                sx={{ maxWidth: "80%", m: 'auto', mb: 2 }}
            >
                <CustomAutocompleteTree
                    label={t('common:tableActions.search')}                    
                    placeholder={t('common:tableActions.option')}
                    text="text"
                    id="id"
                    data={options}
                    multiple
                    parentid="parentid"
             />
            </Box>
        </Your code..>
        )
    `;
}

export default getExample;
