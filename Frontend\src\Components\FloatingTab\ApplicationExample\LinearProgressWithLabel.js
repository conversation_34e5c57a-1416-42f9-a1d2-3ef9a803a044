import React from 'react';
import PropTypes from 'prop-types';
import { LinearProgress, Typography, Box } from '@mui/material';

const LinearProgressWithLabel = ({ progress }) => {
  return !Number.isNaN(progress) && (
    <Box
      display="flex"
      alignItems="center"
    >
      <Box
        width="100%"
        margin="20px 0"
      >
        <div
          style={{
            position: 'relative',
            width: "33vw",
            margin: "auto"
          }}
        >
          <LinearProgress
            variant="determinate"
            style={{
              height: '18px',
              borderRadius: "18px"
            }}
            value={progress}
          />
          <Typography
            style={{
              position: 'absolute',
              top: 0,
              fontSize: "12px",
              lineHeight: "18px",
              left: 'calc(50% - 18px)',
              color: "white"
            }}
          >
            {Math.round(progress) + "%"}
          </Typography>
        </div>
      </Box>
    </Box>
  );
};

LinearProgressWithLabel.propTypes = {
  progress: PropTypes.number
};

export default LinearProgressWithLabel;
