const getCode = () => {
    return `
     import React from "react";
    import { Box, Avatar, Typography } from '@mui/material';
    import { PropTypes } from "prop-types";
    import { useTheme } from '@emotion/react';

    const AvatarOutline = ({ theme, children, avatarsize = 100, wrapperColor }) => {
        const _avatarSize = avatarsize;
        const borderSize = avatarsize / 10;
        return <Box
            sx={{
                position: 'relative',
                width: _avatarSize + borderSize * (wrapperColor ? 3 : 3),
                height: _avatarSize + borderSize * (wrapperColor ? 3 : 3),
                background: wrapperColor ? wrapperColor : theme.palette.common.paper,
                borderRadius: "100%",
                '& .gradientBorder, & .MuiAvatar-root': {
                    position: 'absolute',
                    top: \`calc(50 % - $\{ _avatarSize / 2}px)\`,
                    left: \`calc(50 % - $\{ _avatarSize / 2}px)\`,
                    width: _avatarSize,
                    height: _avatarSize
                },
                '& .gradientBorder': {
                    borderRadius: _avatarSize,
                    // margin: -borderSize,
                    top: \`calc(43 % - $\{ _avatarSize / 2}px)\`,
                    left: \`calc(43 % - $\{ _avatarSize / 2}px)\`,
                    background: \`linear - gradient(to right, transparent, $\{ theme.palette.primary.main })\`,
                    width: _avatarSize + borderSize * 2,
                    height: _avatarSize + borderSize * 2,
                },
                '& .MuiAvatar-root': {
                    border: \`solid $\{ borderSize }px $\{ theme.palette.primary.main }\`
                }
            }}
        >
            {children}
        </Box>
    };
    AvatarOutline.propTypes = {
        theme: PropTypes.object,
        avatarsize: PropTypes.number,
        wrapperColor: PropTypes.string,
        children: PropTypes.array
    };

    const AvatarMedal = ({ image, label, textPosition = "right", sublabel }) => {
        const theme = useTheme();
        return (
            <div style={{
                display: "flex",
                alignItems: "center",
                flexDirection: textPosition === "right" ? "row" : "column"
            }}
            >
                <AvatarOutline theme={theme}>
                    <div className="gradientBorder" />
                    <Avatar
                        src={image}
                        alt={label}
                    />
                </AvatarOutline>
                {
                    label || sublabel ?
                        <Box
                            sx={{
                                ml: textPosition === "right" ? "20px" : "initial",
                            }}
                        >
                            {
                                sublabel ?
                                    <Typography
                                        color="textPrimary"
                                        variant="h1"
                                    >
                                        {sublabel}
                                    </Typography>
                                    :
                                    null
                            }
                            <Typography
                                color="textPrimary"
                                variant="h1"
                            >
                                <b>{label}</b>
                            </Typography>
                        </Box>
                        :
                        null
                }
            </div>
        )
    }

    AvatarMedal.propTypes = {
        label: PropTypes.string,
        sublabel: PropTypes.string,
        textPosition: PropTypes.string,
        image: PropTypes.string.isRequired,
    }
    export default AvatarMedal;

    `;
}

export default getCode;
