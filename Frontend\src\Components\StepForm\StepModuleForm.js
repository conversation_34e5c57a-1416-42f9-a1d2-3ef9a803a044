/* eslint-disable no-constant-condition */
import React, { forwardRef, useState, useEffect } from 'react';
import {
  Autocomplete, FormHelperText, Box, Card, CardHeader, TextField, Grid, CardContent, IconButton, Collapse, Divider
} from '@mui/material';
import PropTypes from 'prop-types';
import { StepModuleFormMenu } from '.';
import {
  DragIndicator as DragIndicatorIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
} from '@mui/icons-material';
import { styled } from '@mui/system';
import CustomCheckbox from './CustomStepFormComponents/CustomCheckbox';

const CustomInlineCheckboxWrapper = styled("div")({
  display: "flex",
  height: "100%",
  paddingTop: "16px"
});

const CustomCard = ({ dragging, children, onClick }) => {
  return (
    <Card
      raised={dragging}
      sx={{
        ...(dragging && {
          backgroundColor: 'background.paper'
        }),
        width: '100%',
      }}
      variant={dragging ? 'elevation' : 'outlined'}
      onClick={onClick}
    >
      {children}
    </Card>
  )
}

CustomCard.propTypes = {
  dragging: PropTypes.bool,
  children: PropTypes.node,
  onClick: PropTypes.func
};

const StepNameTextField = ({ renaming, process, ...props }) => {
  if (!renaming) return process.stepName;
  return (<TextField {...props} />)
}

StepNameTextField.propTypes = {
  renaming: PropTypes.bool,
  process: PropTypes.object
};

const StepNameErrorMessage = ({ errors, renaming }) => {
  if (errors?.stepName && !renaming) {
    return (<Box sx={{ mt: 2 }}>
      <FormHelperText error>
        {errors?.stepName}
      </FormHelperText>
    </Box>)
  }
}

StepNameErrorMessage.propTypes = {
  renaming: PropTypes.bool,
  errors: PropTypes.object
};

const PermissionsAutocomplete = ({ process, ...props }) => {
  if (process?.stepPermissions) return;
  return (<Autocomplete {...props} />)
}

PermissionsAutocomplete.propTypes = {
  process: PropTypes.object,
};

const CustomStepModuleFormMenu = ({ opportunityView, handleRemoveStep, handleRenaming, toggle, onClick }) => {
  if (opportunityView) return null;
  return (<>
    <StepModuleFormMenu
      handleRemoveStep={handleRemoveStep}
      handleRenaming={handleRenaming}
    />
    <IconButton
      aria-label="collapse"
      size="small"
      onClick={onClick}
    >
      {
        toggle ?
          <KeyboardArrowUpIcon fontSize="small" />
          :
          <KeyboardArrowDownIcon fontSize="small" />
      }
    </IconButton>
  </>)
}

CustomStepModuleFormMenu.propTypes = {
  opportunityView: PropTypes.bool,
  handleRemoveStep: PropTypes.func,
  handleRenaming: PropTypes.func,
  onClick: PropTypes.func,
  toggle: PropTypes.bool,
};

const StepModuleForm = React.memo(forwardRef((props, ref) => {
  const { disableAll, dragging, stepIndex, moduleIndex, options, process, step, setFieldValue, errors,
    touched, processType, handleRemoveModule, opportunityView, dragProps, provided, handleFormikBlur, handleFormikChange, setActiveModuleCard, setWasModified, processTypeCode } = props;
  const [toggle, setToggle] = useState(true);
  const [renaming, setRenaming] = useState(true);
  const processTypeContinuos = "CONTINUOUS";
  const hasFormData = process.formData && process.formData[0];

  const handleModified = () => {
    if (setWasModified) setWasModified(true);
  }

  const handleOnBlur = (e) => {
    if (process.stepName !== "") {
      setRenaming(!renaming);
    }
    handleFormikBlur(e);
  };

  const updateFieldValue = (field, _value) => {
    const path = field === "stepName"
      ? `stepsList[${stepIndex}].moduleList[${moduleIndex}].stepName`
      : `stepsList[${stepIndex}].moduleList[${moduleIndex}][${field}]`
    setFieldValue(path, _value);
  };

  const handleOnChange = (e) => {
    handleModified();
    updateFieldValue("stepName", e.target.value)
    handleFormikChange(e);
  };

  useEffect(() => {
    if (processType === 942002) {
      return;
    }
    handleModified();
    setFieldValue(`stepsList[${stepIndex}].moduleList[${moduleIndex}].sequential`);
  }, [processType, moduleIndex, setFieldValue, setWasModified, stepIndex]);

  // const getError = (fieldName) => Boolean(touched?.[fieldName] && errors?.[fieldName]);

  const getError = (fieldName) => {
    const isTouched = touched?.[fieldName];
    const hasError = errors?.[fieldName];
    return Boolean(isTouched && hasError);
  };

  function getReference() {
    return provided.innerRef || ref;
  }

  function defaultOptions(value) {
    return value || [];
  }

  // const isOptionEqualToValue = (option, value) => {
  //   return option?.value && value?.value && value.value !== "" && option.value === value.value;
  // };

  const isOptionEqualToValue = (option, value) => {
    const optionHasValue = option?.value;
    const valueHasValue = value?.value && value.value !== "";
    const isEqual = option.value === value.value;
    return optionHasValue && valueHasValue && isEqual;
  };

  // const getOptionLabel = (option) => {
  //   return option && option.text ? option.text : '';
  // };

  const getOptionLabel = (option) => {
    if (!option || !option.text) return '';
    return option.text;
  };

  // const getTemplateTypeId = () => {
  //   return hasFormData ? process.formData[0].templateTypeID : {};
  // }

  const getTemplateTypeId = () => {
    if (!hasFormData) return {};
    return process.formData[0].templateTypeID;
  }

  // const getEvaluatorId = () => {
  //   return hasFormData ? process.formData[0].evaluaterID : [];
  // }

  const getEvaluatorId = () => {
    if (!hasFormData) return [];
    return process.formData[0].evaluaterID;
  }

  // const getGranularity = () => {
  //   return hasFormData ? process.formData[0].granularity : '';
  // }

  const getGranularity = () => {
    if (hasFormData) return '';
    return process.formData[0].granularity;
  }

  return (
    <Box
      ref={getReference()}
      {...provided.draggableProps}
    >
      <Grid
        container
        spacing={2}
      >
        <Grid
          item
          xs={12}
        >
          <CustomCard
            dragging={dragging}
            onClick={() => setActiveModuleCard(moduleIndex)}
          >
            <CardHeader
              title={
                <div style={{
                  display: "flex",
                  alignItems: "center"
                }}
                >
                  <div
                    style={{
                      flex: 1,
                      maxWidth: 24
                    }}
                    {...dragProps}
                  >
                    <DragIndicatorIcon />
                  </div>
                  <div
                    style={{
                      flex: 1
                    }}
                  >
                    <StepNameTextField
                      renaming={renaming}
                      process={process}
                      error={getError('stepName')}
                      helperText={errors?.stepName}
                      name="stepName"
                      placeholder="Nova Etapa"
                      onBlur={(e) => handleOnBlur(e)}
                      onChange={(e) => handleOnChange(e)}
                      value={process.stepName}
                      variant="standard"
                      disabled={opportunityView}
                      autoFocus={process.stepName === ""}
                      sx={{
                        minWidth: "15vw",
                        maxWidth: "300px"
                      }}
                    />
                    <StepNameErrorMessage
                      errors={errors}
                      renaming={renaming}
                    />
                  </div>
                </div>
              }
              action={
                <CustomStepModuleFormMenu
                  handleRemoveStep={() => handleRemoveModule(stepIndex, moduleIndex)}
                  handleRenaming={() => setRenaming(!renaming)}
                  onClick={() => setToggle(!toggle)}
                  toggle={toggle}
                />
              }
            />
            <Divider />
            <Collapse in={toggle}>
              {
                process.showContent &&
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={2}>
                          <CustomCheckbox
                            text="Permissões da Fase"
                            disabled={disableAll}
                            checked={process.stepPermissions}
                          // onChange={(e) => { setFieldValue(`stepsList[${stepIndex}].moduleList[${moduleIndex}].stepPermissions`, e.target.checked); handleModified() }}
                          />
                        </Grid>
                        <Grid item xs={12} sm={10}>
                          <PermissionsAutocomplete
                            getOptionLabel={(option) => option.text}
                            isOptionEqualToValue={isOptionEqualToValue}
                            options={defaultOptions(options?.rolesList)}
                            multiple
                            disabled={disableAll || process.stepPermissions}
                            value={process.stepPermissions ? step.rolePermissions : process.rolePermissions}
                            //onChange={(e, value) => { setFieldValue(`stepsList[${stepIndex}].moduleList[${moduleIndex}].rolePermissions`, value); handleModified(); }}
                            renderInput={(params) => (
                              <TextField
                                fullWidth
                                label="Permissões"
                                name="rolePermissions"
                                variant="outlined"
                                error={getError('rolePermissions')}
                                helperText={errors?.rolePermissions}
                                {...params}
                              />
                            )}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item xs={12}>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                          <Autocomplete
                            options={defaultOptions(options?.moduleList)}
                            disabled={disableAll}
                            isOptionEqualToValue={isOptionEqualToValue}
                            getOptionLabel={getOptionLabel}
                            // onChange={handleUpdateModuleListData.bind(this, 'formData[0].templateTypeID')}
                            value={getTemplateTypeId()}
                            groupBy={(option) => option.group}
                            renderInput={(params) => (
                              <TextField
                                label="Formulário"
                                error={getError('templateTypeID')}
                                helperText={errors?.templateTypeID}
                                {...params}
                              />
                            )}
                          />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <Autocomplete
                            options={defaultOptions(options?.evaluaterList)}
                            isOptionEqualToValue={isOptionEqualToValue}
                            getOptionLabel={getOptionLabel}
                            multiple
                            disabled={disableAll && processTypeCode !== processTypeContinuos}
                            // onChange={handleUpdateModuleListData.bind(this, 'formData[0].evaluaterID')}
                            value={getEvaluatorId()}
                            renderInput={(params) => (
                              <TextField
                                label="Avaliador"
                                error={getError('formData')}
                                helperText={errors && errors.formData && errors?.formData[0].evaluaterID}
                                {...params}
                              />)}
                          />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <Autocomplete
                            options={defaultOptions(options?.granularityList)}
                            disabled={disableAll}
                            isOptionEqualToValue={isOptionEqualToValue}
                            getOptionLabel={getOptionLabel}
                            // onChange={handleUpdateModuleListData.bind(this, 'formData[0].granularity')}
                            value={getGranularity()}
                            renderInput={(params) => (
                              <TextField
                                label="Granularidade"
                                error={getError('granularity')}
                                helperText={errors?.granularity}
                                {...params}
                              />)}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                    <Grid item xs={12}>
                      <CustomInlineCheckboxWrapper>
                        <CustomCheckbox
                          text="Data de evento"
                          // checked={process.formData[0] && process.formData[0].eventDate ? process.formData[0].eventDate : false}
                          // onChange={() => handleUpdateModuleListData('formData[0].eventDate', this, !process.formData[0].eventDate)}
                          disabled={disableAll}
                        />
                        <CustomCheckbox
                          text="Acesso externo"
                          // checked={process.formData[0] && process.formData[0].test ? process.formData[0].test : false}
                          // onChange={() => handleUpdateModuleListData('formData[0].test', this, !process.formData[0].test)}
                          disabled={disableAll}
                        />
                        <CustomCheckbox
                          text="Opcional"
                          // checked={process.formData[0] && process.formData[0].optional ? process.formData[0].optional : false}
                          // onChange={() => handleUpdateModuleListData('formData[0].optional', this, !process.formData[0].optional)}
                          disabled={disableAll}
                        />
                        <CustomCheckbox
                          text="Manter o estado anterior"
                          // checked={process.formData[0] && process.formData[0].keepStatus ? process.formData[0].keepStatus : false}
                          // onChange={() => handleUpdateModuleListData('formData[0].keepStatus', this, !process.formData[0].keepStatus)}
                          disabled={disableAll}
                        />
                      </CustomInlineCheckboxWrapper>
                    </Grid>
                  </Grid>
                </CardContent>
              }
            </Collapse>
          </CustomCard>
        </Grid>
      </Grid>
    </Box>
  );
}));

StepModuleForm.propTypes = {
  dragging: PropTypes.bool,
  opportunityView: PropTypes.bool,
  stepIndex: PropTypes.string,
  moduleIndex: PropTypes.number,
  processType: PropTypes.string,
  options: PropTypes.object,
  process: PropTypes.object.isRequired,
  step: PropTypes.object.isRequired,
  setFieldValue: PropTypes.func.isRequired,
  errors: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.array,
  ]),
  touched: PropTypes.array,
  handleRemoveStep: PropTypes.func,
  handleRemoveModule: PropTypes.func,
  getProcessStepDocument: PropTypes.func,
  disableAll: PropTypes.bool,
  provided: PropTypes.object,
  dragProps: PropTypes.object,
  handleFormikBlur: PropTypes.func,
  handleFormikChange: PropTypes.func,
  activeModuleCard: PropTypes.number,
  setActiveModuleCard: PropTypes.func,
  setWasModified: PropTypes.func,
  processTypeCode: PropTypes.string
};

StepModuleForm.defaultProps = {
  dragging: false
};

export default StepModuleForm;
