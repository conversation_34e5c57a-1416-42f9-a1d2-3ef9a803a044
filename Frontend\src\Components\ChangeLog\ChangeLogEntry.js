import { Chip, Box, Card } from '@mui/material';
import { PropTypes } from 'prop-types';
import Collapsible from 'react-collapsible';
import { styled } from '@mui/system';

const StyledCard = styled(Card)({
    width: '80%',
    margin: 'auto',
    marginTop: 10,
    marginBottom: 10,
    padding: 15,
    textAlign: "justify",
})

/*
    Defines chip colors.
    When importing ChangeLogEntry component, also import chipStyles to render the chip
*/

export const chipStyles = (theme) => ({
    green: {
        backgroundColor: `${theme.palette.states.success.main} !important`,
        border: `1px solid ${theme.palette.states.success.main}`,
        color: "white",
        marginRight: 15
    },
    red: {
        backgroundColor: `${theme.palette.states.fail.main} !important`,
        border: `1px solid ${theme.palette.states.fail.main}`,
        color: "white",
        marginRight: 15
    },
    grey: {
        backgroundColor: `${theme.palette.states.draft.main} !important`,
        border: `1px solid ${theme.palette.states.draft.main}`,
        color: "white",
        marginRight: 15
    }
});

export const ChangeLogEntry = ({ changeLog, version }) => {
    /*
        props:
            changeLog = Array (label, className(green, red, grey), content)
            version = Version Name
    */

    return (
        <StyledCard>
            <Collapsible
                triggerStyle={{ cursor: "pointer", margin: "auto" }}
                triggerTagName="h4"
                trigger="CHANGELOG"
            >
                <h4>{version}</h4>
                {changeLog && changeLog.map((entry, index) =>
                    <Box
                        key={"chip" + index}
                    >
                        <Chip
                            label={entry?.label}
                            variant="outlined"
                            className={entry?.className}
                            sx={{
                                m: 0.5,
                            }}
                            size="small"
                        />
                        {entry?.content}
                    </Box>)}
            </Collapsible>
        </StyledCard>
    )
}
ChangeLogEntry.propTypes = {
    version: PropTypes.string,
    changeLog: PropTypes.array,

};
