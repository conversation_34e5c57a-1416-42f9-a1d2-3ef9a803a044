﻿using System.Collections.Generic;

namespace RnD.BackEnd.API.Model.Email
{
    /// <summary>
    /// Email message DTO class
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class EmailMessageDto<T>
    {
        /// <summary>
        /// Gets or sets the template data.
        /// </summary>
        /// <value>
        /// The template data.
        /// </value>
        public T TemplateData { get; set; }

        /// <summary>
        /// Gets or sets the recipients.
        /// </summary>
        /// <value>
        /// The recipients.
        /// </value>
        public List<string> Recipients { get; set; }

        /// <summary>
        /// Gets or sets the recipients cc.
        /// </summary>
        /// <value>
        /// The recipients cc.
        /// </value>
        public List<string> RecipientsCC { get; set; }

        /// <summary>
        /// Gets or sets the recipients BCC.
        /// </summary>
        /// <value>
        /// The recipients BCC.
        /// </value>
        public List<string> RecipientsBCC { get; set; }
    }
}
