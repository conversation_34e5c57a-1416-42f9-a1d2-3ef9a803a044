CREATE VIEW [dbo].[vw_ConsumoSubscricao]
AS
SELECT 
    s.ID as SubscriptionTableID,
    s.SubscriptionID,
    cs.ID as ConsumoID,
    cs.ResourceGroup,
    cs.ResourceName,
    cs.ResourceLocation,
    cs.Cost,
    cs.CostUSD,
    cs.Currency,
    cs.StartDate,
    cs.EndDate,
    ROW_NUMBER() OVER (PARTITION BY s.SubscriptionID ORDER BY cs.StartDate DESC) as RowNum
FROM 
    [dbo].[Subscription] s
    INNER JOIN [dbo].[ConsumoSubscricao] cs ON s.SubscriptionID = cs.SubscriptionID; 