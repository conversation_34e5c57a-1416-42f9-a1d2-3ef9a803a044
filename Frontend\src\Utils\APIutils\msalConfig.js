import { useMsal } from "@azure/msal-react";

export const msalConfig = {
    auth: {
        clientId: process.env.REACT_APP_CUSTOM_AUTH_CLIENT_ID,
        authority: `https://login.microsoftonline.com/${process.env.REACT_APP_CUSTOM_AUTH_TENANT}`,
        redirectUri: process.env.REACT_APP_CUSTOM_AUTH_REDIRECT_URI
    },
};
export const loginRequest = {
    scopes: [`api://${process.env.REACT_APP_CUSTOM_AUTH_CLIENT_ID}/access_as_user`] // optional Array<string>
};
export const graphConfig = {
    graphMeEndpoint: "https://graph.microsoft.com"
};

//--------------------------------------------------------Dummy----------------------------------------------------

export const LoginAD = () => {
    const { instance, inProgress } = useMsal();

    return new Promise((resolve) => {
        if (inProgress === "none" || inProgress === "startup") {
            instance.handleRedirectPromise();
            if (instance && instance.getAllAccounts()[0]) {
                instance.acquireTokenSilent({ ...loginRequest, account: instance.getAllAccounts()[0] }).then((response) => {
                    resolve(response.accessToken);
                }).catch((err) => {
                    console.log(err);
                    if (err.name === 'InteractionRequiredAuthError') {
                        instance.acquireTokenPopup({ ...loginRequest, account: instance.getAllAccounts()[0] })
                            .then((response) => {
                                resolve(response.accessToken);
                            });
                    }
                });
            } else {
                instance.loginRedirect(loginRequest);
            }
        } else {
            resolve(false);
        }
    })
};
