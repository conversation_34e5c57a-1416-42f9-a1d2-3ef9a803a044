﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RnD.BackEnd.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ClienteSubscricaoController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public ClienteSubscricaoController(ApplicationDbContext context)
        {
            _context = context;
        }

        // POST: api/ClienteSubscricao
        [HttpPost]
        [Consumes("application/json")]
        public async Task<ActionResult<ClienteSubscricao>> CreateClienteSubscricao([FromBody] ClienteSubscricaoInputModel input)
        {
            // Verificar se a subscrição já está associada
            var subscricaoEmUso = await _context.ClienteSubscricao
                .AnyAsync(cs => cs.SubscriptionID == input.SubscriptionID);

            if (subscricaoEmUso)
            {
                return BadRequest("Esta subscrição já está associada a um cliente.");
            }

            var clienteSubscricao = new ClienteSubscricao
            {
                ClienteID = input.ClienteID,
                SubscriptionID = input.SubscriptionID
            };

            _context.ClienteSubscricao.Add(clienteSubscricao);
            await _context.SaveChangesAsync();

            return Ok(new
            {
                Id = clienteSubscricao.ID,
                ClienteID = clienteSubscricao.ClienteID,
                SubscriptionID = clienteSubscricao.SubscriptionID
            });
        }

        // GET: api/ClienteSubscricao/cliente/5
        [HttpGet("cliente/{clienteId}")]
        public async Task<ActionResult<IEnumerable<int>>> GetSubscricoesByCliente(int clienteId)
        {
            var subscricoesIds = await _context.ClienteSubscricao
                .Where(cs => cs.ClienteID == clienteId)
                .Select(cs => cs.SubscriptionID)
                .ToListAsync();

            return subscricoesIds;
        }

        // GET: api/ClienteSubscricao/cliente/5/details
        [HttpGet("cliente/{clienteId}/details")]
        public async Task<ActionResult<IEnumerable<object>>> GetSubscricoesDetailsByCliente(int clienteId)
        {
            // Primeiro verifica se o cliente existe
            var cliente = await _context.Cliente.FindAsync(clienteId);
            if (cliente == null)
            {
                return NotFound($"Cliente com ID {clienteId} não encontrado");
            }

            var clienteSubscricoes = await _context.ClienteSubscricao
                .Where(cs => cs.ClienteID == clienteId)
                .Include(cs => cs.Subscription)
                .Select(cs => new
                {
                    cs.ID,
                    cs.ClienteID,
                    cs.SubscriptionID,
                    SubscriptionDetails = new
                    {
                        cs.Subscription.ID,
                        cs.Subscription.SubscriptionID
                    }
                })
                .ToListAsync();

            if (!clienteSubscricoes.Any())
            {
                return Ok(new List<object>()); // Retorna lista vazia em vez de NotFound
            }

            return clienteSubscricoes;
        }

        // GET: api/ClienteSubscricao/available
        [HttpGet("available")]
        public async Task<ActionResult<IEnumerable<object>>> GetAvailableSubscriptions()
        {
            var subscriptionsInUse = await _context.ClienteSubscricao
                .Select(cs => cs.SubscriptionID)
                .ToListAsync();

            var availableSubscriptions = await _context.Subscription
                .Where(s => !subscriptionsInUse.Contains(s.ID))
                .Select(s => new
                {
                    s.ID,
                    s.SubscriptionID
                })
                .ToListAsync();

            return availableSubscriptions;
        }

        // DELETE: api/ClienteSubscricao/cliente/5/subscricao/10
        [HttpDelete("cliente/{clienteId}/subscricao/{subscriptionId}")]
        public async Task<IActionResult> DeleteClienteSubscricao(int clienteId, int subscriptionId)
        {
            var clienteSubscricao = await _context.ClienteSubscricao
                .FirstOrDefaultAsync(cs => cs.ClienteID == clienteId && cs.SubscriptionID == subscriptionId);

            if (clienteSubscricao == null)
            {
                return NotFound();
            }

            _context.ClienteSubscricao.Remove(clienteSubscricao);
            await _context.SaveChangesAsync();

            return NoContent();
        }
    }

    public class ClienteSubscricaoInputModel
    {
        public int ClienteID { get; set; }
        public int SubscriptionID { get; set; }
    }
}