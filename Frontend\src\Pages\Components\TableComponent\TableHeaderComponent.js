import {
  Box,
  Divider,
  Typography,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  IconButton,
  <PERSON>lt<PERSON>,
  <PERSON>,
} from "@mui/material";
import { useTheme } from '@emotion/react';
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import { useState } from "react";
import TableHeader from "../../../Components/TableUtils/TableHeader";
import {
  ExpandMore as ExpandMoreIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from "@mui/icons-material";
import { PropTypes } from "prop-types";
import getTableHeaderIntro from "./mocks/tableHeaderIntro";
import getTableHeaderCode from "./mocks/tableHeaderCode";
import getTableHeaderExample from "./mocks/tableHeaderEx";
import getTableHeaderApplication from "./mocks/tableHeaderApplication";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import { styled } from '@mui/system';

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh"
})

const linkStyle = {
  marginRight: 20,
  alignItems: "center",
  textDecoration: "none",
};

const headCells = [
  { id: "example1", label: "Example1" },
  { id: "example2", label: "Example2", sort: true, filter: true },
  { id: "example3", label: "Example3" },
  { id: "example4", label: "Example4", align: "center" },
  { id: "example5", align: "right" },
];

const headCellsEx2 = [
  { id: "example", label: "Example", visibilityToggle: true },
  { id: "example1", label: "Example1" },
  { id: "example2", label: "Example2" },
];

export default function TableHeaderComponent({ open: openProp }) {
  const theme = useTheme();
  const [orderBy, setOrderBy] = useState("example1");
  const [order, setOrder] = useState("asc");
  const [privacy, setPrivacy] = useState(true);
  const [intro, setIntro] = useState(getTableHeaderIntro);
  const [code, setCode] = useState(getTableHeaderCode);
  const [example, setExample] = useState(getTableHeaderExample);
  const [apply, setApply] = useState(getTableHeaderApplication);
  const [open, setOpen] = useState(openProp);

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box
        sx={{
          m: "auto",
          textAlign: "center"
        }}
      >
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h2>Table Header</h2>
          <p>
            The following code snippet demonstrates how Table Header component
            is displayed. You can copy the full source code to your src folder.
          </p>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={open ? code : intro} />
                <Tooltip
                  title={
                    !open ? "Show full source code" : "Hide full source code"
                  }
                >
                  <IconButton
                    onClick={handleToggle}
                    sx={{
                      color: `${theme.palette.primary.main}`,
                      "&:hover": {
                        backgroundColor: `${theme.palette.secondary.main}`,
                        color: "primary.main",
                      },
                      height: 35,
                      width: 35,
                      mt: 1,
                    }}
                  >
                    {!open ? <ExpandMoreIcon /> : <KeyboardArrowUpIcon />}
                  </IconButton>
                </Tooltip>
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={open ? code : intro}
              onValueChange={
                open ? (_code) => setCode(_code) : (_intro) => setIntro(_intro)
              }
              highlight={
                open
                  ? (_code) => highlight(_code, languages.js)
                  : (_intro) => highlight(_intro, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          xs={12}
          sx={{ m: "auto", mt: 5, textAlign: "left", maxWidth: "80%" }}
        >
          <Typography color="textPrimary">
            In cases where we want to add a checkbox to our table, a new column,
            with this function, will be added. Attention: A checkbox is set by
            default. To disable it, we need to pass the following prop:
            <br />
            <b>hasCheckbox=false</b>
          </Typography>
          <Table
            stickyHeader
            sx={{
              border: `solid 1px ${theme.palette.secondary.main}`,
              mt: 2,
              mb: 2,
            }}
          >
            <TableHeader headCells={headCells} />
          </Table>
          <Typography color="textPrimary">
            Sometimes we want to keep some data private. To hide information,
            just pass the following props:
            <br />
            <b>togglePrivacy</b>
            <br />
            <b>privacy</b>
            <br />
            The information in the table will appear blurred:
          </Typography>
          <Table
            stickyHeader
            sx={{
              border: `solid 1px ${theme.palette.secondary.main}`,
              mt: 2,
              mb: 2,
            }}
          >
            <TableHeader
              headCells={headCellsEx2}
              togglePrivacy={() => setPrivacy(!privacy)}
              privacy={privacy}
              hasCheckbox={false}
            />
          </Table>
          <Typography color="textPrimary">
            There are cases where we want to change the order of the
            information. We might want to change the order, asc or desc. This
            function allows us to sort the information shown.
          </Typography>
          <Table
            stickyHeader
            sx={{
              border: `solid 1px ${theme.palette.secondary.main}`,
              mt: 2,
              mb: 2,
            }}
          >
            <TableHeader
              headCells={headCells}
              hasCheckbox={false}
              onSortClick={handleRequestSort}
            />
          </Table>
        </Box>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={example} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={example}
              onValueChange={(_example) => setExample(_example)}
              highlight={(_example) => highlight(_example, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Typography
          sx={{
            display: "flex",
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          You will have to import the component as below:
        </Typography>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={apply} />
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={apply}
              onValueChange={(_apply) => setApply(_apply)}
              highlight={(_apply) => highlight(_apply, languages.js)}
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          xs={12}
          sx={{
            m: "auto",
            textAlign: "justify",
            maxWidth: "80%",
            color: "textPrimary",
          }}
        >
          <p>
            Table Header receives the following props and is used in conjunction
            with
            <Link
              style={linkStyle}
              href="https://mui.com/material-ui/react-table/"
            >
              Material UI
            </Link>
            components.
          </p>
          <ul>
            <li>
              <b>order</b> - Used to sort the result set in ascending or
              descending order;
            </li>
            <li>
              <b>orderBy</b> - Used to sort a range or rows in ascending or
              descending order based on the values ​​of one or more columns;
            </li>
            <li>
              <b>selectedSome</b> - Returns the length of selected values;{" "}
            </li>
            <li>
              <b>selectedAll</b> - Returns the length of all values. All values
              are selected;
            </li>
            <li>
              <b>onSelectAllClick</b> - Function to select a value in the table;{" "}
            </li>
            <li>
              <b>hasCheckbox = true/false</b> - Sets whether the header has a
              checkbox;
            </li>
            <li>
              <b>togglePrivacy</b> - Shows privacy icon to hide/show
              information;
            </li>
            <li>
              <b>privacy</b> - Define whether an information is private or not;
            </li>
            <li>
              <b>disabledCheckBox = true/false</b> - Disables checkBox if value
              equals true;
            </li>
          </ul>
        </Box>
        <Box sx={{ textAlign: "center", mt: 5 }}>
          See also:
          <br />
          <Link
            style={linkStyle}
            href="/Components/FunctionalTableExample/"
          >
            Functional Table Example
          </Link>
          <Link
            style={linkStyle}
            href="/Components/TableLoading/"
          >
            Table Loading
          </Link>
          <Link
            style={linkStyle}
            href="/Components/TableActions/"
          >
            Table Actions
          </Link>
          <Link
            style={linkStyle}
            href="/Components/TableUtils/"
          >
            Table Utils
          </Link>
        </Box>
      </Box>
      <ChangeLogEntry
        version="V1.0.0"
        changeLog={ChangeLog()}
      />
    </Box>
  );
}
TableHeaderComponent.propTypes = {
  open: PropTypes.func,
};
const ChangeLog = () => {
  const theme = useTheme()
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content: "Imported Table Actions Component.",
      className: chips.green,
    },
  ];

  return changeLog;
};
