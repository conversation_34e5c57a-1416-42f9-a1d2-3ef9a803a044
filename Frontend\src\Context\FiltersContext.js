import { createContext, useEffect, useState } from 'react';
import PropTypes from 'prop-types';

const initialFilters = {
  PROFILE: [],
  PROCESS: [],
}

export const restoreFilters = () => {
  let filters = null;

  try {
    const PROFILE_storedData = window.localStorage.getItem('filters.PROFILE');
    const PROCESS_storedData = window.localStorage.getItem('filters.PROCESS');

    filters = {
      PROFILE: PROFILE_storedData ? JSON.parse(PROFILE_storedData) : [],
      PROCESS: PROCESS_storedData ? JSON.parse(PROCESS_storedData) : [],
    }
  } catch (err) {
    console.error(err);
    // If stored data is not a strigified JSON this will fail,
    // that's why we catch the error
  }

  return filters;
};

export const storeFilters = (key, filters) => {
  window.localStorage.setItem(`filters.${key}`, JSON.stringify(filters));
};

const FiltersContext = createContext({
  filters: initialFilters,
  saveFilters: (newFilter) => {
    console.log('Save filters logic here:', newFilter);
  }
});

export const FiltersProvider = (props) => {
  const { children } = props;
  const [filters, setFilters] = useState(initialFilters);

  useEffect(() => {
    const filters_ = restoreFilters();

    if (filters_) {
      setFilters(filters_);
    }
  }, []);

  const onSaveFilters = (key, appliedFilters) => {
    const _filters = { ...filters };
    _filters[key] = appliedFilters;
    setFilters(_filters);
  }

  const saveFilters = (key, updatedFilters) => {
    onSaveFilters(key, updatedFilters);
    storeFilters(key, updatedFilters);
  };

  return (
    <FiltersContext.Provider
      value={{
        filters,
        saveFilters
      }}
    >
      {children}
    </FiltersContext.Provider>
  );
};

FiltersProvider.propTypes = {
  children: PropTypes.node.isRequired
};

export const FiltersConsumer = FiltersContext.Consumer;

export default FiltersContext;
