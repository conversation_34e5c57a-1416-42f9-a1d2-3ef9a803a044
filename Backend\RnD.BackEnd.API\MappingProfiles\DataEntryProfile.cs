﻿using AutoMapper;
using RnD.BackEnd.API.Model.DataEntry;
using RnD.BackEnd.Domain.Entities.DataEntry;

namespace RnD.BackEnd.API.MappingProfiles
{
    /// <summary>
    /// Data entry mapping profile
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class DataEntryProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DataEntryProfile"/> class.
        /// </summary>
        public DataEntryProfile()
        {
            CreateMap<DataEntryEntity, EntityDto>();

            CreateMap<DataEntryEntitiesList, EntityListDto>();
        }
    }
}
