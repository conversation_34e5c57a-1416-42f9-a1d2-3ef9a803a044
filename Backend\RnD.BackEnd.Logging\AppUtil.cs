﻿using System.Reflection;

namespace RnD.BackEnd.Logging
{
    public static class AppUtil
    {
        /// <summary>
        /// Gets the module method.
        /// </summary>
        /// <param name="info">The information.</param>
        /// <returns>The module method information</returns>
        public static string GetModuleMethod(MethodBase info)
        {
            return $"[ Method : {info.Name} | Class : {info.ReflectedType?.Name} | Module : {info.Module?.Name}]";
        }
    }
}
