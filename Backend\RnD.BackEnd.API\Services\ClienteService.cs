using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using System.Threading.Tasks;

namespace RnD.BackEnd.API.Services
{
    public class ClienteService : IClienteService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ClienteService> _logger;

        public ClienteService(ApplicationDbContext context, ILogger<ClienteService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<int> GetClienteIdByName(string nomeCliente)
        {
            // Buscar cliente existente
            var cliente = await _context.Cliente.FirstOrDefaultAsync(c => c.Name == nomeCliente);
            
            if (cliente != null)
            {
                return cliente.ID;
            }
            
            // Se não encontrou, criar novo cliente
            var novoCliente = new Cliente
            {
                Name = nomeCliente,
                Contact = "000000000" // Contacto temporário
            };
            
            _context.Cliente.Add(novoCliente);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation($"Novo cliente criado: {nomeCliente} com ID: {novoCliente.ID}");
            return novoCliente.ID;
        }
    }
}