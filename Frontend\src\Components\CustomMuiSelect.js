import React from 'react';
import {
    FormControl,
    InputLabel,
    Select,
    MenuItem
} from '@mui/material';
import PropTypes from 'prop-types';

const CustomMuiSelect = ({ data = [], name, label, selected, handleSelect = () => null, width = 200, itemText = "text", itemValue = "value", hasEmpty }) => {
    return <FormControl sx={{
        width: { width }
    }}
    >
        <InputLabel id={`simple-select-${label}`}>{name}</InputLabel>
        <Select
            labelId={`simple-select-${label}`}
            id={`simple-select-${label}`}
            value={selected && itemValue ? selected[itemValue] : selected ? selected : null}
            label={label}
            onChange={handleSelect}
        >
            {
                hasEmpty && <MenuItem
                    value=""
                >
                    Todos
                </MenuItem>
            }
            {
                data.map((d, d_i) => (
                    <MenuItem
                        key={"menuItem_" + d_i}
                        value={itemValue ? d[itemValue] : d}
                    >
                        {itemText ? d[itemText] : d}
                    </MenuItem>
                ))
            }
        </Select>
    </FormControl>
}

CustomMuiSelect.propTypes = {
    data: PropTypes.array,
    name: PropTypes.string,
    label: PropTypes.string,
    selected: PropTypes.oneOfType([
        PropTypes.number,
        PropTypes.string,
        PropTypes.object
    ]),
    handleSelect: PropTypes.func,
    width: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number
    ]),
    itemText: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.bool
    ]),
    itemValue: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.bool
    ]),
    hasEmpty: PropTypes.bool
}

export default CustomMuiSelect;
