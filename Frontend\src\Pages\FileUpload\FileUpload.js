import React, { useEffect, useState, useRef } from "react";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Card,
  CardContent,
  CardActions,
  CardMedia,
  CircularProgress,
  Alert,
  Snackbar
} from "@mui/material";
import { 
  PictureAsPdf, 
  TableChart,
  CloudUpload
} from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { uploadPDF, uploadExcelAzure, uploadExcelReseller, forceContainerReload } from "../../Services/fileUploadService";

const FileUpload = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success"
  });
  
  // Refs para os inputs de arquivo
  const pdfInputRef = useRef(null);
  const excelAzureInputRef = useRef(null);
  const excelResellerInputRef = useRef(null);

  useEffect(() => {
    document.title = `Carregar Ficheiros | Portal Cloud Services`;
  }, []);

  // Função para fechar o snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Função para mostrar um snackbar de sucesso
  const showSuccess = (message) => {
    setSnackbar({
      open: true,
      message,
      severity: "success"
    });
  };

  // Função para mostrar um snackbar de erro
  const showError = (message) => {
    setSnackbar({
      open: true,
      message,
      severity: "error"
    });
  };

  // Handler para o botão "Selecionar Ficheiro" do PDF
  const handlePDFButtonClick = () => {
    pdfInputRef.current.click();
  };

  // Handler para quando um arquivo PDF é selecionado
  const handlePDFFileChange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      setLoading(true);
      await uploadPDF(file);
      await forceContainerReload();
      showSuccess(`Ficheiro PDF "${file.name}" carregado com sucesso!`);
      // Limpar o input para permitir carregar o mesmo arquivo novamente
      event.target.value = null;
    } catch (error) {
      showError(`Erro ao carregar o ficheiro PDF: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handler para o botão "Selecionar Ficheiro" do Excel Azure
  const handleExcelAzureButtonClick = () => {
    excelAzureInputRef.current.click();
  };

  // Handler para quando um arquivo Excel Azure é selecionado
  const handleExcelAzureFileChange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      setLoading(true);
      await uploadExcelAzure(file);
      await forceContainerReload();
      showSuccess(`Ficheiro Excel Azure "${file.name}" carregado com sucesso!`);
      // Limpar o input para permitir carregar o mesmo arquivo novamente
      event.target.value = null;
    } catch (error) {
      showError(`Erro ao carregar o ficheiro Excel Azure: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handler para o botão "Selecionar Ficheiro" do Excel Reseller
  const handleExcelResellerButtonClick = () => {
    excelResellerInputRef.current.click();
  };

  // Handler para quando um arquivo Excel Reseller é selecionado
  const handleExcelResellerFileChange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    try {
      setLoading(true);
      await uploadExcelReseller(file);
      await forceContainerReload();
      showSuccess(`Ficheiro Excel Reseller "${file.name}" carregado com sucesso!`);
      // Limpar o input para permitir carregar o mesmo arquivo novamente
      event.target.value = null;
    } catch (error) {
      showError(`Erro ao carregar o ficheiro Excel Reseller: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Box sx={{ padding: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CloudUpload color="primary" /> Carregar Ficheiros
        </Typography>

        <Paper sx={{ p: 3, mt: 2 }} variant="outlined">
          <Grid container spacing={4} justifyContent="center">
            
            {/* Opção 1: Carregar PDF */}
            <Grid item xs={12} sm={6} md={4}>
              <Card 
                variant="outlined" 
                sx={{ 
                  height: '100%', 
                  display: 'flex', 
                  flexDirection: 'column',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                  }
                }}
              >
                <CardMedia
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: '#f5f5f5',
                    p: 2,
                    height: 140
                  }}
                >
                  <PictureAsPdf sx={{ fontSize: 80, color: '#f44336' }} />
                </CardMedia>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography gutterBottom variant="h5" component="h2" align="center">
                    Carregar PDF
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Carregue ficheiros PDF para processamento no sistema.
                  </Typography>
                </CardContent>
                <CardActions sx={{ justifyContent: 'center', pb: 2 }}>
                  <input
                    type="file"
                    accept="application/pdf"
                    style={{ display: 'none' }}
                    ref={pdfInputRef}
                    onChange={handlePDFFileChange}
                  />
                  <Button 
                    size="large" 
                    variant="contained" 
                    startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <CloudUpload />}
                    onClick={handlePDFButtonClick}
                    disabled={loading}
                  >
                    Selecionar Ficheiro
                  </Button>
                </CardActions>
              </Card>
            </Grid>
            
            {/* Opção 2: Carregar Excel Azure */}
            <Grid item xs={12} sm={6} md={4}>
              <Card 
                variant="outlined" 
                sx={{ 
                  height: '100%', 
                  display: 'flex', 
                  flexDirection: 'column',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                  }
                }}
              >
                <CardMedia
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: '#f5f5f5',
                    p: 2,
                    height: 140
                  }}
                >
                  <TableChart sx={{ fontSize: 80, color: '#4caf50' }} />
                </CardMedia>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography gutterBottom variant="h5" component="h2" align="center">
                    Carregar Excel Azure
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Carregue ficheiros Excel para processamento relacionado com Azure.
                  </Typography>
                </CardContent>
                <CardActions sx={{ justifyContent: 'center', pb: 2 }}>
                  <input
                    type="file"
                    accept=".xlsx, .xls, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    style={{ display: 'none' }}
                    ref={excelAzureInputRef}
                    onChange={handleExcelAzureFileChange}
                  />
                  <Button 
                    size="large" 
                    variant="contained" 
                    color="success" 
                    startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <CloudUpload />}
                    onClick={handleExcelAzureButtonClick}
                    disabled={loading}
                  >
                    Selecionar Ficheiro
                  </Button>
                </CardActions>
              </Card>
            </Grid>
            
            {/* Opção 3: Carregar Excel Reseller */}
            <Grid item xs={12} sm={6} md={4}>
              <Card 
                variant="outlined" 
                sx={{ 
                  height: '100%', 
                  display: 'flex', 
                  flexDirection: 'column',
                  transition: 'transform 0.2s, box-shadow 0.2s',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                  }
                }}
              >
                <CardMedia
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: '#f5f5f5',
                    p: 2,
                    height: 140
                  }}
                >
                  <TableChart sx={{ fontSize: 80, color: '#2196f3' }} />
                </CardMedia>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography gutterBottom variant="h5" component="h2" align="center">
                    Carregar Excel Reseller
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Carregue ficheiros Excel para processamento relacionado com Reseller.
                  </Typography>
                </CardContent>
                <CardActions sx={{ justifyContent: 'center', pb: 2 }}>
                  <input
                    type="file"
                    accept=".xlsx, .xls, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    style={{ display: 'none' }}
                    ref={excelResellerInputRef}
                    onChange={handleExcelResellerFileChange}
                  />
                  <Button 
                    size="large" 
                    variant="contained" 
                    color="primary" 
                    startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <CloudUpload />}
                    onClick={handleExcelResellerButtonClick}
                    disabled={loading}
                  >
                    Selecionar Ficheiro
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          </Grid>
        </Paper>
      </Box>

      {/* Snackbar para feedback ao usuário */}
      <Snackbar 
        open={snackbar.open} 
        autoHideDuration={6000} 
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbar.severity} 
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default FileUpload; 