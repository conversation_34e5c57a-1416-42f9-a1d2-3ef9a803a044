﻿using System;
using Microsoft.Azure.Cosmos;

namespace RnD.BackEnd.Infrastructure.Interfaces
{
    /// <summary>
    /// Cosmos DB manager interface
    /// </summary>
    /// <seealso cref="System.IDisposable" />
    public interface ICosmosDbManager : IDisposable
    {
        /// <summary>
        /// Gets the container.
        /// </summary>
        /// <param name="database">The database name.</param>
        /// <param name="container">The container name.</param>
        /// <returns>
        /// The container.
        /// </returns>
        Container GetContainer(string database, string container);
    }
}
