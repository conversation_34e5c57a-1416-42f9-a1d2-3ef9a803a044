﻿using AutoMapper;
using RnD.BackEnd.Domain.Entities.Vehicle;
using RnD.BackEnd.Infrastructure.DataModels.Vehicle;

namespace RnD.BackEnd.Infrastructure.MappingProfiles
{
    /// <summary>
    /// Vehicle mapping profile
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class VehicleProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="VehicleProfile"/> class.
        /// </summary>
        public VehicleProfile()
        {
            CreateMap<VehicleModel, Vehicle>();
            CreateMap<Vehicle, VehicleModel>();
        }
    }
}
