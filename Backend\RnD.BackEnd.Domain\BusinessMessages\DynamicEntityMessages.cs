﻿namespace RnD.BackEnd.Domain.BusinessMessages
{
    public static class DynamicEntityMessages
    {
        public const string Error_Get = "Error getting entity with id {0}.";
        public const string Error_Delete = "Error deleting entity.";
        public const string Error_Update = "Error updating entity.";
        public const string Error_Create = "Error creating entity.";
        public const string Error_List = "Error getting list of entities.";
        public const string Not_Found = "Entity not found";
        public const string Id_Mandatory = "Id is mandatory";
    }
}
