using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json.Serialization;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Extensions;
using RnD.BackEnd.API.Helpers;
using RnD.BackEnd.API.Models;
using RnD.BackEnd.Domain.Constants;
using RnD.BackEnd.Domain.Extensions;
using RnD.BackEnd.Domain.Settings;
using RnD.BackEnd.Domain.Settings.Cosmos;
using RnD.BackEnd.Licensing.Extensions;
using RnD.BackEnd.Licensing.Middlewares;
using RnD.BackEnd.Logging;
using Swashbuckle.AspNetCore.Filters;
using System.Security.Claims;
using RnD.BackEnd.API.Services;
using Microsoft.AspNetCore.Http;

var builder = WebApplication.CreateBuilder(args);

//Excel Data Reader
System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
builder.Services.AddScoped<ExcelAzureReaderService>();
builder.Services.AddScoped<LogProcessamentoService>();
builder.Services.AddScoped<IClienteService, ClienteService>();
builder.Services.AddScoped<IConsumoSubscricaoExcelService, ConsumoSubscricaoExcelService>();
builder.Services.AddScoped<IConsumoPDFService, ConsumoPDFService>();
builder.Services.AddScoped<IPDFProcessor, ArrowPDFProcessor>();

// Serviço para testes de extração Crayon (sem persistência na BD)
builder.Services.AddScoped<CrayonTestExtractorService>();

// Configure Azure Blob Storage
builder.Services.AddSingleton(x => new BlobServiceClient(builder.Configuration["AzureStorage:ConnectionString"]));

// Configure Azure Blob Storage for PDF (named service)
builder.Services.AddSingleton<BlobServiceClient>(x => new BlobServiceClient(builder.Configuration["AzureStoragePDF:ConnectionString"]));
builder.Services.AddHostedService<ConsumoSubscricaoMonitorBackgroundService>();
builder.Services.AddHostedService<PDFMonitorBackgroundService>();
// Configure settings
AppSettings appSettings = builder.Services.ConfigureAppSettings(builder.Configuration);
builder.Services.ConfigureConnectionStrings(builder.Configuration);

// Configure Azure Ad settings
AzureAd azureAd = builder.Services.ConfigureAzureAd(builder.Configuration);

// Configure data access layer
builder.Services.ConfigureInfrastructure(appSettings);

// Adiciona o DbContext e configura a conexão com SQL Server ??
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DatabaseCS")));

// Configure business services
builder.Services.ConfigureBusinessServices();

// Configure license validation
builder.Services.ConfigureLicenseValidation();

//Configure policies
builder.Services.ConfigurePolicies();

// Application Insights
builder.Services.AddApplicationInsightsTelemetry();

// Automapper
builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

// Configure authentication
builder.Services.AddAuthentication(sharedOptions =>
{
    sharedOptions.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
    sharedOptions.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
   .AddJwtBearer(options =>
   {
       options.Audience = $"api://{azureAd.ClientId}";
       options.Authority = $"{azureAd.Instance}{azureAd.TenantId}";

       // Desabilitar validações para diagnóstico
       options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
       {
           ValidateIssuer = true,
           ValidIssuer = $"https://sts.windows.net/{azureAd.TenantId}/",
           ValidateAudience = true,
           ValidAudience = $"api://{azureAd.ClientId}",
           ValidateLifetime = true,
           ValidateIssuerSigningKey = true,
           RequireSignedTokens = false,
           // Indicar que estamos usando Azure AD B2C
           NameClaimType = "name",
           RoleClaimType = "roles"
       };

       // Habilitar salvamento do token
       options.SaveToken = true;

       // Configuração para salvar informações de user após autenticação
       options.Events = new JwtBearerEvents
       {
           OnMessageReceived = context =>
           {
               Console.WriteLine(">>> EVENTO: OnMessageReceived");
               Console.WriteLine($">>> Token: {context.Token?.Substring(0, Math.Min(20, context.Token?.Length ?? 0))}...");
               return Task.CompletedTask;
           },

           OnAuthenticationFailed = context =>
           {
               Console.WriteLine(">>> EVENTO: OnAuthenticationFailed");
               Console.WriteLine($">>> Erro: {context.Exception.Message}");
               Console.WriteLine($">>> Tipo de erro: {context.Exception.GetType().Name}");

               if (context.Exception.InnerException != null)
               {
                   Console.WriteLine($">>> Inner Exception: {context.Exception.InnerException.Message}");
                   Console.WriteLine($">>> Inner Exception Type: {context.Exception.InnerException.GetType().Name}");
               }

               // Capturar a stack trace para debugging
               Console.WriteLine($">>> Stack Trace: {context.Exception.StackTrace}");

               return Task.CompletedTask;
           },

           OnChallenge = context =>
           {
               Console.WriteLine(">>> EVENTO: OnChallenge");
               return Task.CompletedTask;
           },

           OnForbidden = context =>
           {
               Console.WriteLine(">>> EVENTO: OnForbidden");
               return Task.CompletedTask;
           },

           OnTokenValidated = async context =>
           {
               Console.WriteLine("\n>>> EVENTO: OnTokenValidated");
               Console.WriteLine(">>> ==========================================");

               try
               {
                   // Log de todas as claims disponíveis
                   Console.WriteLine("\n>>> TODAS AS CLAIMS DISPONÍVEIS:");
                   foreach (var claim in context.Principal.Claims)
                   {
                       Console.WriteLine($">>> Tipo: {claim.Type}");
                       Console.WriteLine($">>> Valor: {claim.Value}");
                       Console.WriteLine(">>> ------------------------------------------");
                   }

                   // Obter o ID do Azure AD do token
                   var azureAdId = context.Principal.FindFirstValue("http://schemas.microsoft.com/identity/claims/objectidentifier") ??
                                   context.Principal.FindFirstValue("oid");

                   Console.WriteLine($"\n>>> AzureAdId: {azureAdId ?? "NÃO ENCONTRADO"}");

                   // Se não encontrou o AzureId, tenta usar o Name como identificador alternativo
                   if (string.IsNullOrEmpty(azureAdId))
                   {
                       azureAdId = context.Principal.FindFirstValue(ClaimTypes.NameIdentifier) ??
                                   context.Principal.FindFirstValue("sub") ??
                                   Guid.NewGuid().ToString();

                       Console.WriteLine($">>> Usando ID alternativo: {azureAdId}");
                   }

                   // Acessar o contexto do banco de dados usando o escopo do request
                   var dbContext = context.HttpContext.RequestServices.GetRequiredService<ApplicationDbContext>();

                   // Verificar se o user já existe no banco de dados
                   var existingUser = await dbContext.User.FirstOrDefaultAsync(u => u.AzureId == azureAdId);

                   Console.WriteLine($"\n>>> user existente: {(existingUser != null ? "Sim" : "Não")}");

                   // Obter informações corretas do user das claims
                   var name = context.Principal.FindFirstValue("name") ??
                              context.Principal.FindFirstValue(ClaimTypes.Name) ??
                              context.Principal.Identity?.Name ??
                              "user " + azureAdId.Substring(0, Math.Min(azureAdId.Length, 8));

                   var email = context.Principal.FindFirstValue(ClaimTypes.Upn) ??
                              context.Principal.FindFirstValue("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/upn") ??
                              context.Principal.FindFirstValue("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name") ??
                              context.Principal.FindFirstValue("upn") ??
                              context.Principal.FindFirstValue("preferred_username") ??
                              context.Principal.FindFirstValue("unique_name") ??
                              context.Principal.FindFirstValue(ClaimTypes.Email) ??
                              $"user-{azureAdId.Substring(0, Math.Min(azureAdId.Length, 8))}@example.com";

                   Console.WriteLine($"\n>>> Informações obtidas do token:");
                   Console.WriteLine($">>> Nome: {name}");
                   Console.WriteLine($">>> Email: {email}");

                   if (existingUser == null)
                   {
                       // Se não existe, criar um novo user
                       var newUser = new User
                       {
                           AzureId = azureAdId,
                           Username = name,
                           Email = email
                       };

                       try
                       {
                           dbContext.User.Add(newUser);
                           await dbContext.SaveChangesAsync();

                           // Adicionar permissão padrão (903001)
                           var defaultPermission = await dbContext.Permission
                               .FirstOrDefaultAsync(p => p.Id == 1);

                           if (defaultPermission != null)
                           {
                               var userPermission = new UserPermission
                               {
                                   UserID = newUser.UserID,
                                   PermissionID = defaultPermission.Id
                               };

                               dbContext.UserPermission.Add(userPermission);
                               await dbContext.SaveChangesAsync();
                           }

                           Console.WriteLine(">>> Usuário criado com sucesso e permissão padrão atribuída!");
                       }
                       catch (Exception ex)
                       {
                           Console.WriteLine($">>> ERRO ao criar user: {ex.Message}");
                           if (ex.InnerException != null)
                           {
                               Console.WriteLine($">>> Detalhe: {ex.InnerException.Message}");
                           }
                       }
                   }
                   else
                   {
                       // Se já existe, atualizar as informações do user
                       bool alteracaoRealizada = false;

                       // Verificar se as informações são diferentes e atualizar
                       if (existingUser.Email != email)
                       {
                           Console.WriteLine($">>> Atualizando email de '{existingUser.Email}' para '{email}'");
                           existingUser.Email = email;
                           alteracaoRealizada = true;
                       }

                       if (existingUser.Username != name)
                       {
                           Console.WriteLine($">>> Atualizando nome de '{existingUser.Username}' para '{name}'");
                           existingUser.Username = name;
                           alteracaoRealizada = true;
                       }

                       if (alteracaoRealizada)
                       {
                           try
                           {
                               await dbContext.SaveChangesAsync();
                               Console.WriteLine(">>> user atualizado com sucesso!");
                           }
                           catch (Exception ex)
                           {
                               Console.WriteLine($">>> ERRO ao atualizar user: {ex.Message}");
                               if (ex.InnerException != null)
                               {
                                   Console.WriteLine($">>> Detalhe: {ex.InnerException.Message}");
                               }
                           }
                       }
                       else
                       {
                           Console.WriteLine(">>> Informações do user já estão atualizadas");
                       }
                   }
               }
               catch (Exception ex)
               {
                   // Apenas logar o erro mas não impedir a autenticação
                   Console.WriteLine($">>> ERRO no OnTokenValidated: {ex.Message}");
                   if (ex.InnerException != null)
                   {
                       Console.WriteLine($">>> Detalhe: {ex.InnerException.Message}");
                   }
               }
           }
       };
   });

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy(AuthenticationPolicy.AZURE_AD, new AuthorizationPolicyBuilder()
           .RequireAuthenticatedUser()
           .AddAuthenticationSchemes(JwtBearerDefaults.AuthenticationScheme)
           .Build());
});

builder.Services.AddControllers()
    .AddNewtonsoftJson((options) =>
    {
        options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver
        {
            NamingStrategy = new CamelCaseNamingStrategy
            {
                // Do not change dictionary keys casing
                ProcessDictionaryKeys = false
            }
        };

        // Ignorar referências circulares
        options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
    });

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddHttpContextAccessor();

// Swagger Configuration
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc("v1", new OpenApiInfo
    {
        Version = "v1",
        Title = "R&D Backend Framework"
    });

    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme.",
        Type = SecuritySchemeType.Http,
        Scheme = "bearer"
    });

    options.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme()
            {
                Reference = new OpenApiReference{
                    Id = "Bearer",
                    Type = ReferenceType.SecurityScheme
                }
            },
            new List<string>()
        }
    });

    options.SchemaFilter<EnumSchemaFilter>();

    options.ExampleFilters();

    options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, $"{Assembly.GetExecutingAssembly().GetName().Name}.xml"));
});
builder.Services.AddSwaggerExamplesFromAssemblyOf<Program>();

builder.Services.Configure<ConnectionStrings>(builder.Configuration.GetSection(nameof(ConnectionStrings)));
builder.Services.Configure<CosmosSettings>(builder.Configuration.GetSection(nameof(AppSettings)).GetSection(nameof(CosmosSettings)));
builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection(nameof(AppSettings)).GetSection(nameof(EmailSettings)));

builder.Services.Configure<ApiBehaviorOptions>(options =>
{
    options.SuppressConsumesConstraintForFormFileParameters = true;
    options.SuppressInferBindingSourcesForParameters = true;
    options.SuppressModelStateInvalidFilter = false;
});

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll",
        builder =>
        {
            builder
               .WithOrigins("*")

                .AllowAnyMethod()
                .AllowAnyHeader();
        });
});

// O serviço de background agora usa IServiceScopeFactory
builder.Services.AddHostedService<ExcelMonitorBackgroundService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment() || app.Environment.IsProduction())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}


// Use CORS
app.UseCors("AllowAll");

app.UseHttpsRedirection();
app.UseAuthentication();
app.UseRouting();
app.UseAuthorization();
app.MapControllers();
app.UseMiddleware<LocalizationMiddleware>();
app.UseMiddleware<LoggingMiddleware>();
//app.UseMiddleware<LicenseValidationMiddleware>();
app.Run();
