﻿using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.Domain.Entities.Exception;

namespace RnD.BackEnd.Domain.Entities.Generic
{
    public class ServiceOutput<T>
    {
        public HttpStatusCode Code { get; set; } = HttpStatusCode.OK;

        public string Description { get; set; }

        public T Value { get; set; }

        public bool Error { get; set; }

        public ModelException ExceptionMessages { get; set; }

        public Dictionary<string, object> Properties { get; set; }

        public ServiceOutput()
        {
            ExceptionMessages = new ModelException();
            Properties = new Dictionary<string, object>();
        }
    }
}
