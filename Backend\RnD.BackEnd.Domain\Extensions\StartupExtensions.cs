﻿using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RnD.BackEnd.Domain.Settings;

namespace RnD.BackEnd.Domain.Extensions
{
    /// <summary>
    /// Startup extensions class with configuration methods to be used by API, Jobs, etc.
    /// </summary>
    public static class StartupExtensions
    {
        /// <summary>
        /// Configures the application settings.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <param name="configuration">The configuration.</param>
        /// <returns>
        /// The App Settings object
        /// </returns>
        /// <exception cref="RnD.BackEnd.Domain.Entities.Exception">
        /// No appsettings section has been found
        /// or
        /// No valid settings.
        /// </exception>
        /// <exception cref="System.Exception">No appsettings section has been found
        /// or
        /// No valid settings.</exception>
        public static AppSettings ConfigureAppSettings(this IServiceCollection services, IConfiguration configuration)
        {
            IConfigurationSection appSettingsSection = configuration.GetSection(nameof(AppSettings));
            if (appSettingsSection == null)
            {
                throw new ArgumentException("No appsettings section has been found");
            }

            AppSettings appSettings = appSettingsSection.Get<AppSettings>();

            if (!appSettings.IsValid())
            {
                throw new ArgumentException("No valid settings.");
            }

            services.Configure<AppSettings>(appSettingsSection);

            return appSettings;
        }

        /// <summary>
        /// Configures the connection strings.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <param name="configuration">The configuration.</param>
        /// <returns>
        /// The Connection strings object.
        /// </returns>
        /// <exception cref="RnD.BackEnd.Domain.Entities.Exception">No ConnectionStrings section has been found</exception>
        /// <exception cref="System.Exception">No ConnectionStrings section has been found</exception>
        public static ConnectionStrings ConfigureConnectionStrings(this IServiceCollection services, IConfiguration configuration)
        {
            IConfigurationSection connectionStringsSection = configuration.GetSection(nameof(ConnectionStrings));
            if (connectionStringsSection == null)
            {
                throw new ArgumentException("No ConnectionStrings section has been found");
            }

            ConnectionStrings connectionStrings = connectionStringsSection.Get<ConnectionStrings>();

            services.Configure<ConnectionStrings>(connectionStringsSection);

            return connectionStrings;
        }

        /// <summary>
        /// Configures the azure ad.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <param name="configuration">The configuration.</param>
        /// <returns>
        /// The Azure Ad settings object.
        /// </returns>
        /// <exception cref="RnD.BackEnd.Domain.Entities.Exception">No AzureAd section has been found</exception>
        /// <exception cref="Access4all.BackEnd.Domain.Entities.Exception">No AzureAd section has been found</exception>
        public static AzureAd ConfigureAzureAd(this IServiceCollection services, IConfiguration configuration)
        {
            IConfigurationSection azureAdSection = configuration.GetSection(nameof(AzureAd));
            if (azureAdSection == null)
            {
                throw new ArgumentException("No AzureAd section has been found");
            }

            AzureAd azureAd = azureAdSection.Get<AzureAd>();

            services.Configure<AzureAd>(azureAdSection);

            return azureAd;
        }
    }
}
