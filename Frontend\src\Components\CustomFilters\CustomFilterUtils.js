export const handleAddCustomFilterUtils = (value, selectedCustomFilters) => {
  console.log("value", value)
  console.log("selectedCustomFilters", selectedCustomFilters)
  const _selectedCustomFilters = [...selectedCustomFilters];
  const matchIndex = _selectedCustomFilters.findIndex(x => x.elementID === value.elementID);
  if (matchIndex > -1) {
    if (value.conditionLabel) {
      _selectedCustomFilters.push(value);
    } else {
      const valuesSet = new Set(_selectedCustomFilters[matchIndex].selectedValues.map(d => d.value));
      _selectedCustomFilters[matchIndex].selectedValues = [..._selectedCustomFilters[matchIndex].selectedValues, ...value.selectedValues.filter(d => !valuesSet.has(d.value))];
      _selectedCustomFilters[matchIndex].value = _selectedCustomFilters[matchIndex].value.concat(value.value.filter((item) => _selectedCustomFilters[matchIndex].value.indexOf(item) < 0));
    }
  } else {
    _selectedCustomFilters.push(value);
  }
  return _selectedCustomFilters;
};

export const handleEditCustomFilterUtils = (customFilterEdited, selectedCustomFilters) => {
  const _selectedCustomFilters = [...selectedCustomFilters];
  const matchIndex = _selectedCustomFilters.findIndex(x => x.elementID === customFilterEdited.elementID);
  if (matchIndex > -1) {
    if (customFilterEdited.conditionLabel) {
      _selectedCustomFilters.push(customFilterEdited);
    } else if (matchIndex === customFilterEdited.customFilterIndex) {
      _selectedCustomFilters[matchIndex] = customFilterEdited;
    } else {
      _selectedCustomFilters.splice(customFilterEdited.customFilterIndex, 1);
      const valuesSet = new Set(_selectedCustomFilters[matchIndex].selectedValues.map(d => d.value));
      _selectedCustomFilters[matchIndex].selectedValues = [..._selectedCustomFilters[matchIndex].selectedValues, ...customFilterEdited.selectedValues.filter(d => !valuesSet.has(d.value))];
      _selectedCustomFilters[matchIndex].value = _selectedCustomFilters[matchIndex].value.concat(customFilterEdited.value.filter((item) => _selectedCustomFilters[matchIndex].value.indexOf(item) < 0));
    }
  } else {
    _selectedCustomFilters.push(customFilterEdited);
  }
  return _selectedCustomFilters;
}

export const handleDeleteCustomFilterUtils = (value, selectedCustomFilters) => {
  const _selectedCustomFilters = [...selectedCustomFilters];
  _selectedCustomFilters.splice(value, 1);
  return _selectedCustomFilters;
};
