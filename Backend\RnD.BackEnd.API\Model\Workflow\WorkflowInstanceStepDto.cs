﻿using System;
using System.Collections.Generic;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Workflow instance step DTO class
    /// </summary>
    public class WorkflowInstanceStepDto
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the step identifier.
        /// </summary>
        /// <value>
        /// The step identifier.
        /// </value>
        public Guid? StepId { get; set; }

        /// <summary>
        /// Gets or sets the instance identifier.
        /// </summary>
        /// <value>
        /// The instance identifier.
        /// </value>
        public Guid InstanceId { get; set; }

        /// <summary>
        /// Gets or sets the display name.
        /// </summary>
        /// <value>
        /// The display name.
        /// </value>
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [all users must approve].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [all users must approve]; otherwise, <c>false</c>.
        /// </value>
        public bool AllUsersMustApprove { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [skip over other tasks].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [skip over other tasks]; otherwise, <c>false</c>.
        /// </value>
        public bool SkipOverOtherTasks { get; set; }

        /// <summary>
        /// Gets or sets the order.
        /// </summary>
        /// <value>
        /// The order.
        /// </value>
        public int Order { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this instance is active.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is active; otherwise, <c>false</c>.
        /// </value>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the step status.
        /// </summary>
        /// <value>
        /// The step status.
        /// </value>
        public string StepStatus { get; set; }

        /// <summary>
        /// Gets or sets the number of actions required.
        /// </summary>
        /// <value>
        /// The number of actions required.
        /// </value>
        public int NumberOfActionsRequired { get; set; }

        /// <summary>
        /// Gets or sets the number of actions completed.
        /// </summary>
        /// <value>
        /// The number of actions completed.
        /// </value>
        public int NumberOfActionsCompleted { get; set; }

        /// <summary>
        /// Gets or sets the tasks.
        /// </summary>
        /// <value>
        /// The tasks.
        /// </value>
        public IList<WorkflowInstanceTaskDto> Tasks { get; set; }
    }
}
