import { useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { LinearProgress } from '@mui/material';
import UserTable from '../../../Components/Examples/Users/<USER>';
import { userAPI } from '../../../API/userAPI';
import { filtersAPI } from '../../../API/filtersAPI';
import useFilters from '../../../Context/Hooks/useFilters';

const UserList = () => {
  const [userList, setUserList] = useState([]);
  const [isLoading, setLoading] = useState(true);
  //--Filters--
  const [customFiltersList, setCustomFiltersList] = useState([]);
  const { filters } = useFilters();
  const [selectedCustomFilters, setSelectedCustomFilters] = useState(filters.PROFILE && filters.PROFILE.length > 0 ? filters.PROFILE : []);

  /*
  API call to get filters:
      params: context
  */
  const getAvailableFilters = useCallback(async () => {
    try {
      const _customFiltersList = await filtersAPI.getAvailableFilters({ context: "PROFILE" });

      if (_customFiltersList && !_customFiltersList.error) {
        setCustomFiltersList(_customFiltersList);
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  /*
  API call to get data to populate table:
      params: props (filters)
  */
  const getUserList = useCallback(async () => {
    try {
      setUserList([]);

      const props = { filters: selectedCustomFilters }
      const data = await userAPI.GetUsersList(props);
      setLoading(false);

      if (!data.error) {
        const list = [];
        data.value.forEach(user => list.push(user))
        setUserList(list);
      } else if (data.exceptionMessages && data.exceptionMessages.hasMessages) {
        data.exceptionMessages.messages.forEach(m => {
          toast.error(m.description);
        })
      } else {
        toast.error("Ocorreu um erro inesperado")
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  /*
  When there are multiple roleIDs, separated by commas,
  check if one of the items matches the filter value */
  const getFromMultipleID = (element, ids) => {
    const elementCollection = element.split(',');
    const result = ids.filter(id => elementCollection.find(x => x === id))
    return result.length > 0;
  }

  const value = filters?.PROFILE[0]?.value
  const handleFilter = () => {
    if (filters?.PROFILE[0]?.elementID === "CheckBox") {
      const newList = userList.filter((element) => {
        return getFromMultipleID(element.roleIDs, value)
      })
      setUserList(newList)
    } else if (filters?.PROFILE[0]?.condition === "eq") {
      const selected = filters?.PROFILE[0].selectedValues[0].value
      const newList = userList.filter((element) => element.roleIDs === selected)
      setUserList(newList)
    } else {
      const newList = userList.filter(roles => roles.roleNames.toLowerCase().includes(value))
      setUserList(newList)
    }
  }

  useEffect(() => {
    getAvailableFilters();
  }, [getAvailableFilters]);

  useEffect(() => {
    /*If there are no filters selected, data will be rendered in full*/
    if (!filters?.PROFILE.length) {
      getUserList()
    } else {
      /*
      Whenever a filter is selected:
        profiles list will be re-rendered (newList) and there will be a match check
      */
      handleFilter()
    }
  }, [filters]);

  return (
    <>
      {isLoading && <LinearProgress
        variant="indeterminate"
        // top must be header min height
        sx={{ width: '100%', position: 'absolute', top: '64px', left: 0 }}
      />}
      <UserTable
        userList={userList}
        isLoading={isLoading}
        customFiltersList={customFiltersList}
        onSelectedCustomFilters={setSelectedCustomFilters}
      />
    </>
  );
};

export default UserList;
