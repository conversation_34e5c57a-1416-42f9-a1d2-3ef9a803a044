﻿using System;
using System.Collections.Generic;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Weather;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController
{
    /// <summary>
    /// Weather example of a 201 response
    /// </summary>
    public class Weather201 : IExamplesProvider<ApiOutput<WeatherDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a weather response for 201.
        /// </returns>
        public ApiOutput<WeatherDto> GetExamples()
        {
            return new ApiOutput<WeatherDto>
            {
                Code = System.Net.HttpStatusCode.Created,
                Value = new WeatherDto
                {
                    Id = Guid.NewGuid(),
                    Location = "Warsaw",
                    Summary = "Very cold",
                    Temperature = -5
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}