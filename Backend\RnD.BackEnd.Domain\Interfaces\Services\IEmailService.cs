﻿using System.Threading.Tasks;
using RnD.BackEnd.Domain.Entities.Email;
using RnD.BackEnd.Domain.Entities.Generic;

namespace RnD.BackEnd.Domain.Interfaces.Services
{
    /// <summary>
    /// Email operations service interface
    /// </summary>
    public interface IEmailService
    {
        /// <summary>
        /// Sends the templated email.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="templateId">The template identifier.</param>
        /// <returns>
        /// The service response.
        /// </returns>
        Task<ServiceOutput<bool>> SendTemplatedEmail(EmailMessage<object> request, string templateId);

        /// <summary>
        /// Sends the code academy email.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The service response.
        /// </returns>
        Task<ServiceOutput<bool>> SendCodeAcademyEmail(EmailMessage<CodeAcademyTemplate> request);

        /// <summary>
        /// Sends the default template email.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The service response.
        /// </returns>
        Task<ServiceOutput<bool>> SendDefaultTemplateEmail(EmailMessage<BaseEmail> request);

        /// <summary>
        /// Sends the default template email with attachments.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The service response.
        /// </returns>
        Task<ServiceOutput<bool>> SendDefaultTemplateEmailWithAttachments(EmailAttachments<BaseEmail> request);
    }
}