/* eslint-disable no-unused-vars */
import React, { useState } from "react";
import {
  <PERSON>,
  Typography,
  Divider,
  Card,
  CardHeader,
  CardContent,
  Grid,
  Tooltip,
  IconButton,
  Link,
} from "@mui/material";
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import { useTheme } from '@emotion/react';
import FloatingTab from "../../../Components/FloatingTab";
import Metrics from "../../../Components/FloatingTab/ApplicationExample/Metrics";
import getExampleIntro from "./mock/ExampleIntro";
import getExampleFullCode from "./mock/ExampleFullCode";
import getCodeIntro from "./mock/CodeIntro";
import getCode from "./mock/Code";
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import {
  ExpandMore as ExpandMoreIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from "@mui/icons-material";
import getMetricsIntro from "./mock/metricsIntro";
import getMetrics from "./mock/metrics";
import { styled } from "@mui/system";

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh",
})

const CustomEditor = styled(Editor)(({ theme }) => ({
  padding: 10,
  fontFamily: '"Fira code", "Fira Mono", monospace',
  fontSize: 12,
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
}))

const metrics = [
  {
    title: "Lorem ipsum dolor sit amet",
    value: "100%",
    subtitle: null,
    note: null,
    order: 0,
  },
  {
    title: "Lorem ipsum dolor",
    value: 0.5,
    subtitle: "Lorem  (10.00%) + Ipsum  (90.00%)",
    note: null,
    order: 0,
  },
  {
    title: "Lorem ipsum",
    value: 0.06,
    subtitle: "Lorem ipsum dolor (87.50%) + Lorem ipsum dolor (12.50%)",
    note: "",
    order: 0,
  },
];

const FloatingTabs = () => {
  const theme = useTheme();
  const [floatingTabOpen, setFloatingTabOpen] = useState(true);
  const [exampleIntro, setExampleIntro] = useState(getExampleIntro);
  const [example, setExample] = useState(getExampleFullCode);
  const [codeIntro, setCodeIntro] = useState(getCodeIntro);
  const [code, setCode] = useState(getCode);
  const [contentIntro, setContentIntro] = useState(getMetricsIntro);
  const [content, setContent] = useState(getMetrics);
  const [open, setOpen] = useState({
    index: false,
    code: false,
    content: false,
  });

  const handleToggle = (e) => {
    const name = e?.currentTarget?.name;
    setOpen({
      ...open,
      [name]: !open[name],
    });
  };

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box
        sx={{
          m: "auto",
          textAlign: "center",
        }}
      >
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h2>Floating Tab</h2>
          <p>
            This component creates a tab that can be hidden by the user whenever
            they want. It may contain statistical data, information, or any
            other content that may be relevant to the user.
            <br />
            The following code snippet demonstrates how Floating Tab is
            displayed.{" "}
          </p>
        </Box>
        <Grid>
          {metrics && metrics.length > 0 && (
            <FloatingTab
              onOpen={() => {
                setFloatingTabOpen(floatingTabOpen);
              }}
              onClose={() => setFloatingTabOpen(!floatingTabOpen)}
              data={
                <Box>
                  {metrics.map((t, i) => {
                    return (
                      <Box
                        key={i}
                        sx={{
                          mb: i !== metrics.length - 1 ? 2 : 0,
                        }}
                      >
                        <Metrics data={t} />
                        {i !== metrics.length - 1 && <Divider />}
                      </Box>
                    );
                  })}
                </Box>
              }
            />
          )}
        </Grid>
      </Box>
      <StyledEditorBox>
        <CardHeader
          action={
            <>
              <CopyToClipboardButton
                value={open?.index ? example : exampleIntro}
              />
              <Tooltip
                title={
                  !open?.index
                    ? "Show full source Code"
                    : "Hide full Source Code"
                }
              >
                <IconButton
                  name="index"
                  onClick={handleToggle}
                  sx={{
                    color: `${theme.palette.primary.main}`,
                    "&:hover": {
                      backgroundColor: `${theme.palette.secondary.main}`,
                      color: "primary.main",
                    },
                    height: 35,
                    width: 35,
                    mt: 1,
                  }}
                >
                  {!open?.index ? <ExpandMoreIcon /> : <KeyboardArrowUpIcon />}
                </IconButton>
              </Tooltip>
            </>
          }
        />
        <Divider />
        <CardContent
          sx={{
            maxHeight: "50vh",
            overflowY: "scroll",
          }}
        >
          {open?.index ?
            <CustomEditor
              value={example}
              onValueChange={(_example) => setExample(_example)}
              highlight={(_example) => highlight(_example, languages.js)}
            />
            :
            <CustomEditor
              value={exampleIntro}
              onValueChange={(_exampleIntro) => setExampleIntro(_exampleIntro)}
              highlight={(_exampleIntro) => highlight(_exampleIntro, languages.js)}
            />}
        </CardContent>
      </StyledEditorBox>
      <Typography
        color="textPrimary"
        sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
      >
        In this example, statistical data were used (i.e. this tool can be
        useful in reviews, ratings, etc.)
        <br />A component was used to draw the graphics (CircularProgress from
        <Link
          href="https://mui.com/"
          underline="none"
        >
          Material UI
        </Link>
        )
      </Typography>
      <StyledEditorBox>
        <CardHeader
          action={
            <>
              <CopyToClipboardButton
                value={open?.content ? content : contentIntro}
              />
              <Tooltip
                title={
                  !open?.content
                    ? "Show full source Code"
                    : "Hide full Source Code"
                }
              >
                <IconButton
                  name="content"
                  onClick={handleToggle}
                  sx={{
                    color: `${theme.palette.primary.main}`,
                    "&:hover": {
                      backgroundColor: `${theme.palette.secondary.main}`,
                      color: "primary.main",
                    },
                    height: 35,
                    width: 35,
                    mt: 1,
                  }}
                >
                  {!open?.content ? (
                    <ExpandMoreIcon />
                  ) : (
                    <KeyboardArrowUpIcon />
                  )}
                </IconButton>
              </Tooltip>
            </>
          }
        />
        <Divider />
        <CardContent
          sx={{
            maxHeight: "50vh",
            overflowY: "scroll",
          }}
        >
          {open?.content ?
            <CustomEditor
              value={content}
              onValueChange={(_content) => setContent(_content)}
              highlight={(_content) => highlight(_content, languages.js)}
            />
            :
            <CustomEditor
              value={contentIntro}
              onValueChange={(_contentIntro) => setContentIntro(_contentIntro)}
              highlight={(_contentIntro) => highlight(_contentIntro, languages.js)}
              padding={10}
            />}
        </CardContent>
      </StyledEditorBox>
      <Typography
        color="textPrimary"
        sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
      >
        To get started copy the full source code to your scr folder.
      </Typography>
      <StyledEditorBox>
        <CardHeader
          action={
            <>
              <CopyToClipboardButton value={open?.code ? code : codeIntro} />
              <Tooltip
                title={
                  !open?.code
                    ? "Show full source Code"
                    : "Hide full Source Code"
                }
              >
                <IconButton
                  name="code"
                  onClick={handleToggle}
                  sx={{
                    color: `${theme.palette.primary.main}`,
                    "&:hover": {
                      backgroundColor: `${theme.palette.secondary.main}`,
                      color: "primary.main",
                    },
                    height: 35,
                    width: 35,
                    mt: 1,
                  }}
                >
                  {!open?.code ? <ExpandMoreIcon /> : <KeyboardArrowUpIcon />}
                </IconButton>
              </Tooltip>
            </>
          }
        />
        <Divider />
        <CardContent
          sx={{
            maxHeight: "50vh",
            overflowY: "scroll",
          }}
        >
          <Editor
            value={open?.code ? code : codeIntro}
            onValueChange={
              open?.code
                ? (_code) => setCode(_code)
                : (_codeIntro) => setCodeIntro(_codeIntro)
            }
            highlight={
              open?.code
                ? (_code) => highlight(_code, languages.js)
                : (_codeIntro) => highlight(_codeIntro, languages.js)
            }
            padding={10}
            style={{
              fontFamily: '"Fira code", "Fira Mono", monospace',
              fontSize: 12,
              backgroundColor: theme.palette.background.paper,
              borderRadius: theme.shape.borderRadius,
            }}
          />
        </CardContent>
      </StyledEditorBox>
      <Box
        sx={{
          m: "auto",
          textAlign: "justify",
          maxWidth: "80%",
          color: "textPrimary",
        }}
      >
        <p>Floating Tab receives the following props:</p>
        <ul>
          <li>
            <b>onOpen</b> - Handles the action for tab opening;
          </li>
          <li>
            <b>onClose</b> - Handles the action for tab closing;
          </li>
          <li>
            <b>data</b> - Content to be displayed.
          </li>
        </ul>
      </Box>
      <ChangeLogEntry
        version="V1.0.0"
        changeLog={ChangeLog()}
      />
    </Box>
  );
};
export default FloatingTabs;

const ChangeLog = () => {
  const theme = useTheme()
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content: "Imported Floating Tab Component.",
      className: chips.green,
    },
  ];

  return changeLog;
};
