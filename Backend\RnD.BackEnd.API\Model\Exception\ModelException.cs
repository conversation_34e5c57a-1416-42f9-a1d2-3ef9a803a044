﻿using System.Collections.Generic;

namespace RnD.BackEnd.API.Model.Exception
{
    /// <summary>
    /// Model exception class
    /// </summary>
    public class ModelException
    {
        /// <summary>
        /// Gets or sets the messages.
        /// </summary>
        /// <value>
        /// The messages.
        /// </value>
        public List<string> Messages { get; set; }
        /// <summary>
        /// Gets a value indicating whether this instance has messages.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance has messages; otherwise, <c>false</c>.
        /// </value>
        public bool HasMessages { get { return (Messages != null && Messages.Count > 0); } }

        /// <summary>
        /// Initializes a new instance of the <see cref="ModelException"/> class.
        /// </summary>
        public ModelException()
        {
            Messages = new List<string>();
        }
    }
}
