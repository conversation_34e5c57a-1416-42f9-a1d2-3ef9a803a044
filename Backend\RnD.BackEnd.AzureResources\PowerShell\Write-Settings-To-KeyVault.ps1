﻿param(
   [string][parameter(Mandatory = $true)] $vaultName,
   [string][parameter(Mandatory = $true)] $resourceGroupName,
   [string][parameter(Mandatory = $true)] $armOutput
)

function Add-VariableGroupVariable()
{
	[CmdletBinding()]
	param
	(
		[string][parameter(Mandatory = $true)]$variableGroupName,
		[string][parameter(Mandatory = $true)]$variableName,
		[string][parameter(Mandatory = $true)]$variableValue
	)
	BEGIN
	{
		#Retrieve project details
		Write-Host Retrieving project details
		
		[String]$project = "$env:SYSTEM_TEAMPROJECT"
		[String]$projectUri = "$env:SYSTEM_TEAMFOUNDATIONCOLLECTIONURI"
		[String]$apiVersion = "5.0-preview.1"
		
		Write-Host Project: $project
		Write-Host ProjectUri: $projectUri
		

		#Set authorization headers 
		Write-Host Set authorization headers
		if ([string]::IsNullOrEmpty($env:SYSTEM_ACCESSTOKEN))
		{
			Write-Error "The SYSTEM_ACCESSTOKEN environment variable is empty. Remember to explicitly allow the build job to access the OAuth Token!"
		}
		$headers = @{ Authorization = "Bearer $env:SYSTEM_ACCESSTOKEN" }

		#Get variable group
		Write-Host Get variable group
		$getVariableGroupUrl= $projectUri + $project + "/_apis/distributedtask/variablegroups?api-version=" + $apiVersion + "&groupName=" + $variableGroupName
		$variableGroup = (Invoke-RestMethod -Uri $getVariableGroupUrl -Headers $headers -Verbose) 
		
		if($variableGroup.value)
		{
			#Set properties for update of existing variable group
			Write-Host Set properties for update of existing variable group
			$variableGroup = $variableGroup.value[0]
			$variableGroup | Add-Member -Name "description" -MemberType NoteProperty -Value "Variable group that got auto-updated by release '$env:Release_ReleaseName'." -Force
			$variableGroup = @{name=$variableGroupName;description=$variableGroup.description;type=$variableGroup.type;variables=$variableGroup.variables;id=$variableGroup.id;isShared=$variableGroup.isShared;variableGroupProjectReferences=$variableGroup.variableGroupProjectReferences}
			$method = "Put"
			$upsertVariableGroupUrl = $projectUri + $project + "/_apis/distributedtask/variablegroups/" + $variableGroup.id + "?api-version=" + $apiVersion    
		}
		else
		{
			#Set properties for creation of new variable group
			Write-Host Set properties for creation of new variable group
			$variableGroup = @{name=$variableGroupName;type="Vsts";description="Variable group that got auto-updated by release '$env:Release_ReleaseName'.";variables=New-Object PSObject;}
			$method = "Post"
			$upsertVariableGroupUrl = $projectUri + $project + "/_apis/distributedtask/variablegroups?api-version=" + $apiVersion
		}

		#Add variable
		$variableGroup.variables | Add-Member -Name $variableName -MemberType NoteProperty -Value @{value=$variableValue} -Force

		#Upsert variable group
		Write-Host Upsert variable group
		$body = $variableGroup | ConvertTo-Json -Depth 10 -Compress
		Write-Host $upsertVariableGroupUrl
		Write-Host $body
		Invoke-RestMethod -Uri $upsertVariableGroupUrl -Method $method -Body $body -Headers $headers -ContentType 'application/json' -Verbose
	}
}

## Read ARM Outputs
Write-Host "Getting ARM outputs..."
Write-Host $armOutput
$armOutputs = ConvertFrom-Json $armOutput

## BEGIN: handle ARM outputs and put them in KeyVault or Variable Group
Write-Host -ForegroundColor Green "Write appSettings to KeyVault and VariableGroup...STARTING..."
foreach ($output in $armOutputs.PSObject.Properties) {
	$varName = ($output.Name.Substring(0,1).ToUpper() + $output.Name.Substring(1)).Trim()    

	Write-Host "Processing variable $varName"

	Write-Host "Setting variable $varName in KeyVault"
	$secretValue = ConvertTo-SecureString $output.Value.value -AsPlainText -Force
	Set-AzureKeyVaultSecret -VaultName $vaultName -Name $varName -SecretValue $secretValue
}
Write-Host -ForegroundColor Green "Write appSettings to KeyVault and VariableGroup...FINISHED!"
## END