﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Options;
using RnD.BackEnd.Domain.DataModels;
using RnD.BackEnd.Domain.Entities.Vehicle;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos;
using RnD.BackEnd.Domain.Settings.Cosmos;
using RnD.BackEnd.Infrastructure.Constants;
using RnD.BackEnd.Infrastructure.DataModels.Vehicle;
using RnD.BackEnd.Infrastructure.Interfaces;
using RnD.BackEnd.Infrastructure.Repositories.Cosmos.Base;

namespace RnD.BackEnd.Infrastructure.Repositories.Cosmos
{
    /// <summary>
    /// Vehicles repository class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Infrastructure.Repositories.Cosmos.Base.BaseCosmosRepository&lt;RnD.BackEnd.Domain.Entities.Vehicle.Vehicle, RnD.BackEnd.Infrastructure.DataModels.Vehicle.VehicleModel&gt;" />
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos.IVehiclesRepository" />
    public class VehiclesRepository : BaseCosmosRepository<Vehicle, VehicleModel>, IVehiclesRepository
    {
        /// <summary>
        /// Name of the container.
        /// </summary>
        public override string ContainerName { get; } = "Vehicles";

        /// <summary>
        /// Initializes a new instance of the <see cref="VehiclesRepository" /> class.
        /// </summary>
        /// <param name="cosmosDbManager">The cosmos database manager.</param>
        /// <param name="cosmosSettings">The cosmos settings.</param>
        /// <param name="mapper">The mapper.</param>
        public VehiclesRepository(ICosmosDbManager cosmosDbManager, IOptions<CosmosSettings> cosmosSettings, IMapper mapper)
            : base(cosmosDbManager, cosmosSettings, mapper)
        {
        }

        /// <summary>
        /// Generates the identifier.
        /// </summary>
        /// <param name="model"></param>
        /// <returns>
        /// The identifier
        /// </returns>
        public override string GenerateId(object model) => $"{((VehicleModel)model).BrandId}:{Guid.NewGuid()}";

        /// <summary>
        /// Resolves the partition key.
        /// </summary>
        /// <param name="partitionKey">The partition key.</param>
        /// <returns>
        /// The partition key.
        /// </returns>
        public override PartitionKey ResolvePartitionKey(string partitionKey) => new PartitionKey(partitionKey.Split(':')[0]);

        /// <summary>
        /// Adds the vehicles using a transaction.
        /// </summary>
        /// <param name="vehicles">The vehicles.</param>
        /// <returns>
        /// The added vehicles.
        /// </returns>
        public async Task<ResponseModel<List<Vehicle>>> AddAsync(List<Vehicle> vehicles)
        {
            List<VehicleModel> vehiclesModel = _mapper.Map<List<Vehicle>, List<VehicleModel>>(vehicles);
            TransactionalBatch transactionBatch = CreateTransationalBatch(vehicles.FirstOrDefault().BrandId);

            foreach (VehicleModel vehicle in vehiclesModel)
            {
                vehicle.Id = GenerateId(vehicle);
                transactionBatch = transactionBatch.CreateItem(vehicle);
            }

            ResponseModel<List<VehicleModel>> result = await ExecuteBatch<VehicleModel>(transactionBatch);
            return _mapper.Map<ResponseModel<List<VehicleModel>>, ResponseModel<List<Vehicle>>>(result);
        }

        /// <summary>
        /// Lists the vehicles
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="brandId">The brand identifier.</param>
        /// <param name="modelName">Name of the model.</param>
        /// <param name="fuelType">Type of the fuel.</param>
        /// <param name="version">The version.</param>
        /// <param name="year">The year.</param>
        /// <param name="pageNumber">The page number.</param>
        /// <param name="maxItems">The maximum items.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// A filtered list of vehicles
        /// </returns>
        public async Task<ResponseModel<List<Vehicle>>> ListAsync(
            List<string> id,
            List<string> brandId,
            List<string> modelName,
            List<string> fuelType,
            List<string> version,
            List<int> year,
            int? pageNumber,
            int? maxItems,
            string sortField,
            bool sortAscending,
            string continuationToken,
            CancellationToken cancellationToken)
        {
            StringBuilder builder = new("SELECT * FROM c WHERE 1=1 ");
            Dictionary<string, object> parameters = new Dictionary<string, object>();
            int count = 0;

            AddCondition(builder, parameters, id, "id", count, true);
            AddCondition(builder, parameters, brandId, "brandId", count, true);
            AddCondition(builder, parameters, modelName, "modelName", count, true);
            AddCondition(builder, parameters, fuelType, "fuelType", count, true);
            AddCondition(builder, parameters, version, "version", count, true);
            AddCondition(builder, parameters, year, "year", count, true);

            AddPaginationAndSorting(builder, parameters, pageNumber, maxItems, sortField, sortAscending);

            return await GetItemsAsync(builder.ToString(), parameters, maxItems, continuationToken, cancellationToken);
        }

        /// <summary>
        /// Adds the or update.
        /// </summary>
        /// <param name="vehicle">The vehicle.</param>
        /// <returns>
        /// The added or updated vehicle.
        /// </returns>
        public async Task<ResponseModel<Vehicle>> AddOrUpdateAsync(Vehicle vehicle)
        {
            VehicleModel model = _mapper.Map<Vehicle, VehicleModel>(vehicle);

            if (string.IsNullOrWhiteSpace(model.Id))
            {
                model.Id = GenerateId(model);
            }

            dynamic[] parameters = new dynamic[] { model };

            ResponseModel<VehicleModel> result = await ExecuteStoredProcedureAsync<VehicleModel>(CosmosStoredProcedures.AddOrUpdateVehicle, model.Id, parameters);
            return _mapper.Map<ResponseModel<VehicleModel>, ResponseModel<Vehicle>>(result);
        }
    }
}
