const getLike = () => {
    return `
    export const like = (word, searchInput) => {
        const searchableWord = word?.toString().toLowerCase()?.normalize('NFD')?.replace(/[\u0300-\u036f]/g, "");
        let matchesAllWord = true;

        if (searchInput.length >= 1) {
            const searchInputArray = searchInput.split(' ');

            searchInputArray.forEach((w) => {
            if (searchableWord.indexOf(w.toString().toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, "")) < 0) {
                // not found
                matchesAllWord = false;
            }
            });
        } else {
            return false;
        }
        return matchesAllWord;
    };
    `;
}

export default getLike;
