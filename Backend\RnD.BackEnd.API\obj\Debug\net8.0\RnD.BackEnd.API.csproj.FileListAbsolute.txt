C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Azure.Cosmos.ServiceInterop.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Cosmos.CRTCompat.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\msvcp140.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\vcruntime140.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\vcruntime140_1.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\archive\empty.7z
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\archive\empty.bz2
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\archive\empty.gz
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\archive\empty.tar
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\archive\empty.xz
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\archive\empty.zip
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\document\empty.docx
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\document\empty.odt
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\document\empty.pdf
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\document\empty.rtf
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.avif
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.bmp
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.dds
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.dib
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.emf
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.exif
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.gif
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.heic
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.heif
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.ico
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.j2c
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.jfif
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.jp2
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.jpc
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.jpe
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.jpg
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.jxr
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.pbm
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.pcx
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.pgm
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.png
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.ppm
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.rle
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.tga
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.tif
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.wdp
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.webp
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\image\empty.wmp
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\sheet\empty.ods
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\sheet\empty.xlsx
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\slide\empty.odp
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles\slide\empty.pptx
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.API.staticwebassets.endpoints.json
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.API.exe
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.API.deps.json
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.API.runtimeconfig.json
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.API.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.API.pdb
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.API.xml
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Argon.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\AutoMapper.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Azure.Core.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Azure.Identity.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Azure.Storage.Blobs.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Azure.Storage.Common.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Dapper.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Dapper.Contrib.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\DiffEngine.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Docnet.Core.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\EmptyFiles.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ExcelDataReader.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ExcelDataReader.DataSet.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Hangfire.Console.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Hangfire.Console.Extensions.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Hangfire.Core.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Humanizer.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\LinqKit.Core.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\LinqKit.Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.ApplicationInsights.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.ApplicationInsights.AspNetCore.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.AI.DependencyCollector.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.AI.EventCounterCollector.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.AI.PerfCounterCollector.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.AI.WindowsServer.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.AI.ServerTelemetryChannel.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.AspNetCore.JsonPatch.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Azure.Cosmos.Client.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Azure.Cosmos.Core.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Azure.Cosmos.Direct.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Azure.Cosmos.Serialization.HybridRow.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Bcl.HashCode.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Bcl.Memory.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.CodeAnalysis.Workspaces.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Design.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.SqlServer.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Extensions.Logging.ApplicationInsights.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Graph.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Graph.Auth.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Graph.Core.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Identity.Client.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.IdentityModel.Validators.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Kiota.Abstractions.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Kiota.Authentication.Azure.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Kiota.Http.HttpClientLibrary.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Kiota.Serialization.Form.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Kiota.Serialization.Json.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Kiota.Serialization.Multipart.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Kiota.Serialization.Text.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.ServiceModel.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.SqlServer.Server.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Mono.TextTemplating.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Newtonsoft.Json.Bson.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\UglyToad.PdfPig.Core.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\UglyToad.PdfPig.DocumentLayoutAnalysis.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\UglyToad.PdfPig.Fonts.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\UglyToad.PdfPig.Package.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\UglyToad.PdfPig.Tokenization.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\UglyToad.PdfPig.Tokens.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\UglyToad.PdfPig.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Polly.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Polly.Core.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Polly.Extensions.Http.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RestSharp.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RestSharp.Serializers.NewtonsoftJson.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Scrutor.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\SendGrid.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Serilog.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\SimpleInfoName.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\StarkbankEcdsa.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Std.UriTemplate.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.Filters.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.Filters.Abstractions.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.Newtonsoft.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.ClientModel.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.CodeDom.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Composition.AttributedModel.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Composition.Convention.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Composition.Hosting.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Composition.Runtime.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Composition.TypedParts.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Data.SqlClient.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Drawing.Common.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.IO.Hashing.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Management.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Memory.Data.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Runtime.Caching.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Security.Permissions.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\System.Windows.Extensions.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Verify.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\Verify.PdfPig.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\linux-arm\native\LICENSE
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\linux-arm\native\pdfium.so
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\linux-arm64\native\LICENSE
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\linux-arm64\native\pdfium.so
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\linux\native\LICENSE
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\linux\native\pdfium.so
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\osx-arm64\native\LICENSE
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\osx-arm64\native\pdfium.dylib
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\osx-x64\native\LICENSE
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\osx-x64\native\pdfium.dylib
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x64\native\LICENSE
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x64\native\pdfium.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x86\native\LICENSE
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x86\native\pdfium.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x64\native\Cosmos.CRTCompat.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Azure.Cosmos.ServiceInterop.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x64\native\msvcp140.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x64\native\vcruntime140.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x64\native\vcruntime140_1.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-arm64\native\sni.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x64\native\sni.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win-x86\native\sni.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\unix\lib\net8.0\System.Data.SqlClient.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win\lib\net8.0\System.Data.SqlClient.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win\lib\netcoreapp2.0\System.Management.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Domain.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Email.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Infrastructure.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Licensing.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Logging.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Service.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Domain.pdb
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Infrastructure.pdb
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Licensing.pdb
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Logging.pdb
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Service.pdb
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\bin\Debug\net8.0\RnD.BackEnd.Email.pdb
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.csproj.AssemblyReference.cache
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\Verify.Attributes.cs
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.AssemblyInfoInputs.cache
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.AssemblyInfo.cs
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.sourcelink.json
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\scopedcss\bundle\RnD.BackEnd.API.styles.css
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\staticwebassets.build.json.cache
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.Back.FF230653.Up2Date
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\refint\RnD.BackEnd.API.dll
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.xml
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.pdb
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\RnD.BackEnd.API.genruntimeconfig.cache
C:\Users\<USER>\Downloads\PortalCloudServices\Backend\RnD.BackEnd.API\obj\Debug\net8.0\ref\RnD.BackEnd.API.dll
