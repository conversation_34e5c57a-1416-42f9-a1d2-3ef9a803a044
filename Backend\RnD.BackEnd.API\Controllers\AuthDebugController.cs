using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;

namespace RnD.BackEnd.API.Controllers
{
    [Route("api/auth-debug")]
    [ApiController]
    public class AuthDebugController : ControllerBase
    {
        private readonly ApplicationDbContext _dbContext;

        public AuthDebugController(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        [HttpGet("test-connection")]
        public ActionResult TestConnection()
        {
            try
            {
                // Testar se a conexão com o banco de dados está funcionando
                bool canConnect = _dbContext.Database.CanConnect();
                
                return Ok(new { DatabaseConnection = canConnect ? "Success" : "Failed" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message, InnerError = ex.InnerException?.Message });
            }
        }

        [HttpPost("register-test-user")]
        public async Task<ActionResult> RegisterTestUser()
        {
            try
            {
                // Cria um user de teste para verificar se o banco de dados está funcionando
                var testUser = new User
                {
                    AzureId = $"test-{Guid.NewGuid()}",
                    Username = "user de Teste",
                    Email = "<EMAIL>"
                };

                _dbContext.User.Add(testUser);
                await _dbContext.SaveChangesAsync();

                return Ok(new { Message = "user de teste criado com sucesso", User = testUser });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message, InnerError = ex.InnerException?.Message });
            }
        }

        [HttpGet("users")]
        public async Task<ActionResult> GetUsers()
        {
            try
            {
                // Lista todos os users para verificar se estão sendo salvos
                var users = await _dbContext.User.ToListAsync();
                
                return Ok(new { Count = users.Count, Users = users });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message, InnerError = ex.InnerException?.Message });
            }
        }

        [HttpGet("claims")]
        public ActionResult GetClaims()
        {
            try
            {
                // Exibir todas as claims do user para depuração
                var claims = User.Claims.Select(c => new { Type = c.Type, Value = c.Value }).ToList();
                
                return Ok(new { 
                    IsAuthenticated = User.Identity.IsAuthenticated,
                    AuthenticationType = User.Identity.AuthenticationType,
                    ClaimsCount = claims.Count,
                    Claims = claims 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message, InnerError = ex.InnerException?.Message });
            }
        }

        [HttpGet("create-direct-user")]
        public async Task<ActionResult> CreateDirectUser()
        {
            try
            {
                // Tentativa direta de criar um user no banco de dados
                var connectionState = _dbContext.Database.GetDbConnection().State.ToString();
                
                // Tentar abrir a conexão explicitamente
                if (_dbContext.Database.GetDbConnection().State != System.Data.ConnectionState.Open)
                {
                    await _dbContext.Database.GetDbConnection().OpenAsync();
                }
                
                // Verificar se as tabelas existem
                var tableExists = false;
                try
                {
                    // Verificação básica se a tabela existe executando uma consulta simples
                    var userCount = await _dbContext.User.CountAsync();
                    tableExists = true;
                }
                catch (Exception ex)
                {
                    return StatusCode(500, new { 
                        Message = "A tabela User não existe ou não está acessível",
                        Error = ex.Message,
                        InnerError = ex.InnerException?.Message
                    });
                }
                
                if (!tableExists)
                {
                    return BadRequest("A tabela User não existe");
                }
                
                // Criar user direto no banco
                var directUser = new User
                {
                    AzureId = $"direct-{Guid.NewGuid()}",
                    Username = "user Direto",
                    Email = "<EMAIL>"
                };

                _dbContext.User.Add(directUser);
                await _dbContext.SaveChangesAsync();

                return Ok(new { 
                    Message = "user criado diretamente com sucesso", 
                    ConnectionState = connectionState,
                    User = directUser 
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message, 
                    InnerError = ex.InnerException?.Message,
                    StackTrace = ex.StackTrace
                });
            }
        }

        [HttpGet("check-table-structure")]
        public async Task<ActionResult> CheckTableStructure()
        {
            try
            {
                // Obter a string de conexão
                var connectionString = _dbContext.Database.GetConnectionString();
                
                var tableInfo = new List<object>();
                
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Verificar se a tabela User existe
                    var tableSchema = new List<object>();
                    using (var cmd = new SqlCommand(
                        @"SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE 
                          FROM INFORMATION_SCHEMA.COLUMNS 
                          WHERE TABLE_NAME = 'User'", connection))
                    {
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                tableSchema.Add(new
                                {
                                    ColumnName = reader["COLUMN_NAME"].ToString(),
                                    DataType = reader["DATA_TYPE"].ToString(),
                                    MaxLength = reader["CHARACTER_MAXIMUM_LENGTH"] == DBNull.Value ? null : reader["CHARACTER_MAXIMUM_LENGTH"].ToString(),
                                    IsNullable = reader["IS_NULLABLE"].ToString()
                                });
                            }
                        }
                    }
                    
                    // Verificar chaves primárias
                    var primaryKeys = new List<string>();
                    using (var cmd = new SqlCommand(
                        @"SELECT COLUMN_NAME
                          FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                          WHERE OBJECTPROPERTY(OBJECT_ID(CONSTRAINT_SCHEMA + '.' + CONSTRAINT_NAME), 'IsPrimaryKey') = 1
                          AND TABLE_NAME = 'User'", connection))
                    {
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                primaryKeys.Add(reader["COLUMN_NAME"].ToString());
                            }
                        }
                    }
                    
                    // Verificar restrições de unicidade
                    var uniqueConstraints = new List<object>();
                    using (var cmd = new SqlCommand(
                        @"SELECT CONSTRAINT_NAME, COLUMN_NAME
                          FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                          WHERE TABLE_NAME = 'User'
                          AND CONSTRAINT_NAME IN (
                              SELECT CONSTRAINT_NAME 
                              FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
                              WHERE CONSTRAINT_TYPE = 'UNIQUE'
                              AND TABLE_NAME = 'User'
                          )", connection))
                    {
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                uniqueConstraints.Add(new
                                {
                                    ConstraintName = reader["CONSTRAINT_NAME"].ToString(),
                                    ColumnName = reader["COLUMN_NAME"].ToString()
                                });
                            }
                        }
                    }
                    
                    tableInfo.Add(new
                    {
                        TableSchema = tableSchema,
                        PrimaryKeys = primaryKeys,
                        UniqueConstraints = uniqueConstraints
                    });
                }
                
                return Ok(new
                {
                    TableInfo = tableInfo
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Error = ex.Message,
                    InnerError = ex.InnerException?.Message,
                    StackTrace = ex.StackTrace
                });
            }
        }

        [HttpPost("create-table")]
        public async Task<ActionResult> CreateUserTable()
        {
            try
            {
                // Obter a string de conexão
                var connectionString = _dbContext.Database.GetConnectionString();
                
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Verificar se a tabela User existe
                    bool tableExists = false;
                    using (var cmd = new SqlCommand(
                        @"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                          WHERE TABLE_NAME = 'User'", connection))
                    {
                        tableExists = (int)await cmd.ExecuteScalarAsync() > 0;
                    }
                    
                    if (tableExists)
                    {
                        return Ok(new { Message = "A tabela User já existe" });
                    }
                    
                    // Criar a tabela User se não existir
                    using (var cmd = new SqlCommand(
                        @"CREATE TABLE [dbo].[User] (
                            [UserID] INT IDENTITY(1,1) PRIMARY KEY,
                            [Username] NVARCHAR(126) NOT NULL,
                            [Email] VARCHAR(126) NOT NULL,
                            [AzureId] NVARCHAR(255) NOT NULL
                        )", connection))
                    {
                        await cmd.ExecuteNonQueryAsync();
                    }
                    
                    // Adicionar restrições de chave única
                    using (var cmd = new SqlCommand(
                        @"ALTER TABLE [dbo].[User] 
                          ADD CONSTRAINT UC_User_Username UNIQUE (Username)", connection))
                    {
                        await cmd.ExecuteNonQueryAsync();
                    }
                    
                    using (var cmd = new SqlCommand(
                        @"ALTER TABLE [dbo].[User] 
                          ADD CONSTRAINT UC_User_AzureId UNIQUE (AzureId)", connection))
                    {
                        await cmd.ExecuteNonQueryAsync();
                    }
                    
                    // Criar a tabela Permission se não existir
                    bool permissionTableExists = false;
                    using (var cmd = new SqlCommand(
                        @"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                          WHERE TABLE_NAME = 'Permission'", connection))
                    {
                        permissionTableExists = (int)await cmd.ExecuteScalarAsync() > 0;
                    }
                    
                    if (!permissionTableExists)
                    {
                        using (var cmd = new SqlCommand(
                            @"CREATE TABLE [dbo].[Permission] (
                                [Id] INT IDENTITY(1,1) PRIMARY KEY,
                                [Name] NVARCHAR(126) NOT NULL
                            )", connection))
                        {
                            await cmd.ExecuteNonQueryAsync();
                        }
                    }
                    
                    // Criar a tabela User_Permission se não existir
                    bool userPermissionTableExists = false;
                    using (var cmd = new SqlCommand(
                        @"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                          WHERE TABLE_NAME = 'User_Permission'", connection))
                    {
                        userPermissionTableExists = (int)await cmd.ExecuteScalarAsync() > 0;
                    }
                    
                    if (!userPermissionTableExists)
                    {
                        using (var cmd = new SqlCommand(
                            @"CREATE TABLE [dbo].[User_Permission] (
                                [UserID] INT NOT NULL,
                                [PermissionID] INT NOT NULL,
                                PRIMARY KEY ([UserID], [PermissionID]),
                                FOREIGN KEY ([UserID]) REFERENCES [dbo].[User]([UserID]) ON DELETE CASCADE,
                                FOREIGN KEY ([PermissionID]) REFERENCES [dbo].[Permission]([Id]) ON DELETE CASCADE
                            )", connection))
                        {
                            await cmd.ExecuteNonQueryAsync();
                        }
                    }
                }
                
                return Ok(new { Message = "Tabelas criadas com sucesso" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Error = ex.Message,
                    InnerError = ex.InnerException?.Message,
                    StackTrace = ex.StackTrace
                });
            }
        }

        [HttpGet("generate-azure-user")]
        public async Task<ActionResult> GenerateAzureUser()
        {
            try
            {
                // Gerar um ID fake do Azure AD
                string azureId = $"azure-{Guid.NewGuid()}";
                
                // Criar user simulando um user do Azure AD
                var azureUser = new User
                {
                    AzureId = azureId,
                    Username = "user Azure Simulado",
                    Email = "<EMAIL>"
                };

                // Verificar se já existe um user com este ID
                var existingUser = await _dbContext.User.FirstOrDefaultAsync(u => u.AzureId == azureId);
                
                if (existingUser != null)
                {
                    return Ok(new { 
                        Message = "user Azure simulado já existe", 
                        User = existingUser 
                    });
                }

                _dbContext.User.Add(azureUser);
                await _dbContext.SaveChangesAsync();
                
                // Adicionar uma permissão básica ao user
                var defaultPermission = await _dbContext.Permission.FirstOrDefaultAsync(p => p.Name == "Usuario");
                
                if (defaultPermission == null)
                {
                    defaultPermission = new Permission { Name = "Usuario" };
                    _dbContext.Permission.Add(defaultPermission);
                    await _dbContext.SaveChangesAsync();
                }
                
                var userPermission = new UserPermission
                {
                    UserID = azureUser.UserID,
                    PermissionID = defaultPermission.Id
                };
                
                _dbContext.UserPermission.Add(userPermission);
                await _dbContext.SaveChangesAsync();

                return Ok(new { 
                    Message = "user Azure simulado criado com sucesso",
                    User = azureUser,
                    Permission = defaultPermission.Name
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message, 
                    InnerError = ex.InnerException?.Message,
                    StackTrace = ex.StackTrace
                });
            }
        }
    }
} 