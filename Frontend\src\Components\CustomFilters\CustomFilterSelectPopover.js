import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  Box,
  Grid,
  Autocomplete,
  TextField,
  Popover,
  Typography,
  Skeleton
} from '@mui/material';
import CustomMuiSelect from '../CustomMuiSelect';
import { useTranslation } from 'react-i18next';
import { filtersAPI } from '../../API/filtersAPI';
import DateField from './DateField';
import CheckboxField from './CheckboxField';

const CustomFilterSelectPopover = ({
  customFiltersList,
  addCustomFilter,
  deleteCustomFilter,
  resetCustomFilters,
  oid,
  context,
  customFilter = null,
  editCustomFilter,
  customFilterIndex,
  popupState,
  ...rest
}) => {
  const [selectedFilter, setSelectedFilter] = useState(false);
  const [selectedTypeDetails, setSelectedTypeDetails] = useState(false);
  const [selectedValues, setSelectedValues] = useState([]);
  const [selectedCondition, setSelectedCondition] = useState();
  const [fieldSearch, setFieldSearch] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation();

  const APIGetType = (type) => {
    const availableTypes = [
      {
        type: "TextField",
        conditions: [
          {
            label: t("common:conditions.contains"),
            id: "contains"
          },
          {
            label: t("common:conditions.sw"),
            id: "sw"
          },
          {
            label: t("common:conditions.ew"),
            id: "ew"
          },
          {
            label: t("common:conditions.eq"),
            id: "eq"
          }
        ]
      },
      {
        type: "NumberField",
        conditions: [
          {
            label: t("common:conditions.gt"),
            id: "gt"
          },
          {
            label: t("common:conditions.lt"),
            id: "lt"
          },
          {
            label: t("common:conditions.eq"),
            id: "eq"
          }
        ]
      },
      {
        type: "DateField",
        conditions: [
          {
            label: t("common:conditions.gt"),
            id: "gt"
          },
          {
            label: t("common:conditions.lt"),
            id: "lt"
          },
          {
            label: t("common:conditions.eq"),
            id: "eq"
          }
        ]
      },
      {
        type: "CheckBoxField"
      },
      {
        type: "SelectList"
      }
    ];
    const foundType = availableTypes.find(x => x.type === type) || { conditions: [] };
    return foundType;
  }

  const handleSelectValues = (val) => {
    setSelectedValues(val);
  }

  const resetValues = () => {
    setSelectedTypeDetails(false);
    setSelectedFilter(false);
    setSelectedCondition();
    setSelectedValues([]);
    setFieldSearch('');
  }

  const getConditionalLabel = () => {
    return selectedCondition && selectedTypeDetails.conditions
      ? selectedTypeDetails.conditions.find(x => x.id === selectedCondition.id).label
      : false;
  };

  const createFilterObject = (conditionLabel) => {
    const filterObject = {
      elementID: selectedFilter.elementID,
      elementLabel: selectedFilter.elementLabel,
      elementType: selectedFilter.elementType,
      condition: selectedCondition ? selectedCondition.id : 'eq',
      conditionLabel: conditionLabel,
      selectedValues: selectedValues,
      value: Array.isArray(selectedValues) && selectedValues[0] && selectedValues[0].value ? selectedValues.map(x => x.value) : Array.isArray(selectedValues) ? selectedValues : [selectedValues]
    };

    if (customFilter) {
      filterObject.customFilterIndex = customFilterIndex;
    }

    return filterObject;
  };

  const handleSubmit = () => {
    const conditionLabel = getConditionalLabel();
    const filterObject = createFilterObject(conditionLabel);

    if (customFilter) {
      editCustomFilter(filterObject)
    } else {
      addCustomFilter(filterObject);
    }

    resetValues();
    rest.onClose();
  }

  const handleOnClose = () => {
    resetValues();
    rest.onClose();
  }

  const handleChangeCustomFilter = async (e, val) => {
    resetValues();
    if (!val) {
      return;
    }
    setSelectedFilter(val);
    setIsLoading(true);
    const typeDetail = await filtersAPI.getAvailableFilterData({ context: context, id: oid, key: val.elementID });
    const typeDetail_conditions = APIGetType(val?.elementType);
    console.log(typeDetail);
    setIsLoading(false);
    if (typeDetail_conditions && typeDetail_conditions.conditions) {
      setSelectedCondition(typeDetail_conditions.conditions[0])
    }
    setSelectedTypeDetails({ ...typeDetail, ...typeDetail_conditions });
  }

  const handleFilteredSelectedTypeDetails = () => {
    return selectedTypeDetails?.value?.filter(selectedDetail => {
      return fieldSearch === "" || selectedDetail?.elementLabel?.toLowerCase().indexOf(fieldSearch?.toLowerCase()) >= 0
    }) || [];
  };

  const handleSelectedValue = (filteredSelectedTypeDetails) => {
    if (selectedValues.length <= 0) {
      return setSelectedValues(filteredSelectedTypeDetails);
    }
    return [];
  }

  const getValueComponent = () => {
    let matchComponent = null;
    const filteredSelectedTypeDetails = handleFilteredSelectedTypeDetails();
    switch (selectedTypeDetails.type) {
      case 'DateField':
        matchComponent =
          <DateField
            handleSelectValues={handleSelectValues}
          />
        break;
      case 'CheckBoxField':
        matchComponent =
          <CheckboxField
            setFieldSearch={setFieldSearch}
            handleSelectedValue={handleSelectedValue}
            handleSelectValues={handleSelectValues}
            filteredSelectedTypeDetails={filteredSelectedTypeDetails}
            selectedValues={selectedValues}
          />
        break;
      case 'SelectList':
        matchComponent = <Autocomplete
          getOptionLabel={(option) => option.elementLabel}
          multiple
          options={selectedTypeDetails?.value || []}
          onChange={(e, v) => handleSelectValues(v)}
          renderInput={(params) => (
            <TextField
              fullWidth
              label={t("common:tableActions.select")}
              variant="outlined"
              {...params}
            />
          )}
        />
        break;
      case "TextField":
      case "NumberField":
      default:
        if (selectedTypeDetails.value && selectedCondition && selectedCondition.id === "eq") {
          matchComponent = <Autocomplete
            getOptionLabel={(option) => option.elementLabel}
            multiple
            options={selectedTypeDetails?.value || []}
            value={selectedValues}
            onChange={(e, v) => handleSelectValues(v)}
            renderInput={(params) => (
              <TextField
                fullWidth
                label={t("common:tableActions.select")}
                variant="outlined"
                {...params}
              />
            )}
          />
        } else {
          matchComponent = <TextField
            fullWidth
            label={t("common:common.value")}
            type={selectedTypeDetails.elementType === "NumberField" ? "number" : "text"}
            value={selectedValues}
            onChange={e => handleSelectValues(e.target.value)}
          />
        }
        break;
    }
    return matchComponent;
  }

  useEffect(() => {
    if (customFilter) {
      handleChangeCustomFilter({}, customFilter)
      handleSelectValues(customFilter.selectedValues)
    }
  }, [popupState])

  return (
    <Popover
      {...rest}
      onClose={handleOnClose}
      sx={{
        ".MuiPopover-paper": {
          overflow: "initial"
        }
      }}
    >
      <Box
        sx={{
          backgroundColor: "white",
          display: "block",
          position: "absolute",
          width: "12px",
          height: "12px",
          top: "-6px",
          boxShadow: "0px 5px 5px -3px rgb(0 0 0 / 20%), 0px 8px 10px 1px rgb(0 0 0 / 14%), 0px 3px 14px 2px rgb(0 0 0 / 12%)",
          webKitTransform: "rotate(45deg)",
          mozTransform: "rotate(45deg)",
          msTransform: "rotate(45deg)",
          transform: "rotate(45deg)",
          left: "calc(50% - 6px)"
        }}
      />
      <Box
        sx={{
          display: "block",
          position: "absolute",
          width: "100%",
          height: "20px",
          top: "0px",
          backgroundColor: "white",
          left: "0px"
        }}
      />
      <Box
        sx={{
          p: 2,
          width: "400px"
        }}
      >
        <Typography
          sx={{
            fontWeight: "bold"
          }}
        >
          {t("common:common.add_filters")}
        </Typography>
        <Grid
          container
          spacing={2}
        >
          <Grid
            item
            xs={12}
          >
            <Box
              sx={{
                maxWidth: "600px"
              }}
            >
              <Autocomplete
                options={customFiltersList || []}
                label={t("common:common.filter")}
                getOptionLabel={option => { return option && option.elementLabel ? option.elementLabel : '' }}
                value={selectedFilter}
                onChange={(e, v) => handleChangeCustomFilter(e, v)}
                renderInput={(params) => (
                  <TextField
                    fullWidth
                    placeholder={t("common:common.choose_filter")}
                    name={t("common:common.filter")}
                    variant="outlined"
                    {...params}
                  />
                )}
              />
            </Box>
          </Grid>
          {
            isLoading &&
            <Grid
              item
              xs={12}
            >
              <Skeleton />
              <Skeleton />
            </Grid>
          }
          {
            selectedTypeDetails && selectedTypeDetails.conditions ?
              <Grid
                item
                xs={12}
              >
                <CustomMuiSelect
                  label={t("common:conditions.condition")}
                  name={t("common:conditions.condition")}
                  width="100%"
                  getOptionLabel={option => { return option && option.label ? option.label : '' }}
                  selected={selectedCondition}
                  handleSelect={e => setSelectedCondition(selectedTypeDetails.conditions.find(x => x.id === e.target.value))}
                  data={selectedTypeDetails.conditions}
                  itemText="label"
                  itemValue="id"
                />
              </Grid>
              :
              null
          }
          {
            selectedTypeDetails ?
              <Grid
                item
                xs={12}
              >
                {getValueComponent()}
              </Grid>
              :
              null
          }
          <Grid
            item
            xs={12}
          >
            <Button
              sx={{
                float: "right"
              }}
              variant="contained"
              disabled={!selectedFilter || !selectedFilter.elementID || !selectedValues}
              onClick={handleSubmit}
            >
              {t('common:buttons.save')}
            </Button>
          </Grid>
        </Grid>

      </Box>
    </Popover>
  )
};

CustomFilterSelectPopover.propTypes = {
  oid: PropTypes.string,
  customFiltersList: PropTypes.array,
  resetCustomFilters: PropTypes.func,
  addCustomFilter: PropTypes.func,
  deleteCustomFilter: PropTypes.func,
  editCustomFilter: PropTypes.func,
  context: PropTypes.string,
  customFilter: PropTypes.object,
  customFilterIndex: PropTypes.number,
  popupState: PropTypes.bool,
}

export default CustomFilterSelectPopover;
