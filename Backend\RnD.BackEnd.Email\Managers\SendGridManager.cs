﻿using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using RnD.BackEnd.Email.Entities;
using RnD.BackEnd.Email.Interfaces;
using RnD.BackEnd.Email.Validators;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace RnD.BackEnd.Email.Managers
{
    /// <summary>
    /// SendGrid email manager class
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Email.Interfaces.IEmailManager" />
    public class SendGridManager : IEmailManager
    {
        private readonly SendGridClient _sendgridClient;
        private readonly bool _allowSendingEmails;
        private readonly List<string> _sendAlwaysToAddresses;

        /// <summary>
        /// Initializes a new instance of the <see cref="SendGridEmailManager" /> class.
        /// </summary>
        /// <param name="apiKey">The API key.</param>
        /// <param name="allowSendingEmails">if set to <c>true</c> [allow sending emails].</param>
        /// <param name="sendAlwaysToAddresses">The send always to addresses.</param>
        public SendGridManager(string apiKey, bool allowSendingEmails, List<string> sendAlwaysToAddresses)
        {
            _sendgridClient = new SendGridClient(apiKey);
            _allowSendingEmails = allowSendingEmails;
            _sendAlwaysToAddresses = sendAlwaysToAddresses;
        }

        /// <summary>
        /// Sends the email.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// Send e-mail response, with Accepted HTTP status code in case of success.
        /// </returns>
        public async Task<SendEmailResponse> SendEmailAsync(EmailMessage request)
        {
            SendEmailResponse output = new SendEmailResponse();

            // In case default recipients addresses is configured, only send e-mail to these email addresses
            if (_sendAlwaysToAddresses != null && _sendAlwaysToAddresses.Count > 0)
            {
                request.Recipients = _sendAlwaysToAddresses;
                request.RecipientsCC = null;
                request.RecipientsBCC = null;
            }

            // Trim e-mail addresses
            request.Recipients = request.Recipients.Select(x => x.Trim()).ToList();
            request.RecipientsCC = request.RecipientsCC?.Select(x => x.Trim()).ToList();
            request.RecipientsBCC = request.RecipientsBCC?.Select(x => x.Trim()).ToList();

            // Validate email message request
            output.ExceptionMessages = EmailServiceValidations.IsSendGridEmailRequestValid(request);
            if (output.HasExceptionMessages)
            {
                output.Code = HttpStatusCode.BadRequest;
            }
            else
            {
                // Only send email if alloed in configuration, otherwise return accepted response
                Response sendGridResponse = _allowSendingEmails
                    ? await _sendgridClient.SendEmailAsync(await CreateMessage(request))
                    : new Response(HttpStatusCode.MethodNotAllowed, null, null);

                if (sendGridResponse.StatusCode != HttpStatusCode.Accepted)
                {
                    output.Code = HttpStatusCode.InternalServerError;
                    output.ExceptionMessages = new List<string>() { $"Error:{await sendGridResponse.Body.ReadAsStringAsync()}" };
                }
            }

            return output;
        }

        #region Private methods

        /// <summary>
        /// Creates the message.
        /// </summary>
        /// <param name="message">The message.</param>
        /// <returns>
        /// The SendGrid e-mail message.
        /// </returns>
        private async Task<SendGridMessage> CreateMessage(EmailMessage inputMessage)
        {
            SendGridMessage message = new SendGridMessage()
            {
                From = new EmailAddress(inputMessage.FromEmailAddress, inputMessage.FromUserName),
                TemplateId = inputMessage.TemplateId
            };

            message.SetTemplateData(inputMessage.EmailData);

            // In case a default recipient address is configured, send always to configured e-mail address
            if (_sendAlwaysToAddresses != null && _sendAlwaysToAddresses.Count > 0)
            {
                message.AddTos(_sendAlwaysToAddresses.Select(x => new EmailAddress(x)).ToList());
            }
            else
            {
                message.AddTos(inputMessage.Recipients.Select(x => new EmailAddress(x)).ToList());
            }

            if (_sendAlwaysToAddresses == null || _sendAlwaysToAddresses.Count == 0)
            {
                // Add CC Recipients
                if (inputMessage.RecipientsCC != null && inputMessage.RecipientsCC.Count > 0)
                {
                    message.AddCcs(inputMessage.RecipientsCC.Select(x => new EmailAddress(x)).ToList());
                }

                // Add BCC Recipients
                if (inputMessage.RecipientsBCC != null && inputMessage.RecipientsBCC.Count > 0)
                {
                    message.AddBccs(inputMessage.RecipientsBCC.Select(x => new EmailAddress(x)).ToList());
                }
            }

            // Add attachments, if exist
            if (inputMessage.Attachments != null && inputMessage.Attachments.Count > 0)
            {
                foreach (EmailAttachment attachment in inputMessage.Attachments)
                {
                    using (Stream file = new MemoryStream(attachment.File))
                    {
                        await message.AddAttachmentAsync(attachment.FileName, file);
                    }
                }
            }

            return message;
        }

        #endregion
    }
}
