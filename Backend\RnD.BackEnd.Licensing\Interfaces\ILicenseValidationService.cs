﻿using System.Threading.Tasks;
using RnD.BackEnd.Licensing.Settings;

namespace RnD.BackEnd.Licensing.Interfaces
{
    /// <summary>
    /// License Validation Middleware
    /// </summary>
    public interface ILicenseValidationService
    {
        /// <summary>
        /// Validates the licence asynchronous.
        /// </summary>
        /// <param name="domainName">Name of the domain.</param>
        /// <param name="validationType">Type of the validation.</param>
        /// <returns>
        /// True if license is valid, False otherwise.
        /// </returns>
        Task<bool> ValidateLicenseAsync(string domainName, ValidationType? validationType = null);
    }
}
