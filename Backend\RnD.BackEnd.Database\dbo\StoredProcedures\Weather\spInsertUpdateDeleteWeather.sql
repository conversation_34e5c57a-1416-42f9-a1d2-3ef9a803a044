﻿CREATE PROCEDURE [dbo].[spInsertUpdateDele<PERSON>Weather]
	@Id UNIQUEIDENTIFIER OUTPUT,
	@Location NVARCHAR(100),
	@Temperature INT,
	@Summary NVARCHAR(265),
	@Status VARCHAR(3),
	@UserId VARCHAR(265),
	@RowVersion TIMESTAMP
AS
	IF @Id IS NOT NULL
		BEGIN
			IF @Status = 'X'
				BEGIN
					UPDATE [dbo].[Weather]
					SET 
						[SYS_STATUS] = @Status,
						[SYS_MODIFY_DATE] = GETUTCDATE(),
						[SYS_MODIFY_USER_ID] = @UserId
					WHERE [Id] = @Id
				END
			ELSE
				BEGIN
					UPDATE [dbo].[Weather]
					SET 
						[Location] = @Location,
						[Temperature] = @Temperature,
						[Summary] = @Summary,
						[SYS_STATUS] = @Status,
						[SYS_MODIFY_DATE] = GETUTCDATE(),
						[SYS_MODIFY_USER_ID] = @UserId
					WHERE [Id] = @Id AND [SYS_ROWVERSION] = @RowVersion
				END
			END
	ELSE
		BEGIN
			SET @Id = NEWID()
			INSERT INTO [dbo].[Weather]
			(
				[Id],
				[Location],
				[Temperature], 
				[Summary],
				[SYS_STATUS],
				[SYS_CREATE_DATE],
				[SYS_CREATE_USER_ID],
				[SYS_MODIFY_DATE],
				[SYS_MODIFY_USER_ID]
			)
			Values
			(
				@Id,
				@Location,
				@Temperature,
				@Summary,
				@Status,
				GETUTCDATE(),
				@UserId,
				GETUTCDATE(),
				@UserId
			)
		END

	IF ( @@ROWCOUNT > 0 )
		BEGIN
			SELECT @Id
		END
	ELSE
		BEGIN
			SELECT @Id = NULL
		END