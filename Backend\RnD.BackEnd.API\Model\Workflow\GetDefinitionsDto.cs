﻿using RnD.BackEnd.API.Model.Workflow.Enums;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Get definitions DTO class
    /// </summary>
    public class GetDefinitionsDto
    {
        /// <summary>
        /// Gets or sets the type of the source.
        /// </summary>
        /// <value>
        /// The type of the source.
        /// </value>
        public string SourceType { get; set; }

        /// <summary>
        /// Gets or sets the type of the workflow.
        /// </summary>
        /// <value>
        /// The type of the workflow.
        /// </value>
        public WorkflowType? WorkflowType { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [fetch inactive].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [fetch inactive]; otherwise, <c>false</c>.
        /// </value>
        public bool FetchInactive { get; set; }
    }
}
