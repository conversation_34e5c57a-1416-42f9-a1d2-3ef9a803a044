﻿using System.Collections.Generic;

namespace RnD.BackEnd.Domain.Settings.Cosmos
{
    public class CosmosContainerInfo
    {
        public string Name { get; set; }
        public string PartitionKey { get; set; }
        public int? Throughput { get; set; }
        public bool Autoscale { get; set; }
        public List<string> UniqueKeys { get; set; }
        public List<ScriptInfo> StoredProcedures { get; set; }
        public List<ScriptInfo> UserDefinedFunctions { get; set; }
    }
}
