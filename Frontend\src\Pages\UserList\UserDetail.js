import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Container,
  Typography,
  CircularProgress,
  Avatar,
  Paper,
  Box,
  TextField,
  Button,
  Grid,
  Divider,
  Snackbar,
  Alert,
  IconButton,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from "@mui/material";
import SaveIcon from '@mui/icons-material/Save';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import DeleteIcon from '@mui/icons-material/Delete';
import BadgeIcon from '@mui/icons-material/Badge';
import AddIcon from '@mui/icons-material/Add';
import { getUserById, updateUser, deleteUser } from "../../Services/userService";

function UserDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);
  const [saving, setSaving] = useState(false);
  const [removingPermissionId, setRemovingPermissionId] = useState(null);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [allPermissions, setAllPermissions] = useState([]);
  const [selectedPermission, setSelectedPermission] = useState("");
  const [loadingPermissions, setLoadingPermissions] = useState(false);

  const [editedUser, setEditedUser] = useState({
    username: "",
    email: ""
  });

  useEffect(() => {
    const fetchUserDetails = async () => {
      try {
        setLoading(true);
        const userData = await getUserById(id);
        console.log("Dados do usuário recebidos:", JSON.stringify(userData, null, 2));

        // Tratamento especial para garantir que userPermissions seja carregado corretamente
        let userPermissions = [];

        // Verificar diferentes formatos possíveis de permissões no retorno da API
        if (userData.userPermissions && Array.isArray(userData.userPermissions)) {
          userPermissions = userData.userPermissions;
          console.log("Permissões em formato de array encontradas:", userPermissions);
        } else if (userData.permissions && Array.isArray(userData.permissions)) {
          userPermissions = userData.permissions;
          console.log("Permissões em formato alternativo encontradas:", userPermissions);
        } else if (typeof userData.userPermissions === 'object' && userData.userPermissions !== null) {
          // Converter objeto em array se necessário
          userPermissions = [userData.userPermissions];
          console.log("Permissão em formato de objeto encontrada:", userPermissions);
        }

        console.log("Permissões do usuário após processamento:", userPermissions);

        // Configurar o estado do usuário
        setUser({
          userID: userData.userID,
          username: userData.username,
          email: userData.email,
          userPermissions: userPermissions
        });

        setEditedUser({
          username: userData.username,
          email: userData.email
        });

        // Buscar permissões direto do endpoint de permissões
        fetchUserPermissionsDirectly(userData.userID);
      } catch (errorFetch) {
        console.error("❌ ERRO ao buscar detalhes do user:", errorFetch);
        setError("Não foi possível carregar os detalhes do user");
      } finally {
        setLoading(false);
      }
    };

    fetchUserDetails();
    fetchAllPermissions();
  }, [id]);

  const fetchAllPermissions = async () => {
    try {
      setLoadingPermissions(true);
      console.log('Buscando todas as permissões disponíveis...');
      const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/permissions`);
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Erro na resposta: ${errorText}`);
        throw new Error('Falha ao buscar permissões');
      }
      const data = await response.json();
      console.log('Permissões carregadas:', data);
      setAllPermissions(data);
    } catch (err) {
      console.error('Erro ao buscar permissões:', err);
      setError("Não foi possível carregar as permissões disponíveis");
    } finally {
      setLoadingPermissions(false);
    }
  };

  // Função para recarregar apenas as permissões do usuário
  const reloadUserPermissions = async () => {
    try {
      console.log(`Recarregando permissões para o usuário ID: ${user.userID}`);
      const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/user-permissions/${parseInt(user.userID, 10)}`);
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Erro na resposta: ${errorText}`);
        throw new Error('Erro ao carregar permissões do usuário');
      }

      const permissions = await response.json();
      console.log('Permissões recarregadas (resposta bruta):', JSON.stringify(permissions, null, 2));

      // Tratamento para diferentes formatos possíveis de permissões
      let userPermissions = [];

      if (permissions && Array.isArray(permissions)) {
        userPermissions = permissions;
        console.log("Permissões em formato de array encontradas (reload):", userPermissions);
      } else if (typeof permissions === 'object' && permissions !== null) {
        // Se for apenas um objeto, transformar em array
        userPermissions = [permissions];
        console.log("Permissão em formato de objeto encontrada (reload):", userPermissions);
      }

      console.log("Permissões processadas para atualização:", userPermissions);

      // Verificar uma última vez se temos um array válido
      if (!Array.isArray(userPermissions)) {
        console.warn("Permissões não são um array válido, definindo como array vazio");
        userPermissions = [];
      }

      // Atualizar o estado do usuário com as novas permissões
      setUser(prevUser => ({
        ...prevUser,
        userPermissions: userPermissions
      }));

      console.log("Estado do usuário atualizado com novas permissões");
    } catch (err) {
      console.error('Erro ao recarregar permissões:', err);
      // Em caso de erro, definir permissões como array vazio para evitar problemas
      setUser(prevUser => ({
        ...prevUser,
        userPermissions: []
      }));
    }
  };

  const assignPermissionToUser = async (permissionId) => {
    try {
      setSaving(true);
      setError(null);

      console.log('Tentando atribuir permissão:', { userId: user.userID, permissionId: parseInt(permissionId, 10) });

      // Verificar se o usuário já tem esta permissão
      const permissionExists = user.userPermissions.some(p =>
        (p.permissionID === parseInt(permissionId, 10)) ||
        (p.permissionId === parseInt(permissionId, 10)) ||
        (p.id === parseInt(permissionId, 10)));

      if (permissionExists) {
        throw new Error("Já tem essa permissão!");
      }

      // Usando query string em vez de body JSON
      const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/user-permissions?userId=${parseInt(user.userID, 10)}&permissionId=${parseInt(permissionId, 10)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.log('Erro retornado pelo servidor:', errorText);

        // Verificar se é um erro de duplicação
        if (errorText.includes("entity changes") || errorText.includes("duplicate")) {
          throw new Error("Já tem essa permissão!");
        }

        throw new Error(`Falha ao atribuir permissão: ${errorText}`);
      }

      // Recarregar apenas as permissões
      await reloadUserPermissions();

      setSuccessMessage("Permissão atribuída com sucesso!");
      setSuccess(true);
      setSelectedPermission("");
    } catch (err) {
      console.error('Erro ao atribuir permissão:', err);
      setError(err.message || "Ocorreu um erro ao atribuir a permissão");
    } finally {
      setSaving(false);
    }
  };

  const removePermissionFromUser = async (permissionId) => {
    try {
      setSaving(true);
      setRemovingPermissionId(permissionId);
      setError(null);

      // Garantir que o permissionId seja um número
      const parsedPermissionId = parseInt(permissionId, 10);

      console.log('Tentando remover permissão:', {
        userId: user.userID,
        permissionId: parsedPermissionId,
        permissionIdOriginal: permissionId
      });

      // Usando parametros na query string conforme esperado pelo controlador
      const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/user-permissions?userId=${parseInt(user.userID, 10)}&permissionId=${parsedPermissionId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Erro ao remover permissão: ${errorText}`);
        throw new Error(`Falha ao remover permissão: ${errorText}`);
      }

      console.log(`Permissão ${parsedPermissionId} removida com sucesso`);

      // Recarregar apenas as permissões
      await reloadUserPermissions();

      setSuccessMessage("Permissão removida com sucesso!");
      setSuccess(true);
    } catch (err) {
      console.error('Erro ao remover permissão:', err);
      setError(err.message || "Ocorreu um erro ao remover a permissão");
    } finally {
      setSaving(false);
      setRemovingPermissionId(null);
    }
  };

  const handleAddPermission = () => {
    if (selectedPermission) {
      assignPermissionToUser(selectedPermission);
    }
  };

  const handleRemovePermission = (permissionId) => {
    removePermissionFromUser(permissionId);
  };

  const handlePermissionChange = (event) => {
    setSelectedPermission(event.target.value);
  };

  const handleEditToggle = () => {
    if (editMode && hasChanges()) {
      if (!window.confirm("Deseja descartar as alterações não salvas?")) {
        return;
      }
    }

    if (!editMode) {
      setEditedUser({
        username: user.username,
        email: user.email
      });
    }

    setEditMode(!editMode);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setEditedUser(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const hasChanges = () => {
    return user.username !== editedUser.username || user.email !== editedUser.email;
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      // Validação básica
      if (!editedUser.username) {
        throw new Error("Nome de user é obrigatório");
      }

      if (!editedUser.email) {
        throw new Error("E-mail é obrigatório");
      }

      // Validação de formato de e-mail
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(editedUser.email)) {
        throw new Error("Formato de e-mail inválido");
      }

      const userData = {
        userID: user.userID,
        username: editedUser.username,
        email: editedUser.email
      };

      await updateUser(id, userData);

      // Atualizar o estado do user com os novos valores
      setUser({
        ...user,
        username: editedUser.username,
        email: editedUser.email
      });

      setSuccessMessage("user atualizado com sucesso!");
      setSuccess(true);
      setEditMode(false);
    } catch (err) {
      console.error('Erro ao salvar user:', err);
      setError(err.message || "Ocorreu um erro ao atualizar o user");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!confirmDelete) {
      setConfirmDelete(true);
      return;
    }

    try {
      setSaving(true);
      setError(null);

      await deleteUser(id);

      setSuccessMessage("User apagado com sucesso!");
      setSuccess(true);

      // Redirecionar para a lista de users após 1.5 segundos
      setTimeout(() => {
        navigate('/UserList');
      }, 1500);
    } catch (err) {
      console.error('Erro ao apagar user:', err);
      setError(err.message || "Ocorreu um erro ao apagar o user");
      setConfirmDelete(false);
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    navigate('/UserList');
  };

  // Buscar permissões direto do endpoint de user-permissions
  const fetchUserPermissionsDirectly = async (userId) => {
    try {
      console.log(`Buscando permissões diretamente para o usuário ID: ${userId}`);
      const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/user-permissions/${parseInt(userId, 10)}`);
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Erro na resposta direta: ${errorText}`);
        return;
      }

      const permissions = await response.json();
      console.log('Permissões buscadas diretamente:', JSON.stringify(permissions, null, 2));

      // Processar permissões e atualizar o estado se tivermos dados válidos
      if (permissions) {
        let processedPermissions = [];

        if (Array.isArray(permissions)) {
          processedPermissions = permissions;
        } else if (permissions && typeof permissions === 'object') {
          processedPermissions = [permissions];
        }

        if (processedPermissions.length > 0) {
          console.log("Atualizando estado com permissões obtidas diretamente:", processedPermissions);
          setUser(prev => ({
            ...prev,
            userPermissions: processedPermissions
          }));
        }
      }
    } catch (err) {
      console.error('Erro ao buscar permissões diretamente:', err);
    }
  };

  if (loading) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (!user) {
    return (
      <Container>
        <Typography variant="h5" color="error" sx={{ mt: 4 }}>
          user não encontrado
        </Typography>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{ mt: 2 }}
        >
          Voltar para a lista
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
            sx={{ mr: 2 }}
          >
            Voltar
          </Button>
          <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
            {editMode ? "Editar user" : "Detalhes do user"}
          </Typography>
          <Button
            color="secondary"
            onClick={handleEditToggle}
            sx={{ mr: 1 }}
          >
            Cancelar
          </Button>
          {!editMode && (
            <IconButton
              color="error"
              onClick={handleDelete}
              disabled={saving}
            >
              <DeleteIcon />
            </IconButton>
          )}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
          <Avatar
            alt={user.username}
            sx={{ width: 100, height: 100, mr: 3 }}
          />
          <Box>
            <Typography variant="h5" gutterBottom>
              {user.username}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              ID: {user.userID}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ mb: 4 }} />

        {editMode ? (
          <Box component="form" sx={{ mt: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label="Nome de user"
                  name="username"
                  value={editedUser.username}
                  onChange={handleChange}
                  variant="outlined"
                  error={!editedUser.username && !!error}
                  helperText={!editedUser.username && error ? "Nome de user é obrigatório" : ""}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label="E-mail"
                  name="email"
                  type="email"
                  value={editedUser.email}
                  onChange={handleChange}
                  variant="outlined"
                  error={!editedUser.email && !!error}
                  helperText={!editedUser.email && error ? "E-mail é obrigatório" : ""}
                />
              </Grid>
            </Grid>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSave}
                disabled={saving || !hasChanges()}
                startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                size="large"
              >
                {saving ? 'A Salvar...' : 'Salvar Alterações'}
              </Button>
            </Box>
          </Box>
        ) : (
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" fontWeight="bold">
                  Nome de user
                </Typography>
                <Typography variant="body1" paragraph>
                  {user.username}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" fontWeight="bold">
                  E-mail
                </Typography>
                <Typography variant="body1" paragraph>
                  {user.email}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 1 }}>
                  Permissões
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {user.userPermissions && user.userPermissions.length > 0 ? (
                      user.userPermissions.map((permission, index) => {
                        console.log("Renderizando permissão:", JSON.stringify(permission, null, 2));

                        // Determinar o ID da permissão e o nome para exibir
                        let permissionId;
                        let permissionName = 'Desconhecida';

                        // Verificar diferentes estruturas possíveis
                        if (typeof permission === 'number') {
                          // Se a permissão for apenas um número de ID
                          permissionId = permission;
                          const foundPermission = allPermissions.find(p => p.id === permissionId);
                          if (foundPermission) {
                            permissionName = foundPermission.name;
                          } else {
                            permissionName = `Permissão ${permissionId}`;
                          }
                        } else if (typeof permission === 'object' && permission !== null) {
                          // Se for um objeto, extrair ID e nome
                          permissionId = permission.permissionID || permission.permissionId || permission.id;

                          if (permission.permission && permission.permission.name) {
                            permissionName = permission.permission.name;
                          } else if (permission.name) {
                            permissionName = permission.name;
                          } else {
                            // Tentar encontrar o nome da permissão na lista de todas as permissões
                            const foundPermission = allPermissions.find(p => p.id === permissionId);
                            if (foundPermission) {
                              permissionName = foundPermission.name;
                            } else {
                              permissionName = `Permissão ${permissionId}`;
                            }
                          }
                        }

                        console.log(`Permissão processada - ID: ${permissionId}, Nome: ${permissionName}`);

                        // Não renderizar se não tiver ID válido
                        if (!permissionId) {
                          console.warn("Permissão sem ID encontrada, ignorando:", permission);
                          return null;
                        }

                        return (
                          <Chip
                            key={index}
                            icon={<BadgeIcon />}
                            label={permissionName}
                            variant="outlined"
                            color="primary"
                            onClick={() => {
                              if (window.confirm(`Deseja remover a permissão "${permissionName}"?`)) {
                                handleRemovePermission(permissionId);
                              }
                            }}
                            onDelete={() => handleRemovePermission(permissionId)}
                            disabled={removingPermissionId === permissionId}
                            deleteIcon={removingPermissionId === permissionId ? <CircularProgress size={16} /> : undefined}
                            sx={{
                              cursor: 'pointer',
                              '&:hover': {
                                backgroundColor: 'rgba(0, 0, 0, 0.08)'
                              }
                            }}
                          />
                        );
                      }).filter(Boolean) // Filtrar itens nulos ou undefined
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        Sem permissões atribuídas
                      </Typography>
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                    <FormControl variant="outlined" sx={{ minWidth: 200, mr: 2 }}>
                      <InputLabel>Adicionar Permissão</InputLabel>
                      <Select
                        value={selectedPermission}
                        onChange={handlePermissionChange}
                        label="Adicionar Permissão"
                        disabled={loadingPermissions}
                      >
                        <MenuItem value="">
                          <em>Selecione uma permissão</em>
                        </MenuItem>
                        {allPermissions.map((permission) => {
                          // Verifica se o usuário já tem esta permissão
                          const userHasPermission = user.userPermissions.some(p =>
                            (p.permissionID === permission.id) ||
                            (p.permissionId === permission.id) ||
                            (p.id === permission.id));

                          return (
                            <MenuItem
                              key={permission.id}
                              value={permission.id}
                              disabled={userHasPermission}
                              sx={userHasPermission ? { opacity: 0.5 } : {}}
                            >
                              {permission.name} {userHasPermission && "(já atribuída)"}
                            </MenuItem>
                          );
                        })}
                      </Select>
                    </FormControl>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<AddIcon />}
                      onClick={handleAddPermission}
                      disabled={!selectedPermission || saving}
                    >
                      Adicionar
                    </Button>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}

        {confirmDelete && (
          <Box sx={{ mt: 4, p: 2, bgcolor: 'error.light', borderRadius: 1 }}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Tem certeza que deseja apagar este user? Esta ação não pode ser desfeita.
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                onClick={() => setConfirmDelete(false)}
                sx={{ mr: 1 }}
              >
                Cancelar
              </Button>
              <Button
                variant="contained"
                color="error"
                onClick={handleDelete}
                disabled={saving}
                startIcon={saving ? <CircularProgress size={20} /> : <DeleteIcon />}
              >
                {saving ? 'A Apagar...' : 'Confirmar'}
              </Button>
            </Box>
          </Box>
        )}
      </Paper>

      <Snackbar
        open={success}
        autoHideDuration={6000}
        onClose={() => setSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" sx={{ width: '100%' }}>
          {successMessage}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          severity="error"
          sx={{
            width: '100%',
            fontWeight: error === "Já tem essa permissão!" ? 'bold' : 'normal',
            fontSize: error === "Já tem essa permissão!" ? '1rem' : 'inherit'
          }}
        >
          {error}
        </Alert>
      </Snackbar>
    </Container>
  );
}

export default UserDetail;
