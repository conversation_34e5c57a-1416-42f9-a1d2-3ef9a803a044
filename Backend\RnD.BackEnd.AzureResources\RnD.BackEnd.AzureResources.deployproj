﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|AnyCPU">
      <Configuration>Debug</Configuration>
      <Platform>AnyCPU</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|AnyCPU">
      <Configuration>Release</Configuration>
      <Platform>AnyCPU</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>78aa4dc2-b114-4069-adbb-17446091a7a4</ProjectGuid>
  </PropertyGroup>
  <PropertyGroup>
    <PrepareForBuildDependsOn>
    </PrepareForBuildDependsOn>
  </PropertyGroup>
  <Import Condition=" Exists('Deployment.targets') " Project="Deployment.targets" />
  <Import Project="$(MSBuildToolsPath)\Microsoft.Common.targets" />
  <!-- vertag<:>start tokens<:>maj.min -->
  <Import Condition=" Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Deployment\1.1\DeploymentProject.targets') " Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Deployment\1.1\DeploymentProject.targets" />
  <!-- vertag<:>end -->
  <ItemGroup>
    <None Include="ApplicationInsights\azuredeploy.applicationInsights.json" />
    <None Include="ApplicationInsights\azuredeploy.applicationInsights.parameters.json" />
    <None Include="AppService\azuredeploy.apps.json" />
    <None Include="AppService\azuredeploy.apps.parameters.json" />
    <None Include="PowerShell\Create-AppInsights-APIKey.ps1" />
    <None Include="StorageAccount\azuredeploy.storage.json" />
    <None Include="StorageAccount\azuredeploy.storage.parameters.json" />
    <None Include="AzFunction\azuredeploy.functions.json" />
    <None Include="AzFunction\azuredeploy.functions.parameters.json" />
    <None Include="Deployment.targets">
      <Visible>False</Visible>
    </None>
    <None Include="KeyVault\azuredeploy.keyvault.json" />
    <None Include="KeyVault\azuredeploy.keyvault.parameters.json" />
    <None Include="PowerShell\Authorize-DevOps-On-KeyVault.ps1" />
    <None Include="PowerShell\Generate-SqlServerAdminPassword.ps1" />
    <None Include="PowerShell\Get-KeyVault-AccessPolicies.ps1" />
    <None Include="PowerShell\Update-KeyVault-GeneratedName.ps1" />
    <None Include="PowerShell\Write-Settings-To-KeyVault.ps1" />
    <Content Include="CosmosDB\azuredeploy.cosmos.json" />
    <Content Include="CosmosDB\azuredeploy.cosmos.parameters.json" />
    <Content Include="CosmosDB\deploy-cosmos-arm.ps1" />
    <Content Include="CosmosDB\create-database.ps1" />
    <None Include="PowerShell\Write-Variables-To-KeyVault.ps1" />
    <None Include="SQLDatabase\azuredeploy.sql.json" />
    <None Include="SQLDatabase\azuredeploy.sql.parameters.json" />
  </ItemGroup>
  <Target Name="GetReferenceAssemblyPaths" />
</Project>