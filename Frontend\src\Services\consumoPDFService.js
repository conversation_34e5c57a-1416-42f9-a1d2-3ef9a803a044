import axios from "axios";

//URL da API
const API_BASE_URL = `${process.env.REACT_APP_WEBAPI_URL}/api`;

// Buscar todos os consumos de PDF de um cliente específico
export const getConsumoPDFByClienteId = async (clienteId) => {
    console.log('Iniciando getConsumoPDFByClienteId:', { clienteId });
    try {
        // Usando o novo endpoint específico para buscar consumos de PDF por cliente
        console.log('Fazendo requisição para:', `${API_BASE_URL}/ConsumoPDF/cliente/${clienteId}`);
        const response = await axios.get(`${API_BASE_URL}/ConsumoPDF/cliente/${clienteId}`);
        console.log('Resposta recebida:', response.data);

        // Retornar os dados da resposta
        const dados = Array.isArray(response.data) ? response.data : [];
        console.log('Dados processados:', dados);
        return dados;
    } catch (error) {
        console.error("Erro ao buscar consumos PDF do cliente:", error);
        console.log('Detalhes do erro:', {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
        });
        // Retornar array vazio em caso de erro para evitar quebrar a UI
        return [];
    }
};

// Formatar valores monetários
export const formatCurrency = (value, currency = "EUR") => {
    console.log('Formatando valor monetário:', { value, currency });
    if (!value) return "0,00 €";
    const formatted = new Intl.NumberFormat('pt-PT', {
        style: 'currency',
        currency: currency
    }).format(value);
    console.log('Valor formatado:', formatted);
    return formatted;
};

// Formatar datas
export const formatDate = (date) => {
    console.log('Formatando data:', { date });
    if (!date) return "N/A";
    const formatted = new Date(date).toLocaleDateString('pt-PT');
    console.log('Data formatada:', formatted);
    return formatted;
};

export default {
    getConsumoPDFByClienteId,
    formatCurrency,
    formatDate
};
