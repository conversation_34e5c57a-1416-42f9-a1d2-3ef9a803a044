﻿using System.Collections.Generic;
using RnD.BackEnd.Domain.BusinessMessages;
using RnD.BackEnd.Domain.Entities.Exception;

namespace RnD.BackEnd.Domain.Validators.Vehicle
{
    /// <summary>
    /// Vehicle validations class
    /// </summary>
    public static class VehicleValidations
    {
        /// <summary>
        /// Determines whether [is create request valid] [the specified vehicle].
        /// </summary>
        /// <param name="vehicle">The vehicle.</param>
        /// <returns>
        /// ModelException object containing error messages in case they exist.
        /// </returns>
        public static ModelException IsCreateRequestValid(Entities.Vehicle.Vehicle vehicle)
        {
            ModelException modelException = new ModelException()
            {
                Messages = new List<string>()
            };

            if (string.IsNullOrWhiteSpace(vehicle.BrandId))
            {
                modelException.Messages.Add(VehicleMessages.BrandId_Mandatory);
            }

            if (string.IsNullOrWhiteSpace(vehicle.ModelName))
            {
                modelException.Messages.Add(VehicleMessages.ModelName_Mandatory);
            }

            if (string.IsNullOrWhiteSpace(vehicle.Brand))
            {
                modelException.Messages.Add(VehicleMessages.Brand_Mandatory);
            }

            return modelException;
        }

        /// <summary>
        /// Determines whether [is update request valid] [the specified vehicle].
        /// </summary>
        /// <param name="vehicle">The vehicle.</param>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// ModelException object containing error messages in case they exist.
        /// </returns>
        public static ModelException IsUpdateRequestValid(Entities.Vehicle.Vehicle vehicle, string id)
        {
            ModelException modelException = IsCreateRequestValid(vehicle);

            if (string.IsNullOrWhiteSpace(vehicle.Id))
            {
                modelException.Messages.Add(VehicleMessages.Id_Mandatory);
            }

            if (vehicle.Id != id)
            {
                modelException.Messages.Add(VehicleMessages.Id_Mismatch);
            }

            return modelException;
        }

        /// <summary>
        /// Determines if the identifier is valid.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// ModelException object containing error messages in case they exist.
        /// </returns>
        public static ModelException IsIdValid(string id)
        {
            ModelException modelException = new ModelException()
            {
                Messages = new List<string>()
            };

            if (string.IsNullOrWhiteSpace(id))
            {
                modelException.Messages.Add(VehicleMessages.Id_Mandatory);
            }

            return modelException;
        }
    }
}
