﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;
using RnD.BackEnd.API.Data;
using RnD.BackEnd.API.Models;

namespace RnD.BackEnd.API.Controllers
{
    [Route("api/user-permissions")]
    [ApiController]
    public class UserPermissionsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public UserPermissionsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/user-permissions/{userId}
        [HttpGet("{userId}")]
        public async Task<IActionResult> GetUserPermissions(int userId)
        {
            var user = await _context.User.FindAsync(userId);
            if (user == null)
                return NotFound("User não encontrado.");

            var permissions = await _context.UserPermission
                .Where(up => up.UserID == userId)
                .Select(up => up.Permission)
                .ToListAsync();

            return Ok(permissions);
        }

        // POST: api/user-permissions
        [HttpPost]
        public async Task<IActionResult> AssignPermissionToUser(int userId, int permissionId)
        {
            var user = await _context.User.FindAsync(userId);
            var permission = await _context.Permission.FindAsync(permissionId);

            if (user == null || permission == null)
                return NotFound();

            var userPermission = new UserPermission { UserID = userId, PermissionID = permissionId };
            _context.UserPermission.Add(userPermission);
            await _context.SaveChangesAsync();

            return Ok(userPermission);
        }

        // DELETE: api/user-permissions
        [HttpDelete]
        public async Task<IActionResult> RemovePermissionFromUser(int userId, int permissionId)
        {
            var userPermission = await _context.UserPermission
                .FirstOrDefaultAsync(up => up.UserID == userId && up.PermissionID == permissionId);

            if (userPermission == null)
                return NotFound();

            _context.UserPermission.Remove(userPermission);
            await _context.SaveChangesAsync();

            return NoContent();
        }
    }
}
