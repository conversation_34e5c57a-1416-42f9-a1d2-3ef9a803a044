﻿using System;

namespace RnD.BackEnd.Licensing.Extensions
{
    /// <summary>
    /// Convertion extensions class
    /// </summary>
    public static class ConvertExtensions
    {
        /// <summary>
        /// Converts an HexString to byte array.
        /// <para><strong>Convert.FromHexString(hex)</strong> is only available after <strong>.NET 5</strong>.</para>
        /// <para>Use this method if .NET is under version 5.</para>
        /// </summary>
        /// <param name="hex">The hexadecimal string.</param>
        /// <returns>
        /// The byte array.
        /// </returns>
        public static byte[] HexStringToByteArray(this string hex)
        {
            int NumberChars = hex.Length;
            byte[] bytes = new byte[NumberChars / 2];

            for (int i = 0; i < NumberChars; i += 2)
            {
                bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
            }

            return bytes;
        }

        /// <summary>
        /// Converts a byte array to an Hex string.
        /// <para><strong>Convert.ToHexString(bytes)</strong> is only available after <strong>.NET 5</strong>.</para>
        /// <para>Use this method if .NET is under version 5.</para>
        /// </summary>
        /// <param name="bytes">The bytes.</param>
        /// <returns>
        /// The hex string.
        /// </returns>
        public static string ByteArrayToHexString(this byte[] bytes)
        {
            return BitConverter.ToString(bytes)?.Replace("-", string.Empty);
        }
    }
}
