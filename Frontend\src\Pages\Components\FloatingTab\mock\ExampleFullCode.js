const getExampleFullCode = () => {
    return `
import React, { useState } from 'react';
import { Box, Divider } from '@mui/material';   
import FloatingTab from '../../../Components/FloatingTab'; //Import Floating Tab from your src Folder
import Metrics from '../../../Components/FloatingTab/ApplicationExample/Metrics'; //Example of Metric component

    const metrics = [
  {
    "title": "Lorem ipsum dolor sit amet",
    "value": "100%",
    "subtitle": null,
    "note": null,
    "order": 0,
  },
  {
    "title": "Lorem ipsum dolor",
    "value": 0.5,
    "subtitle": "Lorem  (10.00%) + Ipsum  (90.00%)",
    "note": null,
    "order": 0
  },
  {
    "title": "Lorem ipsum",
    "value": 0.06,
    "subtitle": "Lorem ipsum dolor (87.50%) + Lorem ipsum dolor (12.50%)",
    "note": "",
    "order": 0
  }
]

const FloatingTabs = () => {
const [floatingTabOpen, setFloatingTabOpen] = useState(true);

return(

 <Your code...>
        <Grid>
            {
                metrics && metrics.length > 0 && <FloatingTab
                onOpen={() => {
                    setFloatingTabOpen(floatingTabOpen);
                }}
                onClose={() => setFloatingTabOpen(!floatingTabOpen)}
                data={
                    <Box>
                    {
                        metrics.map((t, i) => {
                        return <Box
                            key={i}
                            sx={{
                            mb: i !== metrics.length - 1 ? 2 : 0
                            }}
                        >
                            <Metrics
                            data={t}
                            />
                            {
                            i !== (metrics.length - 1) && <Divider />
                            }
                        </Box>;
                        })
                    }
                    </Box>
                }
                />
            }
            </Grid>
    </Your code...>
    )
}
export default FloatingTabs;
    `;
}

export default getExampleFullCode;
