/* eslint-disable no-constant-condition*/
import React, { useEffect, useState, useCallback } from "react";
import {
  Box,
  Button,
  Grid,
  InputAdornment,
  TextField,
  LinearProgress,
  Tooltip,
  IconButton,
  Badge,
  Card,
  Link,
} from "@mui/material";
import { Link as RouterLink } from "react-router-dom";
import CustomTable from "../../../Components/CustomDataTable/CustomTable";
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterListIcon,
} from "@mui/icons-material";
import moment from "moment";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { userAPI } from "../../../API/userAPI";
import { filtersAPI } from "../../../API/filtersAPI";
import CustomFilters from "../../../Components/CustomFilters";
import useFilters from "../../../Context/Hooks/useFilters";

const UserProfile = () => {
  const [data, setData] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [query, setQuery] = useState("");
  const [loadData, setLoadData] = useState(true);
  const { t } = useTranslation();

  //--------------------FILTERS-----------
  const { filters, saveFilters } = useFilters();
  const [customFiltersList, setCustomFiltersList] = useState([]);
  const [selectedCustomFilters, setSelectedCustomFilters] = useState(
    filters.PROFILE && filters.PROFILE.length > 0 ? filters.PROFILE : []
  );
  const [customFiltersToggled, setCustomFiltersToggled] = useState(
    filters.PROFILE && filters.PROFILE.length > 0
  );

  const getAvailableFilters = useCallback(async () => {
    try {
      const _customFiltersList = await filtersAPI.getAvailableFilters({
        context: "PROFILE",
      });

      if (_customFiltersList && !_customFiltersList.error) {
        setCustomFiltersList(_customFiltersList);
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  //RESET
  const handleResetCustomFilters = () => {
    if (filters.PROFILE.length > 0) {
      saveFilters("PROFILE", []);
      setSelectedCustomFilters([]);
    }
  };
  //ADD
  const handleAddCustomFilter = (value) => {
    const _selectedCustomFilters = [...filters.PROFILE];
    const matchIndex = _selectedCustomFilters.findIndex(
      (x) => x.elementID === value.elementID
    );
    /*
             @TODO: Encadeamento
        */
    if (false && matchIndex >= 0) {
      if (Array.isArray(_selectedCustomFilters[matchIndex].values)) {
        _selectedCustomFilters[matchIndex].values.push("&");
        _selectedCustomFilters[matchIndex].values.concat(value.values);
      } else {
        _selectedCustomFilters[
          matchIndex
        ].values += ` <b> && </b> ${value.values}`;
      }
    } else {
      _selectedCustomFilters.push(value);
    }
    saveFilters("PROFILE", _selectedCustomFilters);
    setSelectedCustomFilters(_selectedCustomFilters);
  };
  //DELETE
  const handleDeleteCustomFilter = (value) => {
    const _customFilters = [...filters.PROFILE];
    _customFilters.splice(value, 1);
    saveFilters("PROFILE", _customFilters);
    setSelectedCustomFilters(_customFilters);
  };

  //-----------------USERS LIST-----------------------
  const getUserList = useCallback(async () => {
    try {
      const dataUsers = await userAPI.GetUsersList();
      if (!dataUsers.error) {
        return dataUsers.value;
      }
      if (
        dataUsers.exceptionMessages &&
        dataUsers.exceptionMessages.hasMessages
      ) {
        dataUsers.exceptionMessages.messages.forEach((m) => {
          toast.error(m.description);
        });
      } else {
        toast.error("Ocorreu um erro inesperado");
      }
    } catch (err) {
      toast.error("Error fetching Users list");
    }
  }, []);

  const headerCells = [
    {
      id: "longDesc",
      label: t("common:tableHeaders.profiles"),
      sort: true,
      filter: true,
    },
    {
      id: "userCount",
      label: t("common:tableHeaders.users"),
      sort: true,
      filter: true,
      align: "center",
    },
    {
      id: "permissionCount",
      label: t("common:tableHeaders.permissions"),
      sort: true,
      filter: true,
      align: "center",
    },
    {
      id: "createdByName",
      label: t("common:tableHeaders.createdBy"),
      avatar: "email",
      sort: true,
      filter: true,
    },
    {
      id: "createDate",
      label: t("common:tableHeaders.createdAt"),
      sort: true,
      filter: true,
      align: "left",
    },
    {
      id: "modifiedByName",
      avatar: "email",
      label: t("common:tableHeaders.modifiedBy"),
      sort: true,
      filter: true,
    },
    {
      id: "modifyDate",
      label: t("common:tableHeaders.modifiedAt"),
      sort: true,
      filter: true,
      align: "left",
    },
  ];

  const handleQueryChange = (event) => {
    setQuery(event.target.value);
  };

  //----------PROFILES---------------
  const getProfiles = async () => {
    try {
      setData([]);

      const props = { Filters: selectedCustomFilters };

      const userData = await getUserList();
      const _data = await userAPI.getProfiles(props);
      if (!_data.error) {
        const list = [];
        _data.value.roleTypes.forEach((element) => {
          element.createDate = moment(element.createDate).format(
            "DD/MM/YYYY"
          );
          element.modifyDate = moment(element.modifyDate).format(
            "DD/MM/YYYY"
          );
          list.push(element);
          setData(list);
        });

        const profileData = _data.value.roleTypes;
        profileData.forEach((profile) => {
          let createdByName = profile.createUser;
          let modifiedByName = profile.modifyUser;
          // For each user, add user to property of createdByName / modifiedByName
          // to use in custom table
          userData.forEach((user) => {
            if (user.username === profile.createUser) {
              // render Link component as per other pages
              createdByName = (
                <>
                  <Link
                    color="inherit"
                    component={RouterLink}
                    to={`/Examples/UserList/user/${user.username}`}
                    sx={{
                      color: "primary.main",
                      alignSelf: "center",
                      marginLeft: 1,
                      textDecoration: "none",
                    }}
                  >
                    {user.name}
                  </Link>
                </>
              );
            }
            if (user.username === profile.modifyUser) {
              modifiedByName = (
                <Link
                  color="inherit"
                  component={RouterLink}
                  to={`/Examples/UserList/user/${user.username}`}
                  sx={{
                    color: "primary.main",
                    alignSelf: "center",
                    marginLeft: 1,
                    textDecoration: "none",
                  }}
                >
                  {user.name}
                </Link>
              );
            }
          });
          profile.createdByName = createdByName;
          profile.modifiedByName = modifiedByName;
        });
      } else {
        throw new Error(_data.Error);
      }
      setLoading(false);
    } catch (err) {
      console.error(err);
    }
    setLoadData(false);
  };

  useEffect(() => {
    getAvailableFilters();
  }, [getAvailableFilters]);

  useEffect(() => {
    if (loadData) getProfiles();
  }, [loadData]);

  return (
    <>
      {isLoading && (
        <LinearProgress
          variant="indeterminate"
          // top must be header min height
          sx={{ width: "100%", position: "absolute", top: "64px", left: 0 }}
        />
      )}
      <Box sx={{ p: "24px 32px 0px 32px" }}>
        <Card>
          <Box
            sx={{
              alignItems: "center",
              display: "flex",
              flexWrap: "wrap",
              m: 2,
              height: "88px",
            }}
          >
            {/*Filters Badge*/}
            <Box>
              <Tooltip title={t("common:common.view")}>
                <IconButton
                  color="primary"
                  onClick={() => setCustomFiltersToggled(!customFiltersToggled)}
                >
                  <Badge
                    color="primary"
                    sx={{
                      ".MuiBadge-badge": {
                        padding: "0 4px !important",
                        minWidth: "17px !important",
                        height: "17px !important",
                        fontSize: "0.6rem !important",
                        right: "-3px",
                      },
                    }}
                  >
                    <FilterListIcon />
                  </Badge>
                </IconButton>
              </Tooltip>
            </Box>
            {/* Search TextField */}
            <Box
              xs={12}
              sx={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                padding: "6px 16px",
              }}
            >
              <TextField
                sx={{
                  borderRadius: "4px",
                  boxSizing: "border-box",
                  gap: "8px",
                  width: "458px",
                  height: "56px",
                }}
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                onChange={handleQueryChange}
                placeholder={t("common:search.searchProfiles")}
                value={query}
                variant="outlined"
              />
            </Box>
            <Button
              color="primary"
              startIcon={<AddIcon fontSize="small" />}
              component={RouterLink}
              sx={{ ml: "auto" }}
              to="profile/new"
              variant="contained"
            // onClick={handleOpenModal}
            >
              {t("common:buttons.profile")}
            </Button>
          </Box>
          <Grid
            container
            sx={{
              pb: customFiltersToggled ? 2 : 0,
            }}
          >
            <Grid
              item
              xs={12}
            >
              <CustomFilters
                context="PROFILE"
                customFiltersToggled={customFiltersToggled}
                customFiltersList={customFiltersList}
                selectedCustomFilters={
                  filters.PROFILE && filters.PROFILE.length > 0
                    ? filters.PROFILE
                    : []
                }
                resetCustomFilters={handleResetCustomFilters}
                addCustomFilter={handleAddCustomFilter}
                deleteCustomFilter={handleDeleteCustomFilter}
              />
            </Grid>
          </Grid>
          <Box>
            <CustomTable
              data={data}
              dataHeaders={headerCells}
              query={query}
              isLoading={isLoading}
              orderByString={headerCells[0].id}
              getProfiles={getProfiles}
            />
          </Box>
        </Card>
      </Box>
    </>
  );
};

export default UserProfile;
