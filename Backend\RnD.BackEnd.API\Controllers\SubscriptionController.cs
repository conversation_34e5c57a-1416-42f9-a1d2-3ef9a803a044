﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Data;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RnD.BackEnd.API.Models;

namespace RnD.BackEnd.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SubscriptionController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public SubscriptionController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/Subscription/available
        [HttpGet("available")]
        public async Task<ActionResult<IEnumerable<Subscription>>> GetAvailableSubscriptions()
        {
            var subscriptionsInUse = await _context.ClienteSubscricao
                .Select(cs => cs.SubscriptionID)
                .ToListAsync();

            var availableSubscriptions = await _context.Subscription
                .Where(s => !subscriptionsInUse.Contains(s.ID))
                .ToListAsync();

            return availableSubscriptions;
        }

        // GET: api/Subscription
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Subscription>>> GetAllSubscriptions()
        {
            return await _context.Subscription.ToListAsync();
        }
    }
}