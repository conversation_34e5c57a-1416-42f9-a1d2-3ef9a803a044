/* eslint-disable no-alert */
import { useState, useEffect, useCallback } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Container,
  LinearProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON>le,
} from "@mui/material";
import { Save as SaveIcon } from "@mui/icons-material";
import ProfilePersonalData from "../../Components/Profile/ProfilePersonalData";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import AvatarMedal from "../../Components/Avatar/AvatarMedal";
import { themed_SVG_string } from "../../Utils/Themed_SVG";
import { format } from "date-fns";
import { profileAPI } from "../../API/profileAPI";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";

//Formik Values:
// validators
const yesterday = new Date(Date.now() - 86400000);
const phoneRegExp =
  /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;
const zipCodeRegExp = /^[0-9]{4}-[0-9]{3}$/;
const citizenCardRegExp = /^[0-9]{8}$/;
const socialSecurityRegExp = /^[0-9]{11}$/;
const taxNoRegExp = /^[0-9]{9}$/;

const Profile = () => {
  const [isLoading, setLoading] = useState(true);
  const [coverSVG, setCoverSVG] = useState("");
  const [profileData, setProfileData] = useState({});
  const [options, setOptions] = useState(null);
  const { t } = useTranslation();

  function defaultStringValue(value) {
    return value || "";
  }

  function defaultDateValue(dateString) {
    return dateString ? format(new Date(dateString), "yyyy-MM-dd") : "";
  }

  function findOptions(fieldName, optionName) {
    if (!options) return null;
    const fieldValue = profileData?.[fieldName];
    return fieldValue ?
      options?.[optionName].find(f => f.value === fieldValue) ?? null
      : null;
  }

  //Formik props
  const initialValues = {
    fullname: defaultStringValue(profileData?.fullname),
    birthday: defaultDateValue(profileData?.birthday),
    contactNo: defaultStringValue(profileData?.contactNo),
    email: defaultStringValue(profileData?.email),
    personalEmail: defaultStringValue(profileData?.personalEmail),
    address1: defaultStringValue(profileData?.address1),
    address2: defaultStringValue(profileData?.address2),
    zipCode: defaultStringValue(profileData?.zipCode),
    zipCodeCity: defaultStringValue(profileData?.zipCodeCity),
    idCard: defaultStringValue(profileData?.idCard),
    idCardExpirationDate: defaultDateValue(profileData?.idCardExpirationDate),
    socialSecurityNo: defaultStringValue(profileData?.socialSecurityNo),
    taxNo: defaultStringValue(profileData?.taxNo),
    dependents: profileData?.dependents || 0,
    employeeNo: defaultStringValue(profileData?.employeeNo),
    displayName: defaultStringValue(profileData?.displayName),
    department: findOptions("department", "departmentOptions"),
    incomeHolderID: findOptions("incomeHolderID", "incomeHolderOptions"),
    maritialStatus: findOptions("maritialStatus", "civilStatusOptions"),
    employee_SYS_STATUS_ID: findOptions("employee_SYS_STATUS_ID", "statusOptions"),
    employee_SYS_STATUS: defaultStringValue(profileData?.employee_SYS_STATUS),
  };

  const validationSchema = Yup.object().shape({
    fullname: Yup.string().required(t("profile:formik.name")).nullable(),
    birthday: Yup.date()
      .default(null)
      .required(t("profile:formik.DOB"))
      .max(yesterday, t("profile:formik.DOBval")),
    contactNo: Yup.string()
      .required(t("profile:formik.contact"))
      .nullable()
      .matches(phoneRegExp, t("profile:formik.contactVal"))
      .max(9, t("profile:formik.contactVal")),
    email: Yup.string()
      .required(t("profile:formik.email"))
      .email(t("profile:formik.emailVal"))
      .nullable(),
    personalEmail: Yup.string()
      .required(t("profile:formik.personalEmail"))
      .email(t("profile:formik.personalEmailVal"))
      .nullable(),
    address1: Yup.string().required(t("profile:formik.address1")).nullable(),
    address2: Yup.string().required(t("profile:formik.address2")).nullable(),
    zipCode: Yup.string()
      .required(t("profile:formik.zipCode"))
      .matches(zipCodeRegExp, t("profile:formik.zipCodeVal"))
      .nullable(),
    zipCodeCity: Yup.string()
      .required(t("profile:formik.zipCodeCity"))
      .nullable(),
    idCard: Yup.string()
      .required(t("profile:formik.idCard"))
      .matches(citizenCardRegExp, t("profile:formik.idCardVal"))
      .nullable(),
    idCardExpirationDate: Yup.date()
      .default(null)
      .required(t("profile:formik.expData"))
      .min(new Date(), t("profile:formik.expDateVal")),
    taxNo: Yup.string()
      .required(t("profile:formik.taxNo"))
      .matches(taxNoRegExp, t("profile:formik.taxNoVal"))
      .nullable(),
    socialSecurityNo: Yup.string()
      .required(t("profile:formik.socialSecurityNo"))
      .matches(socialSecurityRegExp, t("profile:formik.socialSecurityNoVal"))
      .nullable(),
    incomeHolderID: Yup.object()
      .required(t("profile:formik.incomeHolderID"))
      .nullable(),
    dependents: Yup.number()
      .required(t("profile:formik.dependents"))
      .min(0, t("profile:formik.dependentsVal")),
    maritialStatus: Yup.object()
      .required(t("profile:formik.maritialStatus"))
      .nullable(),
  });

  //API call to get Profile Info
  const getProfile = useCallback(async () => {
    try {
      const getProfileData = await profileAPI.getProfile();
      setLoading(false);

      if (!getProfileData.error) {
        setProfileData(getProfileData.value.employeeInfo);
      } else if (
        getProfileData.exceptionMessages &&
        getProfileData.exceptionMessages.hasMessages
      ) {
        getProfileData.exceptionMessages.messages.forEach((m) => {
          toast.error(m.description);
        });
      } else {
        toast.error("Ocorreu um erro inesperado");
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  //API call to get Options
  const getOptions = useCallback(async () => {
    try {
      const optionsData = await profileAPI.getOptions();

      if (!optionsData.error) {
        setOptions(optionsData?.value);
      } else if (
        optionsData?.exceptionMessages &&
        optionsData?.exceptionMessages.hasMessages
      ) {
        optionsData.exceptionMessages.messages.forEach((m) => {
          toast.error(m.description);
        });
      } else {
        toast.error("Ocorreu um erro inesperado");
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  useEffect(() => {
    getProfile();
  }, [getProfile]);

  useEffect(() => {
    getOptions();
  }, [getOptions]);

  //Top SVG Theme
  useEffect(() => {
    async function fethSVGTheme() {
      const innerSVG = await themed_SVG_string(
        "/static/images/covers/cover_0.svg"
      );
      setCoverSVG(`url(data:image/svg+xml;base64,${btoa(innerSVG)})`);
    }
    fethSVGTheme();
  }, []);

  return (
    <>
      {isLoading && (
        <LinearProgress
          variant="indeterminate"
          // top must be header min height
          sx={{ width: "100%", position: "absolute", top: "64px", left: 0 }}
        />
      )}
      {/*Formik Component*/}
      <Formik
        enableReinitialize
        validateOnChange
        validateOnBlur
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={async (values) => {
          alert(JSON.stringify(values, null, 2));
        }}
      >
        {({ errors, handleSubmit }) => (
          <Box>
            {/*Top Background-Image*/}
            <Box
              sx={{
                backgroundImage: coverSVG,
                backgroundPosition: "center",
                backgroundRepeat: "no-repeat",
                backgroundSize: "cover",
                height: 100,
                position: "relative",
                "&:before": {
                  backgroundImage:
                    "linear-gradient(-180deg, rgba(0,0,0,0.00) 58%, rgba(0,0,0,0.32) 100%)",
                  content: '" "',
                  height: "100%",
                  left: 0,
                  position: "absolute",
                  top: 0,
                  width: "100%",
                },
                "&:hover": {
                  "& button": {
                    visibility: "visible",
                  },
                },
              }}
            />
            <Container maxWidth="lg">
              <Box>
                <Box
                  sx={{
                    mt: -4,
                  }}
                >
                  <AvatarMedal
                    image="/static/mock-images/avatars/avatar-image.jpg"
                    label={profileData?.displayName}
                    textPosition="right"
                  />
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                  }}
                >
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={() => {
                      handleSubmit();
                    }}
                  >
                    <SaveIcon
                      sx={{ mr: 1 }}
                      fontSize="small"
                    />
                    {t("common:buttons.save")}
                  </Button>
                </Box>
              </Box>
              {/*Alert Box*/}
              {errors && Object.keys(errors).length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Alert severity="error">
                    <AlertTitle>{t("profile:formik.error")}</AlertTitle>
                    {Object.keys(errors)
                      .map((x) => errors[x])
                      .map((message) => (
                        <div key={message}>{message}</div>
                      ))}
                  </Alert>
                </Box>
              )}
              {/*Form from Formik Component: (Input values) */}
              <Form>
                <ProfilePersonalData
                  optionsData={options ?? null}
                  profileData={profileData}
                />
              </Form>
            </Container>
          </Box>
        )}
      </Formik>
    </>
  );
};

export default Profile;
