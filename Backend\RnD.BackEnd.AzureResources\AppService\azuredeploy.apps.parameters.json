{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"app-service-Name": {"value": "rnd-app-service-dev"}, "jobs-Name": {"value": "rnd-jobs-dev"}, "backend-Name": {"value": "rnd-backend-dev"}, "frontend-Name": {"value": "rnd-frontend-dev"}, "app-service-SkuName": {"value": "B1"}, "always-on": {"value": true}, "keyvault-resourcegroup": {"value": "keyvault-resourcegroup-name"}, "key-vault-Name": {"value": "rndkeyvault"}, "keyVault-secretsPermissions-app-services": {"value": ["Get"]}}}