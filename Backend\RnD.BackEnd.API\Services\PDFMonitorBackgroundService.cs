using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using System.Linq;
using RnD.BackEnd.API.Models;
using RnD.BackEnd.API.Data;
using Microsoft.EntityFrameworkCore;

namespace RnD.BackEnd.API.Services
{
    public class PDFMonitorBackgroundService : BackgroundService
    {
        private readonly ILogger<PDFMonitorBackgroundService> _logger;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly IConfiguration _configuration;
        private DateTime _lastCheckTime = DateTime.MinValue;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(5); // Verificar a cada 5 minutos

        public PDFMonitorBackgroundService(
            ILogger<PDFMonitorBackgroundService> logger,
            IServiceScopeFactory scopeFactory,
            IConfiguration configuration)
        {
            _logger = logger;
            _scopeFactory = scopeFactory;
            _configuration = configuration;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Iniciando serviço de monitoramento de arquivos PDF");
            
            // Criar um scope para registrar o log de inicialização
            using (var scope = _scopeFactory.CreateScope())
            {
                var logService = scope.ServiceProvider.GetRequiredService<LogProcessamentoService>();
                await logService.RegistrarLog(0, "Serviço de monitoramento de arquivos PDF iniciado", TipoLog.Info);
                
                // Busca a data do último arquivo PDF processado
                var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                var ultimoArquivo = await dbContext.ArquivosProcessados
                    .ToListAsync()
                    .ContinueWith(t => t.Result
                        .Where(a => a.Nome.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                        .OrderByDescending(a => a.DataProcessamento)
                        .FirstOrDefault());

                if (ultimoArquivo != null)
                {
                    _lastCheckTime = ultimoArquivo.DataProcessamento;
                    
                    // Verificar se a data está no futuro (problema de fuso horário ou configuração)
                    if (_lastCheckTime > DateTime.UtcNow)
                    {
                        _logger.LogWarning($"Data da última verificação está no futuro: {_lastCheckTime}. Ajustando para 24 horas atrás.");
                        _lastCheckTime = DateTime.UtcNow.AddDays(-1);
                    }
                    
                    _logger.LogInformation($"Última verificação definida para: {_lastCheckTime}");
                    
                    // Forçar a verificação de todos os arquivos na primeira execução
                    _lastCheckTime = DateTime.MinValue;
                    _logger.LogWarning($"Forçando verificação de todos os arquivos PDF. Nova data de verificação: {_lastCheckTime}");
                }
                else
                {
                    // Se não houver registro anterior, definir para 24 horas atrás
                    _lastCheckTime = DateTime.UtcNow.AddDays(-1);
                    _logger.LogInformation($"Nenhum registro anterior encontrado. Definindo última verificação para: {_lastCheckTime}");
                }
            }

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CheckForNewPDFFiles();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro ao verificar novos arquivos PDF");
                    
                    // Criar um scope para registrar o log de erro
                    using (var scope = _scopeFactory.CreateScope())
                    {
                        var logService = scope.ServiceProvider.GetRequiredService<LogProcessamentoService>();
                        await logService.RegistrarLog(0, $"Erro ao verificar novos arquivos PDF: {ex.Message}", TipoLog.Erro);
                    }
                }

                // Aguarda o intervalo de verificação
                await Task.Delay(_checkInterval, stoppingToken);
            }
        }

        private async Task CheckForNewPDFFiles()
        {
            _logger.LogInformation("Verificando novos arquivos PDF no Azure Storage");

            string connectionString = _configuration["AzureStoragePDF:ConnectionString"];
            string containerName = _configuration["AzureStoragePDF:ContainerName"] ?? "consumo-pdf";

            _logger.LogInformation($"Usando conexão para Azure Storage PDF: {connectionString?.Substring(0, 30)}... e container: {containerName}");

            var blobContainerClient = new BlobContainerClient(connectionString, containerName);
            await blobContainerClient.CreateIfNotExistsAsync();

            // Listar todos os blobs no container para diagnóstico
            var todosBlobs = blobContainerClient.GetBlobs().ToList();
            _logger.LogInformation($"Total de arquivos no container: {todosBlobs.Count}");
            
            foreach (var blob in todosBlobs.Take(5)) // Limitar a 5 para não sobrecarregar os logs
            {
                _logger.LogInformation($"Blob encontrado: {blob.Name}, Última modificação: {blob.Properties.LastModified}, Tamanho: {blob.Properties.ContentLength} bytes");
            }

            _logger.LogInformation($"Data da última verificação: {_lastCheckTime}");

            // Busca todos os blobs PDF modificados após a última verificação
            var blobs = blobContainerClient.GetBlobs()
                .Where(b => b.Name.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                .ToList();

            _logger.LogInformation($"Total de arquivos PDF no container: {blobs.Count}");

            // Filtrar por data após obter a lista completa para diagnóstico
            _logger.LogInformation($"Data da última verificação antes do filtro: {_lastCheckTime}");
            
            // Verificar se a data está no futuro e corrigir
            if (_lastCheckTime > DateTime.UtcNow)
            {
                _logger.LogWarning($"Data da última verificação está no futuro: {_lastCheckTime}. Ajustando para 24 horas atrás.");
                _lastCheckTime = DateTime.UtcNow.AddDays(-1);
            }
            
            // Forçar a verificação de todos os arquivos para diagnóstico
            _logger.LogWarning("Forçando verificação de todos os arquivos PDF para diagnóstico");
            _lastCheckTime = DateTime.MinValue;
            
            // Adicionar logs detalhados para cada blob antes da filtragem
            foreach (var blob in blobs.Take(5))
            {
                _logger.LogInformation($"Verificando blob: {blob.Name}, LastModified: {blob.Properties.LastModified}, _lastCheckTime: {_lastCheckTime}, Condição: {blob.Properties.LastModified > _lastCheckTime}");
            }
            
            // Processar todos os PDFs no container para diagnóstico
            var blobsFiltrados = blobs.ToList();

            _logger.LogInformation($"Processando todos os arquivos PDF para diagnóstico: {blobsFiltrados.Count}");

            // Atualiza o tempo da última verificação
            _lastCheckTime = DateTime.UtcNow;

            if (blobsFiltrados.Any())
            {
                _logger.LogInformation($"Encontrados {blobsFiltrados.Count} novos arquivos PDF");

                using (var scope = _scopeFactory.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                    var logService = scope.ServiceProvider.GetRequiredService<LogProcessamentoService>();

                    foreach (var blob in blobsFiltrados)
                    {
                        // Verificar se o arquivo já existe usando ToList para evitar problemas de tradução
                        var arquivosProcessados = await dbContext.ArquivosProcessados.ToListAsync();
                        bool arquivoJaExiste = arquivosProcessados.Any(a => a.Nome == blob.Name);

                        _logger.LogInformation($"Verificando arquivo {blob.Name}: Já existe? {arquivoJaExiste}");

                        // Para fins de teste, permitir o reprocessamento do arquivo
                        // Remover ou comentar esta linha em produção
                        arquivoJaExiste = false;

                        if (arquivoJaExiste)
                        {
                            await logService.RegistrarLog(0, $"Arquivo {blob.Name} já foi processado anteriormente. Ignorando.", TipoLog.Aviso);
                            continue;
                        }
                        else
                        {
                            await logService.RegistrarLog(0, $"Processando arquivo {blob.Name}", TipoLog.Info);
                        }

                        try
                        {
                            var pdfService = scope.ServiceProvider.GetRequiredService<IConsumoPDFService>();
                            var resultados = await pdfService.ProcessPDFFile(blob.Name);
                            
                            _logger.LogInformation($"Resultados do processamento do arquivo {blob.Name}: {(resultados != null ? resultados.Count : 0)} registros");
                            
                            if (resultados != null && resultados.Any())
                            {
                                // O arquivo já é registrado dentro do método ProcessPDFFile, não precisamos registrar novamente
                                await logService.RegistrarLog(0, $"Arquivo PDF {blob.Name} processado com sucesso. {resultados.Count} registros extraídos.", TipoLog.Info);
                            }
                            else
                            {
                                await logService.RegistrarLog(0, $"Arquivo PDF {blob.Name} processado, mas nenhum registro foi extraído.", TipoLog.Aviso);
                            }
                        }
                        catch (Exception ex)
                        {
                            await logService.RegistrarLog(0, $"Erro ao processar arquivo PDF {blob.Name}: {ex.Message}", TipoLog.Erro);
                            _logger.LogError(ex, $"Erro ao processar arquivo PDF {blob.Name}");
                        }
                    }
                }
            }
            else
            {
                _logger.LogInformation("Nenhum novo arquivo PDF encontrado após a última verificação");
                
                // Se não encontrou arquivos novos mas existem PDFs no container, pode ser um problema com a data de verificação
                if (blobs.Any() && _lastCheckTime > DateTime.UtcNow.AddDays(-1))
                {
                    _logger.LogWarning($"Existem {blobs.Count} arquivos PDF no container, mas nenhum foi modificado após {_lastCheckTime}");
                    
                    // Para fins de diagnóstico, se não encontrou arquivos nas últimas 24h, reseta o tempo de verificação
                    if (!blobsFiltrados.Any())
                    {
                        var novaDataVerificacao = DateTime.UtcNow.AddDays(-1);
                        _logger.LogWarning($"Redefinindo data de última verificação para: {novaDataVerificacao}");
                        _lastCheckTime = novaDataVerificacao;
                    }
                }
            }
        }
    }
}