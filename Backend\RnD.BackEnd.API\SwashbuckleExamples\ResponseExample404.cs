﻿using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples
{
    /// <summary>
    /// The example for a 404 response.
    /// </summary>
    public class ResponseExample404 : IExamplesProvider<ApiOutput<object>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example for a 404 response.
        /// </returns>
        public ApiOutput<object> GetExamples()
        {
            return new ApiOutput<object>
            {
                Code = HttpStatusCode.BadRequest,
                Description = "Not Found",
                Error = true,
                ExceptionMessages = new Model.Exception.ModelException()
                {
                    Messages = new List<string>
                    {
                        "Error message 1",
                        "Error message 2",
                    }
                },
                Properties = new Dictionary<string, object>()
            };
        }
    }
}