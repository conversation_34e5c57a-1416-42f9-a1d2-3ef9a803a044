﻿using System;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database;

namespace RnD.BackEnd.Domain.Interfaces.Infrastructure.UnitsOfWork
{
    /// <summary>
    /// Database UnitOfWork interface
    /// </summary>
    /// <seealso cref="System.IDisposable" />
    public interface IDatabaseUnitOfWork : IDisposable
    {
        /// <summary>
        /// Gets the weather repository.
        /// </summary>
        /// <value>
        /// The weather repository.
        /// </value>
        IWeatherRepository WeatherRepository { get; }

        /// <summary>
        /// Begins a transaction
        /// </summary>
        void Begin();

        /// <summary>
        /// Handles transaction commit, rollback and dispose
        /// </summary>
        void Commit();

        /// <summary>
        /// Rollbacks the transaction.
        /// </summary>
        void Rollback();
    }
}
