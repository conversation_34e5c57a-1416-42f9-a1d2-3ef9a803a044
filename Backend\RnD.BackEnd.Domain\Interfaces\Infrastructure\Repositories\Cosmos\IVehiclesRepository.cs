﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using RnD.BackEnd.Domain.DataModels;
using RnD.BackEnd.Domain.Entities.Vehicle;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos.Base;

namespace RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos
{
    /// <summary>
    /// Vehicles repository interface
    /// </summary>
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Cosmos.Base.IBaseCosmosRepository&lt;RnD.BackEnd.Domain.Entities.Vehicle.Vehicle&gt;" />
    public interface IVehiclesRepository : IBaseCosmosRepository<Vehicle>
    {
        /// <summary>
        /// Adds the vehicles using a transaction.
        /// </summary>
        /// <param name="vehicles">The vehicles.</param>
        /// <returns>
        /// The added vehicles.
        /// </returns>
        Task<ResponseModel<List<Vehicle>>> AddAsync(List<Vehicle> vehicles);

        /// <summary>
        /// Lists the vehicles
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="brandId">The brand identifier.</param>
        /// <param name="modelName">Name of the model.</param>
        /// <param name="fuelType">Type of the fuel.</param>
        /// <param name="version">The version.</param>
        /// <param name="year">The year.</param>
        /// <param name="pageNumber">The page number.</param>
        /// <param name="maxItems">The maximum items.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// A filtered list of vehicles
        /// </returns>
        Task<ResponseModel<List<Vehicle>>> ListAsync(
            List<string> id,
            List<string> brandId,
            List<string> modelName,
            List<string> fuelType,
            List<string> version,
            List<int> year,
            int? pageNumber,
            int? maxItems,
            string sortField,
            bool sortAscending,
            string continuationToken,
            CancellationToken cancellationToken);

        /// <summary>
        /// Adds the or update.
        /// </summary>
        /// <param name="vehicle">The vehicle.</param>
        /// <returns>
        /// The added or updated vehicle.
        /// </returns>
        Task<ResponseModel<Vehicle>> AddOrUpdateAsync(Vehicle vehicle);

    }
}
