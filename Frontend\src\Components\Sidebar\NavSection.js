import PropTypes from 'prop-types';
import { useState } from 'react';
import { matchPath } from 'react-router-dom';
import { List } from '@mui/material';
import NavItem from './NavItem';
import { useLocation } from 'react-router';

const renderNavItems = ({ location, title, _children, path, pathPrefix = "", _partialMatch, icon }) => {
  const _path = pathPrefix + path;
  const exactMatch = path ? !!matchPath({
    path: path,
    end: true
  }, location.pathname) : false;

  return <NavItem
    key={`NavItem-${title}`}
    icon={icon}
    title={title}
    path={_path.replace("//", "/")}
    active={_partialMatch || exactMatch}
  >
    {
      _children?.length > 0 && _children.filter((x) => x.title).map(child => {
        const partialMatch = child.path ? !!matchPath({
          path: child.path,
          end: false
        }, location.pathname) : false;
        return renderNavItems({ location: location, title: child.title, _children: child._children, path: child.path, pathPrefix: path, partialMatch, icon: child.icon })
      })
    }
  </NavItem>
}

const NavSection = ({ title, path, _children, icon }) => {
  const location = useLocation();
  const [renderedItems] = useState(
    renderNavItems({
      location,
      title,
      _children,
      path,
      icon
    })
  );

  return (
    <List
      key={`TitleList-${title}`}
    >
      {renderedItems}
    </List>
  );
};

NavSection.propTypes = {
  title: PropTypes.string,
  path: PropTypes.string,
  _children: PropTypes.array,
  icon: PropTypes.node
};

export default NavSection;
