const getKPIFullCode = () => {
    return `    
import React from 'react';
import {
    Box,
    Typography,
    Grid,
    Card
} from '@mui/material';
import PropTypes from 'prop-types';
import KPIProgress from './KPIProgress';

const CardKPI = ({
    title,
    value,
    unit = "",
    description,
    extraContent
}) => {
    return (
        <Card
            sx={{
                p: 2,
                height: "100%"
            }}
        >
            <Grid
                container
                sx={{
                    height: "100%"
                }}
            >
                <Grid
                    item
                    xs={extraContent ? 6 : 12}
                >
                    <Typography
                        sx={{
                            display: "inline-flex",
                            fontWeight: "bold",
                            minHeight: "50px"
                        }}
                        variant="h2g"
                    >
                        {title}
                    </Typography>
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "flex-end"
                        }}
                    >
                        <Grid
                            container
                        >
                            <Grid
                                item
                                xs={12}
                            >
                                <Box
                                    sx={{
                                        mr: "10px"
                                    }}
                                >
                                    <Typography
                                        sx={{
                                            fontWeight: "bold",
                                            fontSize: "30px",
                                            display: "inline-flex"
                                        }}
                                    >
                                        {value ? value : '-'}
                                    </Typography>
                                    {unit && <small style={{ fontSize: "0.9rem" }}>{unit}</small>}
                                </Box>
                                {
                                    description && <Typography
                                        sx={{
                                            display: "inline-flex"
                                        }}
                                    >
                                        <small>{description}</small>
                                    </Typography>
                                }
                            </Grid>
                        </Grid>
                    </Box>
                </Grid>
                {
                    extraContent && <Grid
                        item
                        xs={6}
                    >
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "flex-end",
                                height: "100%"
                            }}
                        >
                            {extraContent.variant === "linearContent" && <KPIProgress {...extraContent} />}
                        </Box>
                    </Grid>
                }
            </Grid>
        </Card>
    )
};

CardKPI.propTypes = {
    title: PropTypes.string,
    value: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number
    ]),
    unit: PropTypes.string,
    description: PropTypes.string,
    extraContent: PropTypes.object
}

export default CardKPI;

    `;
}

export default getKPIFullCode;
