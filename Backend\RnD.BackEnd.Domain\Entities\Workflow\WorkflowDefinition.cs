﻿using System;

namespace RnD.BackEnd.Domain.Entities.Workflow
{
    public class WorkflowDefinition
    {
        public Guid? Id { get; set; }
        public string DisplayName { get; set; }
        public string RulesConditionType { get; set; }
        public string SourceType { get; set; }
        public string WorkflowType { get; set; }
        public string Purpose { get; set; }
        public int NumberOfSteps { get; set; }
        public int NumberOfRules { get; set; }
        public bool IsActive { get; set; }
    }
}