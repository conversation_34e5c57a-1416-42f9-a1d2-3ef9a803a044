﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Dapper;
using RnD.BackEnd.Domain.Constants;
using RnD.BackEnd.Domain.DataModels;
using RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database.Base;
using RnD.BackEnd.Infrastructure.DataModels.Base;

namespace RnD.BackEnd.Infrastructure.Repositories.Database.Base
{
    /// <summary>
    /// SQL base repository class, with methods to query and execute stored procedures against SQL
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <typeparam name="D"></typeparam>
    /// <seealso cref="RnD.BackEnd.Infrastructure.Repositories.Database.Base.SqlOperations" />
    /// <seealso cref="RnD.BackEnd.Domain.Interfaces.Infrastructure.Repositories.Database.Base.IBaseSqlRepository&lt;T&gt;" />
    public abstract class BaseSqlRepository<T, D> : SqlOperations, IBaseSqlRepository<T> where T : class where D : BaseSqlModel, new()
    {
        /// <summary>
        /// The mapper
        /// </summary>
        protected readonly IMapper _mapper;

        /// <summary>
        /// The base data model properties
        /// </summary>
        private Dictionary<string, object> _baseDataModelProperties;

        /// <summary>
        /// The command stored procedure name
        /// </summary>
        protected readonly string _commandStoredProcedure;

        /// <summary>
        /// The query stored procedure name
        /// </summary>
        protected readonly string _queryStoredProcedure;

        /// <summary>
        /// Initializes a new instance of the <see cref="BaseSqlRepository{T, D}" /> class.
        /// </summary>
        /// <param name="mapper">The mapper.</param>
        /// <param name="commandStoredProcedure">The command stored procedure.</param>
        /// <param name="queryStoredProcedure">The query stored procedure.</param>
        protected BaseSqlRepository(IMapper mapper, string commandStoredProcedure, string queryStoredProcedure)
        {
            _mapper = mapper;
            _commandStoredProcedure = commandStoredProcedure;
            _queryStoredProcedure = queryStoredProcedure;
        }

        /// <summary>
        /// Adds the item.
        /// </summary>
        /// <param name="item">The item.</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// The added item.
        /// </returns>
        public async Task<ResponseModel<T>> AddAsync(T item, string userId)
        {
            D model = _mapper.Map<T, D>(item);

            DynamicParameters parameters = new DynamicParameters(GetModelPropertiesWithoutBase(model));
            parameters.Add("@Id", null, DbType.Guid, ParameterDirection.InputOutput);
            parameters.Add("@UserId", userId);
            parameters.Add("@Status", SystemConstants.KEY_ACTIVE);
            parameters.Add("@RowVersion", null, DbType.Binary);

            _ = await ExecuteSPAsync(_commandStoredProcedure, param: parameters);

            Guid? output = parameters.Get<Guid?>("@Id");
            if (output.HasValue)
            {
                ResponseModel<T> result = await GetAsync(output.Value);
                result.Code = HttpStatusCode.Created;
                return result;
            }

            return new ResponseModel<T>()
            {
                Code = HttpStatusCode.InternalServerError
            };
        }

        /// <summary>
        /// Updates the item asynchronous.
        /// </summary>
        /// <param name="item">The item.</param>
        /// <param name="rowversion">The rowversion</param>
        /// <returns>
        /// The updated item
        /// </returns>
        public async Task<ResponseModel<T>> UpdateAsync(T item, string userId, string rowversion)
        {
            D model = _mapper.Map<T, D>(item);

            DynamicParameters parameters = new DynamicParameters(GetModelPropertiesWithoutBase(model));
            parameters.Add("@Id", model.Id, DbType.Guid, ParameterDirection.InputOutput);
            parameters.Add("@UserId", userId);
            parameters.Add("@Status", SystemConstants.KEY_ACTIVE);
            parameters.Add("@RowVersion", Convert.FromBase64String(rowversion));

            _ = await ExecuteSPAsync(_commandStoredProcedure, param: parameters);

            Guid? output = parameters.Get<Guid?>("@Id");

            if (output.HasValue)
            {
                return await GetAsync(output.Value);
            }

            return new ResponseModel<T>()
            {
                Code = HttpStatusCode.PreconditionFailed
            };
        }

        /// <summary>
        /// Deletes the item.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="userId">The user identifier.</param>
        /// <returns>
        /// True if operation succeeded. False otherwise.
        /// </returns>
        public async Task<ResponseModel<bool>> DeleteAsync(Guid id, string userId)
        {
            DynamicParameters parameters = new DynamicParameters(GetModelPropertiesWithoutBase(new D()));
            parameters.Add("@Id", id, DbType.Guid, ParameterDirection.InputOutput);
            parameters.Add("@UserId", userId);
            parameters.Add("@Status", SystemConstants.KEY_INACTIVE);
            parameters.Add("@RowVersion", null, DbType.Binary);

            _ = await ExecuteSPAsync(_commandStoredProcedure, param: parameters);

            Guid? output = parameters.Get<Guid?>("@Id");

            return new ResponseModel<bool>()
            {
                Value = output.HasValue
            };
        }

        /// <summary>
        /// Gets the object by Id.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The object.
        /// </returns>
        public async Task<ResponseModel<T>> GetAsync(Guid id)
        {
            ResponseModel<T> result = new ResponseModel<T>();
            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("@Id", id, DbType.Guid);

            IList<D> response = await QuerySPAsync<D>(_queryStoredProcedure, param: parameters);

            D data = response?.FirstOrDefault();
            if (data != null)
            {
                result.Value = _mapper.Map<D, T>(data);
                result.RowVersion = Convert.ToBase64String(data.SysRowVersion);
            }
            else
            {
                result.Value = null;
                result.RowVersion = null;
            }
            return result;
        }

        #region Protected methods

        /// <summary>
        /// Gets the model properties without the inherited properties
        /// </summary>
        /// <param name="model">The model.</param>
        /// <returns>
        /// The model properties, without the inherited properties.
        /// </returns>
        protected Dictionary<string, object> GetModelPropertiesWithoutBase(D model)
        {
            _baseDataModelProperties ??= _mapper.Map<BaseSqlModel, Dictionary<string, object>>(new BaseSqlModel());
            Dictionary<string, object> allProperties = _mapper.Map<Dictionary<string, object>>(model);

            return allProperties
                .Where(x => !_baseDataModelProperties.ContainsKey(x.Key))
                .Select(x => new KeyValuePair<string, object>("@" + x.Key, x.Value))
                .ToDictionary(k => k.Key, v => v.Value);
        }

        /// <summary>
        /// Adds the pagination parameters to query.
        /// </summary>
        /// <param name="parameters">The parameters.</param>
        /// <param name="itemsPerPage">The items per page.</param>
        /// <param name="page">The page.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        /// <returns>
        /// The Dynamic Parameters with pagination and sorting.
        /// </returns>
        protected DynamicParameters AddPaginationAndSortToQuery(
            DynamicParameters parameters,
            int? itemsPerPage = null,
            int? page = null,
            string sortField = null,
            bool sortAscending = false)
        {
            if (itemsPerPage != null && page != null && itemsPerPage > 0 && page > 0)
            {
                parameters.Add("@Skip", (page - 1) * itemsPerPage);
                parameters.Add("@Take", itemsPerPage);
            }

            if (!string.IsNullOrWhiteSpace(sortField))
            {
                parameters.Add("@SortField", sortField, DbType.String);
                parameters.Add("@SortOrder", sortAscending ? SystemConstants.SORT_ASC : SystemConstants.SORT_DESC, DbType.String);
            }

            return parameters;
        }

        #endregion
    }
}
