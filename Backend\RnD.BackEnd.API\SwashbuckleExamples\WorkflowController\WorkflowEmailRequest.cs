﻿using System.Collections.Generic;
using RnD.BackEnd.API.Model.Workflow;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// Send Email request example
    /// </summary>
    public class WorkflowEmailRequest : IMultipleExamplesProvider<WorkflowEmailDto>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to Send Email.
        /// </returns>
        public IEnumerable<SwaggerExample<WorkflowEmailDto>> GetExamples()
        {
            yield return SwaggerExample.Create("With Custom Email", new WorkflowEmailDto
            {
                TemplateId = "d-c43961e085e04f1c8e1f0192db1c69d8",
                Recipients = new List<string>() { "<EMAIL>", "<EMAIL>" },
                TemplateData = new Dictionary<string, string>()
                {
                    { "Message", "This is an example of Email" }
                },
                Subject = "FrameWork Example of Email",
                SendEmail = true
            });
            yield return SwaggerExample.Create("Don't send e-mail", new WorkflowEmailDto() { SendEmail = false });
            yield return SwaggerExample.Create("Send default WFE e-mail (same as with no request)", new WorkflowEmailDto() { SendEmail = true });
        }
    }
}
