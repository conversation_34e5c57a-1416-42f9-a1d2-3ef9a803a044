import axios from 'axios';

export default async (props) => {
    // Obter token do localStorage (verificar várias opções possíveis)
    const azureToken = localStorage.getItem('azureToken');
    const session = localStorage.getItem('session');
    const accessToken = localStorage.getItem('accessToken');
    // Determinar qual token usar, na ordem de preferência
    let authToken = null;
    if (azureToken) {
        authToken = azureToken;
    } else if (session) {
        try {
            // O session pode ser um JSON string
            const parsedSession = JSON.parse(session);
            authToken = parsedSession;
        } catch (e) {
            authToken = session;
        }
    } else if (accessToken) {
        try {
            // O accessToken pode ser um JSON string
            const parsedToken = JSON.parse(accessToken);
            authToken = parsedToken;
        } catch (e) {
            authToken = accessToken;
        }
    }

    // Log para debugging
    console.log('Token disponível para requisição:', authToken ? 'Sim' : 'Não');

    const availableLanguages = process.env.REACT_APP_AVAILABLE_LANGUAGES?.split(';') || ['pt', 'en'];

    // Determinar a URL completa
    let url = props.method;
    // Se a URL não começar com http ou /, adicionar a URL base da API
    if (!url.startsWith('http') && !url.startsWith('/')) {
        url = `${process.env.REACT_APP_WEBAPI_URL}/${url}`;
    }

    // Construir o cabeçalho de autorização adequadamente
    // Corrigir o problema de "Bearer Bearer"
    const authHeader = authToken ?
        (authToken.startsWith('Bearer ') ? authToken : `Bearer ${authToken}`) :
        null;

    return axios({
        method: props.type,
        url: url,
        data: props.params,
        timeout: props.timeout ? props.timeout : 60000,
        headers: {
            Authorization: authHeader,
            'Content-Type': 'application/json',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, Content-Length, X-Requested-With, Accept-Language',
            'accept': 'application/json',
            'Accept-Language': availableLanguages.length > 1 && localStorage.getItem('preferedLanguage') ? localStorage.getItem('preferedLanguage') : availableLanguages[0],
        }
    }).then((response) => {
        if (response && response.status === 200) {
            return response.data;
        }
        return response;
    }).catch((error) => {
        console.error('Erro na requisição API:', error);

        // Log detalhado da requisição que falhou
        console.error('Detalhes da requisição que falhou:', {
            url: props.method,
            metodo: props.type,
            cabecalhoAuth: authHeader ? `${authHeader.substring(0, 20)}...` : 'Nenhum',
            temToken: !!authToken
        });

        // Informações mais detalhadas sobre o erro
        if (error.response) {
            console.error('Dados da resposta:', error.response.data);
            console.error('Status:', error.response.status);
        }

        const response = {
            error: true,
            description: error.message || 'Erro desconhecido'
        };

        return response;
    });
};
