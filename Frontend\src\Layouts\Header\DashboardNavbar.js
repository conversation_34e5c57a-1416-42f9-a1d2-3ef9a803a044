import { useState, useCallback, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import { AppBar, Box, IconButton, Toolbar } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useTheme } from '@emotion/react';
import AccountPopover from '../../Components/AppBar/AccountPopover';
import NotificationsPopover from '../../Components/AppBar/NotificationsPopover';
import { Menu as MenuIcon } from "@mui/icons-material";
import Logo from '../../Assets/images/bi4all.png'
import LanguagePopover from '../../Components/AppBar/LanguagePopover';
import { profileAPI } from '../../API/profileAPI';
import toast from 'react-hot-toast';

const DashboardNavbarRoot = ({ theme, children }) => {
  return <AppBar
    sx={{
      ...(theme.palette.mode === 'light' && {
        backgroundColor: theme.palette.primary.contrastText,
        boxShadow: 'none',
        color: theme.palette.primary.contrastText
      }),
      ...(theme.palette.mode === 'dark' && {
        backgroundColor: theme.palette.background.paper,
        borderBottom: `1px solid ${theme.palette.divider}`,
        boxShadow: 'none'
      }),
      zIndex: theme.zIndex.drawer //+ 100
    }}
  >
    {children}
  </AppBar>
};
DashboardNavbarRoot.propTypes = {
  theme: PropTypes.object,
  children: PropTypes.object
}

const StyledIconButton = styled(IconButton, {
  shouldForwardProp: (prop) => prop !== 'customStyle',
})(({ theme, customStyle }) => ({
  height: '38px',
  width: '38px',
  '&:hover': {
    backgroundColor: theme.palette.hover,
  },
  ...customStyle,
}));

const DashboardNavbar = (props) => {
  const { onSidebarOpen } = props;
  const [profileData, setProfileData] = useState({});
  const theme = useTheme();

  const getProfile = useCallback(async () => {
    try {
      const getProfileData = await profileAPI.getProfile();

      if (!getProfileData.error) {
        setProfileData(getProfileData.value.employeeInfo);
      } else if (getProfileData.exceptionMessages && getProfileData.exceptionMessages.hasMessages) {
        getProfileData.exceptionMessages.messages.forEach(m => {
          toast.error(m.description);
        })
      } else {
        toast.error("Ocorreu um erro inesperado")
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  useEffect(() => {
    getProfile();
  }, [getProfile]);

  return (
    <DashboardNavbarRoot theme={theme}>
      <Toolbar sx={{ height: "64px !important", borderBottom: `0.5px solid ${theme.palette.navBar}` }}>
        {/* Menu Icon: Allows user to open/hide sidebar menu (24px 24px)*/}
        <StyledIconButton onClick={onSidebarOpen}>
          <MenuIcon
            sx={{
              color: 'primary.main',
              height: '24px',
              width: '24px'
            }}
          />
        </StyledIconButton>
        <Box
          sx={{
            height: "100%"
          }}
        >
          <RouterLink
            style={{
              height: "inherit",
              display: "flex"
            }}
            to="/"
          >
            <>
              {/* Company (Left) and Product Logo (Right) */}
              <img
                style={{
                  height: "inherit",
                  display: "flex",
                  padding: "10px"
                }}
                alt="Components"
                src={Logo}
              />
              <div style={{ height: "inherit", borderRight: `0.5px solid ${theme.palette.navBar}` }} />
            </>
            <img
              style={{
                height: "inherit",
                display: "flex",
                padding: "10px"
              }}
              alt="Components"
              src="/static/images/logo.svg"
            />
          </RouterLink>
        </Box>
        <Box
          sx={{
            flexGrow: 1,
            ml: 2
          }}
        />
        {process.env.REACT_APP_AVAILABLE_LANGUAGES.split(';').length > 1 &&
          <Box sx={{ ml: 1 }}>
            <LanguagePopover />
          </Box>}
        <Box sx={{ ml: 1 }}>
          <AccountPopover
            name={profileData.displayName}
            department={profileData.departmentDesc}
          />
        </Box>
        <Box sx={{ ml: 2 }}>
          <NotificationsPopover />
        </Box>
      </Toolbar>
    </DashboardNavbarRoot>
  );
};

DashboardNavbar.propTypes = {
  onSidebarOpen: PropTypes.func,
};

export default DashboardNavbar;
