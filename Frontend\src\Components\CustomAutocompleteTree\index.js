import React, { useState, useEffect } from 'react';
import { TextField, Autocomplete, Chip, Typography, IconButton, Checkbox } from '@mui/material';
import { styled } from '@mui/system';
import PropTypes from 'prop-types';
import {
    ExpandMore as ExpandMoreIcon,
    ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const StyledDiv = styled('div')(({ level, open, isSearching }) => ({
    display: "flex",
    width: "100%",
    padding: "6px 10px",
    ...(level > 0 && {
        maxHeight: "0vh",
        overflow: "hidden",
        transition: "100ms",
        paddingTop: 0,
        paddingBottom: 0,
    }),
    ...((open || isSearching) && {
        maxHeight: "100vh",
        paddingTop: "6px",
        paddingBottom: "6px"
    })
}))

const StyledChip = styled(Chip)({
    '& .ellipsis': {
        maxWidth: '75px !important',
        whiteSpace: 'nowrap !important',
        overflow: 'hidden !important',
        textOverflow: 'ellipsis !important',
    },
})

const CustomAutocompleteTree = ({
    label = "",
    placeholder = "Search...",
    multiple,
    defaultSelection,
    data = [],
    onAppliedValues,
    parentid = "parentid",
    id = "id",
    text = "text",
    maxChips = 1,
    treeSelection = true,
    ...rest
}) => {
    const [options, setOptions] = useState([]);
    const [appliedValues, setAppliedValues] = useState(defaultSelection ? defaultSelection : multiple ? [] : {});
    const [isSearching, setSearching] = useState(false);
    const { t } = useTranslation();

    const toggleDescendants = (_options, element) => {
        if (element.hasChildren) {
            _options = _options.map(o => {
                if (o[parentid] === element[id]) {
                    o.open = element.toggled;
                    o.toggled = !element.toggled ? false : o.toggled;
                    _options = toggleDescendants(_options, o);
                }
                return o;
            });
        }
        return _options;
    }

    const findDescendants = (element) => {
        let descendants = [];
        if (element.hasChildren) {
            const children = [...options].filter(y => y[parentid] === element[id]);
            descendants = [...descendants, ...children];
            children.forEach(x => {
                if (x.hasChildren) {
                    descendants = [...descendants, ...findDescendants(x)];
                }
            });
        }
        return descendants;
    };

    const nestChildren = (item, collection, level = 0) => {
        let children = collection.filter(x => x[parentid] === item[id]);
        if (children.length > 0) {
            children.sort((a, b) => a[text].localeCompare(b[text]));
            children = children.map(child => {
                return nestChildren(child, collection, level + 1);
            });
        }
        item.children = children.sort((a, b) => a[text].localeCompare(b[text]));
        item.level = level;
        item.open = false;
        item.toggled = false;
        item.hasParent = item[parentid] ? true : false;
        item.hasChildren = children?.length;
        return item;
    }

    const flatten = (array, children = "children") => {
        let result = [];
        array.forEach((a) => {
            result.push(a);
            if (Array.isArray(a[children])) {
                result = result.concat(flatten(a[children]));
            }
        });
        return result;
    }

    const handleData = () => {
        if (!data || data.length <= 0) {
            return;
        }
        const list = [...data];
        let newList = list.filter(x => x[parentid] === null || !list.find(y => y[id] === x[parentid]));
        list.map(x => {
            x.level = 0;
            return x;
        })
        newList = newList.map(item => nestChildren(item, list));
        const _options = flatten(newList);
        setOptions(_options);
    }

    const handleSingleSelect = (values, event) => {
        setAppliedValues(values);
        if (onAppliedValues) {
            onAppliedValues(event, values, id, parentid, text);
        }
    }

    const handleTreeSelectOption = (values, additionalValues) => {
        options.forEach(o => {
            const match = values.find(x => x[id] === o[parentid]);
            let matchDup;
            let alreadyAdded;
            if (match) {
                matchDup = additionalValues.find(x => x[id] === o[id]);
                alreadyAdded = values.find(x => x[id] === o[id]);
                if (!matchDup && !alreadyAdded) {
                    additionalValues.push(o);
                }

                const descendants = findDescendants(o);
                descendants.forEach(desc => {
                    matchDup = additionalValues.find(x => x[id] === desc[id]);
                    alreadyAdded = values.find(x => x[id] === desc[id]);
                    if (!matchDup && !alreadyAdded) {
                        additionalValues.push(desc);
                    }
                });
            }
        });
        setSearching(false);
    }

    const handleSelect = (values, type, event) => {
        if (!multiple) {
            handleSingleSelect(values, event);
            return;
        }

        let newValues = [...values];
        const additionalValues = [];

        if (treeSelection) {
            if (type === "selectOption") {
                handleTreeSelectOption(values, additionalValues);
            }

            if (type === 'removeOption') {
                const itemsRemoved = appliedValues.filter(x => !values.includes(x));
                itemsRemoved.forEach(d => {
                    newValues = newValues.filter(x => x[parentid] !== d[id]);
                    const descendants = findDescendants(d);
                    descendants.forEach(desc => {
                        newValues = newValues.filter(x => x[id] !== desc[id]);
                    });
                });
            }
        }

        setAppliedValues([...newValues, ...additionalValues]);
        if (onAppliedValues) {
            onAppliedValues([...newValues, ...additionalValues], id, parentid, text);
        }
    };

    const handleSearch = (e) => {
        if (e && e.target && e.target.value && e.target.value.length > 0) {
            if (!isSearching) {
                setSearching(true);
            }
        } else if (isSearching) {
            setSearching(false);
        }

        return e.target.value;
    };

    const handleUpdateOption = (item, action) => {
        let _options = [...options];
        const matchIndex = _options.findIndex(x => x[id] === item[id]);
        if (matchIndex >= 0) {
            _options[matchIndex][action] = !_options[matchIndex][action] //toggled
        }
        if (action === 'toggled') {
            _options = toggleDescendants(_options, item);
        }
        setOptions(_options);
    }

    useEffect(() => {
        handleData();
    }, []);

    useEffect(() => {
        setAppliedValues(defaultSelection ? defaultSelection : multiple ? [] : {})
    }, [defaultSelection, multiple]);

    return (
        <Autocomplete
            multiple={multiple}
            id="CustomAutocompleteTree"
            options={options}
            isOptionEqualToValue={(option, value) => { return option[id] === value[id] }}
            value={appliedValues}
            onChange={(event, values, type) => handleSelect(values, type, event)}
            onBlur={() => setSearching(false)}
            disableCloseOnSelect
            getOptionLabel={(option) => { return option && option[text] ? option[text] : '' }}
            classes={{
                paper: {
                    "& .MuiAutocomplete-listbox": {
                        "& .MuiAutocomplete-option": {
                            padding: 0
                        }
                    }
                }
            }}
            renderOption={
                (props, option, { selected }) => <li
                    {...props}
                    style={{ width: "100%" }}
                    key={option[id]}
                >
                    <div style={{ display: "flex", width: "100%" }}>
                        <StyledDiv
                            level={option.level}
                            open={option.open}
                            isSearching={isSearching}
                        >
                            <Checkbox
                                sx={{
                                    marginLeft: !isSearching ? option.level * 16 + "px" : 0,
                                    marginRight: "8px"
                                }}
                                checked={selected ? true : false}
                                onChange={() => handleUpdateOption(option, 'checked')}
                            />
                            <div
                                style={{
                                    display: "flex",
                                    width: "100%",
                                    flex: 1,
                                    alignItems: "center"
                                }}
                            >
                                {option[text]} {
                                    option.hasChildren > 0 ?
                                        <small style={{ color: "lightgray", marginLeft: "5px" }}>{`(${option.hasChildren})`}</small>
                                        : ""
                                }
                            </div>
                            {
                                option.hasChildren && !isSearching ?
                                    <IconButton
                                        color="primary"
                                        aria-label="upload picture"
                                        component="span"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleUpdateOption(option, 'toggled');
                                        }}
                                    >
                                        {
                                            option.toggled ? <ExpandLessIcon /> : <ExpandMoreIcon />
                                        }
                                    </IconButton>
                                    :
                                    null
                            }
                        </StyledDiv>
                    </div>
                </li>
            }
            renderInput={(params) => (
                <TextField
                    {...params}
                    label={label}
                    placeholder={placeholder}
                    onChange={handleSearch}
                />
            )}
            renderTags={(values, getTagProps) => {
                return <>
                    {
                        values.map((option, index) => {
                            if (index < maxChips) {
                                return <StyledChip
                                    key={"autocomplete_chip_" + option[id]}
                                    title={option[text]}
                                    label={
                                        <Typography>
                                            {option[text]}
                                        </Typography>
                                    }
                                    {...getTagProps({ index })}
                                    sx={{ height: "100%" }}
                                />
                            }
                            return null;
                        })
                    }
                    {
                        values.length - maxChips > 0 && <StyledChip
                            key={"autocomplete_chip_" + Math.random() * 100000}
                            title={values.filter((x, i) => i >= maxChips).map(option => " " + option[text])}
                            label={<Typography>
                                {`+${values.length - maxChips} ` + t('common:labels.more')}
                            </Typography>}
                            sx={{ height: "100%" }}
                        />
                    }
                </>
            }}
            {...rest}
        />
    );
};

CustomAutocompleteTree.propTypes = {
    data: PropTypes.array,
    defaultSelection: PropTypes.oneOfType([
        PropTypes.array,
        PropTypes.object
    ]),
    label: PropTypes.string,
    parentid: PropTypes.string,
    multiple: PropTypes.bool,
    id: PropTypes.string,
    text: PropTypes.string,
    placeholder: PropTypes.string,
    onAppliedValues: PropTypes.func,
    maxChips: PropTypes.number,
    treeSelection: PropTypes.bool
};

export default CustomAutocompleteTree;
