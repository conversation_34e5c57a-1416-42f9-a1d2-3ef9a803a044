import { useState, useEffect } from "react";
import { Link as RouterLink } from "react-router-dom";
import PropTypes from "prop-types";
import {
  Avatar,
  Box,
  Card,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TablePagination,
  TableRow,
  TextField,
  Link,
  Tooltip,
  Grid,
  IconButton,
  Badge,
  Chip,
  Typo<PERSON>,
  <PERSON><PERSON>
} from "@mui/material";
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  PersonAdd as PersonAddIcon
} from "@mui/icons-material";
import BadgeIcon from '@mui/icons-material/Badge';
import Scrollbar from "../../Components/Scrollbar";
import TableHeader from "../../Components/TableUtils/TableHeader";
import TableLoading from "../../Components/TableUtils/TableLoading";
import {
  applyPagination,
  applySort,
  applyFilters,
} from "../../Components/TableUtils/TableUtils";
import { useTranslation } from "react-i18next";
import CustomFilters from "../../Components/CustomFilters/index";
import useFilters from "../../Context/Hooks/useFilters";
import AddUserModal from "./AddUserModal";

const UserTable = (props) => {
  const {
    userList,
    isLoading,
    customFiltersList,
    onSelectedCustomFilters,
    onUserAdded,
    ...other
  } = props;

  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const [selectedRole] = useState();
  const [filteredUserList] = useState();
  const [query, setQuery] = useState("");
  const [allPermissions, setAllPermissions] = useState([]);
  const [setLoadingPermissions] = useState(false);
  const [orderBy, setOrderBy] = useState("name");
  const [order, setOrder] = useState("asc");
  const [userPermissionsMap, setUserPermissionsMap] = useState({});
  const { t } = useTranslation('common');
  const [showAddUserModal, setShowAddUserModal] = useState(false);

  useEffect(() => {
    // Buscar todas as permissões disponíveis
    fetchAllPermissions();
  }, []);

  useEffect(() => {
    if (userList && userList.length > 0) {
      userList.forEach(user => {
        if (user && user.userID) {
          fetchUserPermissions(user.userID);
        }
      });
    }
  }, [userList]);

  const fetchUserPermissions = async (userId) => {
    if (!userId) return;

    try {
      console.log(`Buscando permissões para o userio ID: ${userId}`);
      // Utilizando o mesmo endpoint que funciona no UserDetail.js
      const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/user-permissions/${parseInt(userId, 10)}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Erro na resposta: ${errorText}`);
        throw new Error(`Falha ao buscar permissões para o userio ${userId}`);
      }

      const permissions = await response.json();
      console.log(`Permissões obtidas para userio ${userId}:`, JSON.stringify(permissions, null, 2));

      // Processar permissões para lidar com diferentes formatos
      let processedPermissions = [];

      if (Array.isArray(permissions)) {
        processedPermissions = permissions;
      } else if (permissions && typeof permissions === 'object') {
        processedPermissions = [permissions];
      }

      // Adicionar ao mapa de permissões
      setUserPermissionsMap(prev => ({
        ...prev,
        [userId]: processedPermissions.filter(Boolean)
      }));
    } catch (err) {
      console.error(`Erro ao buscar permissões para userio ${userId}:`, err);
      // Em caso de erro, definir como array vazio no mapa
      setUserPermissionsMap(prev => ({
        ...prev,
        [userId]: []
      }));
    }
  };

  const fetchAllPermissions = async () => {
    try {
      setLoadingPermissions(true);
      console.log('Buscando todas as permissões disponíveis para a tabela...');
      const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/permissions`);
      if (!response.ok) {
        throw new Error('Falha ao buscar permissões');
      }
      const data = await response.json();
      console.log('Permissões carregadas na tabela:', data);
      setAllPermissions(data);
    } catch (err) {
      console.error('Erro ao buscar permissões para a tabela:', err);
    } finally {
      setLoadingPermissions(false);
    }
  };

  const headerCells = [
    {
      id: "name",
      label: t("common:tableHeaders.name"),
      sort: true,
      filter: true,
    },
    {
      id: "username",
      label: t("common:tableHeaders.email"),
      sort: true,
      filter: true,
    },
    {
      id: "permissions",
      label: t("common:tableHeaders.permissions"),
      sort: true,
      filter: false,
    },
  ];

  const handleQueryChange = (event) => {
    setQuery(event.target.value);
    setPage(0);
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleLimitChange = (event) => {
    setLimit(parseInt(event.target.value, 10));
  };

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const filtereduserlist = applyFilters(
    headerCells,
    selectedRole ? filteredUserList : userList,
    query
  );
  const sorteduserlist = applySort(filtereduserlist, orderBy, order);
  const paginateduserlist = applyPagination(sorteduserlist, page, limit);

  //---------------------Filters---------------------------------
  const { filters, saveFilters } = useFilters();
  const [customFiltersToggled, setCustomFiltersToggled] = useState(
    filters.PROFILE && filters.PROFILE.length > 0
  );

  //RESET
  const handleResetCustomFilters = () => {
    if (filters.PROFILE.length > 0) {
      saveFilters("PROFILE", []);
      onSelectedCustomFilters([]);
    }
  };
  //ADD
  const handleAddCustomFilter = (value) => {
    const _selectedCustomFilters = [...filters.PROFILE];
    const matchIndex = _selectedCustomFilters.findIndex(
      (x) => x.elementID === value.elementID
    );
    /*
         @TODO: Encadeamento
    */
    if (matchIndex === 123456 && matchIndex >= 0) {
      if (Array.isArray(_selectedCustomFilters[matchIndex].values)) {
        _selectedCustomFilters[matchIndex].values.push("&");
        _selectedCustomFilters[matchIndex].values.concat(value.values);
      } else {
        _selectedCustomFilters[
          matchIndex
        ].values += ` <b> && </b> ${value.values}`;
      }
    } else {
      _selectedCustomFilters.push(value);
    }
    saveFilters("PROFILE", _selectedCustomFilters);
    onSelectedCustomFilters(_selectedCustomFilters);
  };
  //DELETE
  const handleDeleteCustomFilter = (value) => {
    const _customFilters = [...filters.PROFILE];
    _customFilters.splice(value, 1);
    saveFilters("PROFILE", _customFilters);
    onSelectedCustomFilters(_customFilters);
  };

  // Adicionar função para lidar com a adição de usuários
  const handleUserAdded = () => {
    // Recarregar a lista de usuários
    onUserAdded && onUserAdded();
  };

  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Card {...other}>
        <Box
          sx={{
            alignItems: "center",
            display: "flex",
            flexWrap: "wrap",
            m: 2,
            height: "88px",
          }}
        >
          <Box>
            <Tooltip title={t("common:common.view")}>
              <IconButton
                color="primary"
                onClick={() => setCustomFiltersToggled(!customFiltersToggled)}
              >
                <Badge
                  color="primary"
                  sx={{
                    ".MuiBadge-badge": {
                      padding: "0 4px !important",
                    },
                  }}
                  badgeContent={filters?.PROFILE?.length || 0}
                >
                  <FilterListIcon />
                </Badge>
              </IconButton>
            </Tooltip>
          </Box>
          <TextField
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
            placeholder={t("placeholder.search")}
            sx={{ maxWidth: 500 }}
            value={query}
            onChange={handleQueryChange}
          />
          
          {/* Botão para adicionar usuários do Azure AD */}
          <Box sx={{ ml: 'auto' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<PersonAddIcon />}
              onClick={() => setShowAddUserModal(true)}
            >
              {t("user.addBI4ALLUsers")}
            </Button>
          </Box>
        </Box>
        <Grid
          container
          sx={{
            pb: customFiltersToggled ? 2 : 0,
          }}
        >
          <Grid
            item
            xs={12}
          >
            <CustomFilters
              customFiltersToggled={customFiltersToggled}
              context="PROFILE"
              customFiltersList={customFiltersList}
              selectedCustomFilters={
                filters.PROFILE && filters.PROFILE.length > 0
                  ? filters.PROFILE
                  : []
              }
              resetCustomFilters={handleResetCustomFilters}
              addCustomFilter={handleAddCustomFilter}
              deleteCustomFilter={handleDeleteCustomFilter}
            />
          </Grid>
        </Grid>
        <Scrollbar>
          <Box sx={{ p: 2, pt: 0, minWidth: 700 }}>
            <Table stickyHeader>
              <TableHeader
                hasCheckbox={false}
                headCells={headerCells}
                onSortClick={handleRequestSort}
                orderBy={orderBy}
                order={order}
              />
              <TableBody>
                <TableLoading
                  isLoading={isLoading}
                  headCells={headerCells}
                  numRows={limit}
                />
                {paginateduserlist.map((user) => {
                  return (
                    <TableRow
                      hover
                      key={user.username}
                      sx={{ height: "77px" }}
                    >
                      <TableCell>
                        <Box sx={{ display: "flex" }}>
                          <Avatar
                            mx={2}
                            key={`avatar-${user.username}`}
                            alt={user.username}
                          />

                          <Link
                            color="inherit"
                            component={RouterLink}
                            to={`/UserList/${user.userID}`}
                            sx={{
                              color: "primary.main",
                              textDecoration: "none",
                              fontStyle: "normal",
                              fontWeight: 700,
                              fontSize: "14px",
                              lineHeight: "20px",
                              alignSelf: "center",
                              marginLeft: 1,
                            }}
                          >
                            {user.username}
                          </Link>
                        </Box>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell align="left">
                        {(() => {
                          const userId = user.userID;
                          console.log(`userio completo ${user.username}:`, JSON.stringify(user, null, 2));

                          // Verificar diferentes fontes de permissões em ordem de prioridade
                          let permissions = [];

                          // 1. Verificar no mapa de permissões carregado diretamente da API
                          if (userPermissionsMap[userId]) {
                            permissions = userPermissionsMap[userId];
                            console.log(`Permissões do mapa para ${user.username}:`, permissions);
                          } else if (user.userPermissions && Array.isArray(user.userPermissions)) {
                            permissions = user.userPermissions;
                          } else if (user.permissions && Array.isArray(user.permissions)) {
                            permissions = user.permissions;
                          } else if (typeof user.userPermissions === 'object' && user.userPermissions !== null) {
                            permissions = [user.userPermissions];
                          }

                          if (permissions && permissions.length > 0) {
                            return permissions.map((p, index) => {
                              // Determinar o nome da permissão para exibir
                              let permissionName = 'Desconhecida';
                              const permissionId = p.id || p.permissionId || (p.permission && p.permission.id);

                              if (p.permission && p.permission.name) {
                                permissionName = p.permission.name;
                              } else if (p.name) {
                                permissionName = p.name;
                              } else if (permissionId && allPermissions.length > 0) {
                                const foundPerm = allPermissions.find(ap => ap.id === permissionId);
                                if (foundPerm) {
                                  permissionName = foundPerm.name;
                                }
                              }

                              return (
                                <Chip
                                  key={index}
                                  icon={<BadgeIcon />}
                                  label={permissionName}
                                  variant="outlined"
                                  color="primary"
                                  size="small"
                                  sx={{ mr: 0.5, mb: 0.5 }}
                                />
                              );
                            });
                          }

                          // Se estiver carregando, mostrar uma mensagem diferente
                          if (!userPermissionsMap[userId] && userId) {
                            setTimeout(() => fetchUserPermissions(userId), 0);
                            return (
                              <Typography variant="body2" color="text.secondary">
                                {t("common:tableActions.loadingPermissions")}
                              </Typography>
                            );
                          }

                          return (
                            <Typography variant="body2" color="text.secondary">
                              {t("user.noPermissions")}
                            </Typography>
                          );
                        })()}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </Box>
        </Scrollbar>
        <TablePagination
          component="div"
          count={filtereduserlist.length}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleLimitChange}
          page={page}
          rowsPerPage={limit}
          rowsPerPageOptions={[5, 10, 25]}
          showFirstButton
          showLastButton
          labelRowsPerPage={t("tableActions.rowsPerPage")}
        />
      </Card>
      
      {/* Modal para adicionar usuários */}
      <AddUserModal 
        open={showAddUserModal} 
        onClose={() => setShowAddUserModal(false)} 
        onAddUsers={handleUserAdded}
      />
    </Box>
  );
};

UserTable.propTypes = {
  userList: PropTypes.array.isRequired,
  photoMap: PropTypes.object,
  roles: PropTypes.array,
  handleGetPhotos: PropTypes.func,
  isLoading: PropTypes.bool,
  customFiltersList: PropTypes.array,
  onSelectedCustomFilters: PropTypes.func,
  onUserAdded: PropTypes.func,
};

export default UserTable;
