﻿param(
   [string][parameter(Mandatory = $true)] $variableName,
   [string][parameter(Mandatory = $true)] $vaultName
)

Write-Host This task will verify if the secret $variableName exists on the Key Vault and set an output variable output_$variableName.
Write-Host If secret with name $variableName does not exist, a new value will be generated and written to the output variable output_$variableName.

$secret = Get-AzureKeyVaultSecret -VaultName $vaultName -Name $variableName

if(-not $secret)
{
    $newPassword = New-Guid
    $secretvalue = ConvertTo-SecureString $newPassword -AsPlainText -Force

    $secret = Set-AzureKeyVaultSecret -VaultName $vaultName -Name $variableName -SecretValue $secretvalue

    $variableValue = $secret.SecretValueText
    Write-Host "##vso[task.setvariable variable=output_$variableName]$variableValue"
    Write-Host This task generated a new value for $variableName secret and defined the output variable output_$variableName.
} else {
    $variableValue = $secret.SecretValueText
    Write-Host "##vso[task.setvariable variable=output_$variableName]$variableValue"
    Write-Host This task found the secret $variableName and only defined the output variable output_$variableName.
}