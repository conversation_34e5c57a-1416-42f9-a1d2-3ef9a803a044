import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  CircularProgress,
  Checkbox,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  TextField,
  InputAdornment,
  Paper,
  Avatar,
  Alert,
  Grid,
  Divider,
  Pagination
} from '@mui/material';
import { Email as EmailIcon, PersonSearch } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import toast from 'react-hot-toast';

const AddUserModal = ({ open, onClose, onAddUsers }) => {
  const { t } = useTranslation('common');
  const [loading, setLoading] = useState(false);
  const [azureUsers, setAzureUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [emailQuery, setEmailQuery] = useState('');
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(0);
  const [totalUsers, setTotalUsers] = useState(0);

  // Buscar usuários da bi4all no Azure AD por email
  const fetchAzureUsersByEmail = async (emailSearch, pageNum = 0) => {
    if (!emailSearch || !emailSearch.includes('@')) {
      toast.error('Por favor, insira um email válido');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      // Construir o URL com parâmetros
      let url = `${process.env.REACT_APP_WEBAPI_URL}/api/auth/azure-users-public?page=${pageNum}&pageSize=${pageSize}&email=${encodeURIComponent(emailSearch)}`;
      
      console.log('Buscando usuário com email específico:', emailSearch);
      
      // Buscar usuários do Azure AD
      const response = await fetch(url);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Falha ao buscar usuários do Azure AD: ${response.status} ${errorText}`);
      }
      
      const data = await response.json();
      console.log('Dados recebidos da API:', data);
      
      // Verificar se temos a nova estrutura com informações de paginação
      if (data && data.users && Array.isArray(data.users)) {
        // Nova estrutura
        setAzureUsers(data.users);
        setTotalPages(data.totalPages || 1);
        setTotalUsers(data.totalUsers || data.users.length);
        setPage(data.currentPage || 0);
        
        if (data.users.length === 0) {
          setError('Nenhum usuário encontrado com este email');
        }
      } 
      // Compatibilidade com a resposta anterior (array simples)
      else if (Array.isArray(data)) {
        setAzureUsers(data);
        setTotalPages(1);
        setTotalUsers(data.length);
        
        if (data.length === 0) {
          setError('Nenhum usuário encontrado');
        }
      } 
      else {
        throw new Error('Formato de resposta desconhecido');
      }
    } catch (error) {
      console.error('Erro ao buscar usuários:', error);
      setError(error.message || 'Erro ao buscar usuários do Azure AD');
      toast.error('Erro ao buscar usuários do Azure AD');
    } finally {
      setLoading(false);
    }
  };

  // Limpar o estado ao abrir o modal
  useEffect(() => {
    if (open) {
      setAzureUsers([]);
      setSelectedUsers([]);
      setEmailQuery('');
      setError(null);
      setPage(0);
    }
  }, [open]);

  const handleToggleUser = (user) => {
    const isSelected = selectedUsers.some(u => u.id === user.id);
    
    if (isSelected) {
      setSelectedUsers(selectedUsers.filter(u => u.id !== user.id));
    } else {
      setSelectedUsers([...selectedUsers, user]);
    }
  };

  const handleAddUsers = async () => {
    if (selectedUsers.length === 0) {
      toast.error('Selecione pelo menos um usuário');
      return;
    }

    setLoading(true);
    try {
      // Adicionar usuários com permissão de admin usando o endpoint público
      const response = await fetch(`${process.env.REACT_APP_WEBAPI_URL}/api/user/add-azure-users-public`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          users: selectedUsers.map(user => ({
            azureId: user.id,
            displayName: user.displayName,
            email: user.mail || user.userPrincipalName
          })),
          isAdmin: true
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Falha ao adicionar usuários: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      console.log('Resultado da adição de usuários:', result);

      toast.success(`${result.totalAdded || selectedUsers.length} usuário(s) adicionado(s) com sucesso`);
      onAddUsers(); // Atualizar lista de usuários na tela principal
      onClose();
    } catch (error) {
      console.error('Erro ao adicionar usuários:', error);
      toast.error(error.message || 'Erro ao adicionar usuários');
    } finally {
      setLoading(false);
    }
  };

  // Lidar com a mudança de página da paginação
  const handlePageChange = (event, newPage) => {
    // Nossa API começa com página 0, mas o componente MUI começa com 1
    fetchAzureUsersByEmail(emailQuery, newPage - 1);
  };

  // Lidar com tecla Enter no campo de email
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && emailQuery) {
      fetchAzureUsersByEmail(emailQuery);
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle sx={{ fontSize: '1.25rem', fontWeight: 500 }}>
        {t("user.addBI4ALLUsers")}
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ mb: 3, mt: 1 }}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            {t("user.searchByEmail")}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={9}>
              <TextField
                fullWidth
                placeholder={t("user.emailPlaceholder")}
                value={emailQuery}
                onChange={(e) => setEmailQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailIcon />
                    </InputAdornment>
                  )
                }}
                size="small"
              />
            </Grid>
            <Grid item xs={3}>
              <Button 
                variant="contained" 
                fullWidth 
                onClick={() => fetchAzureUsersByEmail(emailQuery)}
                disabled={loading || !emailQuery}
                startIcon={<PersonSearch />}
              >
                {t("user.search")}
              </Button>
            </Grid>
          </Grid>
        </Box>
        
        <Divider sx={{ my: 2 }} />
        
        {/* Exibir a mensagem de erro se houver algum */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {azureUsers.length > 0 && (
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  {totalUsers > 0 ? t("user.usersFound", { count: totalUsers }) : t("user.noUsersFound")}
                </Typography>
              </Box>
            )}
            
            {azureUsers.length > 0 ? (
              <Paper variant="outlined" sx={{ maxHeight: 400, overflow: 'auto', mb: 2 }}>
                <List dense>
                  {azureUsers.map((user) => {
                    const isSelected = selectedUsers.some(u => u.id === user.id);
                    return (
                      <ListItem 
                        key={user.id} 
                        button 
                        onClick={() => handleToggleUser(user)}
                        selected={isSelected}
                        sx={{
                          '&.Mui-selected': {
                            backgroundColor: 'rgba(0, 114, 229, 0.1)',
                          }
                        }}
                      >
                        <ListItemIcon>
                          <Checkbox
                            edge="start"
                            checked={isSelected}
                            tabIndex={-1}
                            disableRipple
                          />
                        </ListItemIcon>
                        <ListItemIcon>
                          <Avatar>{user.displayName?.[0] || '?'}</Avatar>
                        </ListItemIcon>
                        <ListItemText 
                          primary={user.displayName}
                          secondary={user.mail || user.userPrincipalName}
                        />
                      </ListItem>
                    );
                  })}
                </List>
              </Paper>
            ) : (
              !error && (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Typography color="text.secondary">
                    {t("user.enterEmailToSearch")}
                  </Typography>
                </Box>
              )
            )}
            
            {/* Paginação */}
            {totalPages > 1 && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <Pagination 
                  count={totalPages} 
                  page={page + 1} 
                  onChange={handlePageChange} 
                  color="primary" 
                />
              </Box>
            )}
          </>
        )}
        
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="body2" color="text.secondary">
            {selectedUsers.length > 0 ? t("user.usersSelected", { count: selectedUsers.length }) : ''}
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} color="inherit">
          {t("buttons.cancel")}
        </Button>
        <Button 
          onClick={handleAddUsers} 
          variant="contained" 
          disabled={selectedUsers.length === 0 || loading}
        >
          {t("user.addUsers")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddUserModal; 