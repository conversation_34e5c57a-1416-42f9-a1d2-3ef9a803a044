﻿using System;
using System.Collections.Generic;

namespace RnD.BackEnd.API.Models
{
    public class Consumo
    {
        public int ID { get; set; }

        public int ClienteID { get; set; }
        public DateTime DataInicio { get; set; }
        public DateTime DataFim { get; set; }

        public decimal CountryListTotal { get; set; }
        public decimal CountryResellerTotal { get; set; }
        public decimal CountryCustomerTotal { get; set; }

        public string TipoServico { get; set; }
        public string Regiao { get; set; }
        public string Moeda { get; set; }

        // Relação com Cliente (via Consumo_Cliente)
        public ICollection<Consumo_Cliente> Consumo_Cliente { get; set; }
    }
}
