﻿using System.Net;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Options;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.Domain.BusinessMessages;
using RnD.BackEnd.Domain.Entities.Generic;
using RnD.BackEnd.Domain.Settings;

namespace RnD.BackEnd.API.Helpers
{
    /// <summary>
    /// Action filter Attribute to check if workflow engine is allowed
    /// </summary>
    /// <seealso cref="Microsoft.AspNetCore.Mvc.Filters.ActionFilterAttribute" />
    public class WorkflowEngineAllowedAttribute : ActionFilterAttribute
    {
        private readonly IMapper _mapper;
        private readonly bool? _allowWorkflowEngine;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkflowEngineAllowedAttribute" /> class.
        /// </summary>
        /// <param name="mapper">The mapper.</param>
        /// <param name="settings">The settings.</param>
        public WorkflowEngineAllowedAttribute(IMapper mapper, IOptions<AppSettings> settings)
        {
            _mapper = mapper;
            _allowWorkflowEngine = settings?.Value?.WorkflowSettings?.AllowWorkflowEngine;
        }

        /// <summary>
        /// Intercept a request to validate if workflow engine is allowed
        /// </summary>
        /// <param name="context"></param>
        /// <param name="next"></param>
        /// <inheritdoc />
        public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            if (_allowWorkflowEngine.HasValue && _allowWorkflowEngine.Value)
            {
                await next();
            }
            else
            {
                ServiceOutput<bool> output = new ServiceOutput<bool>()
                {
                    Code = HttpStatusCode.InternalServerError,
                    Description = WorkflowsMessages.WorkflowEngineDisabled
                };

                context.Result = new ObjectResult(_mapper.Map<ServiceOutput<bool>, ApiOutput<bool>>(output))
                {
                    StatusCode = (int)output.Code
                };
            }
        }
    }
}
