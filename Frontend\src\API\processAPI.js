import handleMethod from '../Utils/APIutils/handleMethod';

class ProcessAPI {
  async getProcess(pid, status, hid) {
    pid = pid && pid !== null ? `?ProcessID=${pid}` : '';
    status = status ? `?statusDescription=${status}` : '';
    hid = hid ? `&HiringOfferID=${hid}` : '';
    const Input = {
      type: 'GET',
      method: `getProcess${pid}${status}${hid}`,
      params: {}
    };

    const response = await handleMethod(Input);
    return {
      error: false,
      value: {
        processList: response
      }
    };
  }

  async getProcessTypes() {
    const Input = {
      type: 'GET',
      method: `getProcessTypes`,
      params: {}
    };

    const response = await handleMethod(Input);

    return response;
  }

  async getProcessStepDocument(processStepID) {
    processStepID = processStepID ? `?ProcessStepID=${processStepID}&` : '';
    const Input = {
      type: 'GET',
      method: `getProcess${processStepID}`,
      params: {}
    };

    const response = await handleMethod(Input);

    return response;
  }
}

export const processAPI = new ProcessAPI();
