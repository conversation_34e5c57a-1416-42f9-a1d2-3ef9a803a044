using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using RnD.BackEnd.API.Models;
using System.Threading.Tasks;

namespace RnD.BackEnd.API.Services
{
    public interface IConsumoPDFService
    {
        Task<List<ConsumoPDFModel>> ProcessPDFFile(string fileName, bool forceReprocessing = false);
        Task<string> UploadPDFFile(IFormFile file);
        Task<Dictionary<string, string>> GetCrayonPDFsRawText();
        Task<Dictionary<string, CrayonPDFExtractionResult>> GetCrayonPDFsExtractedData();
    }
}