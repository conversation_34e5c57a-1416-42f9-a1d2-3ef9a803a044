import axios from 'axios';

const API_BASE_URL = `${process.env.REACT_APP_WEBAPI_URL}/api`;

export const getConsumoSummaryByCliente = async (clienteId, dataInicio = null, dataFim = null) => {
    console.log('Iniciando getConsumoSummaryByCliente:', { clienteId, dataInicio, dataFim });
    try {
        // Construir a URL com parâmetros de data se fornecidos
        let url = `${API_BASE_URL}/ConsumoSubscricao/cliente/${clienteId}/summary`;
        
        const params = [];
        if (dataInicio) {
            params.push(`startDate=${dataInicio.toISOString()}`);
        }
        if (dataFim) {
            params.push(`endDate=${dataFim.toISOString()}`);
        }
        
        if (params.length > 0) {
            url += `?${params.join('&')}`;
        }
        
        console.log('Fazendo requisição para:', url);
        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        console.log('Resposta recebida:', response.data);
        return response.data;
    } catch (error) {
        console.error('Erro ao buscar resumo de consumo Azure:', error);
        console.log('Detalhes do erro:', {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
        });
        // Retorna um objeto vazio com a estrutura esperada em caso de erro
        return {
            TotalCost: 0,
            TotalCostUSD: 0,
            ResourceCount: 0,
            Currency: "EUR",
            ResourceGroups: [],
            Locations: []
        };
    }
};

// Função para buscar os dados de consumo reseller para um cliente específico
export const getConsumoResellerByCliente = async (clienteId, dataInicio = null, dataFim = null) => {
    console.log('Iniciando getConsumoResellerByCliente:', { clienteId, dataInicio, dataFim });
    try {
        // Construir a URL com parâmetros de data se fornecidos
        let url = `${API_BASE_URL}/Consumo_Cliente/cliente/${clienteId}`;
        
        const params = [];
        if (dataInicio) {
            params.push(`dataInicio=${dataInicio.toISOString()}`);
        }
        if (dataFim) {
            params.push(`dataFim=${dataFim.toISOString()}`);
        }
        
        if (params.length > 0) {
            url += `?${params.join('&')}`;
        }
        
        console.log('Fazendo requisição para:', url);
        const response = await axios.get(url, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        console.log('Resposta recebida (consumo reseller):', response.data);
        
        // Se não houver dados, retornar objeto vazio com estrutura esperada
        if (!response.data || response.data.length === 0) {
            return {
                countryCustomerTotal: 0,
                countryResellerTotal: 0,
                countryListTotal: 0
            };
        }
        
        // Filtrar os registros por data se necessário (caso o backend não suporte o filtro)
        let filteredData = response.data;
        if (dataInicio || dataFim) {
            filteredData = response.data.filter(item => {
                const dataInicioConsumo = new Date(item.consumo?.dataInicio);
                const dataFimConsumo = new Date(item.consumo?.dataFim);
                
                let passesFilter = true;
                if (dataInicio && dataInicioConsumo < dataInicio) {
                    passesFilter = false;
                }
                if (dataFim && dataFimConsumo > dataFim) {
                    passesFilter = false;
                }
                return passesFilter;
            });
            
            console.log('Dados filtrados por data:', filteredData.length);
        }
        
        // Calcular a soma dos valores countryCustomerTotal de todos os registros
        const countryCustomerTotal = filteredData.reduce(
            (sum, item) => sum + (item.consumo?.countryCustomerTotal || 0), 0
        );
        
        // Incluir também os outros valores que apareceram na interface
        const countryResellerTotal = filteredData.reduce(
            (sum, item) => sum + (item.consumo?.countryResellerTotal || 0), 0
        );
        
        const countryListTotal = filteredData.reduce(
            (sum, item) => sum + (item.consumo?.countryListTotal || 0), 0
        );
        
        return {
            countryCustomerTotal,
            countryResellerTotal,
            countryListTotal
        };
    } catch (error) {
        console.error('Erro ao buscar consumo reseller:', error);
        console.log('Detalhes do erro:', {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
        });
        // Retorna um objeto vazio com a estrutura esperada em caso de erro
        return {
            countryCustomerTotal: 0,
            countryResellerTotal: 0,
            countryListTotal: 0
        };
    }
};

export default {
    getConsumoSummaryByCliente,
    getConsumoResellerByCliente
}; 