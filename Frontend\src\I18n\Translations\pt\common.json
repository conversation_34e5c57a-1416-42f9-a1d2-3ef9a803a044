{"common": {"value": "Valor", "filter": "Filtro", "add_filters": "<PERSON><PERSON><PERSON><PERSON>", "choose_filter": "E<PERSON><PERSON><PERSON> filtro", "view": "Visualizar filtros personalizados", "en": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pt": "Português", "logout": "<PERSON><PERSON>"}, "placeholder": {"search": "Pesquisar..."}, "buttons": {"update": "<PERSON><PERSON><PERSON><PERSON>", "return": "Voltar", "createClient": "<PERSON><PERSON><PERSON>", "save": "Guardar", "submit": "Submeter", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "copy": "Copiar", "download": "Download", "clone": "Duplicar", "remove": "Remover", "revert": "<PERSON><PERSON><PERSON>", "archive": "<PERSON><PERSON><PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON><PERSON>", "unlock": "Desb<PERSON>que<PERSON>", "user": "Utilizador", "saveChanges": "Guardar Alterações", "deleteUser": "Eliminar Utilizador", "profile": "Perfil", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "observations": "Observações", "add": "<PERSON><PERSON><PERSON><PERSON>", "confirmDeletion": "Confirmar <PERSON>", "deleting": "A Eliminar..."}, "user": {"addBI4ALLUsers": "Adicionar Utilizadores BI4ALL", "addUsers": "<PERSON><PERSON><PERSON><PERSON>", "searchByEmail": "Busca por email no Azure AD:", "search": "Buscar", "enterEmailToSearch": "Insira um email para buscar utilizadores", "usersFound": "{{count}} utilizadores encontrados", "noUsersFound": "Nenhum utilizador encontrado", "usersSelected": "{{count}} utilizadores selecionados", "emailPlaceholder": "<EMAIL>", "noPermissions": "Sem permiss<PERSON>"}, "subscription": {"id": "ID da Subscrição", "totalCostEUR": "Custo Total (EUR)", "totalCostUSD": "Custo Total (USD)", "resourceCount": "Número de Recursos", "startDate": "Data Início", "endDate": "Data Fim", "noSubscriptionsFound": "Nenhuma subscrição encontrada", "associatedSubscriptions": "Subscrições Associadas", "addSubscription": "Adicionar Subscrição", "noAssociatedSubscriptions": "Nenhuma subscrição associada a este cliente.", "noAvailableSubscriptions": "Não há subscrições disponíveis para associar.", "selectAvailableSubscription": "Selecione uma subscrição disponível"}, "months": {"jan": "Janeiro", "feb": "<PERSON><PERSON>", "mar": "Março", "apr": "Abril", "may": "<PERSON><PERSON>", "jun": "<PERSON><PERSON>", "jul": "<PERSON><PERSON>", "aug": "Agosto", "sep": "Setembro", "oct": "Out<PERSON>ro", "nov": "Novembro", "dec": "Dezembro"}, "labels": {"allfiles": "Todos os arquivos", "alltypes": "Todos os tipos", "employee": "Colaborador", "team": "Equipa", "month": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON>", "total": "Total", "days": "<PERSON><PERSON>", "approvalStatus": "Estado de Aprovação", "more": "mais", "noResultsFound": "Nenhum resultado encontrado", "profilesAccess": "<PERSON><PERSON><PERSON>", "connectProfiles": "Relaciona Perfis", "all": "Todos", "user": "Utilizador", "apiUser": "Utilizador API", "profiles": "<PERSON><PERSON><PERSON>", "deleteUser": "Eliminar Utilizador", "permissions": "Permissões", "leaveComment": "De<PERSON>ar coment<PERSON>rio...", "processedFile": "Arquivo Processado", "processedFiles": "Arquivos Processados", "processingDate": "Data de Processamento", "processingStatus": "Status de Processamento", "successfullyProcessed": "Processado com Sucesso", "processingError": "Erro no Processamento", "processingWarning": "Aviso de Processamento", "infoLog": "Log de Informação", "clientDetails": "Detalhes do Cliente", "editClient": "<PERSON><PERSON>", "clientNotFound": "Cliente não encontrado", "confirmDeleteClient": "Tem certeza que deseja apagar este cliente? Esta ação não pode ser desfeita."}, "tableActions": {"noValues": "Sem valores", "loading": "A Carregar...", "search": "<PERSON><PERSON><PERSON><PERSON>", "record_one": "registo", "record_other": "registos", "select": "Selecionar", "selected_one": "sele<PERSON><PERSON><PERSON>", "selected_other": "seleccionados", "select_all": "Selecionar todos", "noResults": "<PERSON><PERSON>", "option": "Escolhe uma opção", "loadingPermissions": "A Carregar permissões...", "initialdate": "Data Inicial", "finaldate": "Data Final", "rowsPerPage": "<PERSON><PERSON> por página:"}, "formik": {"requiredField": "Campo Obrigatório"}, "conditions": {"condition": "Condição", "contains": "contém", "sw": "começa com", "ew": "acaba com", "eq": "é igual a", "gt": "superior a", "lt": "inferior a"}, "Chips": {"new": "Novo", "improved": "<PERSON><PERSON><PERSON>", "fixed": "Resolvido"}, "tableHeaders": {"comparation": "Comparações", "resellerFacturation": "Faturação Reseller", "azureConsume": "Consumo Azure", "resellerConsume": "Consumo Reseller", "consumoAzure": "Consumo Azure", "consumoReseller": "Consumo Reseller", "faturacaoReseller": "Faturação Reseller", "basicInfo": "Informações Básicas", "errorMessage": "Mensagem de Erro", "hourDate": "Hora/Data", "fileID": "ID Arquivo", "file": "Arquivo", "logtype": "<PERSON><PERSON><PERSON> de <PERSON>", "message": "Mensagem", "logsArchive": "Arquivos Processados", "costs": "<PERSON><PERSON><PERSON>", "email": "E-mail", "functionalName": "Nome Funcional", "technicalName": "Nome Técnico", "createdBy": "<PERSON><PERSON><PERSON>", "createdAt": "Criado Em", "modifiedBy": "Alterado Por", "modifiedAt": "Alterado Em", "profiles": "<PERSON><PERSON><PERSON>", "users": "Utilizadores", "permission": "<PERSON><PERSON><PERSON><PERSON>", "entities": "Entidades", "nFields": "Nº de Campos", "nRecords": "Nº de Registos", "fieldType": "Tipo de Campo", "validation": "Validação", "size": "<PERSON><PERSON><PERSON>", "entity": "Entidade", "entityField": "Campo de Entidade", "mandatory": "Obrigatório", "hidden": "<PERSON>scondi<PERSON>", "name": "Nome", "roles": "<PERSON><PERSON><PERSON>", "permissions": "Permissões", "model": "<PERSON><PERSON>", "username": "Utilizador", "contact": "Contacto", "services": "Serviços", "fileName": "Nome do Arquivo", "processTime": "Tempo de Processamento", "recordsProcessed": "Registros Processados"}, "search": {"search": "Procurar...", "searchRole": "<PERSON><PERSON><PERSON>", "searchUser": "<PERSON><PERSON><PERSON>", "searchClient": "<PERSON><PERSON><PERSON>", "searchProfiles": "Procurar perfis...", "searchProfile": "<PERSON><PERSON><PERSON>", "searchSubscription": "Pesquisar Subscrição"}, "form": {"functionalName": "Nome Funcional", "technicalName": "Nome Técnico", "name": "Nome", "roleDescription": "Descrição do perfil", "userSearch": "<PERSON><PERSON><PERSON>", "username": "Utilizador"}, "warnings": {"unsavedData": "Poderá haver dados que não foram guardados. Tem a certeza que quer sair?", "profileMandatory": "Precisa pelo menos de um Perfil", "deleteMessageUser": "Tem a certeza que quer eliminar?", "deleteMessageRowsPerm": "Esta ação vai eliminar o(s) item(s) permanentemente. Tem a certeza que quer eliminar?", "deleteMessageRows": "Tem a certeza que quer eliminar o(s) item(s)?", "roleDescription": "Descrição do perfil é obrigatória", "onePermission": "Pelo menos uma permissão é obrigatória", "deleteModel": "Eliminar Modelo", "deleteEntity": "Eliminar Entidade", "deleteProfile": "Eliminar Perfil"}}