﻿using System;
using System.Collections.Generic;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Workflow instance DTO class
    /// </summary>
    public class WorkflowInstanceDto
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public Guid? Id { get; set; }

        /// <summary>
        /// Gets or sets the workflow identifier.
        /// </summary>
        /// <value>
        /// The workflow identifier.
        /// </value>
        public Guid WorkflowId { get; set; }

        /// <summary>
        /// Gets or sets the display name.
        /// </summary>
        /// <value>
        /// The display name.
        /// </value>
        public string DisplayName { get; set; }

        /// <summary>
        /// Gets or sets the source identifier.
        /// </summary>
        /// <value>
        /// The source identifier.
        /// </value>
        public string SourceId { get; set; }

        /// <summary>
        /// Gets or sets the source URL.
        /// </summary>
        /// <value>
        /// The source URL.
        /// </value>
        public string SourceUrl { get; set; }

        /// <summary>
        /// Gets or sets the workflow status.
        /// </summary>
        /// <value>
        /// The workflow status.
        /// </value>
        public string WorkflowStatus { get; set; }

        /// <summary>
        /// Gets or sets the type of the source.
        /// </summary>
        /// <value>
        /// The type of the source.
        /// </value>
        public string SourceType { get; set; }

        /// <summary>
        /// Gets or sets the type of the workflow.
        /// </summary>
        /// <value>
        /// The type of the workflow.
        /// </value>
        public string WorkflowType { get; set; }

        /// <summary>
        /// Gets or sets the purpose.
        /// </summary>
        /// <value>
        /// The purpose.
        /// </value>
        public string Purpose { get; set; }

        /// <summary>
        /// Gets or sets the state processing required.
        /// </summary>
        /// <value>
        /// The state processing required.
        /// </value>
        public bool? StateProcessingRequired { get; set; }

        /// <summary>
        /// Gets or sets the name of the active step.
        /// </summary>
        /// <value>
        /// The name of the active step.
        /// </value>
        public string ActiveStepName { get; set; }

        /// <summary>
        /// Gets or sets the steps.
        /// </summary>
        /// <value>
        /// The steps.
        /// </value>
        public IList<WorkflowInstanceStepDto> Steps { get; set; }

        /// <summary>
        /// Gets or sets the system create user identifier.
        /// </summary>
        /// <value>
        /// The system create user identifier.
        /// </value>
        public string SysCreateUserId { get; set; }

        /// <summary>
        /// Gets or sets the system modify date.
        /// </summary>
        /// <value>
        /// The system modify date.
        /// </value>
        public DateTimeOffset SysModifyDate { get; set; }
    }
}
