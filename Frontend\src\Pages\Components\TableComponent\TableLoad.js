/* eslint-disable */
import {
  Box,
  Divider,
  Typography,
  Table,
  TableBody,
  Card,
  CardHeader,
  CardContent,
  Tooltip,
  IconButton,
  Link,
} from "@mui/material";
import { useTheme } from '@emotion/react';
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism.css"; //Example style, you can use another
import CopyToClipboardButton from "../../../Components/CopyToClipBoardButton";
import { useState } from "react";
import TableHeader from "../../../Components/TableUtils/TableHeader";
import TableLoading from "../../../Components/TableUtils/TableLoading";
import {
  ExpandMore as ExpandMoreIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from "@mui/icons-material";
import getTableLoadIntro from "./mocks/tableLoadIntro";
import getTableLoadCode from "./mocks/tableLoadCode";
import {
  ChangeLogEntry,
  chipStyles,
} from "../../../Components/ChangeLog/ChangeLogEntry";
import { styled } from '@mui/system';

const StyledEditorBox = styled(Card)({
  width: "80%",
  margin: "auto",
  marginTop: 10,
  marginBottom: 10,
  maxHeight: "100vh"
})

const linkStyle = {
  marginRight: 20,
  alignItems: "center",
  textDecoration: "none",
};

const headCells = [
  { id: "example1", label: "Example1" },
  { id: "example2", label: "Example2" },
  { id: "example3", label: "Example3" },
  { id: "example4", label: "Example4", align: "center" },
  { id: "example5", align: "right" },
];

export default function TableLoad({ open: openProp }) {
  const theme = useTheme();
  const [isLoading, setLoading] = useState(true);
  const [intro, setIntro] = useState(getTableLoadIntro);
  const [code, setCode] = useState(getTableLoadCode);

  const [open, setOpen] = useState(openProp);
  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };
  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Box
        sx={{
          m: "auto",
          textAlign: "center"
        }}
      >
        <Box
          sx={{
            maxWidth: "80%",
            m: "auto",
            textAlign: "justify",
            color: "textPrimary",
          }}
        >
          <h2>Table Loading</h2>
          <p>
            This component displays a placeholder preview of our content before
            the data gets loaded to reduce load-time frustration.
          </p>
        </Box>
        <Box
          sx={{
            m: "auto",
            p: 2,
            minWidth: 700,
            border: `solid 1px ${theme.palette.secondary.main}`,
            maxWidth: "80%",
          }}
        >
          <Table stickyHeader>
            <TableHeader headCells={headCells} />
            <TableBody>
              <TableLoading
                isLoading={isLoading}
                headCells={headCells}
                numRows={3}
              />
            </TableBody>
          </Table>
        </Box>
        <Typography
          sx={{
            m: "auto",
            mt: 5,
            textAlign: "left",
            maxWidth: "80%",
            color: "textPrimary",
          }}
        >
          The following code snippet demonstrates how Table Loading component is
          displayed. You can copy this code snippet to your src folder:
        </Typography>
        <StyledEditorBox>
          <CardHeader
            action={
              <>
                <CopyToClipboardButton value={open ? code : intro} />
                <Tooltip
                  title={
                    !open ? "Show full source code" : "Hide full source code"
                  }
                >
                  <IconButton
                    onClick={handleToggle}
                    sx={{
                      color: `${theme.palette.primary.main}`,
                      "&:hover": {
                        backgroundColor: `${theme.palette.secondary.main}`,
                        color: "primary.main",
                      },
                      height: 35,
                      width: 35,
                      mt: 1,
                    }}
                  >
                    {!open ? <ExpandMoreIcon /> : <KeyboardArrowUpIcon />}
                  </IconButton>
                </Tooltip>
              </>
            }
          />
          <Divider />
          <CardContent
            sx={{
              maxHeight: "50vh",
              overflowY: "scroll",
            }}
          >
            <Editor
              value={open ? code : intro}
              onValueChange={
                open ? (_code) => setCode(_code) : (_intro) => setIntro(_intro)
              }
              highlight={
                open
                  ? (_code) => highlight(_code, languages.js)
                  : (_intro) => highlight(_intro, languages.js)
              }
              padding={10}
              style={{
                fontFamily: '"Fira code", "Fira Mono", monospace',
                fontSize: 12,
                backgroundColor: theme.palette.background.paper,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </CardContent>
        </StyledEditorBox>
        <Box
          xs={12}
          sx={{
            m: "auto",
            textAlign: "justify",
            maxWidth: "80%",
            color: "textPrimary",
          }}
        >
          <p>
            Table loading receives the following props and is used in
            conjunction with
            <Link
              style={linkStyle}
              href="https://mui.com/material-ui/react-table/"
            >
              Material UI
            </Link>
            components.
          </p>
          <ul>
            <li>
              <b>headCells</b> - The label shown in the table header;
            </li>
            <li>
              <b>isLoading</b> - Indicates whether page loading is true or
              false;
            </li>
            <li>
              <b>numRows</b> - The Number of Rows that will appear;
            </li>
          </ul>
        </Box>
      </Box>
      <Box sx={{ textAlign: "center", mt: 5 }}>
        See also:
        <br />
        <Link style={linkStyle} href="/Components/FunctionalTableExample/">
          Functional Table Example
        </Link>
        <Link style={linkStyle} href="/Components/TableHeader/">
          Table Header
        </Link>
        <Link style={linkStyle} href="/Components/TableActions/">
          Table Actions
        </Link>
        <Link style={linkStyle} href="/Components/TableUtils/">
          Table Utils
        </Link>
      </Box>
      <ChangeLogEntry version="V1.0.0" changeLog={ChangeLog()} />
    </Box>
  );
}
const ChangeLog = () => {
  const theme = useTheme();
  const chips = chipStyles(theme);

  const changeLog = [
    {
      label: "New",
      content: "Imported Table Loading Component.",
      className: chips.green,
    },
  ];

  return changeLog;
};
