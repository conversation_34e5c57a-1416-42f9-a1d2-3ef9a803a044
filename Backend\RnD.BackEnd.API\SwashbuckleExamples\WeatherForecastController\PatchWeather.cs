﻿using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using RnD.BackEnd.API.Model.Weather;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController
{
    /// <summary>
    /// Weather patch request example
    /// </summary>
    public class PatchWeather : IExamplesProvider<JsonPatchDocument<WeatherDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to patch a weather
        /// </returns>
        public JsonPatchDocument<WeatherDto> GetExamples()
        {
            JsonPatchDocument<WeatherDto> changes = new JsonPatchDocument<WeatherDto>();
            changes.Operations.Add(new Operation<WeatherDto>
            {
                op = "replace",
                path = "/temperature",
                value = 21
            });
            return changes;
        }
    }
}