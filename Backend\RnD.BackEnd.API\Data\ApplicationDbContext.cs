﻿using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Models;

namespace RnD.BackEnd.API.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options) { }

        public DbSet<User> User { get; set; }
        public DbSet<Permission> Permission { get; set; }
        public DbSet<UserPermission> UserPermission { get; set; }
        public DbSet<ClienteSubscricao> ClienteSubscricao { get; set; }
        public DbSet<Cliente> Cliente { get; set; }
        public DbSet<Consumo> Consumo { get; set; }
        public DbSet<Consumo_Cliente> Consumo_Cliente { get; set; }
        public DbSet<ConsumoSubscricao> ConsumoSubscricao { get; set; }
        public DbSet<Subscription> Subscription { get; set; }

        public DbSet<LogProcessamento> LogsProcessamento { get; set; }

        public DbSet<ArquivoProcessado> ArquivosProcessados { get; set; }

        public DbSet<ConsumoPDF> ConsumoPDF { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // config do cliente subscricao
            modelBuilder.Entity<ClienteSubscricao>(entity =>
            {
                entity.HasKey(e => e.ID);
                entity.Property(e => e.ID).UseIdentityColumn();

                entity.HasOne(cs => cs.Cliente)
                    .WithMany() // Ou .WithMany(c => c.ClienteSubscricoes) se houver a propriedade de navegação
                    .HasForeignKey(cs => cs.ClienteID)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(cs => cs.Subscription)
                    .WithMany() // Ou .WithMany(s => s.ClienteSubscricoes) se houver a propriedade de navegação
                    .HasForeignKey(cs => cs.SubscriptionID)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(cs => cs.SubscriptionID)
                    .IsUnique();
            });

            // Mapeando UserPermission para a tabela User_Permission
            modelBuilder.Entity<UserPermission>()
                .ToTable("User_Permission")
                .HasKey(up => new { up.UserID, up.PermissionID });

            //Configuração das relações muitos-para-muitos para UserPermission
            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.User)
                .WithMany(u => u.UserPermissions)
                .HasForeignKey(up => up.UserID);

            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.Permission)
                .WithMany(p => p.UserPermissions)
                .HasForeignKey(up => up.PermissionID);

            // Seed das permissões
            modelBuilder.Entity<Permission>().HasData(
                new Permission { Id = 1, Name = "903001" },  // PORTAL
                new Permission { Id = 2, Name = "903010" }   // PORTAL_ADMINISTRATION
            );

            modelBuilder.Entity<Consumo>(entity =>
            {
                entity.Property(e => e.CountryCustomerTotal)
                      .HasPrecision(18, 4); // 18 dígitos no total, 4 após a vírgula

                entity.Property(e => e.CountryListTotal)
                      .HasPrecision(18, 4);

                entity.Property(e => e.CountryResellerTotal)
                      .HasPrecision(18, 4);
            });

            // configuração da relação muitos-para-muitos entre Cliente e Consumo
            modelBuilder.Entity<Consumo_Cliente>()
                .HasKey(cc => cc.ID); // Chave primária

            modelBuilder.Entity<Consumo_Cliente>()
                .HasOne(cc => cc.Cliente)
                .WithMany(c => c.Consumo_Cliente)
                .HasForeignKey(cc => cc.ClienteID);

            modelBuilder.Entity<Consumo_Cliente>()
                .HasOne(cc => cc.Consumo)
                .WithMany(c => c.Consumo_Cliente)
                .HasForeignKey(cc => cc.ConsumoID);

            modelBuilder.Entity<Consumo_Cliente>()
                .HasIndex(cc => new { cc.ClienteID, cc.ConsumoID })
                .IsUnique();

            // Configuração da entidade Subscription
            modelBuilder.Entity<Subscription>(entity =>
            {
                entity.HasKey(e => e.ID);
                entity.Property(e => e.ID).UseIdentityColumn();
                
                entity.Property(e => e.SubscriptionID)
                    .HasMaxLength(255)
                    .IsRequired();

                entity.HasIndex(e => e.SubscriptionID)
                    .IsUnique();
            });

            // Configuração da entidade ConsumoSubscricao
            modelBuilder.Entity<ConsumoSubscricao>(entity =>
            {
                entity.HasKey(e => e.ID);
                entity.Property(e => e.ID).UseIdentityColumn();
                
                entity.Property(e => e.SubscriptionID)
                    .HasMaxLength(255)
                    .IsRequired();

                entity.Property(e => e.ResourceGroup)
                    .HasMaxLength(255)
                    .IsRequired();

                entity.Property(e => e.ResourceName)
                    .HasMaxLength(255)
                    .IsRequired();

                entity.Property(e => e.ResourceLocation)
                    .HasMaxLength(255)
                    .IsRequired();

                entity.Property(e => e.Cost)
                    .HasPrecision(18, 2)
                    .IsRequired();

                entity.Property(e => e.CostUSD)
                    .HasPrecision(18, 2)
                    .IsRequired();

                entity.Property(e => e.Currency)
                    .HasMaxLength(3)
                    .IsRequired();

                entity.HasOne(e => e.Subscription)
                    .WithMany(s => s.Consumos)
                    .HasForeignKey(e => e.SubscriptionID)
                    .HasPrincipalKey(s => s.SubscriptionID)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Configuração da entidade ConsumoPDF
            modelBuilder.Entity<ConsumoPDF>(entity =>
            {
                entity.Property(e => e.Total)
                    .HasPrecision(18, 2);
                    
                entity.Property(e => e.Iva)
                    .HasPrecision(18, 2);
                    
                entity.Property(e => e.TotalFatura)
                    .HasPrecision(18, 2);
            });
        }
    }
}
