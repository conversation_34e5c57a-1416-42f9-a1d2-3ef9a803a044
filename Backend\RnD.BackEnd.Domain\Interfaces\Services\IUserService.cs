using System.Collections.Generic;
using System.Threading.Tasks;

namespace RnD.BackEnd.Domain.Interfaces.Services
{
    public interface IUserService
    {
        Task<object> GetUserByAzureIdAsync(string azureId);
        Task<object> CreateUserAsync(string azureId, string username, string email);
        Task AssignDefaultPermissionsAsync(object user);
        Task<List<string>> GetUserPermissionsAsync(int userId);
    }
} 