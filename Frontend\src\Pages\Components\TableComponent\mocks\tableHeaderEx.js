const getTableHeaderExample = () => {
    return `
    //Example:

    const headCells = [
    { id: 'example1', label: 'Example1' },
    { id: 'example2', label: 'Example2', sort: true, filter: true },
    { id: 'example3', label: 'Example3' }
    ]
    <Your code...>
        <TableHeader
            headCells={headCells}
            selectedAll={selectedAllprocesses}
            selectedSome={selectedSomeprocesses}
            onSelectAllClick={handleSelectAllprocesses}
            onSortClick={handleRequestSort}
            orderBy={orderBy}
            order={order}
        />   
    </Your code...>
    `;
}

export default getTableHeaderExample;
