﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Workflow;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// Get Tasks response example for 200
    /// </summary>
    public class GetTasks200 : IExamplesProvider<ApiOutput<WorkflowTasksListDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 for tasks response.
        /// </returns>
        public ApiOutput<WorkflowTasksListDto> GetExamples()
        {
            return new ApiOutput<WorkflowTasksListDto>
            {
                Code = HttpStatusCode.OK,
                Description = null,
                Value = new WorkflowTasksListDto()
                {
                    Tasks = new List<WorkflowTaskDto>()
                    {
                        new WorkflowTaskDto()
                        {
                            Id = Guid.Parse("f80c4b6d-61ce-406a-8cd1-d66361adc977"),
                            WorkflowInstanceId = Guid.Parse("1949db44-5cd7-409c-9244-07a5210fa56f"),
                            StepId = Guid.Parse("53ab3926-3311-4565-b732-dbe2b8d156ac"),
                            UserId = "<EMAIL>",
                            TaskStatus = "TODO",
                            TaskCompleted = false,
                            TaskActivated = true,
                            Comments = "",
                            SourceId = "Project/2023",
                            SourceUrl = null,
                            WorkflowStatus = "IN_PROGRESS",
                            WorkflowName = "Not Billable Project Approval",
                            StepName = "Approval",
                            StepOrder = 1,
                            SysModifyDate = Convert.ToDateTime("2023-01-18T15:59:43.713+00:00"),
                            TotalRows = 0,
                            UserAllowedToEdit = false
                        },
                        new WorkflowTaskDto()
                        {
                            Id = Guid.Parse("cc7876b1-7596-40d4-b113-17e2b4d202e4"),
                            WorkflowInstanceId = Guid.Parse("ed905dba-0d04-4d7e-abc3-580512a7a68f"),
                            StepId = Guid.Parse("bdac4a3f-3775-4582-a52c-0cdc801e8776"),
                            UserId = "<EMAIL>",
                            TaskStatus = "TODO",
                            TaskCompleted = false,
                            TaskActivated = true,
                            Comments = "",
                            SourceId = "959c70a9-cde6-4cd0-ad57-f6c72ae6b981/c0c23c09-0761-4b59-9f2f-5a3645d9cb57",
                            SourceUrl = "/dashboard/admin/projects/959c70a9-cde6-4cd0-ad57-f6c72ae6b981/detail",
                            WorkflowStatus = "IN_PROGRESS",
                            WorkflowName = "PROJECT APPROVAL",
                            StepName = "Step 1",
                            StepOrder = 0,
                            SysModifyDate = Convert.ToDateTime("2023-01-30T17:42:49.507+00:00"),
                            TotalRows = 0,
                            UserAllowedToEdit = false
                        }
                    },
                    TotalRows = 200
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}
