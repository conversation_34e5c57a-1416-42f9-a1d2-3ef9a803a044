﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Workflow;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// The example of a 200 response when listing definitions.
    /// </summary>
    public class GetDefinitions200 : IExamplesProvider<ApiOutput<List<WorkflowDefinitionDto>>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 for a list response.
        /// </returns>
        public ApiOutput<List<WorkflowDefinitionDto>> GetExamples()
        {
            return new ApiOutput<List<WorkflowDefinitionDto>>
            {
                Code = HttpStatusCode.OK,
                Value = new List<WorkflowDefinitionDto>
                {
                    new WorkflowDefinitionDto
                    {
                        Id=Guid.NewGuid(),
                        DisplayName = "Not Billable Project Approval",
                        RulesConditionType = "ANY_RULE",
                        SourceType = "PROJECT",
                        WorkflowType = "AUTOMATIC",
                        Purpose = "APPROVAL",
                        NumberOfSteps = 1,
                        NumberOfRules = 1,
                        IsActive = true

                    },
                    new WorkflowDefinitionDto
                    {
                        Id=Guid.NewGuid(),
                        DisplayName = "Test Skip LR",
                        RulesConditionType = "ANY_RULE",
                        SourceType = "DOCUMENT",
                        WorkflowType = "MANUAL",
                        Purpose = "APPROVAL",
                        NumberOfSteps = 2,
                        NumberOfRules = 0,
                        IsActive = true
                    }
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}