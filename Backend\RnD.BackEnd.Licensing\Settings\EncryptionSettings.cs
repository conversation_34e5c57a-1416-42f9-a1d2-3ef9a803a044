﻿namespace RnD.BackEnd.Licensing.Settings
{
    public static class EncryptionSettings
    {
        public const string DECRYPTION_KEY = "y/B?E(H+KbPeShVmYq3t6w9z$C&F)J@NcQfTjWnZr4u7x!A%D*G-KaPdSgUkXp2s";

        public const string EncryptionKid = "9524e3ea274e387f85c5c775dcd602c9";
        public const string StrPrivEncKey = "<RSAKeyValue><Modulus>44EzPIqi+X+Izb5FHnPJIC7NvfP1TvIvtNT/DXUhGr0A9fydKhDpjxUGCdlfnel/FaCFe0Ecz1ZKnk/T7pxYHdRTJxJTpu84usbIqLu+fcVUPJOoh5/ciTPOmmcfv3Q3NO8zwxBa4J06TkPfih2388G+uX0dEzYIRV78sIjUipwO4jINBLLtZakNOgcKIMs0fdnViIz85BmUIrtH4W4G5wRuw2kTV/nk+FEQjbMK9AyjEHtWyHiy+DzJz9ccbTb28ZCRmwQ4c3xvwnpttRKM56IrY69dCp7XG+eAVVIpLBzMoCWRNDThtXYy9JcMGs29ccX4R6gUk6vede9a/hogA+LIvIxseQv1ZtUcxEV47Qbd8d4CMwmNZQnAEW0LjEkcKiy0JQkCXxALXgrlEuwJCj8SSGlWCgqqljwdQq6h0gLSQt52Nd9Cl3gZras9Oqan8eY7AuYvbhFqNblFDDPzwJH7lbdEMMHljqSC8GHjxAq4xkLGxvqoYVsOazZtnaJh</Modulus><Exponent>AQAB</Exponent><P>91GV6jZgzU2JdID+8Xeea+FaIwDjCuuzOsLX3qEeyTRRSU5vhziSGcU7c+kjJtZgpKSbVtDpvIX7diehnbPx6azWvN9VKvsgvqR4qYDCEaQxH2CLcvV7Swo947de1CwzNcvjk0o7etb3vAQC+7PsiguU5UoE+ABE9iUDQHUmk+P0NZLgyCkS2CBJmsSZdX5RGDd10a5piON6UYcFdd4xqTrOONzzYm4YyrtbO8ZzhtDBaiAAne1hmnZHo9WFe2IH</P><Q>632QsG6xuJiEvLT5KpWajLnvuPzsoCxeWCXfCmfgjeXLcp81RqeYIBKPnT8JUWpBkKcg0Y3MniKUZvRW7J7usmaB79fdZerFHGyDdTjuB6yfN29qt5iM7z6yBVAZ/L/LHXDs7erieLAfKpkocXLovadsZwpzEzyWTo/8Ng4Q6LB5ibdELycuJS7UCC0OGXbyE6cyY7w7aXrS7hWIlKNCE1QHqyy33DKGFiAPKcZOldaQTtQi72grPUpKVyIMmZ5X</Q><DP>YbSemEfhWFpQenFXxsduE8qUlI2OJMtXJFjDdZWLlwuPLXnKP86OkZXE1hb9pyM8TO8m21ZvcOYxoM0pYScs5Bc/QqCsge1yUz6g2L4gbV3DdlTq4oCus8eZCbRwxzurA47IGnFSLEZu2p1vHEdJpIPM0AS9D8jzdZbLwRRHKFf/K4caPJlVc4LV8UnIIo3DteB7MFpKISVVNo40Dwu37NZpkDdiA7nhG36HHYdaLqGcN7Pq6nlUfPRLeyMot8TT</DP><DQ>1CF8YTKV7Y+WotbzTSUO343vIFjWX0MYiZcz4A/K/IfwSQSZ3VfDdcDClgfewPRSs5eZvKtbk2fa/xND6ksRAqvMiN2PlutxPDWUBdiOs2MQ2cMIAvLsoed8PcT2Y/PxaEf9ZiScddNRf/IPuIKmnZFf2UJUfxOo4oRZ0efB25p9IU1xonhM3Tndzq+nb9DyKDoI7kb1zgLSV54WNq8KKpcbgVr7FOR+XRfsENmOmPN7c0ctYTQZH1D4vpzxdMBp</DQ><InverseQ>5JYukyhef7MBu17pNTTXX7R1xWrydXcr205HC6j2HDDKYNw7aoZJElUstKUwqiKhjR5O4UgR4jHAd3rx1z9sJ+FADCFpTfwki/DdcFOuoZEeY0fiJrP8UY3Q7UPjpI8dKiKNAB/b37+DMJQFz6X8Z6FjvxhMwzGkaABgp8UJhqingFDXiNd/yopQAPYlf9nVuR2QoCWBP88juNVb1pLd2wE9J5rZE12Dax0v3lgLJ8YEINYnsgkT4dweOSJoGSnG</InverseQ><D>P8lu2y+nyDqI2d7aNFKUdrCIwRSQmATxeUsrA9PuZzfM+CtOyYeaZKKpYcOPuo/IMQEjy84SzNNN8ptBOaqeTibE9bR+vdRe3Id1pVGWdF8KYj2oR/Z7eACNkd8OGLj17aKPqcaBJG8Q9Pa0y13PwMl2tz0sFMHWidS7FqgWp5Jf3e3YlpOwWguPCNEGdvFik1KrOzEEitPdI+vMmJkn2Ef24lUotkb7euAHuyMTFCAi7DEYwPaOf2lT0DFo5/o7tVpnrOAAm1/JlVhTfIbw5JjSTSBsGfeIlQOS6AiPcQji1iMT5hEkrMVaLXtduo7Dry46gy8n+uZBeDn0Za/cyDdyHQ5UwRFC1JY17RSHR0zI/ctvdkVjrfwHoPvxqxTNlAEIm1uCtN/eY0nhfmgPPiHVdUhIgfOkiCBRK3iUxMVB615/dQHVmiqZmEw2ecAwGBHxiCl2R36NxTeB3Dj3Caj0PaAFvQW1cm5qsipV2D/XmfSNcVEoqmklMLd/CA0d</D></RSAKeyValue>";
        public const string SigningKid = "09b4adf8bfa941dc8ce40a6d1127b6d9";
        public const string StrPubSignKey = "00D112411910C854DE14C1D463490BC3911C857E0DDBD037AB375F26F6ADB92DDADDA968D765E5C10ED2569161BB670CE39CFD97503DEA1B05E1AB26F3E1128688";
    }
}
