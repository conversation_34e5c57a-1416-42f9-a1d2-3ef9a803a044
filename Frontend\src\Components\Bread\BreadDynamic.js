import React, { useState, useEffect } from "react";
import { Breadcrumbs, Link, Typography, Grid } from "@mui/material";
import { useNavigate, useLocation, useMatches } from "react-router-dom";
import { ChevronRight as ChevronRightIcon } from "@mui/icons-material";
import { knownRoutes } from "../../Routes/routes";
import { useTheme } from '@emotion/react';

const Bread = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const pathnames = location.pathname.split("/").filter((x) => x);
  const [crumb, setCrumb] = useState([]);

  //useMatches: Returns the current route matches on the page
  const matches = useMatches();
  const crumbs = matches.map((match) => (match ? match : null));

  //Filter child routes
  const route = knownRoutes.filter((r) => r.children);
  const routeChildren = [];

  //Verify children that have "handle"
  for (const h of route) {
    const val = h.children.filter((x) => x.handle);
    for (const finalVal of val) routeChildren.push(finalVal);
  }

  const findParents = (child, routeList) => {
    let current = child.parent;
    const parent = routeChildren.find(r => r.path === current);
    while (current) {
      if (parent) {
        routeList.push(parent);
        current = parent.parent;
      } else {
        current = null;
      }
    }
  };

  const getRouteList = () => {
    const routeList = [];

    routeChildren.forEach(child => {
      if (child?.handle?.crumb === crumbs[0]?.handle?.crumb &&
        child?.handle?.additionalContent === crumbs[0]?.handle?.additionalContent) {
        routeList.push(child);
        if (child.parent) {
          findParents(child, routeList);
        }
      }
    });

    return routeList;
  };

  useEffect(() => {
    const getList = () => {
      const routeList = getRouteList();
      const length = location.pathname.length > 1;
      setCrumb(length ? routeList.sort((a, b) => a.index - b.index) : []);
    };

    getList();
  }, [location.pathname]);

  return (
    <Grid
      container
      spacing={2}
      pl={4}
    >
      <Grid
        item
        xs={12}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
          }}
        >
          <div>
            {crumb &&
              crumb.map((name, index) => {
                const last = index === crumb.length - 1;
                return (
                  last && (
                    <Typography
                      color="textPrimary"
                      variant="h1"
                      key={"bread_" + index}
                    >
                      <b>{name?.handle?.crumb}</b>
                    </Typography>
                  )
                );
              })}
            <Breadcrumbs separator={<ChevronRightIcon fontSize="small" />}>
              {location.pathname !== "/" && (
                <Link
                  color="textSecondary"
                  underline="none"
                  onClick={() => navigate("/")}
                  sx={{ cursor: "pointer" }}
                >
                  Home
                </Link>
              )}
              {crumb &&
                crumb.map((value, index) => {
                  const last = index === crumb.length - 1;
                  const to = `/${pathnames.slice(0, index + 2).join("/")}`;

                  return last ? (
                    <Typography
                      key={to}
                      color="textSecondary"
                      variant="subtitle2"
                    >
                      {value?.handle?.crumb}
                    </Typography>
                  ) : (
                    <Link
                      key={to}
                      color="textPrimary"
                      underline="none"
                      sx={{ cursor: "pointer" }}
                      href={to}
                    >
                      {value?.handle?.crumb}
                    </Link>
                  );
                })}
            </Breadcrumbs>
          </div>
          {crumbs[0]?.handle?.additionalContent &&
            crumb.map((value, index) => {
              return <div
                key={index}
                style={{
                  marginLeft: theme.spacing(2),
                  marginTop: "auto"
                }}
              >
                {value?.handle?.additionalContent}
              </div>
            })}
        </div>
      </Grid>
    </Grid>
  );
};
export default Bread;
