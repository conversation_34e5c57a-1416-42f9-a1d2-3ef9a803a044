﻿using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Scripts;
using Microsoft.Extensions.Options;
using RnD.BackEnd.Domain.DataModels;
using RnD.BackEnd.Domain.Settings.Cosmos;
using RnD.BackEnd.Infrastructure.Interfaces;

namespace RnD.BackEnd.Infrastructure.Repositories.Cosmos.Base
{
    /// <summary>
    /// Cosmos DB operations class
    /// </summary>
    public abstract class CosmosOperations
    {
        protected readonly Container _container;

        /// <summary>
        /// Initializes a new instance of the <see cref="CosmosOperations"/> class.
        /// </summary>
        /// <param name="cosmosDbManager">The cosmos database manager.</param>
        /// <param name="cosmosSettings">The cosmos settings.</param>
        protected CosmosOperations(ICosmosDbManager cosmosDbManager, IOptions<CosmosSettings> cosmosSettings)
        {
            _container = cosmosDbManager.GetContainer(cosmosSettings.Value?.DatabaseName, ContainerName);
        }

        /// <summary>
        /// Name of the container.
        /// </summary>
        public abstract string ContainerName { get; }

        /// <summary>
        /// Generates the identifier.
        /// </summary>
        /// <param name="entity">The entity.</param>
        /// <returns>The identifier</returns>
        public abstract string GenerateId(object model);

        /// <summary>
        /// Resolves the partition key.
        /// </summary>
        /// <param name="partitionKey">The partition key.</param>
        /// <returns>
        /// The partition key.
        /// </returns>
        public abstract PartitionKey ResolvePartitionKey(string partitionKey);

        #region CRUD

        /// <summary>
        /// Adds the item.
        /// </summary>
        /// <param name="item">The item.</param>
        /// <param name="itemId">The item identifier.</param>
        /// <returns>
        /// The added item.
        /// </returns>
        public async Task<ResponseModel<T>> AddItemAsync<T>(T item, string itemId)
        {
            ResponseModel<T> result = new ResponseModel<T>();

            ItemResponse<T> response = await _container.CreateItemAsync(item, ResolvePartitionKey(itemId));

            result.Code = response.StatusCode;
            if (response.StatusCode == HttpStatusCode.Created)
            {
                result.Value = response.Resource;
            }

            return result;
        }

        /// <summary>
        /// Deletes the item.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// True if operation succeeded. False otherwise.
        /// </returns>
        public async Task<bool> DeleteItemAsync<T>(string id)
        {
            ItemResponse<T> response = await _container.DeleteItemAsync<T>(id, ResolvePartitionKey(id));
            return response.StatusCode == HttpStatusCode.NoContent;
        }

        /// <summary>
        /// Updates the item asynchronous.
        /// </summary>
        /// <param name="item">The item.</param>
        /// <param name="itemId">The item identifier.</param>
        /// <param name="etag">The etag.</param>
        /// <returns>
        /// The updated item.
        /// </returns>
        public async Task<ResponseModel<T>> UpdateItemAsync<T>(T item, string itemId, string etag = default)
        {
            ResponseModel<T> result = new ResponseModel<T>();

            try
            {
                ItemResponse<T> response = await _container.UpsertItemAsync(item, ResolvePartitionKey(itemId), etag is null ? null : new ItemRequestOptions { IfMatchEtag = etag });

                result.Code = response.StatusCode;
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    result.Value = response.Resource;
                }
            }
            catch (CosmosException ex)
            {
                result.Code = ex.StatusCode;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Gets the item.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>
        /// The item.
        /// </returns>
        public async Task<ResponseModel<T>> GetItemAsync<T>(string id)
        {
            ResponseModel<T> result = new ResponseModel<T>();

            try
            {
                ItemResponse<T> response = await _container.ReadItemAsync<T>(id, ResolvePartitionKey(id));
                result.Value = response.Resource;
            }
            catch (CosmosException ex)
            {
                result.Code = ex.StatusCode;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Gets the items asynchronous.
        /// </summary>
        /// <param name="queryString">The query string.</param>
        /// <param name="parameters">The parameters.</param>
        /// <param name="pageSize">Size of the page.</param>
        /// <param name="continuationToken">The continuation token.</param>
        /// <returns>
        /// The list of items.
        /// </returns>
        public async Task<ResponseModel<List<T>>> GetItemsAsync<T>(string queryString, Dictionary<string, object> parameters = null, int? pageSize = default, string continuationToken = default, CancellationToken cancellationToken = default)
        {
            ResponseModel<List<T>> result = new ResponseModel<List<T>>();

            List<T> list = new List<T>();

            QueryDefinition query = new QueryDefinition(queryString);
            AddParameters(ref query, parameters);

            string decodedContinuationToken = !string.IsNullOrEmpty(continuationToken)
                ? Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(continuationToken))
                : null;

            using (FeedIterator<T> resultSetIterator = _container.GetItemQueryIterator<T>(
                query,
                decodedContinuationToken,
                new QueryRequestOptions
                {
                    MaxItemCount = pageSize ?? -1
                }))
            {
                while (resultSetIterator.HasMoreResults)
                {
                    FeedResponse<T> response = await resultSetIterator.ReadNextAsync(cancellationToken);

                    result.Code = response.StatusCode;
                    if (response.StatusCode != HttpStatusCode.OK)
                    {
                        break;
                    }

                    list.AddRange(response.ToList());

                    if (pageSize.HasValue)
                    {
                        result.ContinuationToken = !string.IsNullOrWhiteSpace(response.ContinuationToken)
                            ? WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(response.ContinuationToken))
                            : null;
                        break;
                    }
                }
            }

            result.Value = list;
            return result;
        }

        #endregion CRUD

        #region Transactional batches

        /// <summary>
        /// Creates the transational batch.
        /// </summary>
        /// <param name="partitionKey">The partition key.</param>
        /// <returns>
        /// The transactional batch.
        /// </returns>
        public TransactionalBatch CreateTransationalBatch(string partitionKey)
        {
            return _container.CreateTransactionalBatch(ResolvePartitionKey(partitionKey));
        }

        /// <summary>
        /// Executes the batch.
        /// </summary>
        /// <param name="batch">The batch.</param>
        /// <returns>
        /// The result of the execution of the transactional batch.
        /// </returns>
        public static async Task<ResponseModel<List<T>>> ExecuteBatch<T>(TransactionalBatch batch)
        {
            ResponseModel<List<T>> output = new ResponseModel<List<T>>()
            {
                Value = new List<T>()
            };

            using (TransactionalBatchResponse batchResponse = await batch.ExecuteAsync())
            {
                output.Code = batchResponse.StatusCode;

                if (batchResponse.IsSuccessStatusCode)
                {
                    for (var i = 0; i < batchResponse.Count; i++)
                    {
                        TransactionalBatchOperationResult<T> result = batchResponse.GetOperationResultAtIndex<T>(i);
                        output.Value.Add(result.Resource);
                    }
                }
                else
                {
                    output.ErrorMessage = batchResponse.ErrorMessage;
                    output.ErrorMessages = new List<string>();

                    for (int i = 0; i < batchResponse.Count; i++)
                    {
                        TransactionalBatchOperationResult result = batchResponse.GetOperationResultAtIndex<dynamic>(i);
                        output.ErrorMessages.Add($"Document {i + 1}: {result.StatusCode}");
                    }
                }
            }

            return output;
        }

        #endregion

        #region Stored procedures

        /// <summary>
        /// Executes the stored procedure asynchronous.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure">The stored procedure.</param>
        /// <param name="id">The identifier.</param>
        /// <param name="parameters">The parameters.</param>
        /// <returns>
        /// The stored procedure response.
        /// </returns>
        public async Task<ResponseModel<T>> ExecuteStoredProcedureAsync<T>(string storedProcedure, string id, dynamic[] parameters = null)
        {
            ResponseModel<T> result = new ResponseModel<T>();

            StoredProcedureExecuteResponse<T> response = await _container.Scripts.ExecuteStoredProcedureAsync<T>(
                storedProcedure,
                ResolvePartitionKey(id),
                parameters,
                new StoredProcedureRequestOptions { EnableScriptLogging = true });

            result.Code = response.StatusCode;
            if (response.StatusCode == HttpStatusCode.OK)
            {
                result.Value = response.Resource;
            }

            return result;
        }

        #endregion

        #region Private methods

        /// <summary>
        /// Adds the parameters to the input variable "query" and return's it by.
        /// </summary>
        /// <param name="query">The ref to the query object in which the parameters will be added.</param>
        /// <param name="parameters">The parameters.</param>
        private static void AddParameters(ref QueryDefinition query, Dictionary<string, object> parameters)
        {
            if (parameters != null)
            {
                foreach (string key in parameters.Keys)
                {
                    query = query.WithParameter(key, parameters[key]);
                }
            }
        }

        #endregion

        #region Query building utilities

        /// <summary>
        /// Adds the condition to a Cosmos query.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query">The query.</param>
        /// <param name="parameters">The parameters.</param>
        /// <param name="filterValues">The filter values.</param>
        /// <param name="property">The property.</param>
        /// <param name="parameterCount">The parameter count.</param>
        /// <param name="applyANDOperator">if set to <c>true</c> [apply and operator].</param>
        protected static void AddCondition<T>(StringBuilder query, Dictionary<string, object> parameters, IEnumerable<T> filterValues, string property, int parameterCount, bool applyANDOperator)
        {
            if (filterValues?.Any() ?? false)
            {
                List<KeyValuePair<string, object>> queryParameters = new List<KeyValuePair<string, object>>();
                foreach (T item in filterValues)
                {
                    KeyValuePair<string, object> queryParameter = new KeyValuePair<string, object>($"@param{parameterCount++}", item);
                    queryParameters.Add(queryParameter);
                    parameters.Add(queryParameter.Key, queryParameter.Value);
                }

                string values = string.Join(",", queryParameters.Select(x => x.Key));
                string queryOperator = applyANDOperator ? "AND" : "OR";
                query.Append($" {queryOperator} c[\"{property}\"] IN ({values}) ");
            }
        }

        /// <summary>
        /// Adds the query filters.
        /// </summary>
        /// <param name="query">The query.</param>
        /// <param name="parameters">The parameters.</param>
        /// <param name="search">The search.</param>
        /// <param name="fieldsToSearch">The fields to search.</param>
        /// <param name="applyANDOperator">if set to <c>true</c> [apply and operator].</param>
        protected static void AddQueryFilters(StringBuilder query, Dictionary<string, object> parameters, string search, List<string> fieldsToSearch, bool applyANDOperator)
        {
            string queryOperator = applyANDOperator ? "AND" : "OR";

            if (fieldsToSearch != null && fieldsToSearch.Count > 0 && !string.IsNullOrWhiteSpace(search))
            {
                for (int i = 0; i < fieldsToSearch.Count; i++)
                {
                    query.Append($" {queryOperator} CONTAINS(c.fields.{fieldsToSearch[i]}, @search, true)");
                }

                parameters.Add("@search", search);
            }
        }

        /// <summary>
        /// Adds the pagination and sorting.
        /// </summary>
        /// <param name="query">The query.</param>
        /// <param name="parameters">The parameters.</param>
        /// <param name="page">The page.</param>
        /// <param name="pageSize">Size of the page.</param>
        /// <param name="sortField">The sort field.</param>
        /// <param name="sortAscending">if set to <c>true</c> [sort ascending].</param>
        protected static void AddPaginationAndSorting(StringBuilder query, Dictionary<string, object> parameters, int? page, int? pageSize, string sortField, bool sortAscending = true)
        {
            // Query sorting
            if (!string.IsNullOrWhiteSpace(sortField))
            {
                query.Append(sortAscending ? $" ORDER BY c.{sortField} ASC" : $" ORDER BY c.{sortField} DESC");
            }

            // Query pagination
            if (page.HasValue && pageSize.HasValue)
            {
                query.Append(" OFFSET @page LIMIT @pageSize");
                parameters.Add("@page", page.Value * pageSize.Value);
                parameters.Add("@pageSize", pageSize);
            }
        }

        #endregion Query building utilities
    }
}
