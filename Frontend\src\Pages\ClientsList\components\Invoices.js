import React from "react";
import {
  Box,
  Grid,
  Typography,
  CircularProgress,
  Card,
  CardContent,
  Divider,
} from "@mui/material";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import CloudIcon from "@mui/icons-material/Cloud";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import ptBR from "date-fns/locale/pt-BR";
import TextField from "@mui/material/TextField";

const Invoices = ({
  pdfLoading,
  consumosPDF,
  formatCurrency,
  formatDate,
  dataInicioFatura,
  setDataInicioFatura,
  dataFimFatura,
  setDataFimFatura,
}) => {
  return (
    <Box sx={{ mt: 2 }}>
      {/* Filtros de período */}
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={12} md={6}>
          <LocalizationProvider
            dateAdapter={AdapterDateFns}
            adapterLocale={ptBR}
          >
            <DatePicker
              label="Data Inicial"
              value={dataInicioFatura}
              onChange={setDataInicioFatura}
              renderInput={(params) => <TextField {...params} fullWidth />}
            />
          </LocalizationProvider>
        </Grid>
        <Grid item xs={12} md={6}>
          <LocalizationProvider
            dateAdapter={AdapterDateFns}
            adapterLocale={ptBR}
          >
            <DatePicker
              label="Data Final"
              value={dataFimFatura}
              onChange={setDataFimFatura}
              renderInput={(params) => <TextField {...params} fullWidth />}
            />
          </LocalizationProvider>
        </Grid>
      </Grid>

      {pdfLoading ? (
        <Box sx={{ display: "flex", justifyContent: "center", my: 4 }}>
          <CircularProgress />
        </Box>
      ) : consumosPDF.length > 0 ? (
        <Grid container spacing={3}>
          {consumosPDF.map((pdf, index) => (
            <Grid item xs={12} key={pdf.id || index}>
              <Card
                elevation={2}
                sx={{
                  borderRadius: "12px",
                  transition: "all 0.3s ease",
                  background: "linear-gradient(to right, #ffffff, #f9fcff)",
                  position: "relative",
                  overflow: "hidden",
                  "&:hover": {
                    transform: "translateY(-3px)",
                    boxShadow: "0 8px 16px rgba(0, 105, 217, 0.15)",
                  },
                  "&::before": {
                    content: '""',
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "6px",
                    height: "100%",
                    backgroundColor: "#1976d2",
                    borderTopLeftRadius: "12px",
                    borderBottomLeftRadius: "12px",
                  },
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 1 }}
                      >
                        <Box
                          sx={{
                            backgroundColor: "rgba(25, 118, 210, 0.1)",
                            borderRadius: "50%",
                            p: 1,
                            mr: 1.5,
                            display: "flex",
                          }}
                        >
                          <CalendarTodayIcon
                            sx={{ color: "#1976d2", fontSize: 20 }}
                          />
                        </Box>
                        <Typography
                          variant="subtitle1"
                          fontWeight="bold"
                          color="#444"
                        >
                          Período
                        </Typography>
                      </Box>
                      <Typography
                        variant="body1"
                        sx={{ pl: 5, fontWeight: 500 }}
                      >
                        {formatDate(pdf.dataInicio)} - {formatDate(pdf.dataFim)}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 1 }}
                      >
                        <Box
                          sx={{
                            backgroundColor: "rgba(25, 118, 210, 0.1)",
                            borderRadius: "50%",
                            p: 1,
                            mr: 1.5,
                            display: "flex",
                          }}
                        >
                          <CloudIcon sx={{ color: "#1976d2", fontSize: 20 }} />
                        </Box>
                        <Typography
                          variant="subtitle1"
                          fontWeight="bold"
                          color="#444"
                        >
                          Serviço
                        </Typography>
                      </Box>
                      <Typography
                        variant="body1"
                        sx={{ pl: 5, fontWeight: 500 }}
                      >
                        {pdf.codigo}
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <Divider sx={{ my: 2, opacity: 0.6 }} />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Box
                        sx={{
                          backgroundColor: "rgba(76, 175, 80, 0.08)",
                          p: 2,
                          borderRadius: "8px",
                          height: "100%",
                        }}
                      >
                        <Typography
                          variant="subtitle2"
                          color="#2e7d32"
                          fontWeight="bold"
                          gutterBottom
                        >
                          Total Líquido
                        </Typography>
                        <Typography
                          variant="h6"
                          color="#2e7d32"
                          fontWeight="600"
                        >
                          {formatCurrency(pdf.total)}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box
                        sx={{
                          backgroundColor: "rgba(255, 152, 0, 0.08)",
                          p: 2,
                          borderRadius: "8px",
                          height: "100%",
                        }}
                      >
                        <Typography
                          variant="subtitle2"
                          color="#ed6c02"
                          fontWeight="bold"
                          gutterBottom
                        >
                          IVA
                        </Typography>
                        <Typography
                          variant="h6"
                          color="#ed6c02"
                          fontWeight="600"
                        >
                          {formatCurrency(pdf.iva)}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box
                        sx={{
                          backgroundColor: "rgba(25, 118, 210, 0.08)",
                          p: 2,
                          borderRadius: "8px",
                          height: "100%",
                          border: "1px dashed rgba(25, 118, 210, 0.4)",
                        }}
                      >
                        <Typography
                          variant="subtitle2"
                          color="#0069d9"
                          fontWeight="bold"
                          gutterBottom
                        >
                          Total Fatura
                        </Typography>
                        <Typography
                          variant="h5"
                          color="#0069d9"
                          fontWeight="600"
                        >
                          {formatCurrency(pdf.totalFatura)}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      ) : (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            py: 5,
            backgroundColor: "rgba(25, 118, 210, 0.04)",
            borderRadius: "12px",
            border: "1px dashed rgba(25, 118, 210, 0.3)",
          }}
        >
          <PictureAsPdfIcon
            sx={{ fontSize: 60, color: "rgba(25, 118, 210, 0.3)", mb: 2 }}
          />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            Nenhuma fatura encontrada
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ maxWidth: "400px", textAlign: "center" }}
          >
            Não existem faturas em PDF disponíveis para este cliente no momento.
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default Invoices;
