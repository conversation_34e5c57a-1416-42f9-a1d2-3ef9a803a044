﻿using System.Collections.Generic;
using RnD.BackEnd.Domain.BusinessMessages;
using RnD.BackEnd.Domain.Entities.Exception;
using RnD.BackEnd.Domain.Extensions;

namespace RnD.BackEnd.Domain.Validators.DynamicEntity
{
    /// <summary>
    /// Entity validations class
    /// </summary>
    public static class EntityValidations
    {
        /// <summary>
        /// Determines whether [is create request valid] [the specified entity].
        /// </summary>
        /// <param name="entity">The entity.</param>
        /// <param name="partitionKeyFieldName">Name of the partition key field.</param>
        /// <returns>
        /// ModelException object containing error messages in case they exist.
        /// </returns>
        public static ModelException IsCreateRequestValid(Dictionary<string, object> entity, string partitionKeyFieldName)
        {
            string partitionKey = entity.GetStringValue(partitionKeyFieldName);

            ModelException modelException = new ModelException()
            {
                Messages = new List<string>()
            };

            if (string.IsNullOrWhiteSpace(partitionKey))
            {
                modelException.Messages.Add(string.Format(CommonMessages.FieldMandatory, partitionKeyFieldName));
            }

            return modelException;
        }

        /// <summary>
        /// Determines whether [is update request valid] [the specified entity].
        /// </summary>
        /// <param name="entity">The entity.</param>
        /// <param name="entityIdFieldName">Name of the entity identifier field.</param>
        /// <returns>
        /// ModelException object containing error messages in case they exist.
        /// </returns>
        public static ModelException IsUpdateRequestValid(Dictionary<string, object> entity, string entityIdFieldName, string partitionKeyFieldName)
        {
            string entityId = entity.GetStringValue(entityIdFieldName);
            string partitionKey = entity.GetStringValue(partitionKeyFieldName);

            ModelException modelException = new ModelException()
            {
                Messages = new List<string>()
            };

            if (string.IsNullOrWhiteSpace(entityId))
            {
                modelException.Messages.Add(DynamicEntityMessages.Id_Mandatory);
            }

            if (string.IsNullOrWhiteSpace(partitionKey))
            {
                modelException.Messages.Add(string.Format(CommonMessages.FieldMandatory, partitionKeyFieldName));
            }

            return modelException;
        }

        /// <summary>
        /// Determines if the identifier is valid.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="partitionKeyFieldName">Name of the partition key field.</param>
        /// <param name="partitionKey">The partition key.</param>
        /// <returns>
        /// ModelException object containing error messages in case they exist.
        /// </returns>
        public static ModelException IsIdValid(string id)
        {
            ModelException modelException = new ModelException()
            {
                Messages = new List<string>()
            };

            if (string.IsNullOrWhiteSpace(id))
            {
                modelException.Messages.Add(DynamicEntityMessages.Id_Mandatory);
            }

            return modelException;
        }
    }
}
