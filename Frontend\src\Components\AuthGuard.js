import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import PropTypes from "prop-types";
import authService from '../Services/authService';

/**
 * Componente de guarda de autenticação para proteger rotas
 * Verifica se o usuário está autenticado e tem a permissão ID 2 (admin)
 */
const AuthGuard = ({ children }) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [hasToken, setHasToken] = useState(false);
  const [hasAdminPermission, setHasAdminPermission] = useState(false);
  const [authError, setAuthError] = useState(false);

  // Verificar se há token
  useEffect(() => {
    console.log('AuthGuard: Verificando token...');
    const token = authService.getAuthToken();
    setHasToken(!!token);

    if (!token) {
      console.log('AuthGuard: Nenhum token encontrado, redirecionando para login');
      setIsLoading(false);
    }
  }, []);

  // Verificar permissões se tiver token
  useEffect(() => {
    const verificarAcesso = async () => {
      if (!hasToken) return;

      try {
        console.log('AuthGuard: Verificando permissões...');
        const status = await authService.verificarStatusCompleto();

        console.log('AuthGuard: Status completo:', status);
        setHasAdminPermission(status.temPermissaoAdmin);
        setAuthError(!status.autenticado);

        // Logs para debug
        if (!status.autenticado) {
          console.error('AuthGuard: Usuário não autenticado');
        } else if (!status.temPermissaoAdmin) {
          console.error('AuthGuard: Usuário sem permissão de administrador');
        }
      } catch (error) {
        console.error('AuthGuard: Erro ao verificar acesso:', error);
        setAuthError(true);
      } finally {
        setIsLoading(false);
      }
    };

    verificarAcesso();
  }, [hasToken]);

  // Gerenciar redirecionamento baseado no estado de verificação
  if (isLoading) {
    console.log('AuthGuard: Carregando...');
    return <div>Carregando...</div>;
  }

  if (!hasToken) {
    console.log('AuthGuard: Redirecionando para login por falta de token');
    navigate('/login');
    return null;
  }

  if (authError) {
    console.log('AuthGuard: Redirecionando para página de erro por falha na autenticação');
    navigate('/error/401');
    return null;
  }

  if (!hasAdminPermission) {
    console.log('AuthGuard: Redirecionando para página de erro por falta de permissão admin');
    navigate('/error/401');
    return null;
  }

  console.log('AuthGuard: Acesso permitido ao conteúdo protegido');
  return children;
};

AuthGuard.propTypes = {
  children: PropTypes.node,
};

export default AuthGuard;
