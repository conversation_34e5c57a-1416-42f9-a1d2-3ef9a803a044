import handleMethod from '../Utils/APIutils/handleMethod';

class GoalsAPI {
  async GetEmployeeGoalsDetails(username, evaluationMomentID, isCurrentUser = false) {
    const Input = {
      type: 'GET',
      method: username ? `getEmployeesGoalsDetails?Username=${username}&isCurrentUser=${isCurrentUser}&EvaluationMomentID=${evaluationMomentID}` :
        `getEmployeesGoalsDetails?isCurrentUser=${isCurrentUser}&EvaluationMomentID=${evaluationMomentID}`,
      params: {}
    };

    const response = await handleMethod(Input);

    return response;
  }

  async GetGoalTypes(category) {
    const Input = {
      type: 'GET',
      method: `getGoalTypes?category=${category}`,
      params: {}
    };

    const response = await handleMethod(Input);
    return response;
  }
}
export const goalsAPI = new GoalsAPI();
