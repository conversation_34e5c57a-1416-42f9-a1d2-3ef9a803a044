/* eslint-disable import/no-unresolved, react/no-danger */
import React from 'react';
import PropTypes from 'prop-types';
import PopupState,
{
  bindTrigger,
  bindPopover
} from 'material-ui-popup-state';
import CustomFilterSelectPopover from './CustomFilterSelectPopover';
import {
  Box,
  Chip,
  Divider,
  Collapse,
  Tooltip
} from '@mui/material';
import clsx from 'clsx';
import {
  FilterAlt as FilterAltIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { styled } from "@mui/system";

const CustomChip = styled(Chip)(({ theme }) => ({
  border: `solid 1px ${theme.palette.primary.main} !important`,
  "&:not(.chipButton)": {
    backgroundColor: `${theme.palette.primary.main}15 !important`,
  },
  "& .MuiChip-label": {
    "& span": {
      display: "inline-block",
      verticalAlign: "middle",
      marginRight: "5px",
      "&:nth-of-type(1),&:nth-of-type(2), & b": {
        color: `${theme.palette.primary.main} !important`
      },
      "&:not(:first-of-type)": {
        overflow: "hidden",
        textOverflow: "ellipsis",
        display: "inline-block"
      }
    },
  },
  "& .MuiSvgIcon-root": {
    color: `${theme.palette.primary.main} !important`
  },
  "&.chipButton": {
    "& .MuiChip-label": {
      "& span": {
        "&:after": {
          content: 'initial'
        }
      }
    }
  }
}));

const CustomFilters = ({
  oid,
  customFiltersToggled,
  customFiltersList,
  selectedCustomFilters,
  addCustomFilter,
  deleteCustomFilter,
  editCustomFilter,
  resetCustomFilters,
  context,
}) => {
  const { t } = useTranslation();

  return (
    <Box>
      <Collapse in={customFiltersToggled}>
        <Divider
          sx={theme => ({
            borderColor: theme.palette.primary.main
          })}
        />
        <Box
          sx={{
            p: 0.5
          }}
        >
          <PopupState
            variant="popover"
            popupId="popup-popover"
          >
            {(popupState) => (
              <>
                <CustomChip
                  className={clsx('chipButton')}
                  variant="outlined"
                  sx={{
                    background: "white",
                    m: 0.5
                  }}
                  icon={<FilterAltIcon fontSize="small" />}
                  onDelete={resetCustomFilters}
                  label={
                    <span>
                      {t('common:common.add_filters')}
                    </span>
                  }
                  {...bindTrigger(popupState)}
                />
                <CustomFilterSelectPopover
                  oid={oid}
                  context={context}
                  customFiltersList={customFiltersList}
                  addCustomFilter={addCustomFilter}
                  deleteCustomFilter={deleteCustomFilter}
                  resetCustomFilters={resetCustomFilters}
                  {...bindPopover(popupState)}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                  }}
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                  }}
                />
              </>
            )}
          </PopupState>
          <Box
            sx={{
              display: "inline",
              px: 1
            }}
          >
            {
              selectedCustomFilters?.length > 0 && selectedCustomFilters.map((customFilter, customFilter_index) => (
                <PopupState
                  variant="popover"
                  popupId="popup-popover"
                  key={`popupState_${customFilter_index}`}
                >
                  {(popupState) => (
                    <>
                      <CustomChip
                        className={clsx('chipButton')}
                        key={`chip_${customFilter_index}`}
                        variant="outlined"
                        sx={{
                          background: "white",
                          m: 0.5
                        }}
                        onDelete={() => deleteCustomFilter(customFilter_index)}
                        label={
                          <>
                            <span>
                              {customFilter?.elementLabel || "N/D"}
                            </span>
                            <span>
                              <b>
                                {customFilter.conditionLabel ? customFilter.conditionLabel : ":"}
                              </b>
                            </span>
                            {
                              customFilter?.selectedValues?.length > 0 ?
                                Array.isArray(customFilter.selectedValues) ?
                                  customFilter.selectedValues[0].elementLabel ?
                                    <Tooltip
                                      title={customFilter.selectedValues.map(x => x.elementLabel).join(', ')}
                                    >
                                      <span>
                                        {customFilter.selectedValues.map(x => x.elementLabel).join(', ')}
                                      </span>
                                    </Tooltip>
                                    :
                                    <Tooltip
                                      title={customFilter.selectedValues.join(', ')}
                                    >
                                      <span>
                                        {customFilter.selectedValues.join(', ')}
                                      </span>
                                    </Tooltip>
                                  :
                                  <Tooltip
                                    title={customFilter.selectedValues.replace('<b>', '').replace('</b>', '')}
                                  >
                                    <span dangerouslySetInnerHTML={{ __html: customFilter.selectedValues }} />
                                  </Tooltip>
                                :
                                "N/D"
                            }
                          </>
                        }
                        {...bindTrigger(popupState)}
                      />
                      <CustomFilterSelectPopover
                        oid={oid}
                        context={context}
                        customFiltersList={customFiltersList}
                        customFilterIndex={customFilter_index}
                        addCustomFilter={addCustomFilter}
                        deleteCustomFilter={deleteCustomFilter}
                        resetCustomFilters={resetCustomFilters}
                        editCustomFilter={editCustomFilter}
                        customFilter={customFilter}
                        popupState={popupState.isOpen}
                        {...bindPopover(popupState)}
                        anchorOrigin={{
                          vertical: 'bottom',
                          horizontal: 'center',
                        }}
                        transformOrigin={{
                          vertical: 'top',
                          horizontal: 'center',
                        }}
                      />
                    </>
                  )}
                </PopupState>
              ))
            }
          </Box>
        </Box>
        <Divider
          sx={theme => ({
            borderColor: theme.palette.primary.main
          })}
        />
      </Collapse>
    </Box>
  );
};

CustomFilters.propTypes = {
  oid: PropTypes.string,
  customFiltersToggled: PropTypes.bool,
  customFiltersList: PropTypes.array,
  selectedCustomFilters: PropTypes.array,
  resetCustomFilters: PropTypes.func,
  addCustomFilter: PropTypes.func,
  deleteCustomFilter: PropTypes.func,
  editCustomFilter: PropTypes.func,
  context: PropTypes.string
};

export default CustomFilters;
