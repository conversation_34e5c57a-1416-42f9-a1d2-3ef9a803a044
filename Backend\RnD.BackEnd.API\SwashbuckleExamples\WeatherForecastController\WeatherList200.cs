﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.API.Model.Weather;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController
{
    /// <summary>
    /// The example of a 200 response when listing vehicles.
    /// </summary>
    public class WeatherList200 : IExamplesProvider<ApiOutput<List<WeatherDto>>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 for a list response.
        /// </returns>
        public ApiOutput<List<WeatherDto>> GetExamples()
        {
            return new ApiOutput<List<WeatherDto>>
            {
                Code = HttpStatusCode.OK,
                Value = new List<WeatherDto>
                {
                    new WeatherDto
                    {
                        Id = Guid.NewGuid(),
                        Temperature = 20,
                        Summary = null,
                        Location = "Lisboa Transaction"
                    },
                    new WeatherDto
                    {
                        Id = Guid.NewGuid(),
                        Temperature = 15,
                        Summary = null,
                        Location = "Porto Transaction"
                    }
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}