export const formatCurrency = (value, currency = 'EUR') => {
    if (value == null || value === '') return 'N/A';

    const formatter = new Intl.NumberFormat('pt-PT', {
        style: 'currency',
        currency: currency
    });

    return formatter.format(value);
};

export const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        if (Number.isNaN(date.getTime())) return 'N/A';

        return date.toLocaleDateString('pt-PT', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    } catch (error) {
        return 'N/A';
    }
};
