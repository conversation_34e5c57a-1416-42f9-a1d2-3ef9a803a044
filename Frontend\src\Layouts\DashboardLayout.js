import React, { useState } from 'react';
import { useTheme } from '@emotion/react';
import DashboardNavbar from './Header/DashboardNavbar';
import DashboardSidebar from './Sidebar/DashboardSidebar';
import Viewport from "./Viewport/viewport";
import PropTypes from 'prop-types';
import { Box } from '@mui/material';
import { useNavigate, Outlet } from 'react-router';

const DashboardLayoutRoot = ({ theme, children }) => {
  return <Box
    sx={{
      backgroundColor: theme.palette.common.paper,
      display: 'flex',
      height: '100%',
      overflow: 'hidden',
      width: '100%',
      flexDirection: 'column'
    }}
  >
    {children}
  </Box>
};
DashboardLayoutRoot.propTypes = {
  theme: PropTypes.object,
  children: PropTypes.array
}

const DashboardLayoutWrapper = ({ theme, issidebareopen, children }) => {
  return <Box
    sx={{
      display: 'flex',
      flex: '1 1 auto',
      overflow: 'hidden',
      justifyContent: "center",
      alignItems: "center",
      transition: "padding-left 225ms cubic-bezier(0, 0, 0.2, 1)",
      [theme.breakpoints.up('sm')]: {
        paddingLeft: issidebareopen === "true" ? '256px' : 'initial'
      }
    }}
  >
    {children}
  </Box>
};
DashboardLayoutWrapper.propTypes = {
  theme: PropTypes.object,
  issidebareopen: PropTypes.string,
  children: PropTypes.node
}

const DashboardLayoutContainer = ({ children }) => {
  return <Box
    sx={{
      display: 'flex',
      flex: '1 1 auto',
      overflow: 'hidden'
    }}
  >
    {children}
  </Box>
};
DashboardLayoutContainer.propTypes = {
  children: PropTypes.node
}

const DashboardLayoutContent = ({ children }) => {
  return <Box
    sx={{
      flex: '1 1 auto',
      paddingTop: "64px",
      height: '100%',
      overflow: 'auto',
      position: 'relative',
      WebkitOverflowScrolling: 'touch'
    }}
  >
    {children}
  </Box>
};
DashboardLayoutContent.propTypes = {
  children: PropTypes.node
}

const DashboardLayout = ({ children, redirectTo }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const theme = useTheme();
  const handleToggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  }
  const navigate = useNavigate();
  return (
    <DashboardLayoutRoot theme={theme}>
      <DashboardSidebar
        onCloseSidebar={handleToggleSidebar}
        openSidebar={isSidebarOpen}
      />
      <DashboardNavbar onSidebarOpen={handleToggleSidebar} />
      <DashboardLayoutWrapper
        theme={theme}
        issidebareopen={isSidebarOpen.toString()}
      >
        <DashboardLayoutContainer>
          <DashboardLayoutContent>
            <Viewport>
              {
                redirectTo ? navigate(redirectTo) : children || <Outlet />
              }
            </Viewport>
          </DashboardLayoutContent>
        </DashboardLayoutContainer>
      </DashboardLayoutWrapper>
    </DashboardLayoutRoot>
  );
};

DashboardLayout.propTypes = {
  children: PropTypes.node,
  redirectTo: PropTypes.string
}

export default DashboardLayout;
