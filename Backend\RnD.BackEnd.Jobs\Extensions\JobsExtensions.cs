﻿using Hangfire;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RnD.BackEnd.Jobs.LicenseValidation;

namespace RnD.BackEnd.Jobs.Extensions
{
    /// <summary>
    /// Jobs extensions class
    /// </summary>
    public static class JobsExtensions
    {
        /// <summary>
        /// Launches the recurring jobs.
        /// </summary>
        /// <param name="app">The application.</param>
        public static void ConfigureRecurringJobs(this WebApplication app)
        {
            IRecurringJobManager recurringJobManager = app.Services.GetService<IRecurringJobManager>();
            IConfiguration configuration = app.Configuration;

            bool launchJobs = bool.Parse(configuration.GetSection("JobsSettings")["launchJobs"]);
            bool launchWorkflowJobs = bool.Parse(configuration.GetSection("JobsSettings")["launchWorkflowJobs"]);

            // Recurring jobs in JobsService
            if (launchJobs)
            {
                string monthlyInterval = configuration.GetSection("JobsSettings")["monthlyInterval"];
                recurringJobManager.AddOrUpdate<IJobRunner>("OutputWeather", service => service.OutputWeatherForecast(), monthlyInterval);

                // Recurring jobs in WorkflowService
                if (launchWorkflowJobs)
                {
                    string checkOverdueTasksInterval = configuration.GetSection("JobsSettings")["checkOverdueTasksInterval"];
                    string processBackgroundWorkflowsInterval = configuration.GetSection("JobsSettings")["processBackgroundWorkflowsInterval"];

                    recurringJobManager.AddOrUpdate<IJobRunner>("Workflows.CheckOverdueTasks", service => service.CheckOverdueTasks(), checkOverdueTasksInterval);
                    recurringJobManager.AddOrUpdate<IJobRunner>("Workflows.CompleteBackgroundWorkflows", service => service.CompleteBackgroundWorkflows(), processBackgroundWorkflowsInterval);
                }
                else
                {
                    recurringJobManager.RemoveIfExists("Workflows.CheckOverdueTasks");
                    recurringJobManager.RemoveIfExists("Workflows.CompleteBackgroundWorkflows");
                }
            }
            else
            {
                recurringJobManager.RemoveIfExists("OutputWeather");
                recurringJobManager.RemoveIfExists("Workflows.CheckOverdueTasks");
                recurringJobManager.RemoveIfExists("Workflows.CompleteBackgroundWorkflows");
            }
        }
    }
}
