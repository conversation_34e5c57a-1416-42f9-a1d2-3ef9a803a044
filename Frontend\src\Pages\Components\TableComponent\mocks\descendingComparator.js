const getDescendingComparator = () => {
    return `
    const descendingComparator = (a, b, orderBy) => {
        const _a = { ...a };
        const _b = { ...b };
        if (!Number.isInteger(_a[orderBy]) && moment(_a[orderBy], 'DD/MM/YYYY', false).isValid() && !Number.isInteger(_b[orderBy]) && moment(_b[orderBy], 'DD/MM/YYYY', false).isValid()) {
            _a[orderBy] = moment(_a[orderBy], 'DD/MM/YYYY').format('YYYYMMDD');
            _b[orderBy] = moment(_b[orderBy], 'DD/MM/YYYY').format('YYYYMMDD');
        }
        if ((Number.isInteger(_a[orderBy]) && _b[orderBy] < _a[orderBy]) || (!Number.isInteger(_a[orderBy]) && nameNormalize(_b[orderBy]) < nameNormalize(_a[orderBy]))) {
            return -1;
        }
        if ((Number.isInteger(_a[orderBy]) && _b[orderBy] > _a[orderBy]) || (!Number.isInteger(_a[orderBy]) && nameNormalize(_b[orderBy]) > nameNormalize(_a[orderBy]))) {
            return 1;
        }
        return 0;
    };
    `;
}

export default getDescendingComparator;
