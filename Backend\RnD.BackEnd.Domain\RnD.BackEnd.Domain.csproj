﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.13.2" />
    <PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Graph" Version="5.75.0" />
    <PackageReference Include="Microsoft.Graph.Auth" Version="1.0.0-preview.7" />
    <PackageReference Include="Microsoft.Graph.Core" Version="3.2.4" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.70.0" />
    <PackageReference Include="Microsoft.Kiota.Authentication.Azure" Version="1.17.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

</Project>
