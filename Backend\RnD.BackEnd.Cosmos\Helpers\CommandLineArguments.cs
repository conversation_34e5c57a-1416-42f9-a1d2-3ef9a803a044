﻿using CommandLine;

namespace RnD.BackEnd.Cosmos.Helpers
{
    public class CommandLineArguments
    {
        private int? _minThroughputAutoScale;
        private int? _maxThroughputAutoScale;
        private int? _minThroughputManualScale;

        [Option('d', "database", Required = true, HelpText = "Cosmos Database name.")]
        public string DatabaseName { get; set; }

        [Option('e', "endpoint", Required = true, HelpText = "Cosmos Endpoint URI.")]
        public string EndpointUri { get; set; }

        [Option('k', "key", Required = true, HelpText = "Cosmos Primary Key.")]
        public string PrimaryKey { get; set; }

        [Option('a', "minAutoScale", HelpText = "Optional. Minimum throughput for auto scale. Default value: 1000.")]
        public int? MinThroughputAutoScale
        {
            get
            {
                return _minThroughputAutoScale.HasValue ? _minThroughputAutoScale.Value : 1000;
            }
            set
            {
                _minThroughputAutoScale = value;
            }
        }

        [Option('s', "maxAutoScale", HelpText = "Optional. Maximum throughput for auto scale. Default value: 1000000.")]
        public int? MaxThroughputAutoScale
        {
            get
            {
                return _maxThroughputAutoScale.HasValue ? _maxThroughputAutoScale.Value : 1000000;
            }
            set
            {
                _maxThroughputAutoScale = value;
            }
        }

        [Option('m', "minManualScale", HelpText = "Optional. Minimum throughput for manual scale. Default value: 400.")]
        public int? MinThroughputManualScale
        {
            get
            {
                return _minThroughputManualScale.HasValue ? _minThroughputManualScale.Value : 400;
            }
            set
            {
                _minThroughputManualScale = value;
            }
        }
    }
}
