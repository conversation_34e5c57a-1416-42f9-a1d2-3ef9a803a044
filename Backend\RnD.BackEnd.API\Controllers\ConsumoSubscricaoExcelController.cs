using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using RnD.BackEnd.API.Models;
using RnD.BackEnd.API.Services;

namespace RnD.BackEnd.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ConsumoSubscricaoExcelController : ControllerBase
    {
        private readonly IConsumoSubscricaoExcelService _excelService;

        public ConsumoSubscricaoExcelController(IConsumoSubscricaoExcelService excelService)
        {
            _excelService = excelService;
        }

        [HttpPost("processar/{fileName}")]
        public async Task<ActionResult<List<ConsumoSubscricaoExcelModel>>> ProcessarExcel(string fileName)
        {
            try
            {
                var consumos = await _excelService.ProcessExcelFile(fileName);
                return Ok(consumos);
            }
            catch (Exception ex)
            {
                return BadRequest($"Erro ao processar o arquivo Excel: {ex.Message}");
            }
        }
    }
} 