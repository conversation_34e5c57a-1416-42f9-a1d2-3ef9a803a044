import PropTypes from 'prop-types';
import { Grid } from '@mui/material';
import ProfileAbout from './ProfileAbout';
import ProfilePersonalDataForm from './ProfilePersonalDataForm';

const ProfilePersonalData = (props) => {
  const { profileData, optionsData, ...other } = props;

  return (
    <div {...other}>
      <Grid
        container
        spacing={3}
      >
        <Grid
          item
          lg={4}
          md={6}
          xs={12}
        >
          <ProfileAbout
            currentCity={profileData?.address2}
            currentJobTitle={profileData?.categoryDesc}
            no={profileData?.employeeNo}
            departmentTitle={profileData?.departmentDesc}
            email={profileData?.email}
            profileProgress={100}
            telephone={profileData?.contactNo}
          />
        </Grid>
        <Grid
          item
          lg={8}
          md={6}
          xs={12}
        >
          <ProfilePersonalDataForm
            options={optionsData}
          />
        </Grid>
      </Grid>
    </div>
  );
};
ProfilePersonalData.propTypes = {
  profileData: PropTypes.object.isRequired,
  optionsData: PropTypes.object,
};
export default ProfilePersonalData;
