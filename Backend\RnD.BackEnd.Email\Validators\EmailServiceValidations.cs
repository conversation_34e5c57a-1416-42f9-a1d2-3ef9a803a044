﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text.RegularExpressions;
using RnD.BackEnd.Email.BusinessMessages;
using RnD.BackEnd.Email.Entities;

namespace RnD.BackEnd.Email.Validators
{
    /// <summary>
    /// Email service validations class
    /// </summary>
    public static class EmailServiceValidations
    {
        /// <summary>
        /// Determines whether [is send grid email request valid] [the specified request].
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>
        /// A list of validation errors, in case they occur. Otherwise, an empty list.
        /// </returns>
        public static List<string> IsSendGridEmailRequestValid(EmailMessage request)
        {
            List<string> errors = new List<string>();

            if (request.Recipients == null || request.Recipients.Count == 0)
            {
                errors.Add(EmailMessages.Recipients_Required);
            }

            if (request.EmailData == null)
            {
                errors.Add(EmailMessages.Template_Data_Required);
            }

            if (string.IsNullOrWhiteSpace(request.TemplateId))
            {
                errors.Add(EmailMessages.Template_Id_Required);
            }

            if (string.IsNullOrWhiteSpace(request.FromEmailAddress))
            {
                errors.Add(EmailMessages.From_Email_Address_Required);
            }

            if (string.IsNullOrWhiteSpace(request.FromUserName))
            {
                errors.Add(EmailMessages.From_User_Name_Required);
            }

            return ValidateEmailAddresses(errors, request);
        }

        /// <summary>
        /// Determines whether [is valid email] [the specified email].
        /// </summary>
        /// <param name="email">The email.</param>
        /// <returns>
        ///   <c>true</c> if [is valid email] [the specified email]; otherwise, <c>false</c>.
        /// </returns>
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return false;
            }

            try
            {
                // Normalize the domain
                email = Regex.Replace(email, @"(@)(.+)$", DomainMapper, RegexOptions.None, TimeSpan.FromMilliseconds(200));

                // Examines the domain part of the email and normalizes it.
                string DomainMapper(Match match)
                {
                    // Use IdnMapping class to convert Unicode domain names.
                    IdnMapping idn = new IdnMapping();

                    // Pull out and process domain name (throws ArgumentException on invalid)
                    string domainName = idn.GetAscii(match.Groups[2].Value);

                    return match.Groups[1].Value + domainName;
                }
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }
            catch (ArgumentException)
            {
                return false;
            }

            try
            {
                return Regex.IsMatch(email,
                    @"^(?("")("".+?(?<!\\)""@)|(([0-9a-z]((\.(?!\.))|[-!#\$%&'\*\+/=\?\^`\{\}\|~\w])*)(?<=[0-9a-z])@))" +
                    @"(?(\[)(\[(\d{1,3}\.){3}\d{1,3}\])|(([0-9a-z][-0-9a-z]*[0-9a-z]*\.)+[a-z0-9][\-a-z0-9]{0,22}[a-z0-9]))$",
                    RegexOptions.IgnoreCase,
                    TimeSpan.FromMilliseconds(250));
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }
        }

        #region Private methods

        /// <summary>
        /// Validates the email addresses.
        /// </summary>
        /// <param name="errors">The errors.</param>
        /// <param name="request">The request.</param>
        /// <returns>
        /// The list of errors with new detected errors in case they exist.
        /// </returns>
        private static List<string> ValidateEmailAddresses(List<string> errors, EmailMessage request)
        {
            foreach (string recipient in request.Recipients)
            {
                if (!IsValidEmail(recipient))
                {
                    errors.Add(string.Format(EmailMessages.Invalid_Email, recipient));
                }
            }

            if (request.RecipientsCC != null && request.RecipientsCC.Count > 0)
            {
                foreach (string recipient in request.RecipientsCC)
                {
                    if (!IsValidEmail(recipient))
                    {
                        errors.Add(string.Format(EmailMessages.Invalid_Email, recipient));
                    }
                }
            }

            if (request.RecipientsBCC != null && request.RecipientsBCC.Count > 0)
            {
                foreach (string recipient in request.RecipientsBCC)
                {
                    if (!IsValidEmail(recipient))
                    {
                        errors.Add(string.Format(EmailMessages.Invalid_Email, recipient));
                    }
                }
            }

            return errors;
        }

        #endregion
    }
}
