﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace RnD.BackEnd.API.Models
{
    public class Consumo_Cliente
    {
        [Key]
        public int ID { get; set; }

        [Required]
        public int ClienteID { get; set; }

        [ForeignKey("ClienteID")]
        public Cliente Cliente { get; set; }

        [Required]
        public int ConsumoID { get; set; }

        [ForeignKey("ConsumoID")]
        public Consumo Consumo { get; set; }
    }
}
