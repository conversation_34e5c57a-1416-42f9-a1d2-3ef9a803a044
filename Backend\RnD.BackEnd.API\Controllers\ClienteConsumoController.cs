﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RnD.BackEnd.API.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RnD.BackEnd.API.Data;

namespace RnD.BackEnd.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class Consumo_ClienteController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public Consumo_ClienteController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Consumo_Cliente>>> GetConsumo_Cliente()
        {
            return await _context.Consumo_Cliente
                .Include(cc => cc.Cliente)
                .Include(cc => cc.Consumo)
                .ToListAsync();
        }

        [HttpPost]
        public async Task<ActionResult<Consumo_Cliente>> PostConsumo_Cliente(Consumo_Cliente Consumo_Cliente)
        {
            _context.Consumo_Cliente.Add(Consumo_Cliente);
            await _context.SaveChangesAsync();
            return CreatedAtAction(nameof(GetConsumo_Cliente), new { id = Consumo_Cliente.ID }, Consumo_Cliente);
        }

        [HttpGet("cliente/{clienteId}")]
        public async Task<ActionResult<IEnumerable<Consumo_Cliente>>> GetConsumosByClienteId(int clienteId)
        {
            var consumos = await _context.Consumo_Cliente
                .Include(cc => cc.Cliente)
                .Include(cc => cc.Consumo)
                .Where(cc => cc.ClienteID == clienteId)
                .ToListAsync();

            // Retorna lista vazia se não encontrar consumos
            return consumos;
        }
    }
}
