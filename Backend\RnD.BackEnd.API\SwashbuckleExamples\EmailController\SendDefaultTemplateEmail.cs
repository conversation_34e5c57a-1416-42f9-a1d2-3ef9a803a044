﻿using System.Collections.Generic;
using RnD.BackEnd.API.Model.Email;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.EmailController
{
    /// <summary>
    /// Send Default Template Email request example
    /// </summary>
    public class SendDefaultTemplateEmail : IExamplesProvider<EmailMessageDto<EmailDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to Send Default Template Email.
        /// </returns>
        public EmailMessageDto<EmailDto> GetExamples()
        {
            return new EmailMessageDto<EmailDto>
            {
                TemplateData = new EmailDto
                {
                    Message = "This is a test e-mail",
                    Subject = "Test e-mail"
                },
                Recipients = new List<string> { "<EMAIL>" },
                RecipientsCC = new List<string> { "<EMAIL>" },
                RecipientsBCC = new List<string> { "<EMAIL>" }
            };
        }
    }
}
