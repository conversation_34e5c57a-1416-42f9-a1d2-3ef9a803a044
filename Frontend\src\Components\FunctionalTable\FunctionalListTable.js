import { useState } from "react";
import { Link as RouterLink } from "react-router-dom";
import PropTypes from "prop-types";
import {
  Box,
  Card,
  Checkbox,
  Divider,
  IconButton,
  InputAdornment,
  Tab,
  Table,
  TableBody,
  TableCell,
  TablePagination,
  TableRow,
  Tabs,
  TextField,
  Button,
  Avatar,
  Autocomplete,
  Link,
} from "@mui/material";
import {
  ArrowForward as ArrowForwardIcon,
  Search as SearchIcon,
  Add as AddIcon
} from "@mui/icons-material";
import Scrollbar from "../Scrollbar";
import TableAction from "../TableUtils/TableAction";
import TableHeader from "../TableUtils/TableHeader";
import TableLoading from "../TableUtils/TableLoading";
import {
  applyPagination,
  applySort,
  applyFilters,
} from "../TableUtils/TableUtils";
import TableRemoveModal from "../Modal/FunctionalTable/TableRemoveCardModal";
import TableCloneCardModal from "../Modal/FunctionalTable/TableCloneCardModal";

//Options for Autocomplete
const Selectoptions = [
  { label: "Option1" },
  { label: "Option2" },
  { label: "Option3" },
  { label: "Option4" },
];
//TABS (i.e): For Active and Archived
const tabs = [
  {
    label: "ACTIVE",
    value: "Ativo",
  },
  {
    label: "ARCHIVED",
    value: "X",
  },
];

//HEADCELLS:
const headCells = [
  { id: "processName", label: "Name", sort: true, filter: true },
  { id: "name", label: "Created By", sort: true, filter: true },
  { id: "processDate", label: "Creation Date", sort: true, filter: true },
  {
    id: "totalOpportunities",
    label: "Oportunities Total",
    align: "center",
    sort: true,
  },
  { id: "Action", align: "right" },
];

const FunctionalListTable = (props) => {
  const { processes, changeStatus, getProcess, isLoading } = props;

  //For default it will open Active Tab
  const [currentTab, setCurrentTab] = useState("Ativo");
  const [selectedprocesses, setSelectedprocesses] = useState([]);
  //For pagination and Search
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const [query, setQuery] = useState("");
  const [openProcessRemoveModal, setOpenProcessRemoveModal] = useState(false);
  const [openProcessCloneCardModal, setOpenProcessCloneCardModal] = useState(false);
  const [filters, setFilters] = useState({
    isActive: true,
  });
  const [orderBy, setOrderBy] = useState("processName");
  const [order, setOrder] = useState("asc");

  const handleTabsChange = (event, value) => {
    const updatedFilters = {
      ...filters,
      isActive: true,
    };

    if (value === "all") {
      updatedFilters[value] = true;
    }

    setPage(0);
    changeStatus(value);
    setFilters(updatedFilters);
    setSelectedprocesses([]);
    setCurrentTab(value);
  };

  const handleQueryChange = (event) => {
    setQuery(event.target.value);
  };

  //Selection:
  const handleSelectAllprocesses = (event) => {
    setSelectedprocesses(
      event.target.checked ? processes.map((process) => process.processID) : []
    );
  };

  const handleSelectOneprocess = (event, processID) => {
    if (!selectedprocesses.includes(processID)) {
      setSelectedprocesses((prevSelected) => [...prevSelected, processID]);
    } else {
      setSelectedprocesses((prevSelected) =>
        prevSelected.filter((id) => id !== processID))
    }
  };

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleLimitChange = (event) => {
    setLimit(parseInt(event.target.value, 10));
  };

  const handleOpenProcessRemoveModal = () => {
    setOpenProcessRemoveModal(true);
  };

  const handleOpenProcessCloneModal = () => {
    setOpenProcessCloneCardModal(!openProcessCloneCardModal);
  };

  const handleCloseProcessRemoveModal = (success) => {
    setOpenProcessRemoveModal(false);
    if (success) {
      setSelectedprocesses([]);
      getProcess();
    }
  };

  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const filteredprocesses = applyFilters(headCells, processes, query);
  const sortedprocesses = applySort(filteredprocesses, orderBy, order);
  const paginatedprocesses =
    currentTab === "Ativo"
      ? applyPagination(sortedprocesses, page, limit)
      : applyPagination(sortedprocesses, page, limit);
  const enableBulkActions = selectedprocesses.length > 0;
  const enableEditAction = selectedprocesses.length === 1;
  const selectedSomeprocesses =
    selectedprocesses.length > 0 && selectedprocesses.length < processes.length;
  const selectedAllprocesses =
    selectedprocesses.length > 0 &&
    selectedprocesses.length === processes.length;
  return (
    <Box sx={{ p: "24px 32px 0px 32px" }}>
      <Tabs
        indicatorColor="primary"
        TabIndicatorProps={{
          sx: { background: "#00B098", height: 2, width: "120px" },
        }}
        onChange={handleTabsChange}
        scrollButtons="auto"
        textColor="primary"
        value={currentTab}
        variant="scrollable"
        sx={{
          mt: 1,
          mb: 0,
        }}
      >
        {tabs.map((tab) => (
          <Tab
            key={tab.value}
            label={tab.label}
            value={tab.value}
          />
        ))}
      </Tabs>
      <Divider sx={{ borderBottom: "1px solid #BECCD7" }} />
      <Box sx={{ py: 3 }}>
        <Card>
          <Box
            sx={{
              alignItems: "center",
              display: "flex",
              flexWrap: "wrap",
              m: 2,
              height: "88px",
            }}
          >
            {/* Search TextField */}
            <Box
              xs={12}
              sx={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                padding: "6px 16px",
              }}
            >
              <TextField
                fullWidth
                sx={{
                  borderRadius: "4px",
                  boxSizing: "border-box",
                  gap: "8px",
                  width: "458px",
                  height: "56px",
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                onChange={handleQueryChange}
                placeholder="Search..."
                value={query}
                variant="outlined"
              />
            </Box>
            {/* Select */}
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                padding: 0,
              }}
            >
              <Autocomplete
                id="Selectoptions"
                options={Selectoptions}
                getOptionLabel={(options) => options?.label}
                sx={{
                  borderRadius: "4px",
                  boxSizing: "border-box",
                  gap: "10px",
                  width: "220px",
                  height: "56px",
                }}
                renderInput={(_params) => (
                  <TextField
                    {..._params}
                    variant="outlined"
                    label="This is a random text"
                  />
                )}
              />
            </Box>
            {/* Button: Add New */}
            <Button
              startIcon={<AddIcon fontSize="small" />}
              sx={{ ml: "auto" }}
              component={RouterLink}
              variant="contained"
              to="Form/new"
            >
              Add New Process
            </Button>
          </Box>
          <Box>
            <TableAction
              handleRemoveAction={handleOpenProcessRemoveModal}
              enableBulkActions={enableBulkActions}
              enableEditAction={enableEditAction}
              selectedCounter={selectedprocesses.length}
              handleCloneAction={handleOpenProcessCloneModal}
              enableClone
              enableArchived={currentTab === "A"}
              handleArchivedAction={handleOpenProcessRemoveModal}
            />
            <Scrollbar>
              <Box sx={{ p: 2, pt: 0, minWidth: 700 }}>
                <Table stickyHeader>
                  <TableHeader
                    headCells={headCells}
                    selectedAll={selectedAllprocesses}
                    selectedSome={selectedSomeprocesses}
                    onSelectAllClick={handleSelectAllprocesses}
                    onSortClick={handleRequestSort}
                    orderBy={orderBy}
                    order={order}
                  />
                  <TableBody>
                    <TableLoading
                      isLoading={isLoading}
                      headCells={headCells}
                      numRows={limit}
                    />
                    {paginatedprocesses.map((_process) => {
                      const isprocessesselected = selectedprocesses.includes(
                        _process.processID
                      );

                      return (
                        <TableRow
                          hover
                          key={_process.processID}
                          selected={isprocessesselected}
                        >
                          <TableCell
                            padding="checkbox"
                            sx={{
                              width: "52px",
                              height: "40px",
                            }}
                          >
                            <Checkbox
                              checked={isprocessesselected}
                              color="primary"
                              onChange={(event) =>
                                handleSelectOneprocess(
                                  event,
                                  _process.processID
                                )}
                              value={isprocessesselected}
                            />
                          </TableCell>
                          <TableCell>
                            <Link
                              color="inherit"
                              component={RouterLink}
                              to={`Form/${_process.processID}`}
                              variant="subtitle2"
                              sx={{
                                color: "primary.main",
                                textDecoration: "none",
                                fontStyle: "normal",
                                fontWeight: 700,
                                fontSize: "14px",
                                lineHeight: "20px",
                              }}
                            >
                              {_process.processName}
                            </Link>
                          </TableCell>
                          <TableCell>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              <Avatar
                                mx={3}
                                key={`avatar-${_process.hiringOfferCandidateID}`}
                                src={_process.photo}
                              />
                              <div
                                style={{
                                  marginLeft: "10px",
                                }}
                              >
                                {_process.name}
                              </div>
                            </Box>
                          </TableCell>
                          <TableCell align="center">
                            {_process.processDate}
                          </TableCell>
                          <TableCell align="center">
                            {_process.totalOpportunities}
                          </TableCell>
                          <TableCell align="right">
                            <IconButton
                              component={RouterLink}
                              to={`/FunctionalTableExample/Form/${_process.processID}`}
                            >
                              <ArrowForwardIcon fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </Box>
            </Scrollbar>
            <TablePagination
              component="div"
              count={filteredprocesses.length}
              onPageChange={handlePageChange}
              onRowsPerPageChange={handleLimitChange}
              page={page}
              rowsPerPage={limit}
              rowsPerPageOptions={[5, 10, 25]}
              showFirstButton
              showLastButton
            />
            <TableRemoveModal
              oid={selectedprocesses[0]}
              selectedProcess={selectedprocesses}
              open={openProcessRemoveModal}
              onClose={handleCloseProcessRemoveModal}
            />
            <TableCloneCardModal
              open={openProcessCloneCardModal}
              onClose={handleOpenProcessCloneModal}
              selectedProcesses={selectedprocesses}
            />
          </Box>
        </Card>
      </Box>
    </Box>
  );
};

FunctionalListTable.propTypes = {
  processes: PropTypes.array.isRequired,
  changeStatus: PropTypes.func,
  getProcess: PropTypes.func,
  isLoading: PropTypes.bool,
};

export default FunctionalListTable;
