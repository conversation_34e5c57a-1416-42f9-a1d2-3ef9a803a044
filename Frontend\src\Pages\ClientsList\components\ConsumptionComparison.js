import React, { useEffect, useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Divider,
  CircularProgress,
  Alert,
  TextField
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';
import WarningIcon from '@mui/icons-material/Warning';
import { getComparacaoData, calculateDifference, formatCurrency, formatPercentage } from '../../../Services/comparacaoService';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ptBR from 'date-fns/locale/pt-BR';
import { startOfMonth, endOfMonth } from 'date-fns';

const ConsumptionComparison = ({ clientId }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [comparacaoData, setComparacaoData] = useState({
    resellerConsumption: null,
    azureConsumption: null,
    resellerInvoice: null
  });
  const [error, setError] = useState(null);
  const [dataInicio, setDataInicio] = useState(startOfMonth(new Date()));
  const [dataFim, setDataFim] = useState(endOfMonth(new Date()));

  useEffect(() => {
    const fetchData = async () => {
      console.log('ConsumptionComparison - Buscando dados para cliente:', clientId);
      try {
        setLoading(true);
        const data = await getComparacaoData(clientId, dataInicio, dataFim);
        console.log('ConsumptionComparison - Dados recebidos:', data);
        setComparacaoData(data);
      } catch (err) {
        console.error('Erro ao buscar dados de comparação:', err);
        setError(err.message || 'Erro ao carregar dados de comparação');
      } finally {
        setLoading(false);
      }
    };

    if (clientId) {
      fetchData();
    }
  }, [clientId, dataInicio, dataFim]);

  const getTrendIcon = (difference) => {
    if (difference === null) return null;
    if (difference > 0) {
      return <TrendingUpIcon color="error" />;
    } else if (difference < 0) {
      return <TrendingDownIcon color="success" />;
    }
    return <TrendingFlatIcon color="info" />;
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            {t('Comparação de Consumos')}
          </Typography>
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
                <DatePicker
                  label={t("common:tableActions.initialdate")}
                  value={dataInicio}
                  onChange={(newValue) => setDataInicio(newValue)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} md={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
                <DatePicker
                  label={t("common:tableActions.finaldate")}
                  value={dataFim}
                  onChange={(newValue) => setDataFim(newValue)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </LocalizationProvider>
            </Grid>
          </Grid>
        </Box>
        
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" p={3}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        ) : (() => {
          const { resellerConsumption, azureConsumption, resellerInvoice } = comparacaoData;
          
          console.log('ConsumptionComparison - Calculando diferenças...');
          const customerTotalDiff = resellerConsumption?.CountryCustomerTotal && azureConsumption?.TotalCost
            ? resellerConsumption.CountryCustomerTotal - azureConsumption.TotalCost
            : null;
          
          const resellerTotalDiff = resellerInvoice?.TotalFatura && resellerConsumption?.CountryResellerTotal
            ? resellerInvoice.TotalFatura - resellerConsumption.CountryResellerTotal
            : null;
          
          console.log('ConsumptionComparison - Valores calculados:', {
            customerTotalDiff,
            resellerTotalDiff,
            resellerConsumptionTotal: resellerConsumption?.CountryCustomerTotal,
            azureConsumptionTotal: azureConsumption?.TotalCost,
            resellerInvoiceTotal: resellerInvoice?.TotalFatura,
            resellerConsumptionResellerTotal: resellerConsumption?.CountryResellerTotal
          });
          
          return (
            <Grid container spacing={3}>
              {/* Comparação Customer Total vs Azure Total */}
              <Grid item xs={12} md={6}>
                <Box p={2}>
                  <Typography variant="subtitle1" gutterBottom>
                    {t('Customer Total vs Azure Total')}
                  </Typography>
                  {!resellerConsumption && !azureConsumption ? (
                    <Alert severity="info" icon={<WarningIcon />}>
                      {t('Não existe documentos de faturação')}
                    </Alert>
                  ) : (
                    <>
                      <Box display="flex" alignItems="center" mb={1}>
                        <Typography variant="body1" mr={1}>
                          {t('Customer Total')}:
                        </Typography>
                        <Typography variant="body1" fontWeight="bold">
                          {resellerConsumption?.CountryCustomerTotal === 0 ? 
                            t('Não existe documentos de faturação') : 
                            formatCurrency(resellerConsumption?.CountryCustomerTotal)}
                        </Typography>
                      </Box>
                      <Box display="flex" alignItems="center" mb={1}>
                        <Typography variant="body1" mr={1}>
                          {t('Azure Total')}:
                        </Typography>
                        <Typography variant="body1" fontWeight="bold">
                          {azureConsumption?.TotalCost === 0 ? 
                            t('Não existe documentos de faturação') : 
                            formatCurrency(azureConsumption?.TotalCost)}
                        </Typography>
                      </Box>
                      <Box display="flex" alignItems="center">
                        {getTrendIcon(customerTotalDiff)}
                        <Typography
                          variant="body1"
                          color={customerTotalDiff > 0 ? 'error' : customerTotalDiff < 0 ? 'success' : 'info'}
                          ml={1}
                        >
                          {customerTotalDiff !== null 
                            ? formatCurrency(customerTotalDiff)
                            : t('Dados incompletos para comparar')}
                        </Typography>
                      </Box>
                    </>
                  )}
                </Box>
              </Grid>
  
              <Grid item xs={12} md={6}>
                <Box p={2}>
                  <Typography variant="subtitle1" gutterBottom>
                    {t('Faturação Reseller vs Country Reseller Total')}
                  </Typography>
                  {!resellerInvoice ? (
                    <Alert severity="info" icon={<WarningIcon />}>
                      {t('Não existe documentos de faturação')}
                    </Alert>
                  ) : (
                    <>
                      <Box display="flex" alignItems="center" mb={1}>
                        <Typography variant="body1" mr={1}>
                          {t('Faturação Reseller')}:
                        </Typography>
                        <Typography variant="body1" fontWeight="bold">
                          {resellerInvoice?.TotalFatura === 0 ? 
                            t('Não existe documentos de faturação') : 
                            formatCurrency(resellerInvoice?.TotalFatura)}
                        </Typography>
                      </Box>
                      <Box display="flex" alignItems="center" mb={1}>
                        <Typography variant="body1" mr={1}>
                          {t('Country Reseller Total')}:
                        </Typography>
                        <Typography variant="body1" fontWeight="bold">
                          {resellerConsumption?.CountryResellerTotal === 0 ? 
                            t('Não existe documentos de faturação') : 
                            formatCurrency(resellerConsumption?.CountryResellerTotal)}
                        </Typography>
                      </Box>
                      <Box display="flex" alignItems="center">
                        {getTrendIcon(resellerTotalDiff)}
                        <Typography
                          variant="body1"
                          color={resellerTotalDiff > 0 ? 'error' : resellerTotalDiff < 0 ? 'success' : 'info'}
                          ml={1}
                        >
                          {resellerTotalDiff !== null 
                            ? formatCurrency(resellerTotalDiff)
                            : t('Dados incompletos para comparar')}
                        </Typography>
                      </Box>
                    </>
                  )}
                </Box>
              </Grid>
            </Grid>
          );
        })()}
      </CardContent>
    </Card>
  );
};

export default ConsumptionComparison;