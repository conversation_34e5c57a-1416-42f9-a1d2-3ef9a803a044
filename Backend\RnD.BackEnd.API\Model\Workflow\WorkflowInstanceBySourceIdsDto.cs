﻿using System.Collections.Generic;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Workflow Instances by SourceId DTO class
    /// </summary>
    public class WorkflowInstanceBySourceIdsDto
    {
        /// <summary>
        /// Gets or sets the source ids.
        /// </summary>
        /// <value>
        /// The source ids.
        /// </value>
        public List<string> SourceIds { get; set; }

        /// <summary>
        /// Gets or sets the status.
        /// </summary>
        /// <value>
        /// The status.
        /// </value>
        public List<string> Status { get; set; }
    }
}