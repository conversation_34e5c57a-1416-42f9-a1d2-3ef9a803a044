import React from 'react';
import PropTypes from 'prop-types';
import { <PERSON>, <PERSON>Header, CardContent, CardActions, Typography, Divider, Alert, Box } from '@mui/material';
import CustomStepper from '../CustomStepper';
import QuizRating from './QuizRating';
import QuizComment from './QuizComment';

export default function QuestionCard(props) {
  const { question, updateQuizAnswer, updateQuizYesOrNo, saveQuizComment, canEdit, errors } = props;

  const handleSaveQuizComment = (value) => {
    saveQuizComment(question, value);
  };

  return (
    <Card sx={{
      mb: 4
    }}
    >
      <CardHeader
        title={question.question}
        titleTypographyProps={{ fontWeight: 'bold' }}
      />
      <Divider />
      <CardContent>
        {
          question.detailedKPIs ?
            <Box sx={{ mt: 2, mb: 2 }}>
              <CustomStepper steps={question.detailedKPIs} />
            </Box>
            :
            null
        }
        {question.defaultMessage && question.defaultMessage !== "" &&
          <Alert severity="warning">{question.defaultMessage}</Alert>}
        <Typography
          variant="subtitle2"
          sx={{
            fontWeight: "bold"
          }}
        >
          {question.questionTitle}
        </Typography>
        <Typography
          color="textSecondary"
          variant="subtitle2"
          sx={{
            fontWeight: "initial"
          }}
        >
          {question.indicator}
        </Typography>
        <QuizRating
          question={question}
          updateQuizAnswer={updateQuizAnswer}
          updateQuizYesOrNo={updateQuizYesOrNo}
          canEdit={canEdit}
          errors={errors}
        />
      </CardContent>
      <CardActions>
        {question.showComments && (
          <div
            style={{
              width: "100%"
            }}
          >
            <QuizComment
              commentsdata={question.comments}
              owncomment={question.currentComments}
              saveQuizComment={handleSaveQuizComment}
              canedit={canEdit}
            />
          </div>
        )}
      </CardActions>
    </Card>
  );
}

QuestionCard.propTypes = {
  question: PropTypes.any.isRequired,
  updateQuizAnswer: PropTypes.func.isRequired,
  updateQuizYesOrNo: PropTypes.func.isRequired,
  saveQuizComment: PropTypes.func,
  canEdit: PropTypes.bool,
  errors: PropTypes.object
};
