import React, { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
  Container,
  Typography,
  CircularProgress,
  Avatar,
  Paper,
  Box,
  TextField,
  Button,
  Grid,
  Divider,
  Snackbar,
  Alert,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Tabs,
  Tab,
  List,
  ListItem,
} from "@mui/material";
import ContentPasteIcon from "@mui/icons-material/ContentPaste";
import { Person as PersonIcon } from "@mui/icons-material";
import SaveIcon from "@mui/icons-material/Save";
import CloudQueueIcon from "@mui/icons-material/CloudQueue";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import CloudIcon from "@mui/icons-material/Cloud";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import {
  getClientById,
  updateClient,
  deleteClient,
} from "../../Services/clientServices";
import {
  getResourceGroupsByClienteId,
  getConsumoSummaryByResourceGroup,
} from "../../Services/consumoService";
import { getConsumoPDFByClienteId } from "../../Services/consumoPDFService";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import ptBR from "date-fns/locale/pt-BR";
import { startOfMonth, endOfMonth } from "date-fns";
import { useTranslation } from "react-i18next";
import {
  getSubscricoesByCliente,
  addSubscricao,
  removeSubscricao,
  getAvailableSubscriptions,
} from "../../Services/clienteSubscricaoService";
import { getSubscriptions } from "../../Services/subscriptionServices";
import AddIcon from "@mui/icons-material/Add";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelIcon from "@mui/icons-material/Cancel";
import Chip from "@mui/material/Chip";
import Autocomplete from "@mui/material/Autocomplete";

// Importando os componentes
import BasicInfo from "./components/BasicInfo";
import Subscriptions from "./components/Subscriptions";
import ResourceConsumption from "./components/ResourceConsumption";
import Invoices from "./components/Invoices";
import AzureConsumption from "./components/AzureConsumption";
import ConsumptionComparison from "./components/ConsumptionComparison";

const API_BASE_URL = `${process.env.REACT_APP_WEBAPI_URL}/api`;

function ClientDetail() {
  const [subscricoes, setSubscricoes] = useState([]);
  const [availableSubscriptions, setAvailableSubscriptions] = useState([]);
  const [selectedSubscription, setSelectedSubscription] = useState(null);
  const [loadingSubscricoes, setLoadingSubscricoes] = useState(false);
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const [client, setClient] = useState(null);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [confirmDelete, setConfirmDelete] = useState(false);

  const [editedClient, setEditedClient] = useState({
    Name: "",
    Contact: "",
  });

  const [subscriptionDetails, setSubscriptionDetails] = useState({});
  const [resourceGroups, setResourceGroups] = useState([]);
  const [selectedResourceGroup, setSelectedResourceGroup] = useState("");
  const [consumoSummary, setConsumoSummary] = useState(null);
  const [consumoLoading, setConsumoLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [dataInicio, setDataInicio] = useState(startOfMonth(new Date()));
  const [dataFim, setDataFim] = useState(endOfMonth(new Date()));

  // Estados para o filtro de faturas
  const [dataInicioFatura, setDataInicioFatura] = useState(
    startOfMonth(new Date())
  );
  const [dataFimFatura, setDataFimFatura] = useState(endOfMonth(new Date()));

  // Estado para os dados de PDF
  const [consumosPDF, setConsumosPDF] = useState([]);
  const [pdfLoading, setPdfLoading] = useState(false);

  // Estados para comparação removidos, pois agora são gerenciados pelo componente ConsumptionComparison

  useEffect(() => {
    const fetchClientDetails = async () => {
      try {
        setLoading(true);
        const clientData = await getClientById(id);
        setClient({
          id: clientData.id,
          Name: clientData.name,
          Contact: clientData.contact || "0",
        });

        setEditedClient({
          Name: clientData.name,
          Contact: clientData.contact || "0",
        });

        // Após carregar o cliente, buscar os resource groups
        try {
          const groups = await getResourceGroupsByClienteId(id);
          setResourceGroups(groups);

          // Seleciona "Geral" por padrão
          setSelectedResourceGroup("Geral");

          // Buscar dados de PDF do cliente
          try {
            setPdfLoading(true);
            const pdfData = await getConsumoPDFByClienteId(id);
            setConsumosPDF(pdfData);
          } catch (pdfError) {
            console.error("Erro ao buscar dados de PDF:", pdfError);
          } finally {
            setPdfLoading(false);
          }
        } catch (rgsError) {
          console.error("Erro ao buscar resource groups:", rgsError);
        }
      } catch (fetchError) {
        console.error("❌ ERRO ao buscar detalhes do cliente:", fetchError);
        setError("Não foi possível carregar os detalhes do cliente");
      } finally {
        setLoading(false);
      }
    };

    fetchClientDetails();
  }, [id]);

  useEffect(() => {
    const fetchSubscricoes = async () => {
      try {
        setLoadingSubscricoes(true);
        const subscricoesData = await getSubscricoesByCliente(id);
        setSubscricoes(subscricoesData);
      } catch (error) {
        console.error("Erro ao buscar subscrições:", error);
      } finally {
        setLoadingSubscricoes(false);
      }
    };

    if (id) {
      fetchSubscricoes();
    }
  }, [id]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoadingSubscricoes(true);

        const subIds = await getSubscricoesByCliente(id);
        const available = await getAvailableSubscriptions();

        const subDetails = await Promise.all(
          subIds.map((id) =>
            handleMethod({
              method: `${API_BASE_URL}/Subscription/${id}`,
              type: "GET",
            })
          )
        );

        const detailsMap = subDetails.reduce((acc, sub) => {
          acc[sub.id] = sub;
          return acc;
        }, {});

        setSubscricoes(subIds);
        setSubscriptionDetails(detailsMap);
        setAvailableSubscriptions(available);
      } catch (error) {
        console.error("Erro ao carregar subscrições:", error);
      } finally {
        setLoadingSubscricoes(false);
      }
    };

    if (id) {
      fetchData();
    }
  }, [id]);

  useEffect(() => {
    const fetchConsumoSummary = async () => {
      if (!selectedResourceGroup) return;

      try {
        setConsumoLoading(true);
        const summary = await getConsumoSummaryByResourceGroup(
          id,
          selectedResourceGroup,
          dataInicio,
          dataFim
        );
        setConsumoSummary(summary);
      } catch (consumoError) {
        console.error("Erro ao carregar resumo de consumo:", consumoError);
      } finally {
        setConsumoLoading(false);
      }
    };

    fetchConsumoSummary();
  }, [id, selectedResourceGroup, dataInicio, dataFim]);

  // Removido o useEffect para buscar dados de comparação, pois agora isso é feito diretamente no componente ConsumptionComparison

  const handleEditToggle = () => {
    if (editMode && hasChanges()) {
      if (!window.confirm("Deseja descartar as alterações não salvas?")) {
        return;
      }
    }

    if (!editMode) {
      setEditedClient({
        Name: client.Name,
        Contact: client.Contact,
      });
    }

    setEditMode(!editMode);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setEditedClient((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const hasChanges = () => {
    return (
      client.Name !== editedClient.Name ||
      client.Contact !== editedClient.Contact
    );
  };

  const handleAddSubscricao = async () => {
    if (!selectedSubscription) return;

    try {
      setLoadingSubscricoes(true);
      await addSubscricao(id, selectedSubscription.id);

      const [subs, available] = await Promise.all([
        getSubscricoesByCliente(id),
        getAvailableSubscriptions(),
      ]);
      setSubscricoes(subs);
      setAvailableSubscriptions(available);

      setSuccessMessage("Subscrição adicionada com sucesso!");
      setSuccess(true);
      setSelectedSubscription(null);
    } catch (error) {
      setError(error.message || "Erro ao adicionar subscrição");
    } finally {
      setLoadingSubscricoes(false);
    }
  };

  const handleRemoveSubscricao = async (subscriptionId) => {
    try {
      setLoadingSubscricoes(true);
      await removeSubscricao(id, subscriptionId);

      const [subs, available] = await Promise.all([
        getSubscricoesByCliente(id),
        getAvailableSubscriptions(),
      ]);
      setSubscricoes(subs);
      setAvailableSubscriptions(available);

      setSuccessMessage("Subscrição removida com sucesso!");
      setSuccess(true);
    } catch (error) {
      setError(error.message || "Erro ao remover subscrição");
    } finally {
      setLoadingSubscricoes(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      if (!editedClient.Name) {
        throw new Error("Nome é obrigatório");
      }

      const clientToSave = {
        ...editedClient,
      };

      if (!clientToSave.Contact || clientToSave.Contact.trim() === "") {
        clientToSave.Contact = "0";
      }

      const clientData = {
        id: client.id,
        Name: clientToSave.Name,
        Contact: clientToSave.Contact,
      };

      await updateClient(id, clientData);

      setClient({
        ...client,
        Name: clientToSave.Name,
        Contact: clientToSave.Contact,
      });

      setEditedClient(clientToSave);

      setSuccessMessage("Cliente atualizado com sucesso!");
      setSuccess(true);
      setEditMode(false);
    } catch (err) {
      console.error("Erro ao salvar cliente:", err);
      setError(err.message || "Ocorreu um erro ao atualizar o cliente");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!confirmDelete) {
      setConfirmDelete(true);
      return;
    }

    try {
      setSaving(true);
      setError(null);

      await deleteClient(id);

      setSuccessMessage("Cliente excluído com sucesso!");
      setSuccess(true);

      setTimeout(() => {
        navigate("/ClientsList");
      }, 1500);
    } catch (err) {
      console.error("Erro ao apagar cliente:", err);
      setError(err.message || "Ocorreu um erro ao apagar o cliente");
      setConfirmDelete(false);
    } finally {
      setSaving(false);
    }
  };

  const handleBack = () => {
    navigate("/ClientsList");
  };

  const handleResourceGroupChange = (event) => {
    setSelectedResourceGroup(event.target.value);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const formatCurrency = (value, currency = "EUR") => {
    return new Intl.NumberFormat("pt-PT", {
      style: "currency",
      currency: currency,
    }).format(value);
  };

  const formatDate = (date) => {
    if (!date) return "N/A";
    return new Date(date).toLocaleDateString("pt-PT");
  };

  // Função para filtrar PDFs pelo período
  const filteredConsumosPDF = consumosPDF.filter((pdf) => {
    const inicio = new Date(pdf.dataInicio);
    const fim = new Date(pdf.dataFim);
    return inicio >= dataInicioFatura && fim <= dataFimFatura;
  });

  if (loading) {
    return (
      <Container
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "50vh",
        }}
      >
        <CircularProgress />
      </Container>
    );
  }

  if (!client) {
    return (
      <Container>
        <Typography variant="h5" color="error" sx={{ mt: 4 }}>
          {t("common:labels.clientNotFound")}
        </Typography>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{ mt: 2 }}
        >
          {t("common:buttons.return")}
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
            sx={{ mr: 2 }}
          >
            {t("common:buttons.return")}
          </Button>
          <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
            {editMode
              ? t("common:labels.editClient")
              : t("common:labels.clientDetails")}
          </Typography>
          {!editMode ? (
            <Button
              startIcon={<EditIcon />}
              variant="outlined"
              onClick={handleEditToggle}
              sx={{ mr: 1 }}
            >
              {t("common:buttons.edit")}
            </Button>
          ) : (
            <Button color="secondary" onClick={handleEditToggle} sx={{ mr: 1 }}>
              {t("common:buttons.cancel")}
            </Button>
          )}
          {!editMode && (
            <IconButton color="error" onClick={handleDelete} disabled={saving}>
              <DeleteIcon />
            </IconButton>
          )}
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", mb: 4 }}>
          <Avatar alt={client.Name} sx={{ width: 100, height: 100, mr: 3 }} />
          <Box>
            <Typography variant="h5" gutterBottom>
              {client.Name}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              ID: {client.id}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ mb: 4 }} />

        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab
            label={t("common:tableHeaders.basicInfo")}
            icon={<PersonIcon />}
            iconPosition="start"
          />
          <Tab
            label={t("common:tableHeaders.resellerConsume")}
            icon={<CloudIcon />}
            iconPosition="start"
          />
          <Tab
            label={t("common:tableHeaders.azureConsume")}
            icon={<CloudQueueIcon />}
            iconPosition="start"
          />
          <Tab
            label={t("common:tableHeaders.resellerFacturation")}
            icon={<ContentPasteIcon />}
            iconPosition="start"
          />
          <Tab
            label={t("common:tableHeaders.comparation")}
            icon={<ContentPasteIcon />}
            iconPosition="start"
          />
        </Tabs>

        {activeTab === 0 && (
          <>
            <BasicInfo
              client={client}
              editMode={editMode}
              editedClient={editedClient}
              handleChange={handleChange}
              handleSave={handleSave}
              saving={saving}
              hasChanges={hasChanges}
              error={error}
            />
            <Subscriptions
              subscricoes={subscricoes}
              subscriptionDetails={subscriptionDetails}
              loadingSubscricoes={loadingSubscricoes}
              availableSubscriptions={availableSubscriptions}
              selectedSubscription={selectedSubscription}
              setSelectedSubscription={setSelectedSubscription}
              handleAddSubscricao={handleAddSubscricao}
              handleRemoveSubscricao={handleRemoveSubscricao}
            />
          </>
        )}

        {activeTab === 1 && (
          <ResourceConsumption
            resourceGroups={resourceGroups}
            selectedResourceGroup={selectedResourceGroup}
            handleResourceGroupChange={handleResourceGroupChange}
            dataInicio={dataInicio}
            setDataInicio={setDataInicio}
            dataFim={dataFim}
            setDataFim={setDataFim}
            consumoLoading={consumoLoading}
            consumoSummary={consumoSummary}
            formatCurrency={formatCurrency}
          />
        )}

        {activeTab === 2 && <AzureConsumption clientId={id} />}

        {activeTab === 3 && (
          <Invoices
            pdfLoading={pdfLoading}
            consumosPDF={filteredConsumosPDF}
            formatCurrency={formatCurrency}
            formatDate={formatDate}
            dataInicioFatura={dataInicioFatura}
            setDataInicioFatura={setDataInicioFatura}
            dataFimFatura={dataFimFatura}
            setDataFimFatura={setDataFimFatura}
          />
        )}

        {activeTab === 4 && <ConsumptionComparison clientId={id} />}

        {confirmDelete && (
          <Box sx={{ mt: 4, p: 2, bgcolor: "error.light", borderRadius: 1 }}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              {t("common:labels.confirmDeleteClient")}
            </Typography>
            <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                onClick={() => setConfirmDelete(false)}
                sx={{ mr: 1 }}
              >
                {t("common:buttons.cancel")}
              </Button>
              <Button
                variant="contained"
                color="error"
                onClick={handleDelete}
                disabled={saving}
                startIcon={
                  saving ? <CircularProgress size={20} /> : <DeleteIcon />
                }
              >
                {saving
                  ? t("common:buttons.deleting")
                  : t("common:buttons.confirmDeletion")}
              </Button>
            </Box>
          </Box>
        )}
      </Paper>

      <Snackbar
        open={success}
        autoHideDuration={6000}
        onClose={() => setSuccess(false)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert severity="success" sx={{ width: "100%" }}>
          {successMessage}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert severity="error" sx={{ width: "100%" }}>
          {error}
        </Alert>
      </Snackbar>
    </Container>
  );
}

export default ClientDetail;
