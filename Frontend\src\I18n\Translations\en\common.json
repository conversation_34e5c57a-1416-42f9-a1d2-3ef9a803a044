{"common": {"value": "Value", "filter": "Filter", "add_filters": "Add filters", "choose_filter": "Choose filter", "view": "View custom filters", "en": "English", "pt": "Portuguese", "logout": "Logout"}, "placeholder": {"search": "Search..."}, "buttons": {"update": "Update", "return": "Return", "createClient": "Create Client", "save": "Save", "submit": "Submit", "cancel": "Cancel", "confirm": "Confirm", "copy": "Copy", "download": "Download", "clone": "<PERSON><PERSON>", "remove": "Remove", "revert": "<PERSON><PERSON>", "archive": "Archive", "approve": "Approve", "reject": "Reject", "unlock": "Unlock", "user": "User", "saveChanges": "Save Changes", "deleteUser": "Delete User", "profile": "Profile", "edit": "Edit", "delete": "Delete", "observations": "Observations", "add": "Add", "confirmDeletion": "Confirm Deletion", "deleting": "Deleting..."}, "user": {"addBI4ALLUsers": "Add BI4ALL Users", "addUsers": "Add Users", "searchByEmail": "Search by email in Azure AD:", "search": "Search", "enterEmailToSearch": "Enter an email to search for users", "usersFound": "{{count}} users found", "noUsersFound": "No users found", "usersSelected": "{{count}} users selected", "emailPlaceholder": "<EMAIL>", "noPermissions": "No permissions"}, "subscription": {"id": "Subscription ID", "totalCostEUR": "Total Cost (EUR)", "totalCostUSD": "Total Cost (USD)", "resourceCount": "Resource Count", "startDate": "Start Date", "endDate": "End Date", "noSubscriptionsFound": "No subscriptions found", "associatedSubscriptions": "Associated Subscriptions", "addSubscription": "Add Subscription", "noAssociatedSubscriptions": "No subscriptions associated with this client.", "noAvailableSubscriptions": "No subscriptions available to associate.", "selectAvailableSubscription": "Select an available subscription"}, "months": {"jan": "January", "feb": "February", "mar": "March", "apr": "April", "may": "May", "jun": "June", "jul": "July", "aug": "August", "sep": "September", "oct": "October", "nov": "November", "dec": "December"}, "labels": {"allfiles": "All files", "alltypes": "All types", "employee": "Employee", "team": "Team", "month": "Month", "year": "Year", "total": "Total", "days": "<PERSON><PERSON>", "approvalStatus": "Approval Status", "more": "more", "noResultsFound": "No results found", "profilesAccess": "Profiles Access", "connectProfiles": "Connect Profiles", "all": "All", "user": "User", "apiUser": "API User", "profiles": "Profiles", "deleteUser": "Delete User", "permissions": "Permissions", "leaveComment": "Leave a comment...", "processedFile": "Processed File", "processedFiles": "Processed Files", "processingDate": "Processing Date", "processingStatus": "Processing Status", "successfullyProcessed": "Successfully Processed", "processingError": "Processing Error", "processingWarning": "Processing Warning", "infoLog": "Information Log", "clientDetails": "Client Details", "editClient": "Edit Client", "clientNotFound": "Client not found", "confirmDeleteClient": "Are you sure you want to delete this client? This action cannot be undone."}, "tableActions": {"noValues": "No values", "loading": "Loading...", "search": "Search", "record_one": "record", "record_other": "records", "select": "Select", "selected_one": "selected", "selected_other": "selected", "select_all": "Select All", "noResults": "No Results", "option": "Choose an option", "loadingPermissions": "Loading permissions...", "initialdate": "Initial Date", "finaldate": "Final Date", "rowsPerPage": "Rows per page:"}, "formik": {"requiredField": "Required field"}, "conditions": {"condition": "Condition", "contains": "contains", "sw": "starts with", "ew": "ends with", "eq": "equals to", "gt": "higher than", "lt": "less than"}, "Chips": {"new": "New", "improved": "Improved", "fixed": "Fixed"}, "tableHeaders": {"comparation": "Comparation", "resellerFacturation": "Reseller Facturation", "azureConsume": "Azure Consumption", "resellerConsume": "Reseller Consumption", "consumoAzure": "Azure Consumption", "consumoReseller": "Reseller Consumption", "faturacaoReseller": "Reseller Billing", "basicInfo": "Basic Information", "errorMessage": "Error Message", "hourDate": "Hour/Date", "fileID": "File ID", "file": "File", "logtype": "Log Type", "message": "Message", "logsArchive": "Processed Files", "costs": "Costs", "email": "Email", "functionalName": "Functional Name", "technicalName": "Technical Name", "createdBy": "Created By", "createdAt": "Created At", "modifiedBy": "Modified By", "modifiedAt": "Modified At", "profiles": "Profiles", "users": "Users", "permission": "Permission", "entities": "Entities", "nFields": "Nº of Fields", "nRecords": "Nº of Records", "fieldType": "Field Type", "validation": "Validation", "size": "Size", "entity": "Entity", "entityField": "Entity Field", "mandatory": "Mandatory", "hidden": "Hidden", "name": "Name", "roles": "Roles", "permissions": "Permissions", "model": "Model", "username": "Username", "contact": "Contact", "services": "Services", "fileName": "File Name", "processTime": "Processing Time", "recordsProcessed": "Records Processed"}, "search": {"search": "Search...", "searchRole": "Search Role", "searchUser": "Search User", "searchClient": "Search Client", "searchProfiles": "Search profiles...", "searchProfile": "Search Profile", "searchSubscription": "Search Subscription"}, "form": {"functionalName": "Functional Name", "technicalName": "Technical Name", "name": "Name", "roleDescription": "Role Description", "userSearch": "User Search", "username": "Username"}, "warnings": {"unsavedData": "There might be unsaved data. Are you sure you want to leave?", "profileMandatory": "Needs at least one Profile", "deleteMessageUser": "Are you sure you want to delete it?", "deleteMessageRowsPerm": "This action will permanently delete the item(s). Are you sure you want to delete?", "deleteMessageRows": "Are you sure you want to delete the item(s)?", "roleDescription": "Role Description is mandatory", "onePermission": "At least one permission is needed", "deleteModel": "Delete Model", "deleteEntity": "Delete Entity", "deleteProfile": "Delete Profile"}}