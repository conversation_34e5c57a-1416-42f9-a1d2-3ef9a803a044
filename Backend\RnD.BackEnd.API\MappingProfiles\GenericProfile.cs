﻿using AutoMapper;
using RnD.BackEnd.API.Model.Exception;
using RnD.BackEnd.API.Model.Generic;
using RnD.BackEnd.Domain.Entities.Generic;

namespace RnD.BackEnd.API.MappingProfiles
{
    /// <summary>
    /// Generic mapping profile
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class GenericProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="GenericProfile"/> class.
        /// </summary>
        public GenericProfile()
        {
            CreateMap(typeof(ServiceOutput<>), typeof(ApiOutput<>));
            CreateMap<Domain.Entities.Exception.ModelException, ModelException>();
        }
    }
}
