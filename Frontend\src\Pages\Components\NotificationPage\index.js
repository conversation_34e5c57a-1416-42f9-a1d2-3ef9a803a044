import React, { useState } from 'react';
import {
    Box,
    Divider,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    Tooltip,
    Card,
    CardHeader,
    CardContent,
    Typography,
} from '@mui/material'
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import Editor from 'react-simple-code-editor';
import { highlight, languages } from 'prismjs/components/prism-core';
import getCode from './mocks/code';
// import getSourceCode from './mocks/sourceCode';
import getNotificationCode from './mocks/notificationCode';
import Notification from '../../../Components/Notification';
import CopyToClipboardButton from '../../../Components/CopyToClipBoardButton';
import { ChangeLogEntry, chipStyles } from '../../../Components/ChangeLog/ChangeLogEntry';
import {
    AccountTree as AccountTreeIcon,
    Star as StarIcon,
    Person as PersonIcon,
} from '@mui/icons-material';

export default function NotificationPage() {
    const theme = useTheme();
    const chips = chipStyles(theme);
    const { t } = useTranslation();
    const [code, setCode] = useState(getCode);

    return (
        <Box>
            <Divider sx={{ mt: 2, mb: 2 }} />
            <Box sx={{ maxWidth: "80%", m: "auto" }}>
                <Box>
                    <h2>Notification Component</h2>
                    <p>The following code snippet demonstrates how a notification component is used:</p>
                </Box>
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                    <Tooltip
                        title={t('common:common.view')}
                    >
                        <Notification />
                    </Tooltip>
                </Box>
                <Card>
                    <CardHeader
                        action={
                            <>
                                <CopyToClipboardButton
                                    value={code}
                                />
                            </>
                        }
                    />
                    <Divider />
                    <CardContent
                        sx={{
                            maxHeight: "50vh",
                            overflowY: "scroll",
                        }}
                    >
                        <Editor
                            value={code}
                            onValueChange={_code => setCode(_code)}
                            highlight={_code => highlight(_code, languages.js)}
                            padding={10}
                            style={{
                                fontFamily: '"Fira code", "Fira Mono", monospace',
                                fontSize: 12,
                                backgroundColor: theme.palette.background.paper,
                                borderRadius: theme.shape.borderRadius,
                            }}
                        />
                    </CardContent>
                </Card>
                <Box>
                    <List>
                        <ListItem disablePadding>
                            <ListItemButton>
                                <ListItemIcon>
                                    <AccountTreeIcon />
                                </ListItemIcon>
                                <ListItemText primary="The AccountTreeIcon icon is used when notificationType is &apos;Recrutamento&apos;" />
                            </ListItemButton>
                        </ListItem>
                        <ListItem disablePadding>
                            <ListItemButton>
                                <ListItemIcon>
                                    <StarIcon />
                                </ListItemIcon>
                                <ListItemText primary="The StarIcon icon is used when notificationType is &apos;Avaliação&apos;" />
                            </ListItemButton>
                        </ListItem>
                        <ListItem disablePadding>
                            <ListItemButton>
                                <ListItemIcon>
                                    <PersonIcon />
                                </ListItemIcon>
                                <ListItemText primary="The PersonIcon icon is used when notificationType is nether of the two options above" />
                            </ListItemButton>
                        </ListItem>
                    </List>
                </Box>
                <Box>
                    <Typography>
                        The structure for each notification should be and object with the following properties:
                    </Typography>
                    <Editor
                        value={getNotificationCode()}
                        onValueChange={_code => setCode(_code)}
                        highlight={_code => highlight(_code, languages.js)}
                        padding={10}
                        style={{
                            fontFamily: '"Fira code", "Fira Mono", monospace',
                            fontSize: 12,
                            backgroundColor: theme.palette.background.paper,
                            borderRadius: theme.shape.borderRadius,
                        }}
                    />
                </Box>
                <Box sx={{ mt: 3 }}>
                    <Typography>
                        <b>Important notes:</b>
                    </Typography>
                    <Typography sx={{ mt: 1 }}>
                        The &apos;notificationText&apos; property supports HTML code.
                    </Typography>
                    <Typography sx={{ mt: 1 }}>
                        <p>There&apos;s two types of possible &apos;notificationActionType&apos;:
                            &apos;Link&apos; and &apos;Mensagem&apos;.
                            When the &apos;notificationActionType&apos; is &apos;Link&apos; the title can be clicked and be redirected to &apos;linkURL&apos; property.</p>
                    </Typography>
                    <Typography sx={{ mt: 1 }}>
                        If the &apos;isRead&apos; property is false, the background color is set to grey, if not is white.
                    </Typography>
                </Box>
                <Box sx={{ mt: 4 }}>
                    <Typography>Notification component doesn&apos;t need any Props to be functional.</Typography>
                </Box>
            </Box>
            <ChangeLogEntry
                version="V1.1.0"
                changeLog={[
                    { label: 'New', content: "Imported Custom Filters Component.", className: chips.green },
                ]}
            />
        </Box>
    );
}
