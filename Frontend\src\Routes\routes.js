/* eslint-disable no-async-promise-executor */
import React from "react";
import { createB<PERSON>er<PERSON><PERSON><PERSON>, RouterProvider } from "react-router-dom";
import DashboardLayout from "../Layouts/DashboardLayout";
import {
  AppRegistration,
  Groups,
  Person as PersonIcon,
  Error as NearbyError,
  AttachMoney,
  CloudQueue,
  Upload as UploadIcon,
} from "@mui/icons-material";
import { loginRequest } from "../Utils/APIutils/msalConfig";
import * as userPermissions from "../mockAPI/consts/userRoles";
import AuthGuard from "../Components/AuthGuard";
import { useIsAuthenticated } from "@azure/msal-react";
import useAuth from "../Context/Hooks/useCustomAuth";
import SplashScreen from "../Components/SplashScreen";
import AuthorizationRequired from "../Pages/Errors/AuthorizationRequired";
import ServerError from "../Pages/Errors/ServerError";
import NotFound from "../Pages/Errors/NotFound";
import ErrorPage from "../Pages/ErrorPage/error";
import UserList from "../Pages/UserList/userlist";
import ClientsRouter from "../Pages/ClientsList/ClientsRouter";
import UserDetailForm from "../Pages/UserList/UserDetail";
import NewClient from "../Pages/ClientsList/NewClient";
import ClientDetail from "../Pages/ClientsList/ClientDetail";
import UserRouter from "../Pages/UserList/UserRouter";
import Logs from "../Pages/Logs/log";
import ClientListCost from "../Pages/ClientsCostList/ClientListCost";
import SubscriptionRouter from "../Pages/SubscriptionList/SubscriptionRouter";
import FileUploadRouter from "../Pages/FileUpload/FileUploadRouter";

const flatten = (arr) => {
  return arr.reduce((ret, item) => {
    return ret.concat(item.constructor === Array ? flatten(item) : [item]);
  }, []);
};

//Defines the path and route element
//The DashboardLayout needs to be implemented whenever a new parent is defined, otherwise the NavBar and Sidebar will not appear
export const knownRoutes = [
  {
    path: "/",
    element: (
      <DashboardLayout>
        <AuthGuard roles={userPermissions.PORTAL}>
          <UserList />
        </AuthGuard>
      </DashboardLayout>
    ),
  },
  {
    title: "sidebar:options.title",
    path: "/",
    element: <DashboardLayout />,
    children: [
      {
        title: "sidebar:options.usersList",
        path: "Userlist",
        icon: <PersonIcon fontSize="small" />,
        element: <UserList />,
        index: 1,
        children: [
          {
            title: "Novo User",
            path: "new",
            element: <UserDetailForm />,
          },
        ],
      },
      {
        title: "sidebar:options.clientsList",
        path: "ClientsList",
        icon: <Groups fontSize="small" />,
        element: <ClientsRouter />,
        index: 1,
      },
      {
        title: "sidebar:options.ClientsCost",
        path: "ClientsCost",
        icon: <AttachMoney fontSize="small" />,
        element: <ClientListCost />,
        index: 1,
      },
      {
        title: "sidebar:options.subscriptions",
        path: "Subscriptions",
        icon: <CloudQueue fontSize="small" />,
        element: <SubscriptionRouter />,
        index: 1,
      },
      {
        title: "Carregar Ficheiros",
        path: "FileUpload",
        icon: <UploadIcon fontSize="small" />,
        element: <FileUploadRouter />,
        index: 1,
      },
      {
        title: "sidebar:options.error",
        path: "ErrorPage",
        icon: <NearbyError fontSize="small" />,
        element: <ErrorPage />,
        index: 1,
      },
      {
        title: "sidebar:options.logs",
        path: "Logs",
        icon: <AppRegistration fontSize="small" />,
        element: <Logs />,
        index: 1,
      },
    ],
  },
  {
    path: "/error/",
    children: [
      {
        path: "404",
        element: <NotFound />,
      },
      {
        path: "401",
        element: <AuthorizationRequired />,
      },
      {
        path: "500",
        element: <ServerError />,
      },
    ],
  },
  {
    path: "/ClientsList/new",
    element: (
      <DashboardLayout>
        <NewClient />
      </DashboardLayout>
    ),
  },
  {
    path: "/ClientsList/:id",
    element: (
      <DashboardLayout>
        <ClientDetail />
      </DashboardLayout>
    ),
  },
  {
    path: "/UserList/*",
    element: (
      <DashboardLayout>
        <UserRouter />
      </DashboardLayout>
    ),
  },
  {
    path: "/Subscriptions/*",
    element: (
      <DashboardLayout>
        <SubscriptionRouter />
      </DashboardLayout>
    ),
  },
  {
    path: "/FileUpload/*",
    element: (
      <DashboardLayout>
        <FileUploadRouter />
      </DashboardLayout>
    ),
  },
];

function assembleRoute(route, parentElement, pathPrefix = "") {
  const _path = pathPrefix + route.path;
  /* eslint-disable react/no-children-prop */
  const _element = parentElement
    ? React.cloneElement(parentElement, null, route.element)
    : route.element;
  const _handle = route.handle;
  const defaultArr = route.element
    ? [
      {
        path: _path.replace("//", "/"),
        element: _element,
        handle: _handle,
      },
    ]
    : [];

  if (route?.children?.length > 0) {
    const _routes = route.children.map((r) =>
      assembleRoute(r, route.element, _path + "/"));
    defaultArr.push(_routes);
  }
  return defaultArr;
}

const routesCollection = knownRoutes.map((r) => assembleRoute(r));
const router = createBrowserRouter(flatten(routesCollection));

export const Routes = () => {
  const isAuthenticated = useIsAuthenticated();
  const auth = useAuth();

  // Verificar tokens disponíveis como salvaguarda
  const hasToken = localStorage.getItem('azureToken') ||
    localStorage.getItem('session') ||
    localStorage.getItem('accessToken');

  console.log('Routes: Estado da autenticação', {
    msalAuth: isAuthenticated,
    contextAuth: auth.isInitialized,
    hasToken: !!hasToken
  });

  // Considerar autenticado se MSAL diz que sim, OU se temos token no localStorage
  const shouldRenderApp = (isAuthenticated && auth.isInitialized) || (!!hasToken);

  return shouldRenderApp ? (
    <RouterProvider router={router} />
  ) : (
    <SplashScreen />
  );
};

export const Authentication = async (instance) => {
  const loginAD = () => {
    return new Promise(async (resolve) => {
      try {
        await instance.handleRedirectPromise();

        if (instance && instance.getAllAccounts().length > 0) {
          console.log("Conta encontrada, solicitando token...");
          try {
            const response = await instance.acquireTokenSilent({
              ...loginRequest,
              account: instance.getAllAccounts()[0],
            });
            console.log("Token obtido silenciosamente");
            resolve(response.accessToken);
          } catch (err) {
            console.error("Erro ao adquirir token silenciosamente:", err);
            if (err.name === "InteractionRequiredAuthError") {
              try {
                console.log("Tentando obter token com popup...");
                const response = await instance.acquireTokenPopup({
                  ...loginRequest,
                  account: instance.getAllAccounts()[0],
                });
                console.log("Token obtido com popup");
                resolve(response.accessToken);
              } catch (popupError) {
                console.error("Erro no popup:", popupError);
                resolve(null);
              }
            } else {
              resolve(null);
            }
          }
        } else {
          console.log("Nenhuma conta encontrada, redirecionando para login...");
          instance.loginRedirect(loginRequest);
          resolve(null); // Não teremos token neste momento
        }
      } catch (error) {
        console.error("Erro geral na autenticação:", error);
        resolve(null);
      }
    });
  };

  const adResponse = await loginAD();
  console.log("Token recebido:", adResponse ? "Sim (tamanho: " + adResponse.length + ")" : "Não");

  if (adResponse) {
    const accountLoggedIn = instance.getAllAccounts()[0];

    // Salvar no localStorage
    try {
      console.log("Salvando token...");

      // Sem JSON.stringify para o token direto
      localStorage.setItem("azureToken", adResponse);

      // Com JSON.stringify para outros formatos
      localStorage.setItem("accessToken", JSON.stringify(adResponse));
      localStorage.setItem("userLoggedIn", JSON.stringify(accountLoggedIn));
      localStorage.setItem("session", JSON.stringify(adResponse));

      console.log("Token salvo com sucesso!");

      // Comentando esta linha para evitar loop infinito
      // window.location.reload();

      // NOVO: Forçar registro do user logo após obter o token
      console.log("Realizando registro direto após autenticação Azure AD...");

      // Tentar com várias URLs possíveis para o registro
      const urlsToTry = [
        'https://localhost:44354/api/user/register',
        'https://localhost:44354/api/user/fallback-register',
        `${process.env.REACT_APP_WEBAPI_URL}/api/user/register`,
        `${process.env.REACT_APP_WEBAPI_URL}/api/user/fallback-register`,
        '/api/user/register',
        '/api/user/fallback-register',
        'api/user/register',
        'api/user/fallback-register'
      ];

      console.log("URLs a testar:", urlsToTry);

      // Testar cada URL até obter sucesso
      let registrationSuccessful = false;

      for (const url of urlsToTry) {
        if (registrationSuccessful) break;

        try {
          console.log(`A tentar registar com URL: ${url}`);
          console.log('Token sendo enviado:', adResponse ? 'Presente' : 'Ausente');

          const registrationResponse = await fetch(url, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${adResponse}`,
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            credentials: 'include',
            mode: 'cors'
          });

          console.log(`Resposta do servidor para ${url}:`, registrationResponse.status);

          const registrationData = await registrationResponse.json();
          console.log(`Resultado do registro com URL ${url}:`, registrationData);

          if (registrationData && !registrationData.error) {
            console.log(`Registro realizado com sucesso usando URL: ${url}`);
            registrationSuccessful = true;
          } else {
            console.error(`Erro no registro com URL ${url}:`, registrationData);
          }
        } catch (registrationError) {
          console.error(`Erro ao registar user com URL ${url}:`, registrationError);
        }
      }
    } catch (error) {
      console.error("Erro ao salvar token:", error);
    }
  } else {
    console.log("Nenhum token recebido, aguardando redirecionamento do Azure AD...");
  }

  return adResponse;
};
