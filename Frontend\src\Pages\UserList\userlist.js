import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { LinearProgress } from "@mui/material";
import UserTable from "./UserTable";
import { filtersAPI } from "../../API/filtersAPI";
import useFilters from "../../Context/Hooks/useFilters";
import { getUsers } from "../../Services/userService";
import { useTranslation } from "react-i18next";

const UserList = () => {
    const { t } = useTranslation();
  useEffect(() => {
    document.title = `${t('titles:userlist')} | Portal Cloud Services`;
  }, []);

  const [userList, setUserList] = useState([]);
  const [isLoading, setLoading] = useState(true);

  //--Filters--
  const [customFiltersList, setCustomFiltersList] = useState([]);
  const { filters } = useFilters();
  const [setSelectedCustomFilters] = useState(
    filters.PROFILE && filters.PROFILE.length > 0 ? filters.PROFILE : []
  );

  /*
  API call to get filters:
      params: context
  */
  const getAvailableFilters = useCallback(async () => {
    try {
      const _customFiltersList = await filtersAPI.getAvailableFilters({
        context: "PROFILE",
      });

      if (_customFiltersList && !_customFiltersList.error) {
        setCustomFiltersList(_customFiltersList);
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  /*
  API call to get data to populate table:
      params: props (filters)
  */
  const getUserList = useCallback(async () => {
    setLoading(true);
    const users = await getUsers();
    setLoading(false);

    if (users.length > 0) {
      setUserList(users);
    } else {
      toast.error("Erro ao buscar users");
    }
  }, []);

  /*
  Quando há múltiplos roleIDs, verifica se um dos itens corresponde ao filtro.
  */
  const getFromMultipleID = (element, ids) => {
    const elementCollection = element.split(",");
    return ids.some(id => elementCollection.includes(id));
  };

  const value = filters?.PROFILE?.[0]?.value;

  const handleFilter = () => {
    if (filters?.PROFILE?.[0]?.elementID === "CheckBox") {
      setUserList(prevList =>
        prevList.filter(element => getFromMultipleID(element.roleIDs, value)));
    } else if (filters?.PROFILE?.[0]?.condition === "eq") {
      const selected = filters?.PROFILE?.[0].selectedValues?.[0]?.value;
      setUserList(prevList => prevList.filter(element => element.roleIDs === selected));
    } else {
      setUserList(prevList =>
        prevList.filter(roles => roles.roleNames.toLowerCase().includes(value)));
    }
  };

  // Função para atualizar a lista de usuários quando novos usuários forem adicionados
  const handleUserAdded = useCallback(() => {
    getUserList();
  }, [getUserList]);

  useEffect(() => {
    getAvailableFilters();
  }, [getAvailableFilters]);

  useEffect(() => {
    if (!filters?.PROFILE || filters.PROFILE.length === 0) {
      getUserList();
    } else {
      handleFilter();
    }
  }, [filters, getUserList]);

  return (
    <>
      {isLoading && (
        <LinearProgress
          variant="indeterminate"
          sx={{ width: "100%", position: "absolute", top: "64px", left: 0 }}
        />
      )}
      <UserTable
        userList={userList || []}
        isLoading={isLoading}
        customFiltersList={customFiltersList}
        onSelectedCustomFilters={setSelectedCustomFilters}
        onUserAdded={handleUserAdded}
      />
    </>
  );
};

export default UserList;
