﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Dapper" Version="2.1.15" />
    <PackageReference Include="Dapper.Contrib" Version="2.0.78" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.WebUtilities" Version="2.2.0" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.35.3" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageReference Include="RestSharp" Version="110.2.0" />
    <PackageReference Include="RestSharp.Serializers.NewtonsoftJson" Version="110.2.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RnD.BackEnd.Domain\RnD.BackEnd.Domain.csproj" />
  </ItemGroup>

</Project>
