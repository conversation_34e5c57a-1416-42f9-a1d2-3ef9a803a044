using System;
using System.Collections.Generic;

namespace RnD.BackEnd.API.Models
{
    /// <summary>
    /// Modelo para dados extraídos de PDFs Crayon para testes (sem persistência na BD)
    /// </summary>
    public class CrayonExtractedData
    {
        /// <summary>
        /// Nome do cliente (normalmente "BI4ALL Consultores de Gestão, Lda")
        /// </summary>
        public string Cliente { get; set; }

        /// <summary>
        /// Descrição/código do serviço ou produto
        /// </summary>
        public string DescricaoServico { get; set; }

        /// <summary>
        /// Data de início do período de serviço
        /// </summary>
        public DateTime DataInicio { get; set; }

        /// <summary>
        /// Data de fim do período de serviço
        /// </summary>
        public DateTime DataFim { get; set; }

        /// <summary>
        /// Preço do produto/serviço (sem IVA)
        /// </summary>
        public decimal Preco { get; set; }

        /// <summary>
        /// Valor do IVA
        /// </summary>
        public decimal Iva { get; set; }

        /// <summary>
        /// Total (Preço + IVA)
        /// </summary>
        public decimal Total { get; set; }

        /// <summary>
        /// Indica se a extração foi bem-sucedida
        /// </summary>
        public bool ExtraidoComSucesso { get; set; }

        /// <summary>
        /// Mensagem de erro caso a extração falhe
        /// </summary>
        public string MensagemErro { get; set; }
    }

    /// <summary>
    /// Resultado da extração de um PDF Crayon
    /// </summary>
    public class CrayonPDFExtractionResult
    {
        /// <summary>
        /// Nome do arquivo PDF
        /// </summary>
        public string NomeArquivo { get; set; }

        /// <summary>
        /// Indica se o PDF foi identificado como Crayon
        /// </summary>
        public bool IsCrayonPDF { get; set; }

        /// <summary>
        /// Texto bruto extraído do PDF
        /// </summary>
        public string TextoBruto { get; set; }

        /// <summary>
        /// Lista de dados extraídos (pode haver múltiplos produtos/serviços por PDF)
        /// </summary>
        public List<CrayonExtractedData> DadosExtraidos { get; set; } = new List<CrayonExtractedData>();

        /// <summary>
        /// Indica se houve erro na extração
        /// </summary>
        public bool TemErro { get; set; }

        /// <summary>
        /// Mensagem de erro geral
        /// </summary>
        public string MensagemErro { get; set; }
    }
}
