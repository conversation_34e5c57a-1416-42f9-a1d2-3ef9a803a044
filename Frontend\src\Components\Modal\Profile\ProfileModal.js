import { useState } from 'react';
import PropTypes from 'prop-types';
import { TextField } from '@mui/material';
import Modal from '../ModalTemplate';

const ProfileModal = (props) => {
    const { onClose, open } = props;
    const [name, setName] = useState("");
    const [username, setUsername] = useState("");

    function handleChange(e, isName) {
        if (e) {
            if (isName === true) {
                setName(e.target.value);
            } else {
                setUsername(e.target.value);
            }
        }
    }

    return (
        <Modal
            size="md"
            onClose={onClose}
            onCancel={onClose}
            onConfirm={open}
            open={open}
            title="Novo Perfil"
            content={
                <>
                    <TextField
                        fullWidth
                        label="Nome"
                        name="processName"
                        onChange={(e) => handleChange(e, true)}
                        value={name}
                        variant="outlined"
                    />
                    <TextField
                        fullWidth
                        label="Username"
                        name="processName"
                        onChange={(e) => handleChange(e, false)}
                        value={username}
                        variant="outlined"
                        sx={{
                            mt: 3
                        }}
                    />
                </>
            }
        />
    );
};

ProfileModal.propTypes = {
    onClose: PropTypes.func,
    open: PropTypes.bool.isRequired,
};

export default ProfileModal;
