import React, { useRef, useState, useEffect, useCallback } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Badge,
  Box,
  Button,
  IconButton,
  Popover,
  Tooltip,
  Typography,
  Link,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  useTheme
} from '@mui/material';
import {
  Notifications as NotificationsIcon
} from "@mui/icons-material";
import toast from 'react-hot-toast';

const notificationAPI = false;

const NotificationsPopover = () => {
  const theme = useTheme();
  const anchorRef = useRef(null);
  const [open, setOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [newNotifications, setNewNotifications] = useState(0);
  const [visible, setVisible] = useState(false);

  const getNotifications = useCallback(async () => {
    try {
      const result = await notificationAPI.getNotifications();
      if (!result.error) {
        setNotifications(result.value.notifications);
        setNewNotifications(result.value.newNotifications);
      } else {
        toast.error(result.exceptionMessages.messages[0].description);
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  const handleMarkAllAsRead = async () => {
    const notificationIDs = [];

    notifications.filter(x => !x.isRead).forEach(x => notificationIDs.push(x.notificationID));
    try {
      const data = {
        notificationID: notificationIDs,
      };
      const result = await notificationAPI.saveNotificationRead(data);

      if (result.error) {
        toast.error(result.error.exceptionMessages[0].description);
      } else {
        getNotifications();
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (notificationAPI) {
      getNotifications();
      setVisible(true)
    }
  }, [getNotifications]);

  return (
    <>
      {visible && <Tooltip title="Notifications">
        <IconButton
          color="inherit"
          ref={anchorRef}
          onClick={handleOpen}
        >
          <Badge
            color="error"
            badgeContent={newNotifications}
          >
            <NotificationsIcon
              sx={{
                color: 'primary.main'
              }}
            />
          </Badge>
        </IconButton>
      </Tooltip>}
      <Popover
        anchorEl={anchorRef.current}
        anchorOrigin={{
          horizontal: 'center',
          vertical: 'bottom'
        }}
        onClose={handleClose}
        open={open}
        PaperProps={{
          sx: { width: 320 }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography
            color="textPrimary"
            variant="h1"
          >
            Notificações
          </Typography>
        </Box>
        {notifications.length === 0
          ? (
            <Box sx={{ p: 2 }}>
              <Typography
                color="textPrimary"
                variant="subtitle2"
              >
                Não existem notificações
              </Typography>
            </Box>
          )
          : (
            <>
              {
                notificationAPI && <List
                  disablePadding
                  style={{ overflow: 'auto' }}
                >
                  {notifications.map((notification, notification_i) => {
                    return (
                      <React.Fragment
                        key={"notification_" + notification_i}
                      >
                        {notification.notificationActionType === 'Link' && (
                          <ListItem
                            divider
                            sx={{
                              backgroundColor: notification.isRead ? 'white' : theme.palette.grey[300]
                            }}
                            component={RouterLink}
                            to={notification.notificationAction}
                            onClick={handleClose}
                          >
                            <ListItemAvatar>
                              <Avatar
                                sx={{
                                  backgroundColor: 'primary.main',
                                  color: 'primary.contrastText'
                                }}
                              />
                            </ListItemAvatar>
                            <ListItemText
                              primary={(
                                <Link
                                  color="textPrimary"
                                  sx={{ cursor: 'pointer' }}
                                  underline="none"
                                  variant="subtitle2"
                                >
                                  {notification.notificationText}
                                </Link>
                              )}
                              secondary={notification.notificationType}
                            />
                          </ListItem>
                        )}
                        {notification.notificationActionType === 'Mensagem' && (
                          <ListItem
                            divider
                            sx={{
                              backgroundColor: notification.isRead ? 'white' : theme.palette.grey[300]
                            }}
                          >
                            <ListItemAvatar>
                              <Avatar
                                sx={{
                                  backgroundColor: 'primary.main',
                                  color: 'primary.contrastText'
                                }}
                              />
                            </ListItemAvatar>
                            <ListItemText
                              primary={(
                                <Link
                                  color="textPrimary"
                                  sx={{ cursor: 'pointer' }}
                                  underline="none"
                                  variant="subtitle2"
                                >
                                  {notification.notificationText}
                                </Link>
                              )}
                              secondary={notification.notificationType}
                            />
                          </ListItem>
                        )}
                      </React.Fragment>
                    );
                  })}
                </List>
              }
              {
                notificationAPI && <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center'
                  }}
                  my={1}
                >
                  <Button
                    color="primary"
                    size="small"
                    variant="text"
                    onClick={handleMarkAllAsRead}
                  >
                    Marcar todas como lidas
                  </Button>
                  <Button
                    color="primary"
                    size="small"
                    variant="text"
                    component={RouterLink}
                    to="/dashboard/NotificationList"
                  >
                    Ver todas as notificações
                  </Button>
                </Box>
              }
            </>
          )}
      </Popover>
    </>
  );
};

export default NotificationsPopover;
