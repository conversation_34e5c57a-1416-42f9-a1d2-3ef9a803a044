﻿using System;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Workflow instance task DTO class
    /// </summary>
    public class WorkflowInstanceTaskDto
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public Guid? Id { get; set; }

        /// <summary>
        /// Gets or sets the user identifier.
        /// </summary>
        /// <value>
        /// The user identifier.
        /// </value>
        public string UserId { get; set; }

        /// <summary>
        /// Gets or sets the task status.
        /// </summary>
        /// <value>
        /// The task status.
        /// </value>
        public string TaskStatus { get; set; }

        /// <summary>
        /// Represents an event that is raised when a task either successfully or unsuccessfully completes.
        /// </summary>
        /// <value>
        ///   <c>true</c> if [task completed]; otherwise, <c>false</c>.
        /// </value>
        public bool TaskCompleted { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [task activated].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [task activated]; otherwise, <c>false</c>.
        /// </value>
        public bool TaskActivated { get; set; }

        /// <summary>
        /// Gets or sets the comments.
        /// </summary>
        /// <value>
        /// The comments.
        /// </value>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the system modify date.
        /// </summary>
        /// <value>
        /// The system modify date.
        /// </value>
        public DateTimeOffset SysModifyDate { get; set; }
    }
}
