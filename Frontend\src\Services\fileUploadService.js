import axios from "axios";

// URL base da API
const API_BASE_URL = `${process.env.REACT_APP_WEBAPI_URL}/api`;

/**
 * Faz upload de um arquivo PDF para o container consumo-pdf
 * @param {File} file - Arquivo PDF a ser enviado
 * @returns {Promise} Resposta do servidor
 */
export const uploadPDF = async (file) => {
  try {
    // Valida se o arquivo é PDF
    if (!file.type.includes('pdf')) {
      throw new Error('O arquivo deve ser um PDF');
    }

    const formData = new FormData();
    formData.append('file', file);

    console.log('Iniciando upload de PDF:', file.name);
    const response = await axios.post(`${API_BASE_URL}/ConsumoPDF/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    console.log('Upload de PDF realizado com sucesso:', response.data);
    return response.data;
  } catch (error) {
    console.error('Erro ao fazer upload de PDF:', error);
    throw error;
  }
};

/**
 * Faz upload de um arquivo Excel para o container consumo-excel-azure
 * @param {File} file - Arquivo Excel a ser enviado
 * @returns {Promise} Resposta do servidor
 */
export const uploadExcelAzure = async (file) => {
  try {
    // Valida se o arquivo é Excel
    if (!file.type.includes('excel') && !file.type.includes('spreadsheetml') && !file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      throw new Error('O arquivo deve ser um Excel (.xlsx ou .xls)');
    }

    const formData = new FormData();
    formData.append('file', file);

    console.log('Iniciando upload de Excel Azure:', file.name);
    // Endpoint específico para carregar Excel Azure
    const response = await axios.post(`${API_BASE_URL}/ConsumoExcelAzure/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    console.log('Upload de Excel Azure realizado com sucesso:', response.data);
    return response.data;
  } catch (error) {
    console.error('Erro ao fazer upload de Excel Azure:', error);
    throw error;
  }
};

/**
 * Faz upload de um arquivo Excel para o container consumo-excel (reseller)
 * @param {File} file - Arquivo Excel a ser enviado
 * @returns {Promise} Resposta do servidor
 */
export const uploadExcelReseller = async (file) => {
  try {
    // Valida se o arquivo é Excel
    if (!file.type.includes('excel') && !file.type.includes('spreadsheetml') && !file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      throw new Error('O arquivo deve ser um Excel (.xlsx ou .xls)');
    }

    const formData = new FormData();
    formData.append('file', file);

    console.log('Iniciando upload de Excel Reseller:', file.name);
    // Endpoint específico para carregar Excel Reseller
    const response = await axios.post(`${API_BASE_URL}/Consumo/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    console.log('Upload de Excel Reseller realizado com sucesso:', response.data);
    return response.data;
  } catch (error) {
    console.error('Erro ao fazer upload de Excel Reseller:', error);
    throw error;
  }
};

/**
 * Força a releitura dos containers (disparar processamento)
 * @returns {Promise} Resposta do servidor
 */
export const forceContainerReload = async () => {
  try {
    console.log('Forçando releitura dos containers...');
    // Para forçar a releitura, faremos um GET para o endpoint FromExcel
    const response = await axios.get(`${API_BASE_URL}/Consumo/FromExcel`);
    
    // Depois também forçamos a leitura dos outros containers se necessário
    // Este seria um ponto para adicionar chamadas para outros endpoints que forçam a leitura
    
    console.log('Releitura dos containers iniciada com sucesso');
    return response.data;
  } catch (error) {
    console.error('Erro ao forçar releitura dos containers:', error);
    throw error;
  }
}; 