import { Toaster } from 'react-hot-toast';
import { CssBaseline, ThemeProvider } from '@mui/material';
import RTL from './Components/RTL';
import useSettings from './Context/Hooks/useSettings';
import { createCustomTheme } from './Assets/theme';
import SettingsDrawer from "./Components/SettingsDrawer";
import { Routes } from './Routes/routes'
import i18n from './i18n';
import { I18nextProvider } from 'react-i18next';
import { useEffect } from 'react';
import axios from 'axios';
import { initializeAuthentication } from './API/userAPI';

function App() {
  const { settings } = useSettings();
  const theme = createCustomTheme({
    direction: settings.direction,
    responsiveFontSizes: settings.responsiveFontSizes,
    roundedCorners: settings.roundedCorners,
    theme: settings.theme,
    language: i18n.language
  });

  window.platform_name = process.env.REACT_APP_PLATFORM_NAME;

  // iniciar auth
  useEffect(() => {
    const initAuth = async () => {
      try {
        console.log('App: Inicializando autenticação...');
        const result = await initializeAuthentication();

        if (result.authenticated) {
          console.log('App: user autenticado com sucesso');
        } else if (result.error) {
          console.error('App: Erro na autenticação:', result.message);
        } else {
          console.log('App: user não autenticado');
        }
      } catch (error) {
        console.error('App: Erro ao iniciar auth:', error);
      }
    };

    initAuth();
  }, []);

  // Configurar interceptores do Axios
  useEffect(() => {
    // Função para obter o token mais atual
    const getAuthToken = () => {
      const azureToken = localStorage.getItem('azureToken');
      const session = localStorage.getItem('session');
      const accessToken = localStorage.getItem('accessToken');

      // Log detalhado dos tokens disponíveis
      console.log('Tokens disponíveis:', {
        azureToken: azureToken ? `${azureToken.substring(0, 15)}...` : null,
        session: session ? 'Presente' : null,
        accessToken: accessToken ? 'Presente' : null
      });

      // Determinar qual token usar
      if (azureToken) return azureToken;

      if (session) {
        try {
          const parsed = JSON.parse(session);
          return parsed;
        } catch (e) {
          return session;
        }
      }

      if (accessToken) {
        try {
          const parsed = JSON.parse(accessToken);
          return parsed;
        } catch (e) {
          return accessToken;
        }
      }

      return null;
    };

    // Interceptor para requisições
    const requestInterceptor = axios.interceptors.request.use(
      config => {
        // Verificar se temos um token e adicionar ao cabeçalho
        const token = getAuthToken();

        if (token && !config.headers.Authorization) {
          config.headers.Authorization = `Bearer ${token}`;
          console.log('Token adicionado à requisição:', config.url);
        } else if (!token) {
          console.warn('Nenhum token disponível para a requisição:', config.url);
        }

        console.log(`[Requisição] ${config.method?.toUpperCase()} ${config.url}`, {
          headers: {
            ...config.headers,
            Authorization: config.headers.Authorization ?
              `${config.headers.Authorization.substring(0, 20)}...` : null
          },
          data: config.data
        });

        return config;
      },
      error => {
        console.error('[Erro de Requisição]', error);
        return Promise.reject(error);
      }
    );

    // Interceptor para respostas
    const responseInterceptor = axios.interceptors.response.use(
      response => {
        console.log(`[Resposta] ${response.status} ${response.config.url}`, {
          data: response.data
        });
        return response;
      },
      error => {
        // Verificar se é erro 401 (não autorizado)
        if (error.response && error.response.status === 401) {
          console.error('Erro 401 - Não autorizado. Verificando token...');
          const token = getAuthToken();
          console.log('Token disponível?', !!token);

          // Se não tiver token, talvez seja necessário redirecionar para login
          if (!token && window.location.pathname !== '/error/401') {
            console.log('Redirecionando para página de erro 401...');
          }
        }

        console.error('[Erro de Resposta]', error.response || error);
        return Promise.reject(error);
      }
    );

    // Limpar interceptores quando o componente for desmontado
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <RTL direction={settings.direction}>
        <CssBaseline />
        <Toaster
          toastOptions={{
            success: {
              style: {
                background: theme.palette.success.main,
                color: theme.palette.success.contrastText
              },
              iconTheme: {
                primary: theme.palette.success.contrastText,
                secondary: theme.palette.success.main,
              }
            },
            error: {
              style: {
                background: theme.palette.error.main,
                color: theme.palette.error.contrastText
              },
              iconTheme: {
                primary: theme.palette.error.contrastText,
                secondary: theme.palette.error.main,
              },
            },
          }}
          position="top-right"
        />
        <SettingsDrawer />
        <I18nextProvider i18n={i18n}>
          {Routes()}
        </I18nextProvider>
      </RTL>
    </ThemeProvider>
  );
}

export default App;
