﻿namespace RnD.BackEnd.Domain.BusinessMessages
{
    public static class VehicleMessages
    {
        public const string Error_Get = "Error getting vehicle with id {0}.";
        public const string Error_Delete = "Error deleting vehicle.";
        public const string Error_Update = "Error updating vehicle.";
        public const string Error_Create = "Error creating vehicle.";
        public const string Error_List = "Error getting list of vehicles.";
        public const string Not_Found = "Vehicle not found";
        public const string ModelName_Mandatory = "Model name is mandatory";
        public const string Id_Mandatory = "Id is mandatory";
        public const string Brand_Mandatory = "Brand is mandatory";
        public const string BrandId_Mandatory = "Brand Id is mandatory";
        public const string Id_Mismatch = "Id must match";
        public const string ETag_Mismatch = "The vehicle has been modified outside this context";
    }
}
