/*eslint-disable react/no-danger*/
import React, { useState, useEffect } from 'react';
import themeConfig from '../Assets/theme/themeConfig';
import PropTypes from "prop-types";

//SVG Theme configuration

export const Themed_SVG = ({ path }) => {
    const [inner_svg, setInner_svg] = useState('');

    const getData = async () => {
        const svg_query = await fetch(path);
        let _inner_svg = await svg_query.text();

        _inner_svg = _inner_svg.replace('%color1%', themeConfig.themeColor1);
        _inner_svg = _inner_svg.replace('%color2%', themeConfig.themeColor2);
        _inner_svg = _inner_svg.replace('%color3%', themeConfig.themeColor3);
        setInner_svg(_inner_svg);
    }

    useEffect(() => {
        getData();
    });

    return <div dangerouslySetInnerHTML={{ __html: inner_svg }} />
}

export const themed_SVG_string = async (path_to_svg) => {
    const svg_query = await fetch(path_to_svg);
    let _inner_svg = await svg_query.text();

    _inner_svg = _inner_svg.replace('%color1%', themeConfig.themeColor1);
    _inner_svg = _inner_svg.replace('%color2%', themeConfig.themeColor2);
    _inner_svg = _inner_svg.replace('%color3%', themeConfig.themeColor3);
    return _inner_svg;
}

Themed_SVG.defaultProps = {
    path: "",
};

Themed_SVG.propTypes = {
    path: PropTypes.string,
};

export default Themed_SVG;
