﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RnD.BackEnd.API.Models
{
    public class UserPermission
    {
        [Key, Column(Order = 0)]
        public int UserID { get; set; }
        
        [Key, Column(Order = 1)]
        public int PermissionID { get; set; }
        
        [ForeignKey("UserID")]
        public User User { get; set; }
        
        [ForeignKey("PermissionID")]
        public Permission Permission { get; set; }
    }
}
