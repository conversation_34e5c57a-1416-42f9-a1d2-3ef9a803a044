{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"tenantId": {"type": "string", "defaultValue": "[subscription().tenantId]", "metadata": {"description": "Specifies the Azure Active Directory tenant ID that should be used for authenticating requests to the key vault. Get it by using Get-AzSubscription cmdlet."}}, "location": {"defaultValue": "[resourceGroup().location]", "type": "string", "metadata": {"description": "Provide the location for resources."}}, "app-service-Name": {"type": "string", "minLength": 1}, "app-service-SkuName": {"type": "string", "defaultValue": "F1", "allowedValues": ["F1", "D1", "B1", "B2", "B3", "S1", "S2", "S3", "P1", "P2", "P3", "P4"], "metadata": {"description": "Describes plan's pricing tier and capacity. Check details at https://azure.microsoft.com/en-us/pricing/details/app-service/"}}, "jobs-Name": {"type": "string", "minLength": 1}, "backend-Name": {"type": "string", "minLength": 1}, "frontend-Name": {"type": "string", "minLength": 1}, "always-on": {"type": "bool", "defaultValue": true, "allowedValues": [true, false], "metadata": {"description": "Specifies whether the App Services should have AlwaysOn enabled."}}, "keyvault-resourcegroup": {"type": "string", "minLength": 1}, "key-vault-Name": {"type": "string", "minLength": 1}, "keyVault-secretsPermissions-app-services": {"type": "array", "defaultValue": ["Get"], "metadata": {"description": "Specifies the permissions to secrets in the vault. Valid values are: all, get, list, set, delete, backup, restore, recover, and purge."}}}, "variables": {}, "resources": [{"name": "[parameters('app-service-Name')]", "type": "Microsoft.Web/serverfarms", "location": "[parameters('location')]", "apiVersion": "2020-12-01", "sku": {"name": "[parameters('app-service-SkuName')]"}, "dependsOn": [], "tags": {"displayName": "app-service"}, "properties": {"name": "[parameters('app-service-Name')]", "numberOfWorkers": 1}}, {"name": "[parameters('jobs-Name')]", "type": "Microsoft.Web/sites", "location": "[parameters('location')]", "apiVersion": "2020-12-01", "dependsOn": ["[resourceId('Microsoft.Web/serverfarms', parameters('app-service-Name'))]"], "identity": {"type": "SystemAssigned"}, "tags": {"[concat('hidden-related:', resourceId('Microsoft.Web/serverfarms', parameters('app-service-Name')))]": "Resource", "displayName": "jobs"}, "properties": {"name": "[parameters('jobs-Name')]", "serverFarmId": "[resourceId('Microsoft.Web/serverfarms', parameters('app-service-Name'))]", "httpsOnly": true}}, {"type": "Microsoft.Web/sites/config", "apiVersion": "2020-12-01", "name": "[concat(parameters('jobs-Name'), '/web')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Web/sites', parameters('jobs-Name'))]"], "tags": {"displayName": "jobs-config"}, "properties": {"alwaysOn": "[parameters('always-on')]"}}, {"name": "[parameters('backend-Name')]", "type": "Microsoft.Web/sites", "location": "[parameters('location')]", "apiVersion": "2020-12-01", "dependsOn": ["[resourceId('Microsoft.Web/serverfarms', parameters('app-service-Name'))]"], "identity": {"type": "SystemAssigned"}, "kind": "api", "tags": {"[concat('hidden-related:', resourceId('Microsoft.Web/serverfarms', parameters('app-service-Name')))]": "Resource", "displayName": "backend"}, "properties": {"name": "[parameters('backend-Name')]", "serverFarmId": "[resourceId('Microsoft.Web/serverfarms', parameters('app-service-Name'))]", "httpsOnly": true}}, {"type": "Microsoft.Web/sites/config", "apiVersion": "2020-12-01", "name": "[concat(parameters('backend-Name'), '/web')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Web/sites', parameters('backend-Name'))]"], "tags": {"displayName": "backend-config"}, "properties": {"alwaysOn": "[parameters('always-on')]"}}, {"name": "[parameters('frontend-Name')]", "type": "Microsoft.Web/sites", "location": "[parameters('location')]", "apiVersion": "2020-12-01", "dependsOn": ["[resourceId('Microsoft.Web/serverfarms', parameters('app-service-Name'))]"], "identity": {"type": "SystemAssigned"}, "tags": {"[concat('hidden-related:', resourceId('Microsoft.Web/serverfarms', parameters('app-service-Name')))]": "Resource", "displayName": "frontend"}, "properties": {"name": "[parameters('frontend-Name')]", "serverFarmId": "[resourceId('Microsoft.Web/serverfarms', parameters('app-service-Name'))]", "httpsOnly": true}}, {"type": "Microsoft.Web/sites/config", "apiVersion": "2020-12-01", "name": "[concat(parameters('frontend-Name'), '/web')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Web/sites', parameters('frontend-Name'))]"], "tags": {"displayName": "frontend-config"}, "properties": {"alwaysOn": "[parameters('always-on')]"}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2019-05-01", "name": "keyvault-add-access-policies", "resourceGroup": "[parameters('keyvault-resourcegroup')]", "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": [{"type": "Microsoft.KeyVault/vaults/accessPolicies", "name": "[concat(parameters('key-vault-Name'), '/add')]", "apiVersion": "2016-10-01", "properties": {"accessPolicies": [{"tenantId": "[parameters('tenantId')]", "objectId": "[reference(concat('Microsoft.Web/sites/', parameters('backend-Name')), '2018-11-01', 'Full').identity.principalId]", "permissions": {"secrets": "[parameters('keyVault-secretsPermissions-app-services')]"}}, {"tenantId": "[parameters('tenantId')]", "objectId": "[reference(concat('Microsoft.Web/sites/', parameters('jobs-Name')), '2018-11-01', 'Full').identity.principalId]", "permissions": {"secrets": "[parameters('keyVault-secretsPermissions-app-services')]"}}]}, "tags": {"displayName": "keyvault-add-access-policies"}}]}}}]}