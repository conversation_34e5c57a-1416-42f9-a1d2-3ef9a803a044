﻿using System;

namespace RnD.BackEnd.API.Models
{
    public class LogProcessamento
    {
        public int Id { get; set; }
        public int FileID { get; set; }
        public string Mensagem { get; set; }
        public string TipoLog { get; set; } // Info, Aviso, Erro
        public DateTime DataRegistro { get; set; } = DateTime.Now;
    }

    // Enum para facilitar o uso dos tipos de log
    public enum TipoLog
    {
        Info,
        Aviso,
        Erro
    }
}
