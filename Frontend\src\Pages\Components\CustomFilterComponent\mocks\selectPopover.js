const getSelectPopover = () => {
    return `
    //CUSTOMFILTERSELECTPOPOVER
    import React, { useState } from 'react';
    import PropTypes from 'prop-types';
    import { Button, Box, Grid, Autocomplete, TextField, Popover, Typography, Skeleton, FormControl, FormGroup, FormControlLabel, Checkbox, Divider } from '@mui/material';
    import { DesktopDatePicker } from '@mui/x-date-pickers';
    import CustomMuiSelect from '../CustomMuiSelect'; //Import customMuiSelect Component from your src folder
import makeStyles from '@emotion/styled';
import { useTranslation } from 'react-i18next';
    import moment from 'moment';
    import { filtersAPI } from '../../API/filtersAPI'; //Import your API data;

    const useStyles = makeStyles(() => ({
        filterBox: {
            overflow: "auto",
                maxHeight: "300px"
            }
        }));

    const CustomFilterSelectPopover = ({ customFiltersList, addCustomFilter, deleteCustomFilter, resetCustomFilters, oid, context, ...rest }) => {
        const [selectedFilter, setSelectedFilter] = useState(false);
        const [selectedTypeDetails, setSelectedTypeDetails] = useState(false);
        const [selectedValues, setSelectedValues] = useState([]);
        const [selectedCondition, setSelectedCondition] = useState();
        const [fieldSearch, setFieldSearch] = useState('');
        const [isLoading, setIsLoading] = useState(false);
        const classes = useStyles();
        const { t } = useTranslation();

        //Conditional search options
        const APIGetType = (type) => {
            const availableTypes = [
                {
                    type: "TextField",
                        conditions: [
                        {
                            label: t('common:conditions.contains'),
                            id: "contains"
                        },
                        {
                            label: t('common:conditions.sw'),
                            id: "sw"
                        },
                        {
                            label: t('common:conditions.ew'),
                            id: "ew"
                        },
                        {
                            label: t('common:conditions.eq'),
                            id: "eq"
                        }
                    ]
                },
                {
                    type: "NumberField",
                        conditions: [
                        {
                            label: t('common:conditions.gt'),
                            id: "gt"
                        },
                        {
                            label: t('common:conditions.lt'),
                            id: "lt"
                        },
                        {
                            label: t('common:conditions.eq'),
                            id: "eq"
                        }
                    ]
                },
                {
                    type: "DateField",
                        conditions: [
                        {
                            label: t('common:conditions.gt'),
                            id: "gt"
                        },
                        {
                            label: t('common:conditions.lt'),
                            id: "lt"
                        },
                        {
                            label: t('common:conditions.eq'),
                            id: "eq"
                        }
                    ]
                },
                {
                    type: "CheckBoxField"
                },
                {
                    type: "SelectList"
                }
            ];
                const foundType = availableTypes.find(x => x.type === type) || { conditions: [] };
                return foundType;
            }

            /*const handleDelete = (id) => {
                const _selectedValues = [...selectedValues];
                const matchIndex = _value.indexOf(id);
                if (matchIndex) {
                    _selectedValues.splice(matchIndex, 1);
                    setSelectedValues([selectedValues]);
                }
            }*/

            //Set the selected value
            const handleSelectValues = (val) => {
                setSelectedValues(val);
            }

            const resetValues = () => {
                setSelectedTypeDetails(false);
                setSelectedFilter(false);
                setSelectedCondition();
                setSelectedValues([]);
            }

            //After clicking submit, a chip will be added, containing the element label and condition label, if any
            const handleSubmit = () => {
                const conditionLabel = selectedCondition && selectedTypeDetails.conditions ? selectedTypeDetails.conditions.find(x => x.id === selectedCondition.id).label : false;
                console.log(selectedValues);
                addCustomFilter({
                    elementID: selectedFilter.elementID,
                    elementLabel: selectedFilter.elementLabel,
                    condition: selectedCondition ? selectedCondition.id : 'eq',
                    conditionLabel: conditionLabel,
                    selectedValues: selectedValues,
                    value: Array.isArray(selectedValues) && selectedValues[0] && selectedValues[0].value ? selectedValues.map(x => x.value) : Array.isArray(selectedValues) ? selectedValues : [selectedValues]
                });
                resetValues();
                rest.onClose();
            }

            const handleOnClose = () => {
                resetValues();
                rest.onClose();
            }

            const handleChangeCustomFilter = async (e, val) => {
                resetValues();
                if (!val) {
                    return;
                }
                setSelectedFilter(val);
                setIsLoading(true);
                const typeDetail = await filtersAPI.getAvailableFilterData({ context: context, id: oid, key: val.elementID });
                const typeDetail_conditions = APIGetType(val?.elementType);
                setIsLoading(false);
                if (typeDetail_conditions && typeDetail_conditions.conditions) {
                    setSelectedCondition(typeDetail_conditions.conditions[0])
                }
                setSelectedTypeDetails({ ...typeDetail.response, ...typeDetail_conditions });
            }

            const getValueComponent = () => {
                let matchComponent = null;
                const filteredSelectedTypeDetails = selectedTypeDetails?.value?.filter(selectedDetail => { return fieldSearch === "" || selectedDetail?.elementLabel?.toLowerCase().indexOf(fieldSearch?.toLowerCase()) >= 0 }) || [];

                switch (selectedTypeDetails.type) {
                    case 'DateField':
                        matchComponent = <DesktopDatePicker
                            inputFormat="dd-MM-yyyy"
                            onChange={(e) => handleSelectValues(moment(e.target.value))}
                            renderInput={(params) => <TextField
                                fullWidth
                                {...params}
                                helperText={false}
                                error={false}
                            />}
                        />
                        break;
                    case 'CheckBoxField':
                        matchComponent =
                            <>
                                <TextField
                                    label={t('common:tableActions.search')}
                                    sx={{
                                        pb: 2
                                    }}
                                    fullWidth
                                    onChange={(e) => setFieldSearch(e?.target?.value)}
                                />
                                <FormControl
                                    component="fieldset"
                                    sx={{
                                        width: "100%"
                                    }}
                                >
                                    <FormGroup
                                        aria-label="select-all"
                                        row
                                    >
                                        <FormControlLabel
                                            onChange={() => setSelectedValues(selectedValues.length <= 0 ? filteredSelectedTypeDetails : [])}
                                            control={
                                                <Checkbox
                                                    color="primary"
                                                    checked={selectedValues.length === filteredSelectedTypeDetails.length}
                                                    indeterminate={selectedValues.length > 0 && selectedValues.length < filteredSelectedTypeDetails.length}
                                                />
                                            }
                                            color="primary"
                                            label={
                                                <Typography
                                                    variant="h6"
                                                >
                                                    {t('common:tableActions.select_all')}
                                                </Typography>
                                            }
                                            labelPlacement="end"
                                        />
                                    </FormGroup>
                                </FormControl>
                                <Divider
                                    sx={{
                                        mb: 1
                                    }}
                                />
                                <Box
                                    className={classes.filterBox}
                                >
                                    <FormControl
                                        component="fieldset"
                                        sx={{
                                            width: "100%"
                                        }}
                                    >
                                        {
                                            filteredSelectedTypeDetails && filteredSelectedTypeDetails.length > 0 ?
                                                filteredSelectedTypeDetails.map((v, i) => (
                                                    <FormGroup
                                                        key={i}
                                                        aria-label="select"
                                                        row
                                                    >
                                                        <FormControlLabel
                                                            value={v.value || v.label}
                                                            onChange={(e) => {
                                                                const val = e.target.value;
                                                                const _selectedValues = [...selectedValues];
                                                                const match = _selectedValues.findIndex(x => x.value === val);
                                                                if (match >= 0) {
                                                                    _selectedValues.splice(match, 1);
                                                                } else {
                                                                    _selectedValues.push(v);
                                                                }
                                                                handleSelectValues(_selectedValues);
                                                            }}
                                                            control={
                                                                <Checkbox
                                                                    color="primary"
                                                                    checked={selectedValues.find(x => x.value === v.value) !== undefined}
                                                                />
                                                            }
                                                            label={v.elementLabel || ""}
                                                            labelPlacement="end"
                                                        />
                                                    </FormGroup>
                                                ))
                                                :
                                                <Typography
                                                    variant="disabled"
                                                >
                                                    {t('common:tableActions.noResults')}
                                                </Typography>
                                        }
                                    </FormControl>
                                </Box>
                            </>
                        break;
                    case 'SelectList':
                        matchComponent = <Autocomplete
                            getOptionLabel={(option) => option.elementLabel}
                            multiple
                            options={selectedTypeDetails.value}
                            onChange={(e, v) => handleSelectValues(v)}
                            renderInput={(params) => (
                                <TextField
                                    fullWidth
                                    label={t("common:tableActions.select")}
                                    variant="outlined"
                                    {...params}
                                />
                            )}
                        />
                        break;

                    case "TextField":
                    case "NumberField":
                    default:
                        if (selectedTypeDetails.value && selectedCondition && selectedCondition.id === "eq") {
                            matchComponent = <Autocomplete
                                getOptionLabel={(option) => option.value}
                                multiple
                                options={selectedTypeDetails.value}
                                onChange={(e, v) => handleSelectValues(v)}
                                renderInput={(params) => (
                                    <TextField
                                        fullWidth
                                        label={t("common:tableActions.select")}
                                        variant="outlined"
                                        {...params}
                                    />
                                )}
                            />
                        } else {
                            matchComponent = <TextField
                                fullWidth
                                label={t("common:common.value")}
                                type={selectedTypeDetails.elementType === "NumberField" ? "number" : "text"}
                                onChange={e => handleSelectValues(e.target.value)}
                            />
                        }
                        break;
                }
                return matchComponent;
            }

            return (
                <Popover
                    {...rest}
                    onClose={handleOnClose}
                    sx={{
                        ".MuiPopover-paper": {
                            overflow: "initial"
                        }
                    }}
                >
                    <Box
                        sx={{
                            backgroundColor: "white",
                            display: "block",
                            position: "absolute",
                            width: "12px",
                            height: "12px",
                            top: "-6px",
                            boxShadow: "0px 5px 5px -3px rgb(0 0 0 / 20%), 0px 8px 10px 1px rgb(0 0 0 / 14%), 0px 3px 14px 2px rgb(0 0 0 / 12%)",
                            webKitTransform: "rotate(45deg)",
                            mozTransform: "rotate(45deg)",
                            msTransform: "rotate(45deg)",
                            transform: "rotate(45deg)",
                            left: "calc(50% - 6px)"
                        }}
                    />
                    <Box
                        sx={{
                            display: "block",
                            position: "absolute",
                            width: "100%",
                            height: "20px",
                            top: "0px",
                            backgroundColor: "white",
                            left: "0px"
                        }}
                    />
                    <Box
                        sx={{
                            p: 2,
                            width: "400px"
                        }}
                    >
                        <Typography
                            sx={{
                                fontWeight: "bold"
                            }}
                        >
                            {t('common:common.add_filters')}
                        </Typography>
                        <Grid
                            container
                            spacing={2}
                        >
                            <Grid
                                item
                                xs={12}
                            >
                                <Box
                                    sx={{
                                        maxWidth: "600px"
                                    }}
                                >
                                    <Autocomplete
                                        options={customFiltersList}
                                        label={t("common:common.filter")}
                                        getOptionLabel={option => { return option && option.elementLabel ? option.elementLabel : '' }}
                                        value={selectedFilter}
                                        onChange={(e, v) => handleChangeCustomFilter(e, v)}
                                        renderInput={(params) => (
                                            <TextField
                                                fullWidth
                                                placeholder={t("common:common.choose_filter")}
                                                name={t("common:common.filter")}
                                                variant="outlined"
                                                {...params}
                                            />
                                        )}
                                    />
                                </Box>
                            </Grid>
                            {
                                isLoading &&
                                <Grid
                                    item
                                    xs={12}
                                >
                                    <Skeleton />
                                    <Skeleton />
                                </Grid>
                            }
                            {
                                selectedTypeDetails && selectedTypeDetails.conditions ?
                                    <Grid
                                        item
                                        xs={12}
                                    >
                                        <CustomMuiSelect
                                            label={t("common:conditions.condition")}
                                            name={t("common:conditions.condition")}
                                            width="100%"
                                            getOptionLabel={option => { return option && option.label ? option.label : '' }}
                                            selected={selectedCondition}
                                            handleSelect={e => setSelectedCondition(selectedTypeDetails.conditions.find(x => x.id === e.target.value))}
                                            data={selectedTypeDetails.conditions}
                                            itemText="label"
                                            itemValue="id"
                                        />
                                    </Grid>
                                    :
                                    null
                            }
                            {
                                selectedTypeDetails ?
                                    <Grid
                                        item
                                        xs={12}
                                    >
                                        {getValueComponent()}
                                    </Grid>
                                    :
                                    null
                            }
                            <Grid
                                item
                                xs={12}
                            >
                                <Button
                                    sx={{
                                        float: "right"
                                    }}
                                    variant="contained"
                                    disabled={!selectedFilter || !selectedFilter.elementID || !selectedValues}
                                    onClick={handleSubmit}
                                >
                                    Guardar
                                </Button>
                            </Grid>
                        </Grid>

                    </Box>
                </Popover>
            )
        };

        CustomFilterSelectPopover.propTypes = {
            oid: PropTypes.string,
            customFiltersList: PropTypes.array,
            resetCustomFilters: PropTypes.func,
            addCustomFilter: PropTypes.func,
            deleteCustomFilter: PropTypes.func,
            context: PropTypes.string
        }

        export default CustomFilterSelectPopover;
    `;
}

export default getSelectPopover;
