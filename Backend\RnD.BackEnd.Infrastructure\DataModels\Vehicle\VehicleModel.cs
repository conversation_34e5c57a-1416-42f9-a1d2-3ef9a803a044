﻿using Newtonsoft.Json;
using RnD.BackEnd.Infrastructure.DataModels.Base;

namespace RnD.BackEnd.Infrastructure.DataModels.Vehicle
{
    public class VehicleModel : BaseCosmosModel
    {
        [JsonProperty(PropertyName = "brandId")]
        public string BrandId { get; set; }

        [JsonProperty(PropertyName = "brand")]
        public string Brand { get; set; }

        [JsonProperty(PropertyName = "modelName")]
        public string ModelName { get; set; }

        [JsonProperty(PropertyName = "fuelType")]
        public string FuelType { get; set; }

        [JsonProperty(PropertyName = "version")]
        public string Version { get; set; }

        [JsonProperty(PropertyName = "year")]
        public int Year { get; set; }

        public override string GetPartitionKey()
        {
            return Id.Split(':')[0];
        }
    }
}
