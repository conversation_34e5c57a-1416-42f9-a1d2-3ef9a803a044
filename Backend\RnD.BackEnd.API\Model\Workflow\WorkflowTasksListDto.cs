﻿using System.Collections.Generic;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Workflow tasks list DTO class
    /// </summary>
    public class WorkflowTasksListDto
    {
        /// <summary>
        /// Gets or sets the tasks.
        /// </summary>
        /// <value>
        /// The tasks.
        /// </value>
        public IList<WorkflowTaskDto> Tasks { get; set; }

        /// <summary>
        /// Gets or sets the total rows.
        /// </summary>
        /// <value>
        /// The total rows.
        /// </value>
        public int TotalRows { get; set; }
    }
}
