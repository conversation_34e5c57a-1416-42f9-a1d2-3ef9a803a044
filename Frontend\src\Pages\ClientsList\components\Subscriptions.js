import React from 'react';
import { Box, Typography, CircularProgress, Chip, Button, Autocomplete, TextField } from '@mui/material';
import CloudQueueIcon from '@mui/icons-material/CloudQueue';
import AddIcon from '@mui/icons-material/Add';
import CancelIcon from '@mui/icons-material/Cancel';
import { useTranslation } from "react-i18next";

const Subscriptions = ({
  subscricoes,
  subscriptionDetails,
  loadingSubscricoes,
  availableSubscriptions,
  selectedSubscription,
  setSelectedSubscription,
  handleAddSubscricao,
  handleRemoveSubscricao
}) => {
  const { t } = useTranslation();
  
  return (
    <Box sx={{ mt: 4 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <CloudQueueIcon sx={{ mr: 1, color: 'primary.main' }} />
        {t('common:subscription.associatedSubscriptions')}
      </Typography>

      {loadingSubscricoes ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
          <CircularProgress size={24} />
        </Box>
      ) : (
        <>
          {subscricoes.length > 0 ? (
            <Box sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 1,
              mb: 2,
              p: 2,
              backgroundColor: 'rgba(25, 118, 210, 0.05)',
              borderRadius: '8px'
            }}>
              {subscricoes.map(sub => (
                <Chip
                  key={sub.id}
                  label={`${t('common:subscription.id')} ${sub.subscriptionDetails.subscriptionID}`}
                  onDelete={() => handleRemoveSubscricao(sub.subscriptionID)}
                  deleteIcon={<CancelIcon />}
                  variant="outlined"
                  color="primary"
                />
              ))}
            </Box>
          ) : (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {t('common:subscription.noAssociatedSubscriptions')}
            </Typography>
          )}

          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            mt: 2
          }}>
            <Autocomplete
              options={availableSubscriptions}
              getOptionLabel={(option) => `${t('common:subscription.id')} ${option.subscriptionID}`}
              value={selectedSubscription}
              onChange={(_, newValue) => setSelectedSubscription(newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={t('common:subscription.addSubscription')}
                  variant="outlined"
                  size="small"
                  fullWidth
                  placeholder={t('common:subscription.selectAvailableSubscription')}
                />
              )}
              sx={{ flexGrow: 1 }}
              disabled={loadingSubscricoes || availableSubscriptions.length === 0}
            />

            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddSubscricao}
              disabled={!selectedSubscription || loadingSubscricoes}
              sx={{
                height: '40px',
                minWidth: '120px'
              }}
            >
              {t('common:buttons.add')}
            </Button>
          </Box>

          {availableSubscriptions.length === 0 && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {t('common:subscription.noAvailableSubscriptions')}
            </Typography>
          )}
        </>
      )}
    </Box>
  );
};

export default Subscriptions; 