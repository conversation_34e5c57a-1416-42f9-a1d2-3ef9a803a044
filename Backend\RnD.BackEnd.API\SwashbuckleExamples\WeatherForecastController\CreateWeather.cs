﻿using System.Collections.Generic;
using RnD.BackEnd.API.Model.Weather;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WeatherForecastController
{
    /// <summary>
    /// Create Weather request example
    /// </summary>
    public class CreateWeather : IMultipleExamplesProvider<CreateWeatherDto>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to Create Weather.
        /// </returns>
        public IEnumerable<SwaggerExample<CreateWeatherDto>> GetExamples()
        {
            yield return SwaggerExample.Create("Warsaw", new CreateWeatherDto
            {
                Location = "Warsaw",
                Summary = "Very Cold",
                Temperature = -5
            });
            yield return SwaggerExample.Create("Australia", new CreateWeatherDto
            {
                Location = "Australia",
                Summary = "Very hot",
                Temperature = 4
            });
        }
    }
}