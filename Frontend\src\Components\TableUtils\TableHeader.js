import { TableHead, TableRow, TableCell, TableSortLabel, Checkbox, IconButton } from '@mui/material';
import {
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import PropTypes from 'prop-types';

const TableHeader = (props) => {
  const { order, orderBy, headCells, selectedSome, selectedAll, onSelectAllClick, onSortClick, hasCheckbox = true, togglePrivacy, privacy, disabledCheckBox = false } = props;
  return (
    <TableHead>
      <TableRow>
        {hasCheckbox ?
          <TableCell
            padding="checkbox"
          >
            <Checkbox
              indeterminate={selectedSome}
              checked={selectedAll}
              onChange={onSelectAllClick}
              inputProps={{ 'aria-label': 'Seleccionar todos' }}
              disabled={disabledCheckBox}
            />
          </TableCell>
          :
          null}
        {
          headCells.filter((x) => !x.hidden).map((headCell) => {
            if (headCell.visibilityToggle) {
              return <TableCell
                key={headCell.id}
                align={headCell.align}
                sx={headCell.style}
              >
                <IconButton
                  aria-label="Privacidade"
                  component="span"
                  onClick={togglePrivacy}
                >
                  {
                    privacy ?
                      <VisibilityOffIcon />
                      :
                      <VisibilityIcon />
                  }
                </IconButton>
              </TableCell>;
            }
            return <TableCell
              key={headCell.id}
              align={headCell.align}
              sortDirection={orderBy === headCell.id ? order : false}
              sx={headCell.style}
            >
              {
                headCell.sort ?
                  <TableSortLabel
                    active={orderBy === headCell.id}
                    direction={orderBy === headCell.id ? order : 'asc'}
                    onClick={() => onSortClick(headCell.id)}
                  >
                    {headCell.label}
                  </TableSortLabel>
                  :
                  headCell.label
              }
            </TableCell>
          })
        }
      </TableRow>
    </TableHead>
  );
};

TableHeader.propTypes = {
  togglePrivacy: PropTypes.func,
  privacy: PropTypes.bool,
  onSortClick: PropTypes.func,
  onSelectAllClick: PropTypes.func,
  selectedAll: PropTypes.bool,
  selectedSome: PropTypes.bool,
  order: PropTypes.oneOf(['asc', 'desc']),
  orderBy: PropTypes.string,
  headCells: PropTypes.array,
  hasCheckbox: PropTypes.bool,
  disabledCheckBox: PropTypes.bool
};

export default TableHeader;
