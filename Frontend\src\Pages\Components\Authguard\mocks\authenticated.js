const getAuthenticatedCode = () => {
    return `
    if (!auth.isAuthenticated || !auth.user) {
        if (process.env.REACT_APP_CUSTOM_AUTH === '(Your Code Here...)') {
            return componentPermission ? <></> : <Navigate to="(Your Code Here...)" />;
        }
        return componentPermission ? <></> : <Navigate to="(Your Code Here...)>;
        }

        if (!auth.user.roles?.includes(roles) || (moduleID && moduleID !== "" && !auth.user.userModules?.find(x => x.id === moduleID)?.enable)) {
        return componentPermission ? <></> : <Navigate to="(Your Code Here...)" />;
    }
    `;
}

export default getAuthenticatedCode;
