﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using RnD.BackEnd.Domain.Entities.Generic;

namespace RnD.BackEnd.Domain.Interfaces.Infrastructure.Managers
{
    /// <summary>
    /// API connector manager interface.
    /// </summary>
    /// <seealso cref="IDisposable" />
    public interface IApiConnectorManager : IDisposable
    {
        /// <summary>
        /// Calls a DELETE and returns a ServiceOutput with a typed object.
        /// </summary>
        /// <typeparam name="T">The typed object returned with ServiceOutput.</typeparam>
        /// <param name="baseUrl">The base URL.</param>
        /// <param name="endpoint">The endpoint.</param>
        /// <param name="token">The token.</param>
        /// <param name="request">The request.</param>
        /// <param name="timeout">The timeout.</param>
        /// <returns>
        /// ServiceOutput with a typed object.
        /// </returns>
        Task<ServiceOutput<T>> DeleteAsync<T>(string baseUrl, string endpoint, string token, object request, int? timeout = null);

        /// <summary>
        /// Calls a GET and returns a ServiceOutput with a typed object.
        /// </summary>
        /// <typeparam name="T">The typed object returned with ServiceOutput.</typeparam>
        /// <param name="baseUrl">The base URL.</param>
        /// <param name="endpoint">The endpoint.</param>
        /// <param name="token">The token.</param>
        /// <param name="queryStringParameters">The query string parameters.</param>
        /// <param name="timeout">The timeout.</param>
        /// <returns>
        /// ServiceOutput with a typed object.
        /// </returns>
        Task<ServiceOutput<T>> GetAsync<T>(string baseUrl, string endpoint, string token = null, Dictionary<string, string> queryStringParameters = null, int? timeout = null);

        /// <summary>
        /// Calls a POST and returns a ServiceOutput with a typed object.
        /// </summary>
        /// <typeparam name="T">The typed object returned with ServiceOutput.</typeparam>
        /// <param name="baseUrl">The base URL.</param>
        /// <param name="endpoint">The endpoint.</param>
        /// <param name="request">The request.</param>
        /// <param name="token">The token.</param>
        /// <param name="timeout">The timeout.</param>
        /// <returns>
        /// ServiceOutput with a typed object.
        /// </returns>
        Task<ServiceOutput<T>> PostAsync<T>(string baseUrl, string endpoint, object request, string token = null, int? timeout = null);

        /// <summary>
        /// Calls a PUT and returns a ServiceOutput with a typed object.
        /// </summary>
        /// <typeparam name="T">The typed object returned with ServiceOutput.</typeparam>
        /// <param name="baseUrl">The base URL.</param>
        /// <param name="endpoint">The endpoint.</param>
        /// <param name="request">The request.</param>
        /// <param name="token">The token.</param>
        /// <param name="timeout">The timeout.</param>
        /// <returns>
        /// ServiceOutput with a typed object.
        /// </returns>
        Task<ServiceOutput<T>> PutAsync<T>(string baseUrl, string endpoint, object request, string token = null, int? timeout = null);
    }
}
