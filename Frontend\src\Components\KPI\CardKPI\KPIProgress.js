import React from 'react';
import { Tooltip, Box, LinearProgress } from '@mui/material';
import {
    InfoRounded as InfoRoundedIcon
} from '@mui/icons-material';
import PropTypes from 'prop-types';
import { useTheme } from '@emotion/react';

const KPIProgress = ({ description, tooltip, progress, progressCap = 100 }) => {
    const theme = useTheme();
    return (
        <div
            style={{
                width: "100%"
            }}
        >
            <div
                style={{
                    display: "flex",
                    alignItems: "center",
                    marginBottom: "8px"
                }}
            >
                <small>
                    <b>
                        {description}
                    </b>
                </small>
                <Tooltip
                    title={tooltip}
                    sx={{
                        ml: "8px"
                    }}
                >
                    <InfoRoundedIcon fontSize="small" />
                </Tooltip>
            </div>
            <Box>
                <LinearProgress
                    variant="determinate"
                    sx={{
                        color: `${theme.palette.primary.grad1}`,
                    }}
                    value={progress || 0}
                    max={progressCap}
                />
            </Box>
            <small>{progress || 0}%</small>
        </div>
    )
};

KPIProgress.propTypes = {
    description: PropTypes.string,
    tooltip: PropTypes.string,
    progress: PropTypes.number,
    progressCap: PropTypes.number
}

export default KPIProgress;
