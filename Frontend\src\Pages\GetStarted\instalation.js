/* eslint-disable react/no-unescaped-entities*/
import { Box, Card, CardContent, Link } from "@mui/material";
import CopyToClipboardButton from "../../Components/CopyToClipBoardButton";
import { styled } from '@mui/system';

const StyledCard = styled(Card)(({ theme }) => ({
  width: '80%',
  margin: 'auto',
  marginTop: theme.spacing(1),
  marginBottom: theme.spacing(1),
  maxHeight: '100vh',
}));

const Instalation = () => {
  let value = "";

  return (
    <>
      <Box
        sx={{ p: "24px 32px 0px 32px" }}
      >
        <Box
          sx={{
            margin: 'auto',
            textAlign: 'center',
          }}
        >
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h2>Default installation</h2>
            Run the following commands to start your project:
          </Box>
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h3>npm</h3>
            This command will allow you to download and install a package and
            it's dependencies.
          </Box>
          <StyledCard>
            <CardContent sx={{ display: "flex" }}>
              <Box
                component="div"
                sx={{ flexDirection: "row", m: "auto" }}
              >
                {(value = "npm install")}
              </Box>
              <CopyToClipboardButton value={value} />
            </CardContent>
          </StyledCard>
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h3>Material UI</h3>
            For more information on how to use Material UI library please see
            <Link
              href="https://mui.com/"
              underline="none"
            >
              {" "}
              MUI website{" "}
            </Link>
            .
          </Box>
          <StyledCard>
            <CardContent sx={{ display: "flex" }}>
              <Box
                component="div"
                sx={{ flexDirection: "row", m: "auto" }}
              >
                {
                  (value =
                    "npm install @mui/material @emotion/react @emotion/styled")
                }
              </Box>
              <CopyToClipboardButton value={value} />
            </CardContent>
          </StyledCard>
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h3>Data Grid / x-data-grid</h3>
          </Box>
          <StyledCard>
            <CardContent sx={{ display: "flex" }}>
              <Box
                component="div"
                sx={{ flexDirection: "row", m: "auto" }}
              >
                {(value = "npm install @mui/x-data-grid")}
              </Box>
              <CopyToClipboardButton value={value} />
            </CardContent>
          </StyledCard>
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h3>lodash merge</h3>
          </Box>
          <StyledCard>
            <CardContent sx={{ display: "flex" }}>
              <Box
                component="div"
                sx={{ flexDirection: "row", m: "auto" }}
              >
                {(value = "npm i lodash.merge")}
              </Box>
              <CopyToClipboardButton value={value} />
            </CardContent>
          </StyledCard>
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h3>i18n</h3>
            For more information on how to use i18n library please see
            <Link
              href="https://react.i18next.com/"
              underline="none"
            >
              {" "}
              i18n website{" "}
            </Link>
            .
          </Box>
          <StyledCard>
            <CardContent sx={{ display: "flex" }}>
              <Box
                component="div"
                sx={{ flexDirection: "row", m: "auto" }}
              >
                {(value = "npm install react-i18next i18next --save")}
              </Box>
              <CopyToClipboardButton value={value} />
            </CardContent>
          </StyledCard>
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h3>Toast</h3>
            For more information on how to use i18n library please see
            <Link
              href="https://react-hot-toast.com/"
              underline="none"
            >
              {" "}
              React Hot Toast website{" "}
            </Link>
            .
          </Box>
          <StyledCard>
            <CardContent sx={{ display: "flex" }}>
              <Box
                component="div"
                sx={{ flexDirection: "row", m: "auto" }}
              >
                {(value = "npm i react-hot-toast")}
              </Box>
              <CopyToClipboardButton value={value} />
            </CardContent>
          </StyledCard>
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h3>moment</h3>A JavaScript date library for parsing, validating,
            manipulating, and formatting dates.
          </Box>
          <StyledCard>
            <CardContent sx={{ display: "flex" }}>
              <Box
                component="div"
                sx={{ flexDirection: "row", m: "auto" }}
              >
                {(value = "npm i moment")}
              </Box>
              <CopyToClipboardButton value={value} />
            </CardContent>
          </StyledCard>
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h3>material-ui-popup-state</h3>
            Provides a Custom React Hook that keeps track of the local state for
            a single popup, and functions to connect trigger, toggle, and
            popover/menu/popper components to the state.
            <br />
            Also provides a Render Props Component that keeps track of the local
            state for a single popup, and passes the state and mutation
            functions to a child render function.
          </Box>
          <StyledCard>
            <CardContent sx={{ display: "flex" }}>
              <Box
                component="div"
                sx={{ flexDirection: "row", m: "auto" }}
              >
                {(value = "npm i material-ui-popup-state")}
              </Box>
              <CopyToClipboardButton value={value} />
            </CardContent>
          </StyledCard>
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h3>clsx</h3>
          </Box>
          <StyledCard>
            <CardContent
              sx={{ display: "flex" }}
            >
              <Box
                component="div"
                sx={{ flexDirection: "row", m: "auto" }}
              >
                {(value = "npm i clsx")}
              </Box>
              <CopyToClipboardButton value={value} />
            </CardContent>
          </StyledCard>
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h3>Microsoft Library (msal)</h3>
            The Microsoft Authentication Library (MSAL) enables developers to
            acquire security tokens from the Microsoft identity platform to
            authenticate users and access secured web APIs. It can be used to
            provide secure access to Microsoft Graph, other Microsoft APIs,
            third-party web APIs, or your own web API.
          </Box>
          <StyledCard>
            <CardContent sx={{ display: "flex" }}>
              <Box
                component="div"
                sx={{ flexDirection: "row", m: "auto" }}
              >
                {(value = "npm i @azure/msal-react @azure/msal-browser")}
              </Box>
              <CopyToClipboardButton value={value} />
            </CardContent>
          </StyledCard>
          <Box
            color="textPrimary"
            sx={{ maxWidth: "80%", m: "auto", textAlign: "justify" }}
          >
            <h3>Formik</h3>
          </Box>
          <StyledCard>
            <CardContent sx={{ display: "flex" }}>
              <Box
                component="div"
                sx={{ flexDirection: "row", m: "auto" }}
              >
                {(value = "npm install formik --save")}
              </Box>
              <CopyToClipboardButton value={value} />
            </CardContent>
          </StyledCard>
        </Box>
      </Box>
    </>
  );
};

export default Instalation;
