{"common": {"about": "Sobre...", "completedProfile": "<PERSON><PERSON><PERSON>", "personalData": "<PERSON><PERSON>", "fullName": "Nome <PERSON>to", "DOB": "Data de Nascimento", "contacts": "Contactos", "tel": "Telemóvel", "email": "Email Profissional", "personalEmail": "<PERSON><PERSON>", "addresses": "Endereços", "address": "<PERSON><PERSON>", "city": "Localidade", "zipCode": "Código Postal", "postalLocation": "Localidade Postal", "taxData": "<PERSON><PERSON>", "id": "Nº Cartão Cidadão", "expDate": "Data Validade", "maritalStatus": "Estado Civil", "socialSecurity": "Nº Segurança Social", "tax": "Nº Identificação Fiscal", "incomeHolder": "Titular Rendimentos", "dependents": "Nº Dependentes", "empNo": "Nº Colaborador", "profile": "Perfil"}, "formik": {"error": "Existem erros de valid<PERSON>", "name": "O nome completo é obrigatório", "DOB": "A data de nascimento é obrigatória", "DOBval": "A data de nascimento não pode ser superior à data actual", "contact": "O telemóvel é obrigatório", "contactVal": "O telemóvel não é válido", "email": "O email é obrigatório", "emailVal": "O email pessoal não é válido", "personalEmail": "O email pessoal é obrigatório", "personalEmailVal": "O email pessoal não é válido", "address1": "A morada é obrigatória", "address2": "A localidade é obrigatória", "zipCode": "O código postal é obrigatório", "zipCodeVal": "O código postal não é válido", "zipCodeCity": "A localidade postal é obrigatória", "idCard": "O cartão de cidadão é obrigatório", "idCardVal": "O cartão de cidadão não é válido", "expDate": "A data de validade é obrigatória", "expDateVal": "A data de validade não pode ser inferior à data actual", "taxNo": "O nº de identificação fiscal é obrigatório", "taxNoVal": "O nº de identificação fiscal não é válido", "socialSecurityNo": "O nº de segurança social é obrigatório", "socialSecurityNoVal": "O nº de segurança social não é válido", "incomeHolderID": "O titular de rendimentos é obrigatório", "dependents": "O número de dependentes é obrigatório", "dependentsVal": "O número de dependentes não pode ser inferior a 0", "maritialStatus": "O estado civil é obrigatório", "profile": "É necessário associar um perfil", "username": "O username é obrigatório", "password": " A Palavra Passe é obrigatório para utilizadores da API."}}