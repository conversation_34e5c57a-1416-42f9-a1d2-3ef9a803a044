﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json;
using RnD.BackEnd.Licensing.Settings;

namespace RnD.BackEnd.Licensing.Handlers
{
    /// <summary>
    /// Validation settings handler class
    /// </summary>
    public class SettingsHandler
    {
        private readonly string _licenseKey;

        /// <summary>
        /// Initializes a new instance of the <see cref="SettingsHandler" /> class.
        /// </summary>
        /// <param name="licenseKey">The license key.</param>
        /// <exception cref="ArgumentNullException">License Key</exception>
        public SettingsHandler(string licenseKey)
        {
            _licenseKey = licenseKey;

            if (_licenseKey == null)
            {
                throw new ArgumentNullException("License Key");
            }
        }

        /// <summary>
        /// Decrypts the specified data into a Validation Settings object.
        /// </summary>
        /// <param name="data">The data.</param>
        /// <returns>
        /// The validation settings
        /// </returns>
        public ValidationSettings GetDecryptedValidationSettings()
        {
            // Get the hash keys
            byte[][] keys = new byte[2][];
            using (SHA256 sha2 = SHA256.Create())
            {
                byte[] rawKey = Encoding.UTF8.GetBytes(EncryptionSettings.DECRYPTION_KEY);
                byte[] rawIV = Encoding.UTF8.GetBytes(EncryptionSettings.DECRYPTION_KEY);

                byte[] hashKey = sha2.ComputeHash(rawKey);
                byte[] hashIV = sha2.ComputeHash(rawIV);

                Array.Resize(ref hashIV, 16);

                keys[0] = hashKey;
                keys[1] = hashIV;
            }

            string decryptedString = DecryptFromBytes_Aes(_licenseKey, keys[0], keys[1]);

            return !string.IsNullOrWhiteSpace(decryptedString) ? JsonConvert.DeserializeObject<ValidationSettings>(decryptedString) : null;
        }

        /// <summary>
        /// Decrypts the string from bytes aes.
        /// source: https://msdn.microsoft.com/de-de/library/system.security.cryptography.aes(v=vs.110).aspx
        /// </summary>
        /// <param name="cipherTextString">The cipher text string.</param>
        /// <param name="Key">The key.</param>
        /// <param name="IV">The iv.</param>
        /// <returns></returns>
        /// <exception cref="System.ArgumentNullException">
        /// cipherText
        /// or
        /// Key
        /// or
        /// IV
        /// </exception>
        private string DecryptFromBytes_Aes(string cipherTextString, byte[] Key, byte[] IV)
        {
            byte[] cipherText = WebEncoders.Base64UrlDecode(cipherTextString);

            if (cipherText == null || cipherText.Length <= 0)
            {
                throw new ArgumentNullException("cipherText");
            }

            if (Key == null || Key.Length <= 0)
            {
                throw new ArgumentNullException("Key");
            }

            if (IV == null || IV.Length <= 0)
            {
                throw new ArgumentNullException("IV");
            }

            string plaintext = string.Empty;

            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Key;
                aesAlg.IV = IV;

                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                using (MemoryStream msDecrypt = new MemoryStream(cipherText))
                {
                    using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                        {
                            plaintext = srDecrypt.ReadToEnd();
                        }
                    }
                }
            }

            return plaintext;
        }
    }
}
