﻿using System;
using System.Collections.Generic;
using System.Net;
using RnD.BackEnd.API.Model.DataEntry;
using RnD.BackEnd.API.Model.Generic;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.DataEntryController
{
    /// <summary>
    /// Get Entities response example for 200
    /// </summary>
    public class GetEntities200 : IExamplesProvider<ApiOutput<EntityListDto>>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a 200 for Get Entities response.
        /// </returns>
        public ApiOutput<EntityListDto> GetExamples()
        {
            return new ApiOutput<EntityListDto>()
            {
                Code = HttpStatusCode.OK,
                Description = null,
                Value = new EntityListDto()
                {
                    Entities = new List<EntityDto>()
                    {
                        new EntityDto()
                        {
                            Id = "9dcfd5fd-ad84-45f2-a326-2792e6ffad49:2645a7b9-b772-4a3f-a032-83ba24f56dff",
                            ModelId = "9dcfd5fd-ad84-45f2-a326-2792e6ffad49",
                            ModelName = null,
                            FunctionalName = "Product Category",
                            TechnicalName = "product_category",
                            DataLength = 4,
                            Created = Convert.ToDateTime("2022-11-13T22:30:47.081Z"),
                            CreatedBy = "<EMAIL>",
                            CreatedByName = null,
                            Modified = Convert.ToDateTime("2022-11-14T15:10:57.481Z"),
                            ModifiedBy = "<EMAIL>",
                            ModifiedByName = null
                        },
                        new EntityDto()
                        {
                            Id = "a7d4a4c1-d314-475e-82c2-40926f022666:6d2418f6-437f-4913-a343-fe98fe435ef7",
                            ModelId = "a7d4a4c1-d314-475e-82c2-40926f022666",
                            ModelName = null,
                            FunctionalName = "Team",
                            TechnicalName = "team",
                            DataLength = 3,
                            Created = Convert.ToDateTime("2022-10-25T17:52:16.425Z"),
                            CreatedBy = "<EMAIL>",
                            CreatedByName = null,
                            Modified = Convert.ToDateTime("2022-11-30T14:50:27.848Z"),
                            ModifiedBy = "<EMAIL>",
                            ModifiedByName = null
                        }
                    },
                    TotalRows = 2
                },
                Error = false,
                ExceptionMessages = new Model.Exception.ModelException(),
                Properties = new Dictionary<string, object>()
            };
        }
    }
}