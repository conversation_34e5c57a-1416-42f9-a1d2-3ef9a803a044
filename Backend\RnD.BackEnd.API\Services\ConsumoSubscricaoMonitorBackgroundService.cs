using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using System.Linq;
using RnD.BackEnd.API.Models;
using RnD.BackEnd.API.Data;
using Microsoft.EntityFrameworkCore;

namespace RnD.BackEnd.API.Services
{
    public class ConsumoSubscricaoMonitorBackgroundService : BackgroundService
    {
        private readonly ILogger<ConsumoSubscricaoMonitorBackgroundService> _logger;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly IConfiguration _configuration;
        private DateTime _lastCheckTime = DateTime.MinValue;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(5);

        public ConsumoSubscricaoMonitorBackgroundService(
            ILogger<ConsumoSubscricaoMonitorBackgroundService> logger,
            IServiceScopeFactory scopeFactory,
            IConfiguration configuration)
        {
            _logger = logger;
            _scopeFactory = scopeFactory;
            _configuration = configuration;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Iniciando serviço de monitoramento de arquivos Excel de consumo por subscrição");
            
            using (var scope = _scopeFactory.CreateScope())
            {
                var logService = scope.ServiceProvider.GetRequiredService<LogProcessamentoService>();
                await logService.RegistrarLog(0, "Serviço de monitoramento de arquivos Excel de consumo por subscrição iniciado", TipoLog.Info);

                // Busca a data do último arquivo processado
                var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                var ultimoArquivo = await dbContext.ArquivosProcessados
                    .OrderByDescending(a => a.DataProcessamento)
                    .FirstOrDefaultAsync();

                if (ultimoArquivo != null)
                {
                    _lastCheckTime = ultimoArquivo.DataProcessamento;
                    _logger.LogInformation($"Última verificação definida para: {_lastCheckTime}");
                }
            }

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CheckForNewExcelFiles();
                    _lastCheckTime = DateTime.UtcNow;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro ao verificar novos arquivos Excel de consumo por subscrição");
                    
                    using (var scope = _scopeFactory.CreateScope())
                    {
                        var logService = scope.ServiceProvider.GetRequiredService<LogProcessamentoService>();
                        await logService.RegistrarLog(0, $"Erro ao verificar novos arquivos Excel: {ex.Message}", TipoLog.Erro);
                    }
                }

                await Task.Delay(_checkInterval, stoppingToken);
            }
        }

        private async Task CheckForNewExcelFiles()
        {
            _logger.LogInformation("Verificando novos arquivos Excel de consumo por subscrição no Azure Storage");

            string connectionString = _configuration["StorageExcelAzure:ConnectionString"];
            string containerName = _configuration["StorageExcelAzure:ContainerName"];

            var blobContainerClient = new BlobContainerClient(connectionString, containerName);
            await blobContainerClient.CreateIfNotExistsAsync();

            var blobs = blobContainerClient.GetBlobs()
                .Where(b => b.Properties.LastModified > _lastCheckTime)
                .OrderByDescending(b => b.Properties.LastModified)
                .ToList();

            if (blobs.Any())
            {
                _logger.LogInformation($"Encontrados {blobs.Count} novos arquivos Excel de consumo por subscrição");

                using (var scope = _scopeFactory.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                    var logService = scope.ServiceProvider.GetRequiredService<LogProcessamentoService>();
                    var excelService = scope.ServiceProvider.GetRequiredService<IConsumoSubscricaoExcelService>();

                    foreach (var blob in blobs)
                    {
                        // Verifica se o arquivo já foi processado
                        bool arquivoJaProcessado = await dbContext.ArquivosProcessados.AnyAsync(a => a.Nome == blob.Name);

                        if (arquivoJaProcessado)
                        {
                            await logService.RegistrarLog(0, $"Arquivo {blob.Name} já foi processado anteriormente. Ignorando.", TipoLog.Aviso);
                            continue;
                        }

                        var consumos = await excelService.ProcessExcelFile(blob.Name);
                        
                        if (consumos.Any())
                        {
                            foreach (var consumo in consumos)
                            {
                                // Verificar se a subscription já existe
                                var subscription = await dbContext.Subscription
                                    .FirstOrDefaultAsync(s => s.SubscriptionID == consumo.SubscriptionId);

                                if (subscription == null)
                                {
                                    subscription = new Subscription
                                    {
                                        SubscriptionID = consumo.SubscriptionId
                                    };
                                    dbContext.Subscription.Add(subscription);
                                    await dbContext.SaveChangesAsync();
                                }

                                var consumoSubscricao = new ConsumoSubscricao
                                {
                                    SubscriptionID = consumo.SubscriptionId,
                                    ResourceGroup = consumo.ResourceGroup,
                                    ResourceName = consumo.ResourceName,
                                    ResourceLocation = consumo.ResourceLocation,
                                    Cost = consumo.Cost,
                                    CostUSD = consumo.CostUSD,
                                    Currency = consumo.Currency,
                                    StartDate = consumo.StartDate,
                                    EndDate = consumo.EndDate
                                };

                                dbContext.ConsumoSubscricao.Add(consumoSubscricao);
                            }

                            await dbContext.SaveChangesAsync();
                            await logService.RegistrarLog(0, $"Processados {consumos.Count} registros do arquivo {blob.Name}", TipoLog.Info);
                        }
                    }
                }
            }
            else
            {
                _logger.LogInformation("Nenhum novo arquivo Excel de consumo por subscrição encontrado");
            }
        }
    }
} 