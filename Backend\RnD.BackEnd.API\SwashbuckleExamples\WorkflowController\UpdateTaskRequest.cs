﻿using System;
using System.Collections.Generic;
using RnD.BackEnd.API.Model.Workflow;
using Swashbuckle.AspNetCore.Filters;

namespace RnD.BackEnd.API.SwashbuckleExamples.WorkflowController
{
    /// <summary>
    /// Update Task request example
    /// </summary>
    /// <seealso cref="Swashbuckle.AspNetCore.Filters.IMultipleExamplesProvider&lt;RnD.BackEnd.API.Model.Workflow.UpdateTaskDto&gt;" />
    public class UpdateTaskRequest : IMultipleExamplesProvider<UpdateTaskDto>
    {
        /// <summary>
        /// Gets the examples.
        /// </summary>
        /// <returns>
        /// The example of a request to update task.
        /// </returns>
        public IEnumerable<SwaggerExample<UpdateTaskDto>> GetExamples()
        {
            yield return SwaggerExample.Create("APPROVED - With Custom Email", new UpdateTaskDto()
            {
                Id = Guid.NewGuid(),
                TaskStatus = "APPROVED",
                TaskCompleted = true,
                Comments = "For me everything is alright i will approve",
                EmailRequest = new WorkflowEmailDto()
                {
                    TemplateId = "d-c43961e085e04f1c8e1f0192db1c69d8",
                    Recipients = new List<string>() { "<EMAIL>", "<EMAIL>" },
                    TemplateData = new Dictionary<string, string>()
                        {
                            { "Message", "This is an example of Email" }
                        },
                    Subject = "FrameWork Example of Email",
                    SendEmail = true
                }
            });
            yield return SwaggerExample.Create("REJECTED - Not Sending Email", new UpdateTaskDto()
            {
                Id = Guid.NewGuid(),
                TaskStatus = "REJECTED",
                TaskCompleted = true,
                Comments = "I don't agreed so i will reject",
                EmailRequest = new WorkflowEmailDto()
                {
                    TemplateId = "d-c43961e085e04f1c8e1f0192db1c69d8",
                    Recipients = new List<string>() { "<EMAIL>", "<EMAIL>" },
                    TemplateData = new Dictionary<string, string>()
                        {
                            { "Message", "This is an example of Email" }
                        },
                    Subject = "FrameWork Example of Email",
                    SendEmail = false
                }
            });
            yield return SwaggerExample.Create("CANCELLED - With Default Email", new UpdateTaskDto()
            {
                Id = Guid.NewGuid(),
                TaskStatus = "CANCELLED",
                TaskCompleted = true,
                Comments = "This Task doesn't make sense now, so i will cancel it",
                EmailRequest = null
            });
        }
    }
}
