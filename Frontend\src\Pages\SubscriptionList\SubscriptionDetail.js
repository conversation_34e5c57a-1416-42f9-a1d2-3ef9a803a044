import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Container,
  Typography,
  CircularProgress,
  Paper,
  Box,
  Grid,
  Divider,
  Snackbar,
  Alert,
  Card,
  CardContent,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  Button,
  TextField
} from "@mui/material";
import {
  Cloud as CloudIcon,
  ArrowBack as ArrowBackIcon,
} from "@mui/icons-material";
import { getSubscriptionById, getSubscriptionByPeriod } from "../../Services/subscriptionServices";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { ptBR } from "date-fns/locale";
import { startOfMonth, endOfMonth } from "date-fns";
import { formatCurrency, formatDate } from "../../Utils/formatUtils";

function SubscriptionDetail() {
  const { subscriptionId } = useParams();
  const navigate = useNavigate();
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [dataInicio, setDataInicio] = useState(startOfMonth(new Date()));
  const [dataFim, setDataFim] = useState(endOfMonth(new Date()));
  const [consumoData, setConsumoData] = useState([]);
  const [consumoLoading, setConsumoLoading] = useState(false);
  const [consumoError, setConsumoError] = useState(null);
  const [selectedResourceGroup] = useState('all');

  useEffect(() => {
    const fetchSubscriptionDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        const subscriptionData = await getSubscriptionById(subscriptionId);
        setSubscription(subscriptionData);
      } catch (fetchError) {
        console.error("❌ ERRO ao buscar detalhes da subscrição:", fetchError);
        setError("Não foi possível carregar os detalhes da subscrição. Por favor, tente novamente mais tarde.");
      } finally {
        setLoading(false);
      }
    };

    if (subscriptionId) {
      fetchSubscriptionDetails();
    }
  }, [subscriptionId]);

  useEffect(() => {
    const fetchConsumoData = async () => {
      if (!subscriptionId) return;

      try {
        setConsumoLoading(true);
        setConsumoError(null);
        const data = await getSubscriptionByPeriod(
          subscriptionId,
          dataInicio,
          dataFim
        );
        setConsumoData(data || []);
      } catch (consumoFetchError) {
        console.error("❌ ERRO ao buscar dados de consumo:", consumoFetchError);
        setConsumoError("Não foi possível carregar os dados de consumo para o período selecionado.");
      } finally {
        setConsumoLoading(false);
      }
    };

    if (activeTab === 1) {
      fetchConsumoData();
    }
  }, [subscriptionId, dataInicio, dataFim, activeTab]);

  const handleBack = () => {
    navigate('/subscriptions');
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const getFilteredConsumoData = () => {
    if (!consumoData) return [];
    if (selectedResourceGroup === 'all') return consumoData;
    return consumoData.filter(item => item.resourceGroup === selectedResourceGroup);
  };

  const calculateResourceTotals = (data) => {
    if (!Array.isArray(data) || data.length === 0) return {};

    return data.reduce((acc, item) => {
      const resource = item.resourceName || 'Sem Nome';
      if (!acc[resource]) {
        acc[resource] = {
          totalEUR: 0,
          totalUSD: 0,
          count: 0
        };
      }
      acc[resource].totalEUR += item.cost || 0;
      acc[resource].totalUSD += item.costUSD || 0;
      acc[resource].count += 1;
      return acc;
    }, {});
  };

  const calculateGeneralTotals = (data) => {
    if (!Array.isArray(data) || data.length === 0) {
      return { totalEUR: 0, totalUSD: 0, resourceCount: 0 };
    }

    return data.reduce((acc, item) => {
      acc.totalEUR += item.cost || 0;
      acc.totalUSD += item.costUSD || 0;
      acc.resourceCount += 1;
      return acc;
    }, { totalEUR: 0, totalUSD: 0, resourceCount: 0 });
  };

  if (loading) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
          <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
            variant="contained"
          >
            Voltar para a lista
          </Button>
        </Box>
      </Container>
    );
  }

  if (!subscription) {
    return (
      <Container>
        <Box display="flex" flexDirection="column" alignItems="center" mt={4}>
          <Alert severity="warning" sx={{ mb: 2 }}>Subscrição não encontrada</Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
            variant="contained"
        >
          Voltar para a lista
        </Button>
        </Box>
      </Container>
    );
  }

  const filteredData = getFilteredConsumoData();
  const resourceTotals = calculateResourceTotals(filteredData);
  const generalTotals = calculateGeneralTotals(filteredData);

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
            variant="contained"
            sx={{ mr: 2 }}
          >
            Voltar
          </Button>
          <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
            Detalhes da Subscrição
          </Typography>
        </Box>

        <Box sx={{ mb: 4 }}>
            <Typography variant="h5" gutterBottom>
            {subscription.subscriptionId}
            </Typography>
        </Box>

        <Divider sx={{ mb: 4 }} />

        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{ mb: 3 }}
        >
          <Tab label="Informações Básicas" icon={<CloudIcon />} iconPosition="start" />
          <Tab label="Consumo" icon={<CloudIcon />} iconPosition="start" />
        </Tabs>

        {activeTab === 0 && (
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Paper elevation={3} sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Informações Básicas
                  </Typography>
                <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="ID da Subscrição"
                            secondary={subscription.subscriptionId || 'N/A'}
                            primaryTypographyProps={{ variant: 'subtitle1', fontWeight: 'bold' }}
                            secondaryTypographyProps={{ variant: 'body1' }}
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Custo Total (EUR)"
                            secondary={formatCurrency(subscription.totalCost || 0)}
                            primaryTypographyProps={{ variant: 'subtitle1', fontWeight: 'bold' }}
                            secondaryTypographyProps={{ variant: 'body1' }}
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Custo Total (USD)"
                            secondary={formatCurrency(subscription.totalCostUSD || 0, 'USD')}
                            primaryTypographyProps={{ variant: 'subtitle1', fontWeight: 'bold' }}
                            secondaryTypographyProps={{ variant: 'body1' }}
                          />
                        </ListItem>
                      </List>
                  </Grid>
                    <Grid item xs={12} md={6}>
                      <List>
                        <ListItem>
                          <ListItemText
                            primary="Número de Recursos"
                            secondary={subscription.resourceCount || 0}
                            primaryTypographyProps={{ variant: 'subtitle1', fontWeight: 'bold' }}
                            secondaryTypographyProps={{ variant: 'body1' }}
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Data Início"
                            secondary={formatDate(subscription.startDate) || 'N/A'}
                            primaryTypographyProps={{ variant: 'subtitle1', fontWeight: 'bold' }}
                            secondaryTypographyProps={{ variant: 'body1' }}
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText
                            primary="Data Fim"
                            secondary={formatDate(subscription.endDate) || 'N/A'}
                            primaryTypographyProps={{ variant: 'subtitle1', fontWeight: 'bold' }}
                            secondaryTypographyProps={{ variant: 'body1' }}
                          />
                        </ListItem>
                      </List>
                </Grid>
                  </Grid>
                </Paper>
                  </Grid>
                </Grid>
              </Box>
        )}

        {activeTab === 1 && (
          <Box sx={{ mt: 2 }}>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
                <Grid container spacing={2} sx={{ mb: 4 }}>
                <Grid item xs={12} md={6}>
                      <DatePicker
                    label="Data Início"
                        value={dataInicio}
                        onChange={(newValue) => setDataInicio(newValue)}
                        renderInput={(params) => <TextField {...params} fullWidth />}
                      />
                  </Grid>
                <Grid item xs={12} md={6}>
                      <DatePicker
                    label="Data Fim"
                        value={dataFim}
                        onChange={(newValue) => setDataFim(newValue)}
                        renderInput={(params) => <TextField {...params} fullWidth />}
                      />
                </Grid>
              </Grid>
            </LocalizationProvider>

            {consumoLoading && (
              <Box display="flex" justifyContent="center" my={4}>
                    <CircularProgress />
                  </Box>
            )}

            {consumoError && (
              <Alert severity="error" sx={{ mb: 4 }}>{consumoError}</Alert>
            )}

            {!consumoLoading && !consumoError && consumoData.length > 0 && (
              <>
                <Card variant="outlined" sx={{ mb: 4 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Resumo do Período
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body1">
                          Total EUR: {formatCurrency(generalTotals.totalEUR)}
                            </Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body1">
                          Total USD: {formatCurrency(generalTotals.totalUSD, "USD")}
                            </Typography>
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <Typography variant="body1">
                          Total de Recursos: {generalTotals.resourceCount}
                            </Typography>
                      </Grid>
                      </Grid>
                          </CardContent>
                        </Card>

                              <Typography variant="h6" gutterBottom>
                  Detalhamento por Recurso
                              </Typography>
                                    <Grid container spacing={2}>
                  {Object.entries(resourceTotals)
                    .sort(([, a], [, b]) => b.totalEUR - a.totalEUR)
                    .map(([resourceName, totals]) => (
                      <Grid item xs={12} key={resourceName}>
                        <Card variant="outlined">
                          <CardContent>
                            <Typography variant="subtitle1" gutterBottom>
                              {resourceName}
                            </Typography>
                                  <Grid container spacing={2}>
                              <Grid item xs={12} md={4}>
                                <Typography variant="body2">
                                  Total EUR: {formatCurrency(totals.totalEUR)}
                                      </Typography>
                                    </Grid>
                              <Grid item xs={12} md={4}>
                                <Typography variant="body2">
                                  Total USD: {formatCurrency(totals.totalUSD, "USD")}
                                      </Typography>
                                    </Grid>
                              <Grid item xs={12} md={4}>
                                <Typography variant="body2">
                                  Registros: {totals.count}
                                      </Typography>
                                    </Grid>
                                  </Grid>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                    </Grid>
              </>
            )}

            {!consumoLoading && !consumoError && consumoData.length === 0 && (
              <Alert severity="info">
                Nenhum dado de consumo encontrado para o período selecionado.
              </Alert>
            )}
          </Box>
        )}
      </Paper>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </Container>
  );
}

export default SubscriptionDetail;
