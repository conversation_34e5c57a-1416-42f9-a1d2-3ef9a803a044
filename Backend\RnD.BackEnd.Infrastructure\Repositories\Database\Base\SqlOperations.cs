﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using Dapper.Contrib.Extensions;

namespace RnD.BackEnd.Infrastructure.Repositories.Database.Base
{
    /// <summary>
    /// SQL operations class, with methods to query and execute stored procedures against SQL
    /// </summary>
    public abstract class SqlOperations
    {
        /// <summary>
        /// The SQL transaction
        /// </summary>
        protected IDbTransaction _transaction;

        /// <summary>
        /// The SQL connection
        /// </summary>
        protected IDbConnection _connection;

        /// <summary>
        /// Initializes a new instance of <see cref="SqlOperations" />
        /// </summary>
        protected SqlOperations()
        {
        }

        /// <summary>
        /// Gets all entities in table typed T
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="commandTimeout">Optional command timeout (in seconds)</param>
        /// <returns>
        /// The list of data typed as T
        /// </returns>
        public IEnumerable<T> List<T>(int? commandTimeout = null) where T : class
        {
            return _connection.GetAll<T>(_transaction, commandTimeout);
        }

        /// <summary>
        /// Inserts an entity in Ts table
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="model">The model.</param>
        /// <param name="commandTimeout">Optional command timeout (in seconds)</param>
        /// <returns>
        /// The identity of the inserted entity
        /// </returns>
        public long Add<T>(T model, int? commandTimeout = null) where T : class
        {
            return _connection.Insert(model, _transaction, commandTimeout: commandTimeout);
        }

        /// <summary>
        /// Gets the entity by it's identifier.
        /// The id must be mapped in T's class as [Key]
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="id">The identifier.</param>
        /// <param name="commandTimeout">The command timeout.</param>
        /// <returns>
        /// The entity
        /// </returns>
        public T Get<T>(object id, int? commandTimeout = null) where T : class
        {
            return _connection.Get<T>(id, _transaction, commandTimeout);
        }

        /// <summary>
        /// Queries a stored procedure and returns the results list
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure">Stored procedure name</param>
        /// <param name="param">Stored procedures parameters</param>
        /// <param name="buffered">Indicates whether to buffer results in memory.</param>
        /// <param name="commandTimeout">Optional command timeout (in seconds)</param>
        /// <returns>
        /// The list of data typed as T
        /// </returns>
        public IList<T> QuerySP<T>(string storedProcedure, dynamic param = null, bool buffered = true, int? commandTimeout = null)
        {
            IEnumerable<T> output = _connection.Query<T>(
                storedProcedure,
                param: (object)param,
                transaction: _transaction,
                buffered: buffered,
                commandTimeout: commandTimeout,
                commandType: CommandType.StoredProcedure);

            return output?.ToList();
        }

        /// <summary>
        /// Queries a stored procedure and returns the results list
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure">Stored procedure name</param>
        /// <param name="param">Stored procedures parameters</param>
        /// <param name="commandTimeout">Optional command timeout (in seconds)</param>
        /// <param name="cancellationToken">The cancellation token.</param>
        /// <returns>
        /// The list of data typed as T
        /// </returns>
        public async Task<IList<T>> QuerySPAsync<T>(string storedProcedure, dynamic param = null, int? commandTimeout = null, CancellationToken? cancellationToken = null)
        {
            IEnumerable<T> output = await _connection.QueryAsync<T>(
                new CommandDefinition(
                    commandText: storedProcedure,
                    parameters: (object)param,
                    transaction: _transaction,
                    commandTimeout: commandTimeout,
                    commandType: CommandType.StoredProcedure,
                    flags: CommandFlags.Buffered,
                    cancellationToken: cancellationToken ?? CancellationToken.None
                )
            );

            return output?.ToList();
        }

        /// <summary>
        /// Executes a parameterized stored procedure
        /// </summary>
        /// <param name="storedProcedure">Stored procedure name</param>
        /// <param name="param">Stored procedures parameters</param>
        /// <param name="commandTimeout">The command timeout (in seconds)</param>
        /// <returns>
        /// The number of rows affected
        /// </returns>
        public int ExecuteSP(string storedProcedure, dynamic param = null, int? commandTimeout = null)
        {
            return _connection.Execute(
                storedProcedure,
                param: (object)param,
                transaction: _transaction,
                commandTimeout: commandTimeout,
                commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Executes a parameterized stored procedure
        /// </summary>
        /// <param name="storedProcedure">Stored procedure name</param>
        /// <param name="param">Stored procedures parameters</param>
        /// <param name="commandTimeout">The command timeout (in seconds)</param>
        /// <returns>
        /// The number of rows affected
        /// </returns>
        public async Task<int> ExecuteSPAsync(string storedProcedure, dynamic param = null, int? commandTimeout = null)
        {
            return await _connection.ExecuteAsync(
                storedProcedure,
                param: (object)param,
                transaction: _transaction,
                commandTimeout: commandTimeout,
                commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Execute parameterized SQL that selects a single value.
        /// </summary>
        /// <param name="storedProcedure">Stored procedure name</param>
        /// <param name="param">Stored procedures parameters</param>
        /// <param name="commandTimeout">The command timeout (in seconds)</param>
        /// <returns>
        /// The first cell selected as System.Object.
        /// </returns>
        public object ExecuteScalar(string storedProcedure, dynamic param = null, int? commandTimeout = null)
        {
            return _connection.ExecuteScalar(
                storedProcedure,
                param: (object)param,
                transaction: _transaction,
                commandTimeout: commandTimeout,
                commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Execute asynchronous parameterized SQL that selects a single value.
        /// </summary>
        /// <param name="storedProcedure">Stored procedure name</param>
        /// <param name="param">Stored procedures parameters</param>
        /// <param name="commandTimeout">The command timeout (in seconds)</param>
        /// <returns>
        /// The first cell selected as System.Object.
        /// </returns>
        public async Task<object> ExecuteScalarAsync(string storedProcedure, dynamic param = null, int? commandTimeout = null)
        {
            return await _connection.ExecuteScalarAsync(
                storedProcedure,
                param: (object)param,
                transaction: _transaction,
                commandTimeout: commandTimeout,
                commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Execute a command that returns multiple result sets, and access each in turn.
        /// </summary>
        /// <param name="storedProcedure">Stored procedure name</param>
        /// <param name="param">Stored procedures parameters</param>
        /// <param name="commandTimeout">The command timeout (in seconds)</param>
        /// <returns>
        /// The result sets
        /// </returns>
        public object QueryMultiple(string storedProcedure, dynamic param = null, int? commandTimeout = null)
        {
            return _connection.QueryMultiple(
                storedProcedure,
                param: (object)param,
                transaction: _transaction,
                commandTimeout: commandTimeout,
                commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Initializes the repository with the database context from the UnitOfWork
        /// </summary>
        /// <param name="connection">The connection</param>
        /// <param name="transaction">The transaction</param>
        public void Initialize(IDbConnection connection, IDbTransaction transaction)
        {
            if (transaction != null)
            {
                _transaction = transaction;
                _connection = transaction.Connection;
            }
            else
            {
                if (connection != null && connection.State != ConnectionState.Open)
                {
                    connection.Open();
                }
                _connection = connection;
            }
        }
    }
}
