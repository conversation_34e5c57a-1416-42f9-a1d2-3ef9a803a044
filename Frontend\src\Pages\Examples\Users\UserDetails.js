import { useCallback, useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { Box } from "@mui/material";
import { userAPI } from "../../../API/userAPI";
import AvatarMedal from "../../../Components/Avatar/AvatarMedal";
import toast from "react-hot-toast";
import UserDetailsForm from "../../../Components/Examples/Users/<USER>";

const UserDetails = () => {
  const [userDetails, setUserDetails] = useState();
  const params = useParams();

  const getUserDetails = useCallback(async () => {
    try {
      const getUserInfo = await userAPI.GetUserDetails(
        params.username,
        params["*"] === "/user/new"
      );
      if (getUserInfo && !getUserInfo.error) {
        setUserDetails(getUserInfo[0]);
      } else if (
        getUserInfo.exceptionMessages &&
        getUserInfo.exceptionMessages.hasMessages
      ) {
        getUserInfo.exceptionMessages.messages.forEach((m) => {
          toast.error(m.description);
        });
      } else {
        toast.error("Ocorreu um erro inesperado");
      }
    } catch (err) {
      console.error(err);
    }
  }, [params.username]);

  useEffect(() => {
    getUserDetails();
  }, []);

  return (
    <>
      <Box sx={{ p: "24px 32px 0px 32px" }}>
        <AvatarMedal
          image=""
          sublabel={userDetails?.name}
          textPosition="right"
        />

        <Box sx={{ py: 3 }}>
          <UserDetailsForm options={userDetails} />
        </Box>
      </Box>
    </>
  );
};

export default UserDetails;
