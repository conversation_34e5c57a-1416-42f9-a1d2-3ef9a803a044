import React from 'react';
import { Routes, Route } from 'react-router-dom';
import ClientList from './ClientList';
import ClientDetail from './ClientDetail';
import NewClient from './NewClient';

function ClientsRouter() {
    return (
        <Routes>
            <Route path="/" element={<ClientList />} />
            <Route path="/new" element={<NewClient />} />
            <Route path="/:id" element={<ClientDetail />} />
        </Routes>
    );
}

export default ClientsRouter;
