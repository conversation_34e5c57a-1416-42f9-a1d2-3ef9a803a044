{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"tenantId": {"type": "string", "defaultValue": "[subscription().tenantId]", "metadata": {"description": "Specifies the Azure Active Directory tenant ID that should be used for authenticating requests to the key vault. Get it by using Get-AzSubscription cmdlet."}}, "location": {"defaultValue": "[resourceGroup().location]", "type": "string", "metadata": {"description": "Provide the location for resources."}}, "keyVault-Name": {"defaultValue": "keyVault-dev", "type": "string"}, "keyVault-enabledForDeployment": {"type": "bool", "defaultValue": false, "allowedValues": [true, false], "metadata": {"description": "Specifies whether Azure Virtual Machines are permitted to retrieve certificates stored as secrets from the key vault."}}, "keyVault-enabledForDiskEncryption": {"type": "bool", "defaultValue": false, "allowedValues": [true, false], "metadata": {"description": "Specifies whether Azure Disk Encryption is permitted to retrieve secrets from the vault and unwrap keys."}}, "keyVault-enabledForTemplateDeployment": {"type": "bool", "defaultValue": false, "allowedValues": [true, false], "metadata": {"description": "Specifies whether Azure Resource Manager is permitted to retrieve secrets from the key vault."}}, "keyVault-SkuName": {"type": "string", "defaultValue": "Standard", "allowedValues": ["Standard", "Premium"], "metadata": {"description": "Specifies whether the key vault is a standard vault or a premium vault."}}, "accessPolicies": {"defaultValue": {"list": []}, "type": "object"}}, "variables": {"keyvaultName": "[concat(parameters('keyVault-Name'), uniqueString(resourceGroup().id))]"}, "resources": [{"type": "Microsoft.KeyVault/vaults", "apiVersion": "2016-10-01", "name": "[variables('keyvaultName')]", "location": "[parameters('location')]", "properties": {"sku": {"family": "A", "name": "[parameters('keyvault-skuName')]"}, "tenantId": "[parameters('tenantId')]", "enabledForDeployment": "[parameters('keyvault-enabledForDeployment')]", "enabledForDiskEncryption": "[parameters('keyvault-enabledForDiskEncryption')]", "enabledForTemplateDeployment": "[parameters('keyvault-enabledForTemplateDeployment')]", "accessPolicies": "[parameters('accessPolicies').list]"}, "tags": {"displayName": "keyvault"}}], "outputs": {"Azure.KeyVaultGeneratedName": {"type": "string", "value": "[variables('keyvaultName')]"}}}