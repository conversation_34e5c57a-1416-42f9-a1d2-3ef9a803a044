﻿using System.Collections.Generic;

namespace RnD.BackEnd.Email.Entities
{
    public class EmailMessage
    {
        public string FromEmailAddress { get; set; }
        public string FromUserName { get; set; }
        public object EmailData { get; set; }
        public List<string> Recipients { get; set; }
        public List<string> RecipientsCC { get; set; }
        public List<string> RecipientsBCC { get; set; }
        public string TemplateId { get; set; }
        public List<EmailAttachment> Attachments { get; set; }
    }
}
