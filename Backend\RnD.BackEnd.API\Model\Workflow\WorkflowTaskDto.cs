﻿using System;

namespace RnD.BackEnd.API.Model.Workflow
{
    /// <summary>
    /// Workflow task DTO class
    /// </summary>
    public class WorkflowTaskDto
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public Guid? Id { get; set; }

        /// <summary>
        /// Gets or sets the workflow instance identifier.
        /// </summary>
        /// <value>
        /// The workflow instance identifier.
        /// </value>
        public Guid WorkflowInstanceId { get; set; }

        /// <summary>
        /// Gets or sets the step identifier.
        /// </summary>
        /// <value>
        /// The step identifier.
        /// </value>
        public Guid StepId { get; set; }

        /// <summary>
        /// Gets or sets the user identifier.
        /// </summary>
        /// <value>
        /// The user identifier.
        /// </value>
        public string UserId { get; set; }

        /// <summary>
        /// Gets or sets the task status.
        /// </summary>
        /// <value>
        /// The task status.
        /// </value>
        public string TaskStatus { get; set; }

        /// <summary>
        /// Represents an event that is raised when a task either successfully or unsuccessfully completes.
        /// </summary>
        /// <value>
        ///   <c>true</c> if [task completed]; otherwise, <c>false</c>.
        /// </value>
        public bool TaskCompleted { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [task activated].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [task activated]; otherwise, <c>false</c>.
        /// </value>
        public bool TaskActivated { get; set; }

        /// <summary>
        /// Gets or sets the comments.
        /// </summary>
        /// <value>
        /// The comments.
        /// </value>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the source identifier.
        /// </summary>
        /// <value>
        /// The source identifier.
        /// </value>
        public string SourceId { get; set; }

        /// <summary>
        /// Gets or sets the source URL.
        /// </summary>
        /// <value>
        /// The source URL.
        /// </value>
        public string SourceUrl { get; set; }

        /// <summary>
        /// Gets or sets the workflow status.
        /// </summary>
        /// <value>
        /// The workflow status.
        /// </value>
        public string WorkflowStatus { get; set; }

        /// <summary>
        /// Gets or sets the name of the workflow.
        /// </summary>
        /// <value>
        /// The name of the workflow.
        /// </value>
        public string WorkflowName { get; set; }

        /// <summary>
        /// Gets or sets the name of the step.
        /// </summary>
        /// <value>
        /// The name of the step.
        /// </value>
        public string StepName { get; set; }

        /// <summary>
        /// Gets or sets the step order.
        /// </summary>
        /// <value>
        /// The step order.
        /// </value>
        public int StepOrder { get; set; }

        /// <summary>
        /// Gets or sets the system modify date.
        /// </summary>
        /// <value>
        /// The system modify date.
        /// </value>
        public DateTimeOffset SysModifyDate { get; set; }

        /// <summary>
        /// Gets or sets the total rows.
        /// </summary>
        /// <value>
        /// The total rows.
        /// </value>
        public int TotalRows { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether [user allowed to edit].
        /// </summary>
        /// <value>
        ///   <c>true</c> if [user allowed to edit]; otherwise, <c>false</c>.
        /// </value>
        public bool UserAllowedToEdit { get; set; }
    }
}
